<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_kbm.mapper.GroupChargeMapper">

    <select id="selectTypeList" resultType="java.lang.String">
        SELECT
            type
        FROM
            mqs_kbm_group_charge
        GROUP BY
            type
    </select>

    <select id="selectAdrgList" resultType="java.lang.String">
        SELECT
            adrg
        FROM
            mqs_kbm_group_charge
        GROUP BY
            adrg
    </select>

    <select id="selectAdrgNameList" resultType="java.lang.String">
        SELECT
            adrg_name
        FROM
            mqs_kbm_group_charge
        GROUP BY
            adrg_name
    </select>
</mapper>