package com.crhms.cloud.mqs.sys.mapper;

import com.crhms.cloud.mqs.sys.domain.SysFundsZone;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 运营管理-基金管理-院区(SysFundsZone)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-20 16:56:42
 */

@Mapper
public interface SysFundsZoneMapper extends BaseMapper<SysFundsZone> {

    /**
     * 批量更新
     * @param datas
     * @return
     */
    void batchUpdate(@Param("list") List<SysFundsZone> datas);

}

