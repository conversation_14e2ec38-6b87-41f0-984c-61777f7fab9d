package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.service.BaseAuditService;
import com.crhms.cloud.mqs.basic.vo.BaseMedicalParam;
import com.crhms.cloud.mqs.basic.vo.HisBaseAuditDto;
import com.crhms.cloud.mqs.basic.vo.EngAuditResultVo;
import com.crhms.cloud.mqs.mqs_hp.domain.HpTf;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpTfMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 住院审核-转院科室审核(HpTf)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 09:48:17
 */
@Service("hpTfService")
public class HpTfService extends ServiceImpl<HpTfMapper, HpTf> implements BaseAuditService {



    @Override
    public void beforeAudit(String batch, HisBaseAuditDto hisBaseAuditDto) {

    }

    @Override
    public void afterAudit(String batch, EngAuditResultVo result, HisBaseAuditDto hisBaseAuditDto, List<BaseMedicalParam> data) {

    }
}
