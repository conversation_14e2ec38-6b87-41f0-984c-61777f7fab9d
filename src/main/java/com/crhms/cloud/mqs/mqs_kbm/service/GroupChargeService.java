package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.GroupCharge;
import com.crhms.cloud.mqs.mqs_kbm.mapper.GroupChargeMapper;
import com.crhms.cloud.mqs.mqs_kbm.vo.GroupChargeVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 知识库管理-分组合理性-收费与入组合理性
 *
 * @author: wangxingang
 * @date: 2023.02.06
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class GroupChargeService extends ServiceImpl<GroupChargeMapper, GroupCharge> {
    /**
     * 查询列表
     *
     * @param pageInfo 分页信息
     * @param filter   查询条件
     * @return 查询列表
     */
    public List<GroupCharge> queryCharge(Page<GroupCharge> pageInfo, GroupChargeVo filter) {
        return page(pageInfo, new LambdaQueryWrapper<GroupCharge>()
                .in(CollUtil.isNotEmpty(filter.getTypeList()), GroupCharge::getType, filter.getTypeList()) // 类型
                .in(CollUtil.isNotEmpty(filter.getAdrgList()), GroupCharge::getAdrg, filter.getAdrgList()) // ADRG
                .in(CollUtil.isNotEmpty(filter.getAdrgNameList()), GroupCharge::getAdrgName, filter.getAdrgNameList()) // ADRG名称
                .like(StrUtil.isNotEmpty(filter.getProjCode()), GroupCharge::getProjCode, filter.getProjCode()) // 项目编码
                .like(StrUtil.isNotEmpty(filter.getProjName()), GroupCharge::getProjName, filter.getProjName()) // 项目名称
        ).getRecords();
    }

    /**
     * 查询类型下拉列表
     */
    public List<String> queryTypeList() {
        return baseMapper.selectTypeList();
    }

    /**
     * 查询ADRG下拉列表
     */
    public List<String> queryAdrgList() {
        return baseMapper.selectAdrgList();
    }

    /**
     * 查询ADRG名称下拉列表
     */
    public List<String> queryAdrgNameList() {
        return baseMapper.selectAdrgNameList();
    }
}
