package com.crhms.cloud.mqs.sys.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
public class SysLogsVO {

    //操作时间
    private String operatorTimeFrom;
    //操作时间
    private String operatorTimeTo;
    //操作人
    private String operatorName;
    //操作内容
    private String operatorText;
    //就诊流水号
    private String admissionNo;
    //明细号
    private String detailNo;
    //患者id/患者名称
    private String patientId;
    //项目编码/项目名称
    private String itemId;
    //项目时间
    private String itemDateFrom;
    //项目时间
    private String itemDateTo;

    /**
     * 分页信息
     */
    private Integer page;
    private Integer pageSize;


}
