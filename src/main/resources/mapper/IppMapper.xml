<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.sys.mapper.IppMapper">

    <resultMap type="com.crhms.cloud.mqs.sys.domain.IppDept" id="IppDeptMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="organizationCode" column="organization_code" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="medicalFlag" column="medical_flag" jdbcType="VARCHAR"/>
        <result property="costDeptCode" column="cost_dept_code" jdbcType="VARCHAR"/>
        <result property="costDeptName" column="cost_dept_name" jdbcType="VARCHAR"/>
        <result property="deptType" column="dept_type" jdbcType="VARCHAR"/>
        <result property="deptSpecialty" column="dept_specialty" jdbcType="VARCHAR"/>
        <result property="medicalType" column="medical_type" jdbcType="VARCHAR"/>
        <result property="parentDeptCode" column="parent_dept_code" jdbcType="VARCHAR"/>
        <result property="lastLevel" column="last_level" jdbcType="INTEGER"/>
        <result property="calcDirect" column="calc_direct" jdbcType="INTEGER"/>
        <result property="allocation" column="allocation" jdbcType="INTEGER"/>
        <result property="enabed" column="enabed" jdbcType="INTEGER"/>
        <result property="hospitalId" column="hospital_id" jdbcType="VARCHAR"/>
        <result property="dataSource" column="data_source" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>


    <select id="selectDeptList" resultType="com.crhms.cloud.mqs.sys.domain.IppDept">
        select
        organization_code, code, name
        from ipp_dept
        where enabed = '1'
        <if test="dept != null and dept != ''">
            and (code like concat('%',concat(#{dept},'%')) or name like concat('%',concat(#{dept},'%')))
        </if>

    </select>
    <select id="selectDoctorList" resultType="com.crhms.cloud.mqs.sys.domain.IppDoctor">
        select
        organization_code, code, name
        from ipp_doctor
        <if test="doctor != null and doctor != ''">
            and (code like concat('%',concat(#{doctor},'%')) or name like concat('%',concat(#{doctor},'%')))
        </if>

    </select>
    <select id="selectZoneList" resultType="com.crhms.cloud.mqs.sys.domain.IppZone">

        select
        organization_code, code, name
        from ipp_zone
        where 1=1
        <if test="zone != null and zone != ''">
            and (code like concat('%',concat(#{zone},'%')) or name like concat('%',concat(#{zone},'%')))
        </if>

    </select>
    <select id="selectUserList" resultType="java.util.Map">
        select
        id,
        username as code,
        realname as name
        from ipp_user
        <where>
            <if test="user != null and user != ''">
                and (username like concat('%',concat(#{user},'%')) or realname like concat('%',concat(#{user},'%')))
            </if>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectUserAuthDetp" resultType="java.lang.String">
        select dept_code
        from ipp_user_dept
        WHERE user_id =  #{userId}
    </select>
    <select id="selectHospitalName" resultType="java.lang.String">
        select
        `name`
        from ipp_hospital_area
        limit 1
    </select>

    <select id="selectZoneMap" resultType="com.crhms.cloud.mqs.sys.domain.IppZone">

        select
        code, name
        from ipp_zone
        where 1=1
        <if test="zone != null and zone != ''">
            and (code like concat('%',concat(#{zone},'%')) or name like concat('%',concat(#{zone},'%')))
        </if>

    </select>

    <select id="queryRoleByMenuCode" resultType="java.lang.String">
        SELECT irp.role_id
        FROM ipp_perms ip
        INNER JOIN ipp_role_perms irp ON ip.id = irp.perms_id
        AND ip.code = #{menuCode}
        AND ip.status = '1';
    </select>

    <select id="queryUserNameAndId" resultType="com.crhms.cloud.mqs.sys.dto.IppUser">
        SELECT iu.realname, iu.id, iu.username
        FROM ipp_user_role iur
        INNER JOIN ipp_user iu ON iu.id = iur.user_id
        WHERE iur.role_id IN
        <foreach collection="roleIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>
</mapper>

