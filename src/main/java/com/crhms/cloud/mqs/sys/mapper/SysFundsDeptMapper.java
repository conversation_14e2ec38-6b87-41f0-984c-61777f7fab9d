package com.crhms.cloud.mqs.sys.mapper;

import com.crhms.cloud.mqs.sys.domain.SysFundsDept;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 运营管理-基金管理-部门(SysFundsDept)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-12-19 09:40:47
 */

@Mapper
public interface SysFundsDeptMapper extends BaseMapper<SysFundsDept> {
    /**
     * 批量更新
     * @param datas
     * @return
     */
    void batchUpdate(@Param("list") List<SysFundsDept> datas);
}

