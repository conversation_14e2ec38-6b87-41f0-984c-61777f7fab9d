package com.crhms.cloud.mqs.sys.disruptor;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.crhms.cloud.mqs.sys.domain.SysLogs;
import com.crhms.cloud.mqs.sys.mapper.SysLogsMapper;
import com.crhms.cloud.mqs.sys.service.SysLogsService;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName SysLogHandler
 * @Description 日志处理类
 * <AUTHOR>
 * @Date 2024-11-01
 **/
@Component
@Slf4j
public class SysLogHandler implements EventHandler<SysLogDTO> {

    @Autowired
    private SysLogsService sysLogsService;
    @Override
    public void onEvent(SysLogDTO sysLogDTO, long l, boolean b) throws Exception {
        try{
            if(null == sysLogDTO){
                return;
            }
            List<SysLogs> logs = sysLogDTO.getLogs();
            if(CollectionUtil.isNotEmpty(logs)){
                sysLogsService.saveBatch(sysLogDTO.getLogs());
            }
        }catch (Exception e){
            log.error("日志记录消费发生异常: ");
            e.printStackTrace();
        }
    }

}
