package com.crhms.cloud.mqs.sys.disruptor;

import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.mqs_mr.domain.MrCase;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlanAudit;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class MrCaseHandlerDTO {

    private List<BaseMedicalCase> baseMedicalCaseList;

    private List<MrPlanAudit> mrPlanAudits;

    private Map<String, MrCase> caseMap;

    private List<String> sceneCodes;

    private String auditScenario;

    private String hospitalId;

}
