package com.crhms.cloud.mqs.mqs_mr.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.Date;


/**
 * 人工审核表(MrCase)实体类
 *
 * <AUTHOR>
 * @since 2023-05-02 10:06:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_mr_case")
public class MrCase implements Serializable {
    private static final long serialVersionUID = -56021938452238300L;

    public static final String BASE_TABLE="mqs_mr_base";

    public static final String FIELD_ID="id";
    public static final String FIELD_ADMISSION_NO="admission_no";
    public static final String FIELD_CASE_NO="case_no";
    public static final String FIELD_MR_STATUS="mr_status";
    public static final String FIELD_MR_OPINION="mr_opinion";
    public static final String FIELD_MR_TIME="mr_time";
    public static final String FIELD_HOSPITAL_ID="hospital_id";
    public static final String FIELD_LABLES="lables";
    public static final String FIELD_AUDIT_SCENARIO="audit_scenario";


    @TableId
    private Long id;


    /**
     * 就诊流水号
     */
    @TableField
    private String admissionNo;
    /**
     * 审核单据号
     */
    @TableField
    private String caseNo;
    /**
     * 人工审核状态
     */
    @TableField
    private String mrStatus;
    /**
     * 分配审核人
     */
    @TableField
    private String mrTo;
    /**
     * 分配审核人名称
     */
    @TableField
    private String mrToName;
    /**
     * 最新审核人
     */
    @TableField
    private String mrBy;
    /**
     * 最新审核人名称
     */
    @TableField
    private String mrByName;
    /**
     * 人工审核意见
     */
    @TableField
    private String mrOpinion;
    /**
     * 人工审核时间
     */
    @TableField
    private Date mrTime;
    /**
     * 院区id
     */
    @TableField
    private String hospitalId;

    /**
     * 场景字段
     */
    @TableField
    private String auditScenario;

    /**
     * 标签设置
     */
    @TableField
    private String lables;

    /**
     * 临床处理状态 （0-未处理、1-处理中、2-处理完成、3-无需处理、4-忽略、5-转自费）
     */
    @TableField
    private String clinicalStatus;


}

