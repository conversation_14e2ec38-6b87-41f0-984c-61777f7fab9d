package com.crhms.cloud.mqs.basic.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;


/**
 * 门诊审核-门诊处方审核(OpPct)实体类
 *
 * <AUTHOR>
 * @since 2022-12-19 09:40:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class BaseDetailQueryVO {
    //审核场景
    private String auditScenario;

    private List<String> auditScenarios;
    // 批次号
    private String batchNo;
    // no单据号 必输
    private String no;
    // 是否历史记录数据 必输
    private String isHis;
    // 院区id
    private String hospitalId;
    // 规则排序，用于优先排序和高亮显示
    private String ruleCode;
    //查询条件 规则 模糊
    private String rule;
    //查询条件 项目名称 模糊
    private String item;
    //查询条件 项目类型
    private String itemTypeCode;
    //医生
    private List<String> docIds;
    //科室
    private List<String> deptCodes;
    //住院医师
    private List<String> rtDoctorCodes;

    private String relatedRecords;

    //分页条件
    private int page;
    private int pageSize;


    //参保人编码
    private String patientId;
    //违规级别
    private List<String> ruleType;
    //审核日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String auditTimeFrom;
    //审核日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String auditTimeTo;
    //人工审核日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String mrTimeFrom;
    //人工审核日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String mrTimeTo;
    //结算日期开始
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String startBillDate;
    //结算日期结束
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endBillDate;
    //出院日期开始
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String startDischargeDate;
    //出院日期结束
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endDischargeDate;
    //险种类型
    private String benefitTypeId;

    //病区
    private List<String> inpatientAreas;
    //机审日期开始
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String startAuditTime;
    //机审日期结束
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endAuditTime;
    //就诊日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String itemDateFrom;
    //就诊日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String itemDateTo;
    //住院号/就诊流水号
    private String admissionNo;
    //参保人
    private String patient;
    //医疗类别
    private String claimTypeId;
    //是否违规 （1-违规、0-不违规）
    private String violationFlag;
    //审核状态 （）
    private String mrStatus;

    //是否出院带药（1-是，0-否）
    private String outpatientMedication;

}

