package com.crhms.cloud.mqs.mqs_bmi.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFbTemp;
import com.crhms.cloud.mqs.mqs_bmi.mapper.BmiFbTempMapper;
import com.crhms.cloud.mqs.mqs_bmi.service.BmiFbTempService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 医保管理-导入临时表(BmiFbTemp)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-10 10:40:07
 */
@Service("bmiFbTempService")
public class BmiFbTempService extends ServiceImpl< BmiFbTempMapper, BmiFbTemp> {

}
