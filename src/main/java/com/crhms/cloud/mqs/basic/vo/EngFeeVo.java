package com.crhms.cloud.mqs.basic.vo;

import cn.hutool.core.util.StrUtil;
import com.crhms.cloud.mqs.core.annotation.InitEngineFile;
import com.googlecode.jmapper.annotations.JMap;
import com.googlecode.jmapper.annotations.JMapConversion;
import lombok.Data;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class EngFeeVo {
    // 明细唯一编码	String(256)	必须为唯一值。	是
    @InitEngineFile(needIndex = false)
    @JMap("detailNo")
    private String id;

    @InitEngineFile(needIndex = true, tableColName = "no", colFileName = "admissionno")
    @JMap("no")
    private String admission_no;

    // 产品类型	String(2)	（0：药品，1：项目）	是
    @InitEngineFile(needIndex = true, tableColName = "ptype", colFileName = "ptype")
    @JMap("ptype")
    private String ptype;

    // 结算日期	String	格式：yyyy-MM-dd HH:mm:ss	是
    @InitEngineFile(needIndex = true, tableColName = "bill_date", colFileName = "billdate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JMap("billDate")
    private String billdate;

    // 项目ID	String(40)	医保编码	是
    @InitEngineFile(needIndex = true, tableColName = "item_id", colFileName = "itemid")
    @JMap("itemId")
    private String item_id;

    // 项目名称	String(256)		是
    @InitEngineFile(needIndex = true, tableColName = "item_name", colFileName = "itemname")
    @JMap("itemName")
    private String item_name;

    // 项目名称	String(256)		是
    @InitEngineFile(needIndex = true, tableColName = "item_date", colFileName = "itemdate")
    @JMap("itemDate")
    private String item_date;

    // 项目类型ID	String(40)	见数据字典5.6项目类型	是
    @InitEngineFile(needIndex = true, tableColName = "item_type_code", colFileName = "itemtypeid")
    @JMap("itemTypeCode")
    private String item_type_id;

    //甲乙类
    @InitEngineFile(needIndex = true, tableColName = "category_name", colFileName = "itemtypeid")
    @JMap("categoryName")
    private String category_name;

    // 项目名称	String(256)		是
    @InitEngineFile(needIndex = true, tableColName = "display_item_id", colFileName = "displayitemid")
    @JMap("hisItemId")
    private String display_item_id;

    // 项目名称	String(256)		是
    @InitEngineFile(needIndex = true, tableColName = "display_item_name", colFileName = "displayitemname")
    @JMap("hisItemName")
    private String display_item_name;

    // 项目类型名称	String(40)		是
    @InitEngineFile(needIndex = true, tableColName = "item_type_name", colFileName = "itemtypename")
    @JMap("itemTypeName")
    private String item_type_name;

    //是否当次明细 1是 0否
    @JMap("isCurrent")
    private String is_current;

    // 数量	Number(128,4)	默认0不能为空	是
    @InitEngineFile(needIndex = false)
    @JMap("numbers")
    private Double numbers;

    // 单价	Number(128,4)		是
    @InitEngineFile(needIndex = false)
    @JMap("price")
    private Double price;

    // 总费用	Number(128,4)		是
    @InitEngineFile(needIndex = false)
    @JMap("costs")
    private Double costs;

    // 开单科室名称	String(256)		是
    @InitEngineFile(needIndex = true, tableColName = "apply_dept_name", colFileName = "deptnamebill")
    @JMap("applyDeptCode")
    private String dept_code_bill;

    // 开单科室名称	String(256)		是
    @InitEngineFile(needIndex = true, tableColName = "apply_dept_name", colFileName = "deptnamebill")
    @JMap("applyDeptName")
    private String dept_name_bill;

    // 医生编码	String(256)		是
    @InitEngineFile(needIndex = true, tableColName = "apply_doctor_code", colFileName = "physicianid")
    @JMap("applyDoctorCode")
    private String physician_id;

    // 医生名称	String(256)		是
    @InitEngineFile(needIndex = true, tableColName = "apply_doctor_name", colFileName = "physicianname")
    @JMap("applyDoctorName")
    private String physician_name;

    // 医师级别	String(2)	1:初级 2:中级 3:副高 4:正高	是
    @InitEngineFile(needIndex = true, tableColName = "apply_doctor_level", colFileName = "physicianlevel")
    @JMap("applyDoctorLevel")
    private String physician_level;

    // 包装单位	String(256)		是
    @InitEngineFile(needIndex = true, tableColName = "usage_unit", colFileName = "usageunit")
    @JMap("usageUnit")
    private String usage_unit;

    // 规格	String(256)		是
    @InitEngineFile(needIndex = true, tableColName = "specification", colFileName = "specification")
    @JMap("specification")
    private String specification;

    //用药天数	String(256)	整数，默认值 0	是
    @InitEngineFile(needIndex = true, tableColName = "usage_days", colFileName = "usagedays")
    @JMap("usageDays")
    private String usage_days;

    // 每次用量	String(256)	西药最多2位小数点，填一次服用的片数，如一次2片的2。	是			默认值 0
    @InitEngineFile(needIndex = true, tableColName = "usage", colFileName = "usage")
    @JMap("usage")
    private String usage;

    // 使用频次	String(256)	见数据字典5.7用药频次	是
    @InitEngineFile(needIndex = true, tableColName = "frequency_interval", colFileName = "frequencyinterval")
    @JMap("frequencyInterval")
    private String frequency_interval;

    // 医保内金额	Number(128,4)	全自费的为0，默认等于总金额	是
    @InitEngineFile(needIndex = false)
    @JMap("bmiConveredAmount")
    private Double eligible_amount;

    // 医保内金额	Number(128,4)	全自费的为0，默认等于总金额	是
    @InitEngineFile(needIndex = false)
    @JMap("bmiOverallAmount")
    private Double bmi_overall_amount;


    // 备案审批号	String(256)	规定病、特药、特治、特检，没有时填””空字符串	否
    @InitEngineFile(needIndex = true, tableColName = "approval_number", colFileName = "approvalnumber")
    @JMap("approvalNumber")
    private String approval_number;

    // 医师行政职务	String(256)	默认为空字符串	否
    @InitEngineFile(needIndex = true, tableColName = "z_physicianap", colFileName = "zphysicianap")
    @JMap("zPhysicianap")
    private String z_physicianap;

    // 帖数	String(256)	饮片贴数  默认0不能为空	是
    @InitEngineFile(needIndex = true, tableColName = "posts_number", colFileName = "postsnumber")
    @JMap("postsNumber")
    private String posts_number;

    // 自付比例	String(128,4)	默认0不能为空	是
    @InitEngineFile(needIndex = false)
    @JMap("payRatio")
    private Double pay_ratio;

    // 出国带药标志	String(2)	1 是	是	;// 		0 否   默认0不能为空
    @InitEngineFile(needIndex = true, tableColName = "abroad_drug_flag", colFileName = "abroaddrugflag")
    @JMap("abroadDrugFlag")
    private String abroad_drug_flag;
    // 出院带药标记	String(2)	1 是	是		0 否   默认0不能为空
    @JMap("outpatientMedication")
    private String is_discharge_item;
    // 外院费用标志	String(2)	1 是	是		0 否   默认0不能为空
    @InitEngineFile(needIndex = true, tableColName = "out_hospital_flag", colFileName = "outhospitalflag")
    @JMap("outHospitalFlag")
    private String out_hospital_flag;

    // 计费标记	String(2)	-1未设置 0未计费 1已计费	是		住院默认是已计费的   	门诊治疗费默认是已计费	门诊其他费根据结算时标记已计费为准
    @InitEngineFile(needIndex = true, tableColName = "charging_flag", colFileName = "chargingflag")
    @JMap("chargingFlag")
    private String charging_flag;

    // 标记是否为被冲销行 0非冲销明细，1冲销明细 ，冲销明细引擎做删除处理
    @InitEngineFile(needIndex = true, tableColName = "reverse_flag", colFileName = "reverseFlag")
    @JMap("reverseFlag")
    private String reverse_flag;

    /*以下字段为医保审核新增相关字段 */

    @JMap("hisItemId")
    private String hosplist_code;
    @JMap("hisItemName")
    private String hosplist_name;
    //是否长期医嘱
    @JMap("longDrordFlag")
    private String long_drord_flag;
    //目录类别
    @JMap("hilistType")
    private String hilist_type;
    //收费类别
    @JMap("chrgType")
    private String chrg_type;
    //医嘱行为
    @JMap("drordBhvr")
    private String drord_bhvr;
    //医保目录等级
    @JMap("hilistLv")
    private String hilist_lv;
    //医保目录价格
    @JMap("hilistPric")
    private Double hilist_pric;
    //自费金额
    @JMap("ownpayAmt")
    private Double ownpay_amt;
    //自付金额
    @JMap("selfpayAmt")
    private Double selfpay_amt;

    /**
     * jmapper类型转换器
     */
    @JMapConversion(from = {"billDate"},
            to = {"billdate"})
    public String convertDateToString(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        return date == null ? null : format.format(date);
    }

    /**
     * jmapper类型转换器
     */
    @JMapConversion(from = {"itemDate"},
            to = {"item_date"})
    public String convertItemDateToString(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        return date == null ? null : format.format(date);
    }

    @JMapConversion(from = {"payRatio","costs", "price", "numbers","bmiOverallAmount","bmiConveredAmount","hilistPric","ownpayAmt","selfpayAmt"},
            to = { "pay_ratio","costs","price","numbers","bmi_overall_amount","eligible_amount","hilist_pric","ownpay_amt","selfpay_amt"})
    public Double convertBigToDou(BigDecimal num) {
        return num == null ? null : num.doubleValue();
    }

}
