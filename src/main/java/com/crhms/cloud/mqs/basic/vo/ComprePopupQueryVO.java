package com.crhms.cloud.mqs.basic.vo;

import lombok.Data;

import java.util.List;

/*
* 综合弹窗查询VO
* */
@Data
public class ComprePopupQueryVO {

    //批次号 (用于区分正常审核渠道进入还是质控患者列表进入)
    private String batchNo;

    //就诊流水号
    private String admissionNo;

    //单据号
    private String no;

    //明细号
    private List<String> detailNos;

    //审核渠道
    private String auditSource;

    //自费状态 （1-是（自费），0-否（医保）2-转自费）
    private String selfExpense;

    //违规级别
    private List<String> ruleTypes;

    //项目名称
    private String itemName;

    //项目类型名称
    private String ptype;

    //项目类型编码
    private String itemTypeCode;

    //场景编码
    private String auditScenario;

    //临床处理状态 （0-未处理、1-处理中、2-处理完成、3-无需处理、4-忽略、5-转自费）
    private String clinicalStatus;

    //是否忽略（1-是，0-否）
    private String isIgnore;

    //甲乙类
    private String categoryName;

    //当前科室
    private String presentDeptCode;

    //主诊医生
    private String docId;

    //违规标识 （1-违规，0-未违规）
    private String violationFlag;

    private String hospitalId;

    //是否增量（true-增量，false-全量）  用于审核渠道分辨his传入的明细是新增还是全量
    private String isIncrement;

    //是否出院带药（1-是，0-否）
    private String outpatientMedication;

    private String isHis;

}
