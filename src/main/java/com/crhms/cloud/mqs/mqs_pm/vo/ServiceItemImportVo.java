package com.crhms.cloud.mqs.mqs_pm.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * 导入接口VO
 * @author: wangxingang
 * @date: 2023/02/01
 */

@Data
public class ServiceItemImportVo {

    /**
     * 生效时间
     */
    @DateTimeFormat(pattern = "yyyy.MM.dd")
    private Date effectTime;

    /**
     * Excel文件
     */
    private MultipartFile file;

    /**
     * 项目编码集合
     */
    private List<String> projectCodeList;
}
