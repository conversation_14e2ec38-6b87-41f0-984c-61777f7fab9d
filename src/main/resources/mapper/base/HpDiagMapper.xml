<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.basic.mapper.HpDiagMapper">

    <sql id="baseSql">
        id,diagnosis_no, no,admission_no,patient_id,patient_name,diagnosis_code,diagnosis_name,diag_dept_code,diag_dept_name,main_flag,diag_inout_type,diag_type,diagnosis_order,diagnosis_time,diagnosis_situation,last_updated_by,last_updated_date,created_by,created_date,hospital_id,batch_no
    </sql>

    <insert id="batchSaveDiag">
        insert into mqs_hp_diag(diagnosis_no, no, admission_no,patient_id,patient_name,diagnosis_code,diagnosis_name,diag_dept_code,diag_dept_name,main_flag,diag_inout_type,diag_type,diagnosis_order,diagnosis_time,diagnosis_situation,last_updated_by,last_updated_date,created_by,created_date,hospital_id,batch_no)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.diagnosisNo},#{entity.no}, #{entity.admissionNo}, #{entity.patientId}, #{entity.patientName}, #{entity.diagnosisCode}, #{entity.diagnosisName}, #{entity.diagDeptCode},#{entity.diagDeptName}, #{entity.mainFlag}, #{entity.diagInoutType}, #{entity.diagType}, #{entity.diagnosisOrder}, #{entity.diagnosisTime}, #{entity.diagnosisSituation}, #{entity.lastUpdatedBy}, now(), #{entity.createdBy},now(), #{entity.hospitalId}, #{entity.batchNo})
        </foreach>
    </insert>

    <insert id="batchSaveDiagHis">
        insert into mqs_hp_diag_his(diagnosis_no, no, admission_no,patient_id,patient_name,diagnosis_code,diagnosis_name,diag_dept_code,diag_dept_name,main_flag,diag_inout_type,diag_type,diagnosis_order,diagnosis_time,diagnosis_situation,last_updated_by,last_updated_date,created_by,created_date,hospital_id,batch_no)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.diagnosisNo},#{entity.no}, #{entity.admissionNo}, #{entity.patientId}, #{entity.patientName}, #{entity.diagnosisCode}, #{entity.diagnosisName}, #{entity.diagDeptCode},#{entity.diagDeptName}, #{entity.mainFlag}, #{entity.diagInoutType}, #{entity.diagType}, #{entity.diagnosisOrder}, #{entity.diagnosisTime}, #{entity.diagnosisSituation}, #{entity.lastUpdatedBy}, now(), #{entity.createdBy},now(), #{entity.hospitalId}, #{entity.batchNo})
        </foreach>
    </insert>

    <delete id="deleteDiagByAdNo">
        DELETE FROM mqs_hp_diag
        WHERE
        hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and admission_no = #{admissionNo}
        </if>
        <if test="nos != null and nos.size() >0 ">
            and `no` in
            <foreach collection="nos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="diagNos != null and diagNos.size() >0 ">
            and diagnosis_no in
            <foreach collection="diagNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

    </delete>

    <select id="queryDiagList" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDiag">
        select
        <include refid="baseSql"/>
        from
        mqs_hp_diag
        where hospital_id = #{hospitalId}
        and admission_no = #{admissionNo}
        <if test="no != null and no != ''">
            and `no` = #{no}
        </if>
    </select>

    <select id="queryDiagListHis" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDiag">
        select
        <include refid="baseSql"/>
        from
        mqs_hp_diag_his
        where hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and admission_no = #{admissionNo}
        </if>
        and batch_no = #{batch}
        <if test="no != null and no != ''">
            and `no` = #{no}
        </if>
    </select>
    <select id="queryMedicalDiags" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDiag">

        select
        <include refid="baseSql"/>
        from
        mqs_hp_diag
        <where>
            <if test="hospitalId != null and hospitalId != ''">
                and hospital_id = #{hospitalId}
            </if>
            <if test="admissionNo != null and admissionNo != ''">
                and admission_no = #{admissionNo}
            </if>
            <if test="no != null and no != ''">
                and `no` = #{no}
            </if>
            <if test="diagnosisNo != null and diagnosisNo != ''">
                and diagnosis_no = #{diagnosisNo}
            </if>
            <if test="mainFlag != null and mainFlag != ''">
                and main_flag = #{mainFlag}
            </if>
            <if test="diagInoutType != null and diagInoutType != ''">
                and diag_inout_type = #{diagInoutType}
            </if>
            <if test="diagType != null and diagType != ''">
                and diag_type = #{diagType}
            </if>
        </where>
        order by main_flag desc, diag_inout_type desc, diagnosis_order desc

    </select>

</mapper>

