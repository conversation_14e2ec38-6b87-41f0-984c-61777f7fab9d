package com.crhms.cloud.mqs.mqs_bmi.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.excel.dto.ExcelExport;
import com.crhms.cloud.core.excel.dto.SheetData;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.mqs_bmi.domain.*;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiDeduction;
import com.crhms.cloud.mqs.mqs_bmi.mapper.BmiDeductionMapper;
import com.crhms.cloud.mqs.mqs_bmi.vo.BmiQueryVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 医保管理-医保扣费(BmiDeduction)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-14 10:43:09
 */
@Service("bmiDeductionService")
public class BmiDeductionService extends ServiceImpl< BmiDeductionMapper, BmiDeduction> {
    @Autowired
    private BmiDeductionTempService bmiDeductionTempService;
    @Autowired
    private BmiFbService bmiFbService;
    /**
     * 院内反馈页面查询 - 用户权限
     *
     * @param bmiQueryVo
     * @param hospitalId
     * @param myPage
     * @return
     */
    public List<BmiDeduction> queryByPage(BmiQueryVo bmiQueryVo, String hospitalId, Page myPage) {
        bmiQueryVo.setHospitalId(hospitalId);
        return this.baseMapper.queryByPage(bmiQueryVo, myPage);
    }

    public List<Map<String,String>> selectDeptList(String dept, String hospitalId) {
        return this.baseMapper.selectDeptList(dept,hospitalId);
    }

    public List<Map<String,String>> selectDoctorList(String doctor, String hospitalId) {
        return this.baseMapper.selectDoctorList(doctor,hospitalId);
    }

    public List<String> selectRules(String rule, String hospitalId) {
        return  this.baseMapper.selectRules(rule,hospitalId);
    }

    public Map importFbExcel(MultipartFile file, String hospitalId) {
        Map<String, String> resultMap = new HashMap<>();
        //生成批次号
        String batchNo = MqsUtils.getUUBatch();
        // 获取sheet页
        List<SheetData> sheets = BigExcelUtil.parseExcel(file).getSheets();

        // 校验Excel
        bmiFbService.checkExcel(sheets);
        // 解析Excel数据
        List<BmiDeductionTemp> importDatas = parseExcel(sheets);
        //判断重复单据号
        List<String> nos = importDatas.stream().map(BmiDeductionTemp::getNo).collect(Collectors.toList());
        List<BmiDeduction> list = this.list(new QueryWrapper<BmiDeduction>().select(BmiDeduction.FIELD_NO).eq(BmiDeduction.FIELD_HOSPITAL_ID, hospitalId).in(BmiDeduction.FIELD_NO, nos));
        List<String> repeatNos = list.stream().map(BmiDeduction::getNo).collect(Collectors.toList());
        importDatas.stream().forEach( x -> {
            x.setHospitalId(hospitalId);
            x.setBatchNo(batchNo);
            if(repeatNos.contains(x.getNo())){
                x.setIsRepeat("1");
            }else {
                x.setIsRepeat("0");
            }
        } );
        //批量保存数据
        bmiDeductionTempService.saveBatch(importDatas,2000);
        resultMap.put("batchNo", batchNo);
        return resultMap;
    }

    //导入文件解析
    List<BmiDeductionTemp> parseExcel(List<SheetData> sheetDatas) {
        // 获取数据
        List<BmiDeductionTemp> headDatas = new ArrayList<>();
        List<BmiDeductionTemp> detailDatas = new ArrayList<>();
        //单据扣除金额汇总
        SheetData headSheet = sheetDatas.get(1);
        //明细扣款
        SheetData detailSheet = sheetDatas.get(0);

        //解析
        if (headSheet.getLines().size() > 0) {
            for (int i = 1; i < headSheet.getLines().size(); i++) {
                List<Object> head = headSheet.getLines().get(i);
                headDatas.add(BmiDeductionTemp.builder().billDate(BigExcelUtil.getItemDate(head, 1))
                        .benefitType(BigExcelUtil.getItemStr(head, 2))
                        .no(BigExcelUtil.getItemStr(head, 3))
                        .bmiConveredAmount(MqsUtils.getBigDecimal(BigExcelUtil.getItem(head, 4)))
                        .admissionNo(BigExcelUtil.getItemStr(head, 5))
                        .patientId(BigExcelUtil.getItemStr(head, 6))
                        .org(BigExcelUtil.getItemStr(head, 7))
                        .feeOrgCode(BigExcelUtil.getItemStr(head, 8))
                        .feeOrgName(BigExcelUtil.getItemStr(head, 9))
                        .deductionAmount1(MqsUtils.getBigDecimal(BigExcelUtil.getItem(head, 10)))
                        .deductionAmount2(MqsUtils.getBigDecimal(BigExcelUtil.getItem(head, 11)))
                        .deductionAmount3(MqsUtils.getBigDecimal(BigExcelUtil.getItem(head, 12)))
                        .build());
            }
        }
        Map<String, BmiDeductionTemp> headMaps = headDatas.stream().collect(Collectors.toMap(BmiDeductionTemp::getNo, e -> e, (k1, k2) -> k1));

        if (detailSheet.getLines().size() > 0) {
            for (int i = 1; i < detailSheet.getLines().size(); i++) {
                List<Object> detail = detailSheet.getLines().get(i);
                String no = BigExcelUtil.getItemStr(detail, 2);
                BmiDeductionTemp BmiDeduction = headMaps.get(no) == null ? new BmiDeductionTemp() : headMaps.get(no);

                detailDatas.add(BmiDeductionTemp.builder().detailNo(BigExcelUtil.getItemStr(detail, 1))
                        .no(BigExcelUtil.getItemStr(detail, 2))
                        .billDate(BigExcelUtil.getItemDate(detail, 3))
                        .fbComplain(BigExcelUtil.getItemStr(detail, 4))
                        .deptCode(BigExcelUtil.getItemStr(detail, 5))
                        .deptName(BigExcelUtil.getItemStr(detail, 6))
                        .itemId(BigExcelUtil.getItemStr(detail, 7))
                        .docId(BigExcelUtil.getItemStr(detail, 8))
                        .docName(BigExcelUtil.getItemStr(detail, 9))
                        .itemName(BigExcelUtil.getItemStr(detail, 10))
                        .proposalCode(BigExcelUtil.getItemStr(detail, 11))
                        .bmiConveredAmount(MqsUtils.getBigDecimal(BigExcelUtil.getItem(detail, 12)))
                        .deductionAmount(MqsUtils.getBigDecimal(BigExcelUtil.getItem(detail, 13)))
                        .rules(BigExcelUtil.getItemStr(detail, 14))
                        .deductionReason(BigExcelUtil.getItemStr(detail, 15))
                        .returnReason(BigExcelUtil.getItemStr(detail, 16))
                        .itemDate(BigExcelUtil.getItemDate(detail, 17))
                        .numbers(MqsUtils.getBigDecimal(BigExcelUtil.getItem(detail, 18)))
                        .price(MqsUtils.getBigDecimal(BigExcelUtil.getItem(detail, 19)))
                        .claimType(BigExcelUtil.getItemStr(detail, 20))
                        .patientId(BigExcelUtil.getItemStr(detail, 21))
                        .patientName(BigExcelUtil.getItemStr(detail, 22))
                        .feeOrgCode(BigExcelUtil.getItemStr(detail, 23))
                        .feeOrgName(BigExcelUtil.getItemStr(detail, 24))
                        .admissionNo(BigExcelUtil.getItemStr(detail, 25))
                        .benefitType(BigExcelUtil.getItemStr(detail, 26))
                        .personnelType(BigExcelUtil.getItemStr(detail, 27))
                        .specification(BigExcelUtil.getItemStr(detail, 28))
                        .usages(BigExcelUtil.getItemStr(detail, 29))
                        .ptype(BigExcelUtil.getItemStr(detail, 30))
                        .version(BigExcelUtil.getItemStr(detail, 31))
                        .deductionAmount1(BmiDeduction.getDeductionAmount1())
                        .deductionAmount2(BmiDeduction.getDeductionAmount2())
                        .deductionAmount3(BmiDeduction.getDeductionAmount3())
                        .isRepeat("0")                                                  //填充默认值
                        .build());
            }
        }
        return detailDatas;
    }

    public List<BmiDeductionTemp> queryImportByPage(String batchNo, String hospitalId, Page myPage) {
        return bmiDeductionTempService.page(myPage,new QueryWrapper<BmiDeductionTemp>().eq(BmiDeductionTemp.FIELD_HOSPITAL_ID,hospitalId).eq(BmiDeductionTemp.FIELD_BATCH_NO,batchNo).eq(BmiDeductionTemp.FIELD_IS_REPEAT,"1")).getRecords();
    }

    public void loadIn(String batchNo, String hospitalId) {
        QueryWrapper<BmiDeductionTemp> queryWrapper = new QueryWrapper<BmiDeductionTemp>().eq(BmiDeductionTemp.FIELD_BATCH_NO, batchNo).eq(BmiDeductionTemp.FIELD_HOSPITAL_ID, hospitalId);
        //忽略标记的数据不导入
        queryWrapper.notIn(BmiDeductionTemp.FIELD_IS_REPEAT, "2");
        List<BmiDeductionTemp> list = bmiDeductionTempService.list(queryWrapper);
        List<BmiDeduction> bmiFbList = new ArrayList<>();
        Set<String> collect = list.stream().map(BmiDeductionTemp::getNo).collect(Collectors.toSet());
        //删除已有的单据号相同的数据
        if(CollectionUtil.isNotEmpty(collect)){
            this.remove(new QueryWrapper<BmiDeduction>().eq(BmiDeduction.FIELD_HOSPITAL_ID, hospitalId).in(BmiDeduction.FIELD_NO, collect));
        }
        //保存数据
        for (BmiDeductionTemp bmiFbTemp : list) {
            BmiDeduction bmiFb = new BmiDeduction();
            BeanUtils.copyProperties(bmiFbTemp, bmiFb);
            bmiFbList.add(bmiFb);
        }
        this.saveBatch(bmiFbList, 2000);
        //清空记录
        bmiDeductionTempService.remove(new QueryWrapper<BmiDeductionTemp>().eq(BmiDeductionTemp.FIELD_BATCH_NO, batchNo).eq(BmiDeductionTemp.FIELD_HOSPITAL_ID, hospitalId));
    }


    /**
     * 扣费分析 指标统计
     * @param itemDateFrom
     * @param itemDateTo
     * @param dept
     * @param hospitalId
     * @return
     */
    public Map indStatistics(String itemDateFrom, String itemDateTo, String dept,String hospitalId){
        return this.baseMapper.indStatistics(itemDateFrom, itemDateTo, dept, hospitalId);
    }

    /**
     * 统计
     * @param itemDateFrom
     * @param itemDateTo
     * @param dept
     * @param orderBy
     * @param hospitalId
     * @return
     */
    public List<Map> genViolationTop(String itemDateFrom, String itemDateTo, String dept, String orderBy, String hospitalId) {
        if(!"rules".equals(orderBy) && !"item_name".equals(orderBy)){
            throw new BaseException("选择统计维度：rules、item_name");
        }
        return this.baseMapper.genViolationTop(itemDateFrom, itemDateTo, dept, orderBy, hospitalId);

    }

    /**
     * 统计分析 部门/医生违规数据
     * @param itemDateFrom
     * @param itemDateTo
     * @param dept
     * @param orderBy
     * @param hospitalId
     * @return
     */
    public List<Map> getAnalysisDetails(String itemDateFrom, String itemDateTo, String dept, String orderBy, String hospitalId) {

        if(!"dept".equals(orderBy) && !"doc".equals(orderBy)){
            throw new BaseException("选择统计维度：dept、doc");
        }

        if("dept".equals(orderBy)){
            orderBy = "dept_code,dept_name";
        }else {
            orderBy = "doc_id,doc_name";
        }
        List<Map> analysisDetails = this.baseMapper.getAnalysisDetails(itemDateFrom, itemDateTo, dept, orderBy, hospitalId);
        BigDecimal analysisTotal = this.baseMapper.getAnalysisTotal(itemDateFrom, itemDateTo, dept, hospitalId);
        for (Map analysisDetail : analysisDetails) {
            analysisDetail.put("ratio", MqsUtils.getBigDecimal(analysisDetail.get("total")).divide(analysisTotal,4,BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(100L)));
        }
        return analysisDetails;
    }

    /**
     * 扣费时间趋势
     * @param itemDateFrom
     * @param itemDateTo
     * @param hospitalId
     * @return
     */
    public List<Map> getAnalysisByMonth(String itemDateFrom, String itemDateTo, String dept,String hospitalId) {
        return this.baseMapper.getAnalysisByMonth(itemDateFrom, itemDateTo,dept, hospitalId);
    }

    public void ignoreData(String batchNo, List<String> ignoreNos, String hospitalId) {
        bmiDeductionTempService.update(new UpdateWrapper<BmiDeductionTemp>().set(BmiDeductionTemp.FIELD_IS_REPEAT,"2").eq(BmiDeductionTemp.FIELD_BATCH_NO,batchNo).eq(BmiDeductionTemp.FIELD_HOSPITAL_ID,hospitalId).in(BmiDeductionTemp.FIELD_NO,ignoreNos));
    }

    public void coverData(String batchNo, List<String> coverNos, String hospitalId) {
        bmiDeductionTempService.update(new UpdateWrapper<BmiDeductionTemp>().set(BmiDeductionTemp.FIELD_IS_REPEAT,"3").eq(BmiDeductionTemp.FIELD_BATCH_NO,batchNo).eq(BmiDeductionTemp.FIELD_HOSPITAL_ID,hospitalId).in(BmiDeductionTemp.FIELD_NO,coverNos));
    }

    public List<String> selectClaimTypeList(String claimType, String hospitalId) {
        return this.baseMapper.selectClaimTypeList(claimType,hospitalId);
    }


    /**
     * 导出医保扣费
     * @param response   响应
     * @param bmiQueryVo 请求参数
     */
    public void export(HttpServletResponse response, BmiQueryVo bmiQueryVo) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        heads.add(CollUtil.list(true,
                "DETAIL_NO", "NO", "BILL_DATE", "DEPT_CODE", "DEPT_NAME", "ITEM_ID", "DOC_ID", "DOC_NAME",
                "ITEM_NAME", "PROPOSAL_CODE", "BMI_CONVERED_AMOUNT", "DEDUCTION_AMOUNT", "RULES", "DEDUCTION_REASON", "RETURN_REASON",
                "ITEM_DATE", "NUMBERS", "PRICE", "CLAIM_TYPE", "PATIENT_ID", "PATIENT_NAME", "FEE_ORG_CODE", "FEE_ORG_NAME",
                "ADMISSION_NO", "BENEFIT_TYPE", "PERSONNEL_TYPE", "SPECIFICATION", "USAGES", "PTYPE", "VERSION"));
        heads.add(CollUtil.list(true,
                "交易流水号", "单据号", "结算日期", "科室编码", "科室名称", "项目编码", "医生编码", "医生名称",
                "项目名称", "意见书编码", "医保内金额", "扣除金额", "规则名称", "扣款原因", "还款原因",
                "费用日期", "数量", "单价", "就医方式", "个人编号", "参保人姓名", "费用机构编码", "费用机构名称",
                "住院号", "参保类型", "人员类别", "规格", "用量", "类别", "版本"));

        List<BmiDeduction> list = queryByPage(bmiQueryVo, LoginContext.getHospitalId(), null);

        for (BmiDeduction item : list) {
            List<Object> line = CollUtil.list(true,
                    item.getDetailNo(), item.getNo(), DateUtil.format(item.getBillDate(), "yyyy-MM-dd HH:mm:ss"), item.getDeptCode(), item.getDeptName(), item.getItemId(), item.getDocId(), item.getDocName(),
                    item.getItemName(), item.getProposalCode(), item.getBmiConveredAmount(), item.getDeductionAmount(), item.getRules(), item.getDeductionReason(), item.getReturnReason(),
                    DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"), item.getNumbers(), item.getPrice(), item.getClaimType(), item.getPatientId(), item.getPatientName(), item.getFeeOrgCode(), item.getFeeOrgName(),
                    item.getAdmissionNo(), item.getBenefitType(), item.getPersonnelType(), item.getSpecification(), item.getUsages(), item.getPtype(), item.getVersion());
            datas.add(line);
        }

        StringBuilder fileName = new StringBuilder();
        fileName.append("医保管理-医保扣费");
        MqsUtils.buildExportFileNameSuffix(fileName, bmiQueryVo.getBillDateFrom(), bmiQueryVo.getBillDateTo());

        List<ExcelExport> exports = new ArrayList<>(1);
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }
}
