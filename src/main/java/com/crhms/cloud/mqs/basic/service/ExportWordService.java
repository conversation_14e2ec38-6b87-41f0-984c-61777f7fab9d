package com.crhms.cloud.mqs.basic.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.sys.domain.IppZone;
import com.crhms.cloud.mqs.sys.domain.SysConfig;
import com.crhms.cloud.mqs.sys.service.IppService;
import com.crhms.cloud.mqs.sys.service.SysConfigService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTJc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class ExportWordService {
    @Autowired
    ResourceLoader resourceLoader;
    @Lazy
    @Autowired
    SysConfigService sysConfigService;
    @Autowired
    IppService ippService;


    /**
     * 自费协议导出通用方法
     * @param insertTextMap      填充映射数据
     * @param tableTagList       填充表格数据
     * @param fileName  导出文件文件名
     * @param fileTemp  使用模板文件名
     * @param response
     * @param hospitalId
     */
    public void exportSelfAgreement(Map insertTextMap, List<TableTag> tableTagList,String fileName, String fileTemp, HttpServletResponse response, String hospitalId) {

        // 设置模板输入和结果输出
        try {
            Resource resource = resourceLoader.getResource("classpath:template/" + fileTemp);
            InputStream inputStream = resource.getInputStream();
            ServletOutputStream outputStream = response.getOutputStream();

            response.setContentType("application/msword;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    java.net.URLEncoder.encode( fileName+ ".docx"));
            // 生成word
            generateWord(inputStream, outputStream, insertTextMap, tableTagList);
            inputStream.close();
            outputStream.flush();
            outputStream.close();

        } catch (Exception e) {
            e.printStackTrace();
            log.warn("自费协议文档导出异常！" + e.getMessage());
        }

    }

    /**
     * @param inputStream
     * @param outputStream
     * @param insertTextMap
     * @param addList
     * @Description 通过word模板生成word的主方法 （未关闭输入输出流)
     * <AUTHOR>
     * @Date 2023/01/06 10:04
     */
    public static void generateWord(InputStream inputStream, OutputStream outputStream, Map<String, String> insertTextMap,List<TableTag> addList) throws IOException {
        //获取docx解析对象
        XWPFDocument xwpfDocument = new XWPFDocument(inputStream);

        // 处理所有可替换数据
        handleParagraphs(xwpfDocument, insertTextMap);
        // 处理表格数据
        handleTable(xwpfDocument, addList);
        // 写出数据
        xwpfDocument.write(outputStream);
    }
    /**
     * @param xwpfDocument
     * @param insertTextMap
     * @Description 处理所有可替换内容数据
     * <AUTHOR>
     * @Date 2023/01/06 10:04
     */
    public static void handleParagraphs(XWPFDocument xwpfDocument, Map<String, String> insertTextMap) {
        for (XWPFParagraph paragraph : xwpfDocument.getParagraphs()) {
            String text = paragraph.getText();
            if (isReplacement(text)) {
                for (XWPFRun run : paragraph.getRuns()) {
                    run.setText(matchesValue(run.text(), insertTextMap), 0);
                }
            }
        }
        for (XWPFTable table : xwpfDocument.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell tableCell : row.getTableCells()) {
                    String text = tableCell.getText();
                    if(isReplacement(text)){
                        tableCell.removeParagraph(0);
                        tableCell.setText(matchesValue(text, insertTextMap));
                    }
                }
            }
        }
    }
    /**
     * @return boolean
     * @Description 测试是否包含需要替换的数据
     * <AUTHOR>
     * @Date 2023/01/06 10:04
     */
    public static boolean isReplacement(String text) {
        boolean check = false;
        if (text.contains("$")) {
            check = true;
        }
        return check;
    }
    /**
     * @param wordValue ${...} 带${}的变量
     * @param map       存储需要替换的数据
     * @return java.lang.String
     * @Description 有${}的值匹配出替换的数据，没有${}就返回原来的数据
     * <AUTHOR>
     * @Date 2023/02/06 10:04
     */
    public static String matchesValue(String wordValue, Map<String, String> map) {
        for (String s : map.keySet()) {
            String s1 = new StringBuilder("${").append(s).append("}").toString();
            wordValue = wordValue.replace(s1, StringUtils.isEmpty(map.get(s))? "":map.get(s));
        }
        return wordValue;
    }
    /**
     * @param xwpfDocument
     * @param addList
     * @param
     * @Description 处理表格数据方法
     * <AUTHOR>
     * @Date 2023/01/06 10:04
     */
    public static void handleTable(XWPFDocument xwpfDocument, List<TableTag> addList) {
        List<XWPFTable> tables = xwpfDocument.getTables();

        for (int i = 0; i < tables.size(); i++) {
            XWPFTable xwpfTable = tables.get(i);
            for (TableTag tableTag : addList) {
                if(tableTag.getSt() == i){
                    //动态增加表格行
                    if(tableTag.getDatas().size() > tableTag.getRows()){
                        addRows(xwpfTable,tableTag.getStRow(),tableTag.getDatas().size() - tableTag.getRows(),tableTag.getStRow());
                    }
                    //数据填充
                    List<String[]> datas = tableTag.getDatas();
                    for (int j = 0; j < datas.size(); j++) {
                        int row = j + tableTag.getStRow();
                        XWPFTableRow tableRow = xwpfTable.getRow(row);
                        List<XWPFTableCell> tableCells = tableRow.getTableCells();
                        for (int k = 0; k < datas.get(j).length; k++) {
                            int column = k + tableTag.getStColumn();
                            XWPFTableCell xwpfTableCell = tableCells.get(column);
                            xwpfTableCell.setText(datas.get(j)[k]);
                        }
                    }
                }
            }
        }
        for (XWPFTable table : tables) {
            if (table.getRows().size() > 1) {
                int rows = table.getRows().size() - 1;
                if (rows < addList.size()) {
                    // 插入表格行
                    for (int i = 0; i < addList.size() - rows; i++) {
                        XWPFTableRow row = table.createRow();
                    }
                }
            }
        }
    }
    /**
     * 表格处理类
     */
    @Data
    public static class TableTag{
        //表格
        int st;
        //起始行坐标
        int stRow;
        //起始列坐标
        int stColumn;
        //模板可填充行数
        int rows;
        //数据
        List<String[]> datas;

        public TableTag(int st,int stRow,int stColumn, int rows,List<String[]> datas){
            this.st = st;
            this.stColumn = stColumn;
            this.stRow = stRow;
            this.rows = rows;
            this.datas = datas;
        }
    }

    /**
     * des:表末尾添加行(表，要复制样式的行，添加行数，插入的行下标索引)
     * @param table
     * @param source
     * @param rows
     */
    public static void addRows(XWPFTable table, int source, int rows, int insertRowIndex){
        try{
            //获取表格的总行数
            int index = table.getNumberOfRows();
            //循环添加行和和单元格
            for(int i=1;i<=rows;i++) {
                //获取要复制样式的行
                XWPFTableRow sourceRow = table.getRow(source);
                //添加新行
                XWPFTableRow targetRow = table.insertNewTableRow(insertRowIndex++);
                //复制行的样式给新行
                targetRow.getCtRow().setTrPr(sourceRow.getCtRow().getTrPr());
                //获取要复制样式的行的单元格
                List<XWPFTableCell> sourceCells = sourceRow.getTableCells();
                //循环复制单元格
                for (XWPFTableCell sourceCell : sourceCells) {
                    //添加新列
                    XWPFTableCell newCell = targetRow.addNewTableCell();
                    //复制单元格的样式给新单元格
                    newCell.getCTTc().setTcPr(sourceCell.getCTTc().getTcPr());
                    //设置垂直居中
                    newCell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);//垂直居中
                    //复制单元格的居中方式给新单元格
                    CTPPr pPr = sourceCell.getCTTc().getPList().get(0).getPPr();
                    if(pPr!=null&&pPr.getJc()!=null&&pPr.getJc().getVal()!=null){
                        CTTc cttc = newCell.getCTTc();
                        CTP ctp = cttc.getPList().get(0);
                        CTPPr ctppr = ctp.getPPr();
                        if (ctppr == null) {
                            ctppr = ctp.addNewPPr();
                        }
                        CTJc ctjc = ctppr.getJc();
                        if (ctjc == null) {
                            ctjc = ctppr.addNewJc();
                        }
                        ctjc.setVal(pPr.getJc().getVal()); //水平居中
                    }

                    //得到复制单元格的段落
                    List<XWPFParagraph> sourceParagraphs = sourceCell.getParagraphs();
                    if (StringUtils.isEmpty(sourceCell.getText())) {
                        continue;
                    }
                    //拿到第一段
                    XWPFParagraph sourceParagraph = sourceParagraphs.get(0);
                    //得到新单元格的段落
                    List<XWPFParagraph> targetParagraphs = newCell.getParagraphs();
                    //判断新单元格是否为空
                    if (StringUtils.isEmpty(newCell.getText())) {
                        //添加新的段落
                        XWPFParagraph ph = newCell.addParagraph();
                        //复制段落样式给新段落
                        ph.getCTP().setPPr(sourceParagraph.getCTP().getPPr());
                        //得到文本对象
                        XWPFRun run = ph.getRuns().isEmpty() ? ph.createRun() : ph.getRuns().get(0);
                        //复制文本样式
                        run.setFontFamily(sourceParagraph.getRuns().get(0).getFontFamily());
                    } else {
                        XWPFParagraph ph = targetParagraphs.get(0);
                        ph.getCTP().setPPr(sourceParagraph.getCTP().getPPr());
                        XWPFRun run = ph.getRuns().isEmpty() ? ph.createRun() : ph.getRuns().get(0);
                        run.setFontFamily(sourceParagraph.getRuns().get(0).getFontFamily());
                    }
                }
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }
    }


}
