package com.crhms.cloud.mqs.mqs_hp.mapper;

import com.crhms.cloud.mqs.mqs_hp.domain.HpOutHis;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 住院审核-出院审核-历史表(HpOutHis)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-17 10:00:57
 */

@Mapper
public interface HpOutHisMapper extends BaseMapper<HpOutHis> {



}

