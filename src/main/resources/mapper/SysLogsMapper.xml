<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.sys.mapper.SysLogsMapper">


    <select id="queryPage" resultType="com.crhms.cloud.mqs.sys.domain.SysLogs">
        select
            operator_name,
            operator_time,
            operator_text,
            admission_no,
            patient_id,
            patient_name,
            detail_no,
            item_name,
            item_id,
            item_date
        from mqs_sys_logs
        where hospital_id = #{hospitalId}
        <if test="queryVO.operatorTimeFrom != null and queryVO.operatorTimeFrom != ''">
             and operator_time >= concat(#{queryVO.operatorTimeFrom},' 00:00:00')
        </if>

        <if test="queryVO.operatorTimeTo != null and queryVO.operatorTimeTo != ''">
             and operator_time &lt;= concat(#{queryVO.operatorTimeTo},' 23:59:59')
        </if>
        <if test="queryVO.operatorName != null and queryVO.operatorName != ''">
             and operator_name like concat('%' , #{queryVO.operatorName} ,'%')
        </if>
        <if test="queryVO.operatorText != null and queryVO.operatorText != ''">
             and operator_text like concat('%' , #{queryVO.operatorText} ,'%')
        </if>
        <if test="queryVO.admissionNo != null and queryVO.admissionNo != ''">
             and admission_no like concat('%' , #{queryVO.admissionNo} ,'%')
        </if>
        <if test="queryVO.detailNo != null and queryVO.detailNo != ''">
             and detail_no like concat('%' , #{queryVO.detailNo} ,'%')
        </if>
        <if test="queryVO.patientId != null and queryVO.patientId != ''">
             and (patient_id like concat('%' , #{queryVO.patientId} ,'%')
             or patient_name like concat('%' , #{queryVO.patientId} ,'%'))
        </if>
        <if test="queryVO.itemId != null and queryVO.itemId != ''">
             and (item_id like concat('%' , #{queryVO.itemId} ,'%')
             or item_name like concat('%' , #{queryVO.itemId} ,'%'))
        </if>
        <if test="queryVO.itemDateFrom != null and queryVO.itemDateFrom != ''">
             and item_date >= concat(#{queryVO.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVO.itemDateTo != null and queryVO.itemDateTo != ''">
             and item_date &lt;= concat(#{queryVO.itemDateTo},' 23:59:59')
        </if>
        order by operator_time desc
    </select>

</mapper>

