package com.crhms.cloud.mqs.sys.init_engine.manager;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.ApplicationContextUtil;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper;
import com.crhms.cloud.mqs.basic.vo.*;
import com.crhms.cloud.mqs.core.annotation.InitEngineFile;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpSettleMapper;
import com.crhms.cloud.mqs.sys.config.JMapperSingleton;
import com.crhms.cloud.mqs.sys.domain.SysConfig;
import com.crhms.cloud.mqs.sys.init_engine.dto.InitEngineFileLog;
import com.crhms.cloud.mqs.sys.service.SysConfigService;
import com.crhms.cloud.mqs.sys.utils.CryptoUtils;
import com.googlecode.jmapper.JMapper;
import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.io.File;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@EnableAsync
@Service
@Slf4j
public class InitEngineManager {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private HpSettleMapper hpSettleMapper;

    @Autowired
    private BaseMedicalMapper baseMedicalMapper;

    @Autowired
    private SysConfigService configService;

    // 诊间审核引擎SFTP配置
    public static final String MQS_ENGINE_SERVER_SFTP = "MQS_ENGINE_SERVER_SFTP";

    // 数据存储路径
    @Value("${app.data-dir}")
    private String dataDir;

    /**
     * 清理任务
     * <AUTHOR>
     * @date 2023/4/3
     **/
    public void clearTask(){
        // 删除缓存日志
        Set<String> keys = redisTemplate.keys("CRHMS:MQS:INIT_ENGINE:*");
        redisTemplate.delete(keys);

        // 删除文件
        FileUtil.del(getBaseDir(null));
    }


    /**
     * 生成引擎初始化单据文件，返回任务ID
     * <AUTHOR>
     * @date 2023/3/28
     **/
    public Long buildFile(){
        // 检查Redis中是否有上次生成的记录
        Object taskId = redisTemplate.opsForValue().get(InitEngineCache.INIT_ENGINE_TASK_ID);
        if(null != taskId){
            throw new BaseException("初始化任务正在进行中，请勿重复启动");
        }

        // 生成任务ID
        Long id = IdWorker.getId();

        // 启动异步任务
        InitEngineManager manager = ApplicationContextUtil.getBean(InitEngineManager.class);
        manager.startBuildFile(id);

        // 存储缓存:
        redisTemplate.opsForValue().set(InitEngineCache.INIT_ENGINE_TASK_ID, String.valueOf(id));
        return id;
    }

    /**
     * 获取任务ID
     * <AUTHOR>
     * @date 2023/3/28
     **/
    public Long getTaskId(){
        Object taskId = redisTemplate.opsForValue().get(InitEngineCache.INIT_ENGINE_TASK_ID);
        if(null==taskId){
            return null;
        }

        return Long.valueOf(String.valueOf(taskId));
    }

    /**
     * 获取任务进度
     * <AUTHOR>
     * @date 2023/3/28
     **/
    public InitEngineFileLog getTaskLog(Long taskId){
        InitEngineFileLog fileLog = new InitEngineFileLog();

        // 日志KEY
        String logKey = StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_LOGS, taskId);
        List<String> logs = redisTemplate.opsForList().range(logKey, 0, -1);
        fileLog.setLogs(StrUtil.join("\n", logs));

        // 进度
        String process = redisTemplate.opsForValue().get(StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_PROCESS, taskId));
        fileLog.setProcess(StrUtil.isEmpty(process) ? 0 : Integer.valueOf(process));

        return fileLog;
    }

    /**
     * 开始异步生成文件
     * <AUTHOR>
     * @date 2023/3/28
     **/
    @Async("asyncPoolTaskExecutor")
    public void startBuildFile(Long taskId) {
        try{
            writeLog(taskId, "##################### 开始初始化引擎单据文件 #####################");
            redisTemplate.opsForValue().set(StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_PROCESS, taskId), "1");

            // 1. 多线程生成主单列数据缓存
            buildMedCaseColData(taskId);
            redisTemplate.opsForValue().set(StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_PROCESS, taskId), "10");

            // 2. 多线程生成费用明细列数据缓存
            buildFeeColData(taskId);
            redisTemplate.opsForValue().set(StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_PROCESS, taskId), "20");

            // 3. 生成主单表列数据文件
            writeMedCaseColDataFile(taskId);
            redisTemplate.opsForValue().set(StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_PROCESS, taskId), "25");

            // 4. 生成费用明细表列数据文件
            writeFeeColDataFile(taskId);
            redisTemplate.opsForValue().set(StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_PROCESS, taskId), "30");

            // 5. 计算批次
            List<Date> batch = calcBatch(taskId);

            // 6. 生成主单表数据索引文件
            writeMedCaseIndexFile(taskId, batch);
            redisTemplate.opsForValue().set(StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_PROCESS, taskId), "60");

            // 7. 生成费用明细表数据索引文件
            writeFeeIndexFile(taskId, batch);
            redisTemplate.opsForValue().set(StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_PROCESS, taskId), "90");

            // 8. FTP文件到引擎服务器
            sftpFile(taskId);
            redisTemplate.opsForValue().set(StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_PROCESS, taskId), "100");

            writeLog(taskId, "##################### 引擎单据文件初始化完成 #####################");
        } catch (BaseException e){
            writeLog(taskId, e.getErrMsg());
        } catch (Exception e){
            writeLog(taskId, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 通过SFTP将文件上传至诊间审核服务器
     * <AUTHOR>
     * @date 2023/4/4
     * @param taskId
     **/
    public void sftpFile(Long taskId){
        writeLog(taskId, "【8/8】开始上传文件到引擎服务器");
        TimeInterval timer = DateUtil.timer();
        timer.start();

        String time = DateUtil.format(DateUtil.date(), "yyyyMMddHH");

        // 引擎服务器SFTP信息
        JSONObject sftp = getEngineSftp();
        String sftpIp = sftp.getStr("serverIp");
        Integer sftpPort = sftp.getInt("sftpPort");
        String sftpUsername = sftp.getStr("username");
        String sftpPassword = sftp.getStr("password");
        String sftpFileDir = sftp.getStr("fileDir");
        // 单据文件：默认基础路径/yyyyMMddHH/
        sftpFileDir = sftpFileDir + "/" + time + "/";

        // 需要上传的文件夹
        List<String> folders = CollUtil.list(true, "medicalCaseValue", "medicalCaseData", "feeListValue", "feeListData");

        // 连接SFTP
        JSch jsch = new JSch();
        Session session = null;
        ChannelSftp channelSftp = null;

        try {
            session = jsch.getSession(sftpUsername, sftpIp, sftpPort);
            session.setPassword(sftpPassword);

            // 跳过公钥检测
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");
            session.setConfig(sshConfig);
            session.setTimeout(5*60*1000);

            // 连接服务器
            session.connect();
            writeLog(taskId, "      引擎服务器登录成功...");

            // 登录SFTP
            channelSftp = (ChannelSftp) session.openChannel("sftp");
            channelSftp.connect();
            writeLog(taskId, "      引擎服务器SFTP服务已连接...");

            mkdirDirectorySFTP(channelSftp, sftpFileDir);
            writeLog(taskId, StrUtil.format("      引擎初始化文件存储主目录创建完成，存储路径:{}", sftpFileDir));

            // 遍历本地目录
            for(String folder : folders){
                // 创建远程目录
                String remotePath = sftpFileDir + folder;
                mkdirDirectorySFTP(channelSftp, remotePath);

                // 查找本地该文件夹下有多少文件，依次上传
                File[] files = FileUtil.ls(getBaseDir(folder));
                for(File localFile : files){
                    String remoteFile = buildFullFilePath(remotePath, localFile.getName());

                    writeLog(taskId, StrUtil.format("      [本地:{}]>>>>>>>[引擎服务器:{}] 文件上传中...", localFile.getPath(), remoteFile));
                    channelSftp.put(FileUtil.getInputStream(localFile), remoteFile);
                }
            }
        } catch (BaseException e){
            throw e;
        } catch (JSchException e) {
            e.printStackTrace();
            throw new BaseException(StrUtil.format("连接服务器异常:{}", e.getMessage()));
        } catch (SftpException e) {
            e.printStackTrace();
            throw new BaseException(StrUtil.format("SFTP上传文件异常:{}", e.getMessage()));
        } finally {
            disConnect(session, channelSftp);
        }

        writeLog(taskId, StrUtil.format("【8/8】上传文件到引擎服务器完成, 耗时{}s", timer.intervalSecond()));
    }

    /**
     * 使用SFTP创建目录
     * <AUTHOR>
     * @date 2023/3/9
     * @param channelSftp   sftp连接
     * @param directory     目录路径，必需以 / 开头
     **/
    public static void mkdirDirectorySFTP(ChannelSftp channelSftp, String directory){
        String dir = "";

        List<String> dirs = StrUtil.split(directory, "/");
        for(String child : dirs){
            if(StrUtil.isEmpty(child)){
                continue;
            }

            dir = dir + "/" + child;
            try {
                channelSftp.cd(dir);
            } catch (SftpException e) {
                // 打开异常，则新建
                try {
                    channelSftp.mkdir(dir);
                } catch (SftpException ex) {
                    throw new BaseException(StrUtil.format("向服务器创建目录{}时发生异常:{}", dir, ex.getMessage()));
                }
            }
        }
    }

    /**
     * 构建完整文件路径
     * /data/file  shell.sh     返回      /data/file/shell.sh
     * /data/file/  shell.sh    返回      /data/file/shell.sh
     * /            shell.sh    返回      /shell.sh
     * ""           shell.sh    返回      /shell.sh
     * <AUTHOR>
     * @date 2023/3/9
     * @param directory     存储目录，必须以 / 开头
     * @param fileName      文件名称
     **/
    public static String buildFullFilePath(String directory, String fileName){
        if(StrUtil.isEmpty(directory)){
            return "/" + fileName;
        }

        if(StrUtil.endWith(directory, "/")){
            return directory + fileName;
        }

        return directory + "/" + fileName;
    }


    /** 
     * 生成费用明细表数据索引文件
     * <AUTHOR>
     * @date 2023/4/3
     * @param taskId    任务ID
     * @param batch     批次信息
     **/
    public void writeFeeIndexFile(Long taskId, List<Date> batch){
        writeLog(taskId, "【7/8】开始生成费用明细表数据索引文件");
        TimeInterval timer = DateUtil.timer();
        timer.start();
        //初始化转换实例
        JMapper<EngFeeVo, BaseMedicalDetail> mqsToEngFeeInstance = JMapperSingleton.getMqsToEngFeeInstance();

        // 明细表引擎初始化配置
        Map<String, InitEngineFile> engineConf = getEngineFeeConf();

        FileWriter writer;
        List<BaseMedicalDetail> feeList;
        EngFeeVo engFee;
        JSONObject feeJson;
        List<String> lines;
        for(Date date : batch){
            lines = new ArrayList<>();
            writeLog(taskId, StrUtil.format("      {}期间: 开始查询数据", DateUtil.format(date, "yyyy-MM")));
            // 查询该期间数据
            feeList = baseMedicalMapper.queryFeeListByBillDate("mqs_hp_settle_detail",  DateUtil.beginOfMonth(date), DateUtil.endOfMonth(date));
            if(CollUtil.isEmpty(feeList)){
                writeLog(taskId, StrUtil.format("      {}期间: 共0条数据", DateUtil.format(date, "yyyy-MM")));
                continue;
            }

            writer = new FileWriter(getBaseDir("feeListData") + DateUtil.format(date, "yyyy-MM"));

            // 依次处理每条费用
            for(BaseMedicalDetail fee : feeList){
                engFee = mqsToEngFeeInstance.getDestination(fee);
                feeJson = JSONUtil.parseObj(engFee, false);

                // 依次处理每个字段，找出每个字段的索引
                for(String key : engineConf.keySet()){
                    if(engineConf.get(key).needIndex()){
                        feeJson.set(key, InitEngineCache.feeColDataCache.getOrDefault(key.replaceAll("_", ""), new LinkedHashMap<>()).get(feeJson.get(key)));
                    }
                }

                // 每次写入5000行
                lines.add(feeJson.toString());
                if(lines.size()==5000){
                    writer.appendLines(lines);
                    lines = new ArrayList<>();
                }
            }

            if(CollUtil.isNotEmpty(lines)){
                writer.appendLines(lines);
            }

            writeLog(taskId, StrUtil.format("      {}期间: 共{}条数据,写入文件完成", DateUtil.format(date, "yyyy-MM"), 0));
        }
        writeLog(taskId, StrUtil.format("【7/8】费用明细表数据索引文件生成完成, 共生成{}个文件, 耗时{}s", 0, timer.intervalSecond()));
    }

    /**
     * 生成主单表数据索引文件
     * <AUTHOR>
     * @date 2023/4/3
     * @param taskId    任务ID
     * @param batch     批次信息
     **/
    public void writeMedCaseIndexFile(Long taskId, List<Date> batch){
        writeLog(taskId, "【6/8】开始生成主单表数据索引文件");
        TimeInterval timer = DateUtil.timer();
        timer.start();
        //初始化转换实例
        JMapper<EngMedicalCaseVo, BaseMedicalCase> mqsToEngMedCaseInstance = JMapperSingleton.getMqsToEngMedCaseInstance();
        // 主单表引擎初始化配置
        Map<String, InitEngineFile> engineConf = getEngineMedCaseConf();

        FileWriter writer;
        List<BaseMedicalCase> medCaseList;
        EngMedicalCaseVo engineMedCase;
        JSONObject medCaseJson;
        List<String> lines;
        for(Date date : batch){
            lines = new ArrayList<>();
            writeLog(taskId, StrUtil.format("      {}期间: 开始查询数据", DateUtil.format(date, "yyyy-MM")));
            // 查询该期间数据
            medCaseList = baseMedicalMapper.queryCaseListByBillDate("mqs_hp_settle",  DateUtil.beginOfMonth(date), DateUtil.endOfMonth(date));
            if(CollUtil.isEmpty(medCaseList)){
                writeLog(taskId, StrUtil.format("      {}期间: 共0条数据", DateUtil.format(date, "yyyy-MM")));
                continue;
            }

            writer = new FileWriter(getBaseDir("medicalCaseData") + DateUtil.format(date, "yyyy-MM"));

            // 依次处理每条主单
            for(BaseMedicalCase medCase : medCaseList){
                engineMedCase = mqsToEngMedCaseInstance.getDestination(medCase);
                medCaseJson = JSONUtil.parseObj(engineMedCase, false);

                // 依次处理每个字段，找出每个字段的索引
                for(String key : engineConf.keySet()) {
                    if (engineConf.get(key).needIndex()) {
                        medCaseJson.set(key, InitEngineCache.medCaseColDataCache.getOrDefault(key.replaceAll("_", ""), new LinkedHashMap<>()).get(medCaseJson.get(key)));
                    }
                }

                // 每次写入5000行
                lines.add(medCaseJson.toString());
                if(lines.size()==5000){
                    writer.appendLines(lines);
                    lines = new ArrayList<>();
                }
            }

            if(CollUtil.isNotEmpty(lines)){
                writer.appendLines(lines);
            }

            writeLog(taskId, StrUtil.format("      {}期间: 共{}条数据,写入文件完成", DateUtil.format(date, "yyyy-MM"), 0));
        }

        writeLog(taskId, StrUtil.format("【6/8】主单表数据索引文件生成完成, 共生成{}个文件, 耗时{}s", 0, timer.intervalSecond()));
    }

    /**
     * 计算开始时间结束时间之内的批次
     * <AUTHOR>
     * @date 2023/3/30
     **/
    public List<Date> calcBatch(Long taskId){
        Date max = hpSettleMapper.queryMaxBillDate();
        Date min = hpSettleMapper.queryMinBillDate();
        List<Date> batch = new ArrayList<>();

        while (true){
            batch.add(min);
            min = DateUtil.offsetMonth(min, 1);

            if(DateUtil.compare(min, max)>0){
                break;
            }
        }

        writeLog(taskId, StrUtil.format("【5/8】结算时间最小:{}, 最大:{}, 批次数:{}", DateUtil.formatDate(min), DateUtil.formatDate(max), batch.size()));
        return batch;
    }


    /**
     * 4. 生成费用明细表列数据文件
     **/
    public void writeFeeColDataFile(Long taskId){
        writeLog(taskId, "【4/8】开始生成费用明细表列数据文件");
        TimeInterval timer = DateUtil.timer();
        timer.start();

        // 遍历主单列数据的各个字段
        Set<Object> values;
        FileWriter writer;
        for(String engCol : InitEngineCache.feeColDataCache.keySet()){
            values = InitEngineCache.feeColDataCache.get(engCol).keySet();
            values = values.stream().filter(i->null!=i).collect(Collectors.toSet());

            // 写入文件
            writer = new FileWriter(getBaseDir("feeListValue") + engCol);
            writer.write(JSONUtil.toJsonStr(values));
        }

        writeLog(taskId, StrUtil.format("【4/8】费用明细表列数据文件生成完成, 共生成{}个文件, 耗时{}s", InitEngineCache.feeColDataCache.size(), timer.intervalSecond()));
    }

    /**
     * 3. 生成主单表列数据文件
     **/
    public void writeMedCaseColDataFile(Long taskId){
        writeLog(taskId, "【3/8】开始生成主单表列数据文件");
        TimeInterval timer = DateUtil.timer();
        timer.start();

        // 遍历主单列数据的各个字段
        Set<Object> values;
        FileWriter writer;
        for(String engCol : InitEngineCache.medCaseColDataCache.keySet()){
            values = InitEngineCache.medCaseColDataCache.get(engCol).keySet();
            values = values.stream().filter(i->null!=i).collect(Collectors.toSet());

            // 写入文件
            writer = new FileWriter(getBaseDir("medicalCaseValue") + engCol);
            writer.write(JSONUtil.toJsonStr(values));
        }

        writeLog(taskId, StrUtil.format("【3/8】主单表列数据文件生成完成, 共生成{}个文件, 耗时{}s", InitEngineCache.medCaseColDataCache.size(), timer.intervalSecond()));
    }


    /**
     * 2. 多线程生成费用明细列数据缓存
     **/
    public void buildFeeColData(Long taskId){
        writeLog(taskId, "【2/8】开始构建费用明细表列数据");
        TimeInterval timer = DateUtil.timer();
        timer.start();

        // Map<引擎属性名, 注解信息>
        Map<String, InitEngineFile> feeConf = getEngineFeeConf();
        // Map<引擎属性名, List<表字段>>
        Map<String, List<String>> feeCol = new HashMap<>();
        for(String engCol : feeConf.keySet()){
            String colFileName = feeConf.get(engCol).colFileName();

            if(!feeCol.containsKey(colFileName)){
                feeCol.put(colFileName, new ArrayList<>());
            }

            feeCol.get(colFileName).add(feeConf.get(engCol).tableColName());
        }

        writeLog(taskId, "【2/8】开始提交多线程任务");
        ThreadPoolTaskExecutor pool = (ThreadPoolTaskExecutor) ApplicationContextUtil.getApplicationContext().getBean("asyncPoolTaskExecutor");

        // 提交任务
        List<Future> feeFuture = new ArrayList<>();
        for(String colFileName : feeCol.keySet()){
            feeFuture.add(
                    pool.submit(
                            new InitColDataRunner(taskId, "mqs_hp_settle_detail",
                                    colFileName,
                                    StrUtil.join(",", feeCol.get(colFileName)))
                    )
            );
        }
        writeLog(taskId, StrUtil.format("【2/8】已提交{}个任务", feeFuture.size()));

        // 等待任务执行完成
        try {
            for (Future future : feeFuture) {
                Object rs = future.get();
            }
        } catch (Exception e){
            e.printStackTrace();
        }

        writeLog(taskId, StrUtil.format("【2/8】构建费用明细表列数据完成, 耗时{}s", timer.intervalSecond()));
    }


    /**
     * 1. 多线程生成主单列数据缓存
     **/
    public void buildMedCaseColData(Long taskId){
        writeLog(taskId, "【1/8】开始构建主单表列数据");
        TimeInterval timer = DateUtil.timer();
        timer.start();

        // Map<引擎属性名, 注解信息>
        Map<String, InitEngineFile> medCaseConf = getEngineMedCaseConf();
        // Map<引擎属性名, List<表字段>>
        Map<String, List<String>> medCaseCol = new HashMap<>();
        for(String engCol : medCaseConf.keySet()){
            String colFileName = medCaseConf.get(engCol).colFileName();

            if(!medCaseCol.containsKey(colFileName)){
                medCaseCol.put(colFileName, new ArrayList<>());
            }

            medCaseCol.get(colFileName).add(medCaseConf.get(engCol).tableColName());
        }

        writeLog(taskId, "【1/8】开始提交多线程任务");
        ThreadPoolTaskExecutor pool = (ThreadPoolTaskExecutor) ApplicationContextUtil.getApplicationContext().getBean("asyncPoolTaskExecutor");

        // 提交任务
        List<Future> medCaseFuture = new ArrayList<>();
        for(String colFileName : medCaseCol.keySet()){
            medCaseFuture.add(
                    pool.submit(
                            new InitColDataRunner(taskId, "mqs_hp_settle",
                                    colFileName,
                                    StrUtil.join(",", medCaseCol.get(colFileName)))
                    )
            );
        }
        writeLog(taskId, StrUtil.format("【1/8】已提交{}个任务", medCaseFuture.size()));

        // 等待任务执行完成
        try {
            for (Future future : medCaseFuture) {
                Object rs = future.get();
            }
        } catch (Exception e){
            e.printStackTrace();
        }

        writeLog(taskId, StrUtil.format("【1/8】构建主单表列数据完成, 耗时{}s\n\n", timer.intervalSecond()));
    }



    /**
     * 记录日志
     * <AUTHOR>
     * @date 2023/3/28
     * @param msg       日志信息
     * @param taskId    任务ID
     **/
    public void writeLog(Long taskId, String msg){
        log.info(msg);

        msg = DateUtil.now() + msg;
        String logKey = StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_LOGS, taskId);
        redisTemplate.opsForList().rightPush(logKey, msg);
    }

    public String getBaseDir(String lastDir) {
        if(StrUtil.isEmpty(lastDir)){
            return dataDir + FileUtil.FILE_SEPARATOR  + "engineInit" + FileUtil.FILE_SEPARATOR;
        }

        String dir = dataDir + FileUtil.FILE_SEPARATOR  + "engineInit" + FileUtil.FILE_SEPARATOR + lastDir + FileUtil.FILE_SEPARATOR;
        return dir;
    }

    /** 
     * 获取费用明细表引擎初始化配置信息
     * <AUTHOR>
     * @date 2023/4/3
     **/
    public Map<String, InitEngineFile> getEngineFeeConf(){
        Map<String, InitEngineFile> confs = new HashMap<>();

        try {
            Class cls = Class.forName("com.crhms.cloud.mqs.basic.vo.EngFeeVo");  //获取类对象
            Field[] field = cls.getDeclaredFields();          //获取类的属性数组
            for(Field f:field){                             //循环属性
                if(f.isAnnotationPresent(InitEngineFile.class)){ //获取属性的注解，并判断是否是Car_color.class注解
                    InitEngineFile conf = f.getAnnotation(InitEngineFile.class);     //获取Car_color注解对象
                    confs.put(f.getName(), conf);
                }
            }

            return confs;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            return confs;
        }
    }

    /**
     * 获取主单表引擎初始化配置信息
     * <AUTHOR>
     * @date 2023/4/3
     **/
    public Map<String, InitEngineFile> getEngineMedCaseConf(){
        Map<String, InitEngineFile> confs = new HashMap<>();

        try {
            Class cls = Class.forName("com.crhms.cloud.mqs.basic.vo.EngMedicalCaseVo");  //获取类对象
            Field[] field = cls.getDeclaredFields();          //获取类的属性数组
            for(Field f:field){                             //循环属性
                if(f.isAnnotationPresent(InitEngineFile.class)){ //获取属性的注解，并判断是否是Car_color.class注解
                    InitEngineFile conf = f.getAnnotation(InitEngineFile.class);     //获取Car_color注解对象
                    confs.put(f.getName(), conf);
                }
            }

            return confs;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            return confs;
        }
    }

    /**
     * 获取引擎restful地址
     * <AUTHOR>
     * @date 2023/3/1
     **/
    private JSONObject getEngineSftp(){
        SysConfig sysConfig = configService.queryByKey(MQS_ENGINE_SERVER_SFTP, LoginContext.getHospitalId());
        if(null==sysConfig){
            throw new BaseException("未配置引擎服务器SFTP信息！");
        }

        String conf = sysConfig.getSysValue();
        return JSONUtil.parseObj(CryptoUtils.decryptPlaintext(conf));
    }

    /**
     * 断开连接
     * <AUTHOR>
     * @date 2023/3/8
     * @param session
     **/
    public static void disConnect(Session session, Channel channel){
        try{
            if(null!=channel){
                channel.disconnect();
            }

            if(null!=session){
                session.disconnect();
            }
        }catch (Exception e){
            log.error("关闭SSH连接异常!", e);
        }
    }
}
