package com.crhms.cloud.mqs.mqs_pm.config;

import com.crhms.cloud.mqs.mqs_pm.domain.PmStdLog;
import com.crhms.cloud.mqs.mqs_pm.service.PmStdLogService;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 日志切面
 *
 * @author: wangxingang
 * @date: 2023/02/01
 */

@Aspect
@Component
public class PmStdLogAspect {

    @Autowired
    private PmStdLogService logService;


    @Pointcut("@annotation(com.crhms.cloud.mqs.mqs_pm.config.PmStdLogRecord)")
    private void pointCut(){

    }


    @Before("pointCut()&&@annotation(logRecord)")
    public void around(PmStdLogRecord logRecord) {
        logService.insertLog(PmStdLog.builder().category(logRecord.category()).log(logRecord.log()).build());
    }
}
