package com.crhms.cloud.mqs.core.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.sys.mapper.IppMapper;
import com.crhms.cloud.mqs.sys.utils.SortListUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.HexValue;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhaoyachao on 2024/10/29
 */
@Slf4j
@Component
public class FromItemVisitorImpl implements FromItemVisitor {

    // 声明增强条件
    private Expression enhancedCondition;

    //维护权限表范围集合-主单表
    private final static String[] CASE_TABLES = new String[]{
            "mqs_op_rg",
            "mqs_op_pt",
            "mqs_op_pct",
            "mqs_hp_do",
            "mqs_hp_pred",
            "mqs_hp_out",
            "mqs_hp_tf",
            "mqs_hp_bk",
            "mqs_hp_rg",
            "mqs_hp_settle"
    };
    //维护权限字段-权限配置静态映射
    private final static HashMap<String, String> PERMISSION_FIELDS = new HashMap<>();
    // 0 全部 1 院区 2 专科 3 科室 4 病区 5 医生组 6 医生
    static {
        PERMISSION_FIELDS.put("1", "hosp_area_code");
        PERMISSION_FIELDS.put("2", "units_dept_code");
        PERMISSION_FIELDS.put("3", "present_dept_code");
        PERMISSION_FIELDS.put("4", "out_zone_code");
        PERMISSION_FIELDS.put("5", "doc_group_code");
        PERMISSION_FIELDS.put("6", "doc_id");
        // 可以根据需要继续添加更多权限配置
    }

    // FROM 表名 <----主要的就是这个，判断用户对这个表有没有权限
    @SneakyThrows
    @Override
    public void visit(Table table) {
        //权限控制表范围
        Optional<String> caseFirst = Arrays.stream(CASE_TABLES).filter(x -> StringUtils.equalsIgnoreCase(table.getName(), x)).findFirst();
        if (caseFirst.isPresent()) {

            Object obj = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            JSONObject jsonObject = (JSONObject) obj;
            JSONArray commonAuthoritys = jsonObject.getJSONArray("commonAuthority");
            //Expression defaultExpression = new HexValue("1 = 2");
            Expression permissionExpression = null;
            // 0 全部 1 院区 2 专科 3 科室 4 病区 5 医生组 6 医生
            String authorityType = jsonObject.getStr("authority_type");
            if ("0".equals(authorityType)) {
                enhancedCondition = new HexValue("1 = 1");
            } else {
                Optional<Object> first = commonAuthoritys.stream().filter(x -> authorityType.equals(((JSONObject) x).getStr("authority_type"))).findFirst();
                if (first.isPresent()) {
                    JSONObject authorityObj = JSONUtil.parseObj(first.get());
                    // 医院编码
                    String hospitalId = authorityObj.getStr("code");
                    // 医院权限码集
                    JSONArray commonInfoList = authorityObj.getJSONArray("commonInfoList");
                    List<String> depts = new ArrayList();
                    for (int j = 0; j < commonInfoList.size(); ++j) {
                        JSONObject dept = commonInfoList.getJSONObject(j);
                        String deptCode = dept.getStr("code");
                        depts.add(deptCode);
                    }
                    String s = authorityRegexp(jsonObject.getStr("username"), table, PERMISSION_FIELDS.get(authorityType), depts);
                    permissionExpression = new HexValue(s);
                } else {
                    log.error("数据授权异常：当前用户没有数据权限!");
                }
                enhancedCondition = permissionExpression;
            }
        }
    }

    /**
     * 获取权限部门判断条件
     *
     * @param field 权限表字
     * @param codes 权限码集
     * @return
     */
    private String authorityRegexp(String username, Table table, String field, List<String> codes) {
        // 权限字典无映射 || 未匹配权限无数据权限
        if (StrUtil.isEmpty(field) || CollectionUtil.isEmpty(codes)) {
            log.error("数据授权异常：当前用户" + username + "数据权限配置异常!field:" + field + " codes:" + codes );
            return "1 = 2";
        }
        StringBuffer regexp = new StringBuffer();
        if(ObjectUtil.isNotNull(table.getAlias())){
            regexp.append(table.getAlias().getName() + ".");
        }
        regexp.append(field);
        regexp.append(" IN ( ");
        regexp.append(codes.stream().collect(Collectors.joining(",")));
        regexp.append(") ");
        return regexp.toString();
    }


    // FROM 子查询
    @Override
    public void visit(SubSelect subSelect) {
        // 如果是子查询的话返回到select接口实现类
        subSelect.getSelectBody().accept(new SelectVisitorImpl());
    }

    // FROM subjoin
    @Override
    public void visit(SubJoin subjoin) {
        subjoin.getLeft().accept(new FromItemVisitorImpl());
//        subjoin.getJoinList().getRightItem().accept(new FromItemVisitorImpl());
    }

    // FROM 横向子查询
    @Override
    public void visit(LateralSubSelect lateralSubSelect) {
        lateralSubSelect.getSubSelect().getSelectBody()
                .accept(new SelectVisitorImpl());
    }

    // FROM value列表
    @Override
    public void visit(ValuesList valuesList) {
    }

    // FROM tableFunction
    @Override
    public void visit(TableFunction tableFunction) {
    }

    @Override
    public void visit(ParenthesisFromItem parenthesisFromItem) {

    }

    public Expression getEnhancedCondition() {
        return enhancedCondition;
    }

}
