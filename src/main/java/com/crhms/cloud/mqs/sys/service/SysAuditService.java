package com.crhms.cloud.mqs.sys.service;


import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.mapper.SysAuditMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class SysAuditService {

    @Autowired
    SysAuditMapper sysAuditMapper;

    /**
     * 查询违规统计
     *
     * @param no
     * @param isHis
     * @param batchNo
     * @param hospitalId
     * @return
     */
    public List<Map<String, Object>> queryAuditRules(String auditScenario, String no, String isHis, String batchNo, String hospitalId) {
        AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(auditScenario);
        if (Objects.isNull(auditScenarioEnum)) {
            throw new BaseException("错误的场景编码！" + auditScenario);
        }
        List<Map<String, Object>> mapList;
        if (!"1".equals(isHis)) {
            mapList = sysAuditMapper.queryTotalAudit(auditScenarioEnum.getTableName(), no, hospitalId);
        } else {
            mapList = sysAuditMapper.queryTotalAuditHis(auditScenarioEnum.getTableName(), no, batchNo, hospitalId);
        }

        mapList.forEach(map->{
            map.put("auditScenario",auditScenarioEnum.getAuditScenario());
        });

        return mapList;
    }

    /**
     * 查询违规结果明细
     * @param auditScenario
     * @param no
     * @param detailNo
     * @param isHis
     * @param batchNo
     * @param hospitalId
     * @return
     */
    public Map<String, Object> queryAuditResult(String auditScenario, String no, String ruleCode, String detailNo, String isHis, String batchNo, String hospitalId) {
        AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(auditScenario);
        if (Objects.isNull(auditScenarioEnum)) {
            throw new BaseException("错误的场景编码！" + auditScenario);
        }
        Map<String, Object> resultMap;
        if (!"1".equals(isHis)) {
            resultMap = sysAuditMapper.queryAuditResult(auditScenarioEnum.getTableName(), no, ruleCode, detailNo, hospitalId);
        } else {
            resultMap = sysAuditMapper.queryAuditResultHis(auditScenarioEnum.getTableName(), no, ruleCode, detailNo, batchNo, hospitalId);
        }
        return resultMap;
    }

    /**
     * 查询主单违规信息
     * @param auditScenario
     * @param no
     * @param hospitalId
     * @return
     */
    public List<Map<String,Object>> queryCaseAuditResult(String auditScenario, String no, String hospitalId) {
        List<Map<String,Object>> resultMap;
        AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(auditScenario);
        resultMap = sysAuditMapper.queryCaseAuditResult(auditScenarioEnum.getTableName(), no, hospitalId);
        return resultMap;
    }
}
