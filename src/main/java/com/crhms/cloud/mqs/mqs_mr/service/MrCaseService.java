package com.crhms.cloud.mqs.mqs_mr.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.utils.ApplicationContextUtil;
import com.crhms.cloud.mqs.basic.domain.BaseAuditIgnore;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper;
import com.crhms.cloud.mqs.mqs_mr.domain.MrCase;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlanAudit;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrAuditMapper;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrCaseMapper;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrReviewMapper;
import com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail;
import com.crhms.cloud.mqs.sys.disruptor.DisruptorQueue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 人工审核表(MrCase)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-02 10:06:12
 */
@Service("mrCaseService")
public class MrCaseService extends ServiceImpl< MrCaseMapper, MrCase> {

    @Autowired
    private BaseMedicalMapper baseMedicalMapper;

    @Autowired
    private MrPlanService mrPlanService;

    @Autowired
    private MrReviewMapper mrReviewMapper;

    @Autowired
    private MrAuditMapper mrAuditMapper;

    /**
     * 对case表不存在的no进行新增
     * @param auditScenario
     * @param cases
     * @param hospitalId
     * */
    public void insertMrCase(String auditScenario, List<BaseMedicalCase> cases, String hospitalId) {
        List<String> mrNos = cases.stream().map(BaseMedicalCase::getNo).collect(Collectors.toList());
        //获取所有已存在的case的no
        Set<String> filterNosSet = this.list(new QueryWrapper<MrCase>()
                        .in(MrCase.FIELD_CASE_NO, mrNos)
                        .eq(MrCase.FIELD_HOSPITAL_ID, hospitalId))
                .stream()
                .map(MrCase::getCaseNo)
                .collect(Collectors.toSet());
        List<MrCase> inserts = new ArrayList<>();
        for (BaseMedicalCase aCase : cases) {
            if(!filterNosSet.contains(aCase.getNo())) {
                inserts.add(MrCase.builder().mrStatus("0").caseNo(aCase.getNo()).hospitalId(hospitalId).admissionNo(aCase.getAdmissionNo()).auditScenario(auditScenario).build());
            }
            aCase.setSourceId(auditScenario);
        }
        //新增mqs_mr_case不存在的记录
        if(CollectionUtil.isNotEmpty(inserts)){
            this.saveBatch(inserts);
        }
    }

    /**
     *  查询人工审核信息
     * @param caseNo
     * @param hospitalId
     * @param auditScenario
     **/
    public List<MrCase> queryMrCase (String caseNo,String hospitalId, String auditScenario) {
        return  getBaseMapper().selectList(new LambdaQueryWrapper<MrCase>()
                .eq(StringUtils.isNotBlank(caseNo),MrCase::getCaseNo,caseNo)
                .eq(StringUtils.isNotBlank(auditScenario),MrCase::getAuditScenario,auditScenario)
                .eq(MrCase::getHospitalId,hospitalId));
    }


    /**
     * 异步批量更新人工审核case表的临床处理状态（转违规、撤销转违规、转自费、插销转自非、忽略、插销忽略）
     * @param baseMedicalCases  单据号
     * @param hospitalId 医院id
     * */
    public void batchUpdateCaseClinicalStatus (String auditScenario,List<BaseMedicalCase> baseMedicalCases ,String hospitalId,List<String> sceneCodes){
        if(CollectionUtil.isEmpty(baseMedicalCases)){
            return;
        }
        //根据标志区分单次提交还是批量提交
        //批量处理 (1000为一组)
        List<List<BaseMedicalCase>> allBaseMedicalCases = ListUtil.split(baseMedicalCases,1000);
        //缓存获取人工审核-审核权限配置信息
        List<MrPlanAudit> mrPlanAudits = mrPlanService.queryMrPlanAuditByHospitalId(hospitalId);
        for(List<BaseMedicalCase> baseMedicalCaseList : allBaseMedicalCases){
            List<String> nos = baseMedicalCaseList.stream().map(BaseMedicalCase::getNo).collect(Collectors.toList());
            //查询旧mrCase记录
            List<MrCase> mrList = this.list(new QueryWrapper<MrCase>().in(MrCase.FIELD_CASE_NO, nos).eq(MrCase.FIELD_HOSPITAL_ID, hospitalId));
            Map<String,MrCase> caseMap = mrList.stream().collect(Collectors.toMap(MrCase::getCaseNo,e -> e));
            //发送到队列进行修改
            DisruptorQueue.MR_CASE_QUEUE.getRingBuffer()
                    .publishEvent((event, sequence, buffer) -> {
                        event.setBaseMedicalCaseList(baseMedicalCaseList);
                        event.setMrPlanAudits(mrPlanAudits);
                        event.setCaseMap(caseMap);
                        event.setSceneCodes(sceneCodes);
                        event.setAuditScenario(auditScenario);
                        event.setHospitalId(hospitalId);
                    });
        }
    }

    /**
     * 单次提交修改临床处理状态
     * */
    public void updateCaseClinicalStatus ( List<String> nos,String hospitalId) {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = ApplicationContextUtil.getBean(ThreadPoolTaskExecutor.class);
        CompletableFuture.runAsync(() -> {
            //查询mrCase信息
            List<MrCase> mrList = this.list(new QueryWrapper<MrCase>().in(MrCase.FIELD_CASE_NO, nos).eq(MrCase.FIELD_HOSPITAL_ID, hospitalId));
            if(CollectionUtil.isEmpty(mrList)){
                return;
            }
            List<MrCase> updateMrCases = getCaseClinicalStatus(mrList,hospitalId);
            updateBatchById(updateMrCases);
        },threadPoolTaskExecutor);
    }

    /**
     * 异步更新人工审核case表的临床处理状态（转违规、撤销转违规、转自费、插销转自非、忽略、插销忽略）
     * */
    public List<MrCase> getCaseClinicalStatus (List<MrCase> mrList,String hospitalId) {
        if(CollectionUtil.isEmpty(mrList)){
            return mrList;
        }
        //临床处理状态 （0-未处理、1-处理中、2-处理完成、3-无需处理、4-忽略、5-转自费）
        List<String> nos = mrList.stream().map(mrCase->mrCase.getCaseNo()).collect(Collectors.toList());
        //查询机审违规明细
        List<MrMedicalDetail> auditDetailNos = mrReviewMapper.queryMrBaseAudit(MrCase.BASE_TABLE,nos,hospitalId);
        //查询人工违规明细
        List<MrMedicalDetail> meAuditDetailNos = mrAuditMapper.queryMrAudit(nos,hospitalId);
        //查询忽略表记录
        List<BaseAuditIgnore> ignores = baseMedicalMapper.queryIgnoreByNos(nos, hospitalId);
        Map<String,List<MrMedicalDetail>> audiDetailMap = auditDetailNos.stream().collect(Collectors.groupingBy(MrMedicalDetail::getNo));
        Map<String,List<MrMedicalDetail>> mrAudiDetailMap = meAuditDetailNos.stream().collect(Collectors.groupingBy(MrMedicalDetail::getNo));
        Map<String,List<BaseAuditIgnore>> ignoresMap = ignores.stream().collect(Collectors.groupingBy(BaseAuditIgnore::getNo));

        for(MrCase mrCase : mrList){
            String clinicalStatus = mrCase.getClinicalStatus();
            String no = mrCase.getCaseNo();
            List<MrMedicalDetail> thisAuditDetailNos = Objects.nonNull(audiDetailMap.get(no)) ? audiDetailMap.get(no) : new ArrayList<>();
            List<MrMedicalDetail> thisMrAuditDetailNos = Objects.nonNull(mrAudiDetailMap.get(no)) ? mrAudiDetailMap.get(no) : new ArrayList<>();
            List<BaseAuditIgnore> thisIgnores = Objects.nonNull(ignoresMap.get(no)) ? ignoresMap.get(no) : new ArrayList<>();
            //无需处理
            if(CollectionUtil.isEmpty(thisAuditDetailNos) && CollectionUtil.isEmpty(thisMrAuditDetailNos)){
                clinicalStatus = "3";
                //重新赋值更新临床处理状态
                mrCase.setClinicalStatus(clinicalStatus);
                continue;
            }
            //违规明细总数
            int num = thisAuditDetailNos.size() + thisMrAuditDetailNos.size();
            //获取全部非转自费detailNo
            List<String> detailNos = Stream.concat(thisAuditDetailNos.stream(),thisMrAuditDetailNos.stream()).filter(e -> !"2".equals(e.getSelfExpense())).map(e -> e.getDetailNo()).collect(Collectors.toList());
            if(num > 0 && detailNos.size() == 0){
                //处理完成（有违规明细 且全部转自费）
                clinicalStatus = "2";
            } else {
                //查询忽略表记录
                List<String> ignoreDetailNos = thisIgnores.stream().filter(ignore -> detailNos.contains(ignore.getDetailNo())).map(ignore ->ignore.getDetailNo()).collect(Collectors.toList());
                if(num > 0 && detailNos.size() == num && ignoreDetailNos.size() == 0){
                    //未处理 (有违规明细，且没有转自非明细，且没有忽略明细)
                    clinicalStatus = "0";
                } else if (num > 0 && detailNos.size() == ignoreDetailNos.size()) {
                    //处理完成 （有违规明细，且所有没有转自费的明细全部进行了忽略）
                    clinicalStatus = "2";
                } else if (num > 0 && detailNos.size() > ignoreDetailNos.size()) {
                    //处理中（有违规明细，且有未转自费明细未进行忽略）
                    clinicalStatus = "1";
                }
            }
            //重新赋值更新临床处理状态
            mrCase.setClinicalStatus(clinicalStatus);
        }
        return mrList;
    }
}
