package com.crhms.cloud.mqs.basic.vo;

import com.googlecode.jmapper.annotations.JGlobalMap;
import com.googlecode.jmapper.annotations.JMap;
import lombok.Data;
import java.util.List;
@Data
public class EngBillVo {

    //收费主单
    @JMap("medical_case")
    public EngMedicalCaseVo medical_case;
    //收费明细
    @JMap("fee_list")
    public List<EngFeeVo> fee_list;
    //医嘱明细
    @JMap("orders_list")
    public List<EngOrdersVo> medical_orders;
    //手术记录
//    @JMap("operation_list")
    public List<EngOperationDetailVo> operation_list;
    //诊断记录
    @JMap("diagnosis_list")
    public List<EngDiagnosisVo> diagnosis_list;

    //是否增量标识
    @JMap("is_increment")
    public Boolean is_increment;

}
