package com.crhms.cloud.mqs.sys.utils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

/**
 * 通用工具类之按对象中某属性排序
 */
public class SortListUtil<E> {
    public static final String DESC = "desc";
    public static final String ASC = "asc";

    /**
     * 对list中的元素按升序排列.
     *
     * @param list  排序集合
     * @param field 排序字段
     * @return
     */
    public static List<?> sort(List<?> list, final String field) {
        return sort(list, field, null);
    }

    /**
     * 对list中的元素进行排序.
     *
     * @param list  排序集合
     * @param field 排序字段
     * @param sort  排序方式: SortList.DESC(降序) SortList.ASC(升序).
     * @return
     */
    @SuppressWarnings("unchecked")
    public static List<?> sort(List<?> list, final String field, final String sort) {
        Collections.sort(list, new Comparator() {
            public int compare(Object a, Object b) {
                int ret = 0;
                try {
//                    Field f = a.getClass().getDeclaredField(field);
                    Field f = getFieldRecursively(a.getClass(), field);
                    f.setAccessible(true);
                    Class<?> type = f.getType();

                    if (type == int.class) {
                        ret = ((Integer) f.getInt(a)).compareTo((Integer) f
                                .getInt(b));
                    } else if (type == double.class) {
                        ret = ((Double) f.getDouble(a)).compareTo((Double) f
                                .getDouble(b));
                    } else if (type == long.class) {
                        ret = ((Long) f.getLong(a)).compareTo((Long) f
                                .getLong(b));
                    } else if (type == float.class) {
                        ret = ((Float) f.getFloat(a)).compareTo((Float) f
                                .getFloat(b));
                    } else if (type == Date.class) {
                        ret = ((Date) f.get(a)).compareTo((Date) f.get(b));
                    } else if (isImplementsOf(type, Comparable.class)) {
                        ret = ((Comparable) f.get(a)).compareTo((Comparable) f
                                .get(b));
                    } else {
                        ret = String.valueOf(f.get(a)).compareTo(
                                String.valueOf(f.get(b)));
                    }

                } catch (SecurityException e) {
                    e.printStackTrace();
                } catch (NoSuchFieldException e) {
                    e.printStackTrace();
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
                if (sort != null && sort.toLowerCase().equals(DESC)) {
                    return -ret;
                } else {
                    return ret;
                }

            }
        });
        return list;
    }

    /**
     * 对list中的元素按fields和sorts进行排序,
     * fields[i]指定排序字段,sorts[i]指定排序方式.如果sorts[i]为空则默认按升序排列.
     *
     * @param list
     * @param fields
     * @param sorts
     * @return
     */
    @SuppressWarnings("unchecked")
    public static List<?> sort(List<?> list, String[] fields, String[] sorts) {
        if (fields != null && fields.length > 0) {
            for (int i = fields.length - 1; i >= 0; i--) {
                final String field = fields[i];
                String tmpSort = ASC;
                if (sorts != null && sorts.length > i && sorts[i] != null) {
                    tmpSort = sorts[i];
                }
                final String sort = tmpSort;
                Collections.sort(list, new Comparator() {
                    public int compare(Object a, Object b) {
                        int ret = 0;
                        try {
                            Field f = a.getClass().getDeclaredField(field);
                            f.setAccessible(true);
                            Class<?> type = f.getType();
                            if (type == int.class) {
                                ret = ((Integer) f.getInt(a))
                                        .compareTo(f.getInt(b));
                            } else if (type == double.class) {
                                ret = ((Double) f.getDouble(a))
                                        .compareTo(f.getDouble(b));
                            } else if (type == long.class) {
                                ret = ((Long) f.getLong(a)).compareTo((Long) f
                                        .getLong(b));
                            } else if (type == float.class) {
                                ret = ((Float) f.getFloat(a))
                                        .compareTo(f.getFloat(b));
                            } else if (type == Date.class) {
                                ret = ((Date) f.get(a)).compareTo((Date) f
                                        .get(b));
                            } else if (isImplementsOf(type, Comparable.class)) {
                                ret = ((Comparable) f.get(a))
                                        .compareTo(f.get(b));
                            } else {
                                ret = String.valueOf(f.get(a)).compareTo(
                                        String.valueOf(f.get(b)));
                            }

                        } catch (SecurityException e) {
                            e.printStackTrace();
                        } catch (NoSuchFieldException e) {
                            e.printStackTrace();
                        } catch (IllegalArgumentException e) {
                            e.printStackTrace();
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        }

                        if (sort != null && sort.toLowerCase().equals(DESC)) {
                            return -ret;
                        } else {
                            return ret;
                        }
                    }
                });
            }
        }
        return list;
    }

    /**
     * 默认按正序排列
     *
     * @param list
     * @param method
     * @return
     */
    public static List<?> sortByMethod(List<?> list, final String method) {
        return sortByMethod(list, method, null);
    }

    @SuppressWarnings("unchecked")
    public static List<?> sortByMethod(List<?> list, final String method,
                                       final String sort) {
        Collections.sort(list, new Comparator() {
            public int compare(Object a, Object b) {
                int ret = 0;
                try {
                    Method m = a.getClass().getMethod(method, null);
                    m.setAccessible(true);
                    Class<?> type = m.getReturnType();
                    if (type == int.class) {
                        ret = ((Integer) m.invoke(a, null))
                                .compareTo((Integer) m.invoke(b, null));
                    } else if (type == double.class) {
                        ret = ((Double) m.invoke(a, null)).compareTo((Double) m
                                .invoke(b, null));
                    } else if (type == long.class) {
                        ret = ((Long) m.invoke(a, null)).compareTo((Long) m
                                .invoke(b, null));
                    } else if (type == float.class) {
                        ret = ((Float) m.invoke(a, null)).compareTo((Float) m
                                .invoke(b, null));
                    } else if (type == Date.class) {
                        ret = ((Date) m.invoke(a, null)).compareTo((Date) m
                                .invoke(b, null));
                    } else if (isImplementsOf(type, Comparable.class)) {
                        ret = ((Comparable) m.invoke(a, null))
                                .compareTo((Comparable) m.invoke(b, null));
                    } else {
                        ret = String.valueOf(m.invoke(a, null)).compareTo(
                                String.valueOf(m.invoke(b, null)));
                    }

                    if (isImplementsOf(type, Comparable.class)) {
                        ret = ((Comparable) m.invoke(a, null))
                                .compareTo((Comparable) m.invoke(b, null));
                    } else {
                        ret = String.valueOf(m.invoke(a, null)).compareTo(
                                String.valueOf(m.invoke(b, null)));
                    }

                } catch (NoSuchMethodException ne) {
                    System.out.println(ne);
                } catch (IllegalAccessException ie) {
                    System.out.println(ie);
                } catch (InvocationTargetException it) {
                    System.out.println(it);
                }

                if (sort != null && sort.toLowerCase().equals(DESC)) {
                    return -ret;
                } else {
                    return ret;
                }
            }
        });
        return list;
    }

    @SuppressWarnings("unchecked")
    public static List<?> sortByMethod(List<?> list, final String methods[],
                                       final String sorts[]) {
        if (methods != null && methods.length > 0) {
            for (int i = methods.length - 1; i >= 0; i--) {
                final String method = methods[i];
                String tmpSort = ASC;
                if (sorts != null && sorts.length > i && sorts[i] != null) {
                    tmpSort = sorts[i];
                }
                final String sort = tmpSort;
                Collections.sort(list, new Comparator() {
                    public int compare(Object a, Object b) {
                        int ret = 0;
                        try {
                            Method m = a.getClass().getMethod(method, null);
                            m.setAccessible(true);
                            Class<?> type = m.getReturnType();
                            if (type == int.class) {
                                ret = ((Integer) m.invoke(a, null))
                                        .compareTo((Integer) m.invoke(b, null));
                            } else if (type == double.class) {
                                ret = ((Double) m.invoke(a, null))
                                        .compareTo((Double) m.invoke(b, null));
                            } else if (type == long.class) {
                                ret = ((Long) m.invoke(a, null))
                                        .compareTo((Long) m.invoke(b, null));
                            } else if (type == float.class) {
                                ret = ((Float) m.invoke(a, null))
                                        .compareTo((Float) m.invoke(b, null));
                            } else if (type == Date.class) {
                                ret = ((Date) m.invoke(a, null))
                                        .compareTo((Date) m.invoke(b, null));
                            } else if (isImplementsOf(type, Comparable.class)) {
                                ret = ((Comparable) m.invoke(a, null))
                                        .compareTo((Comparable) m.invoke(b,
                                                null));
                            } else {
                                ret = String.valueOf(m.invoke(a, null))
                                        .compareTo(
                                                String.valueOf(m
                                                        .invoke(b, null)));
                            }

                        } catch (NoSuchMethodException ne) {
                            System.out.println(ne);
                        } catch (IllegalAccessException ie) {
                            System.out.println(ie);
                        } catch (InvocationTargetException it) {
                            System.out.println(it);
                        }

                        if (sort != null && sort.toLowerCase().equals(DESC)) {
                            return -ret;
                        } else {
                            return ret;
                        }
                    }
                });
            }
        }
        return list;
    }

    /**
     * 判断对象实现的所有接口中是否包含szInterface
     *
     * @param clazz
     * @param szInterface
     * @return
     */
    public static boolean isImplementsOf(Class<?> clazz, Class<?> szInterface) {
        boolean flag = false;

        Class<?>[] face = clazz.getInterfaces();
        for (Class<?> c : face) {
            if (c == szInterface) {
                flag = true;
                return flag;
            } else {
                flag = isImplementsOf(c, szInterface);
                if (flag)
                    return flag;
            }
        }

        if (!flag && null != clazz.getSuperclass()) {
            return isImplementsOf(clazz.getSuperclass(), szInterface);
        }

        return flag;
    }

    public void Sort(List<E> list, final String method, final String sort) {
        // 用内部类实现排序
        Collections.sort(list, new Comparator<E>() {

            public int compare(E a, E b) {
                int ret = 0;
                try {
                    // 获取m1的方法名
                    Method m1 = a.getClass().getMethod(method, null);
                    // 获取m2的方法名
                    Method m2 = b.getClass().getMethod(method, null);

                    if (sort != null && "desc".equals(sort)) {

                        ret = m2.invoke(((E) b), null).toString().compareTo(m1.invoke(((E) a), null).toString());

                    } else {
                        // 正序排序
                        ret = m1.invoke(((E) a), null).toString().compareTo(m2.invoke(((E) b), null).toString());
                    }
                } catch (NoSuchMethodException ne) {
                    System.out.println(ne);
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }
                return ret;
            }
        });
    }

    /**
     * smart sort
     *
     * @param list
     * @param field
     * @param asc
     * @param containNull true:返回包含null false:返回不包含null
     * @param <T>
     * @return
     */
    public static <T> List<T> smartSort(List<T> list, String field, String asc, boolean containNull) {
        if (list == null || StringUtils.isBlank(field)) {
            return list;
        }
        list.removeAll(Collections.singleton(null));
        if (list.size() <= 0) {
            return list;
        }
        try {
            List<T> midList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                Field f = list.get(i).getClass().getDeclaredField(field);
                f.setAccessible(true);
                if (f.get(list.get(i)) == null || f.get(list.get(i)).equals(Double.NaN)) {
                    Class<?> clz = midList.getClass();
                    Method m = clz.getMethod("add", Object.class);
                    m.invoke(midList, list.get(i));
                    list.remove(i);
                    i--;
                }

            }
            // 排序
            sort(list, field, asc);
            if (containNull) {
                if (midList.size() > 0) {
                    list.addAll(midList);
                }
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * smart sort with extends
     *
     * @param list
     * @param field
     * @param asc
     * @param containNull true:返回包含null false:返回不包含null
     * @param <T>
     * @return
     */
    public static <T> List<T> smartSortWithExtends(List<T> list, String field, String asc, boolean containNull) {
        if (list == null || field == null || field.isEmpty()) {
            return list;
        }

        list.removeAll(Collections.singleton(null));
        if (list.size() <= 0) {
            return list;
        }

        try {
            List<T> midList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                T item = list.get(i);
                Field f = getFieldRecursively(item.getClass(), field);

                if (f != null) {
                    f.setAccessible(true);
                    Object value = f.get(item);
                    if (value == null || value.equals(Double.NaN)) {
                        Class<?> clz = midList.getClass();
                        Method m = clz.getMethod("add", Object.class);
                        m.invoke(midList, item);
                        list.remove(i);
                        i--;
                    }
                }
            }

            sort(list, field, asc);

            if (containNull && !midList.isEmpty()) {
                list.addAll(midList);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException | InvocationTargetException e) {
            e.printStackTrace();
        }

        return list;
    }

    private static Field getFieldRecursively(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        try {
            return clazz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            Class<?> superclass = clazz.getSuperclass();
            if (superclass != null) {
                return getFieldRecursively(superclass, fieldName);
            } else {
                throw e;
            }
        }
    }


    public static <T> boolean formatName(List<T> list, StringBuilder text, String sortField, String nameField, String sort, String suffix) {
        if (list.size() <= 0) {
            return false;
        }
        boolean flag = false;

        try {
            Field fname = list.get(0).getClass().getDeclaredField(nameField);
            Field fsort = list.get(0).getClass().getDeclaredField(sortField);
            fname.setAccessible(true);
            fsort.setAccessible(true);

            if ("asc".equals(sort)) {
                if (fname.get(list.get(0)) != null) {
                    text.append(fname.get(list.get(0)) + suffix + "、");
                    flag = true;
                } else {
                    return flag;
                }
                for (int i = 1; i < list.size(); i++) {
                    if (fsort.get(list.get(i)) == null || fsort.get(list.get(i - 1)).toString() == null) {
                        break;
                    }
                    if (Double.compare(Double.parseDouble(fsort.get(list.get(i)).toString()), Double.parseDouble(fsort.get(list.get(i - 1)).toString())) == 0) {
                        text.append(fname.get(list.get(i)).toString() + suffix + "、");
                    } else {
                        break;
                    }
                }
            } else {
                if (fname.get(list.get(list.size() - 1)) != null) {
                    text.append(fname.get(list.get(list.size() - 1)) + suffix + "、");
                    flag = true;
                } else {
                    return flag;
                }
                for (int i = list.size() - 2; i > 0; i--) {
                    if (fsort.get(list.get(i)) == null || fsort.get(list.get(i + 1)) == null) {
                        break;
                    }
                    if (Double.compare(Double.parseDouble(fsort.get(list.get(i)).toString()), Double.parseDouble(fsort.get(list.get(i + 1)).toString())) == 0) {
                        text.append(fname.get(list.get(i)).toString() + suffix + "、");
                    } else {
                        break;
                    }
                }
            }
            if (text.length() > 0) {
                text.deleteCharAt(text.length() - 1);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return flag;
    }

    public static <T> List<T> formatName(List<T> list, StringBuilder text, String sortField, String nameField1, String nameField2, String sort, String suffix) {
        List<T> returnList = new ArrayList<>();
        if (list.size() <= 0) {
            return returnList;
        }

        try {
            Field fname1 = list.get(0).getClass().getDeclaredField(nameField1);
            Field fname2 = list.get(0).getClass().getDeclaredField(nameField2);
            Field fsort = list.get(0).getClass().getDeclaredField(sortField);
            fname1.setAccessible(true);
            fname2.setAccessible(true);
            fsort.setAccessible(true);

            if ("asc".equals(sort)) {
                if (fname1.get(list.get(0)) != null && fname2.get(list.get(0)) != null) {
                    text.append("\"" + fname1.get(list.get(0)) + " " + fname2.get(list.get(0)) + "\"" + suffix + "、");
                    returnList.add(list.get(0));
                } else {
                    return returnList;
                }
                for (int i = 1; i < list.size(); i++) {
                    if (fsort.get(list.get(i)) == null || fsort.get(list.get(i - 1)).toString() == null) {
                        break;
                    }
                    if (Double.compare(Double.parseDouble(fsort.get(list.get(i)).toString()), Double.parseDouble(fsort.get(list.get(i - 1)).toString())) == 0) {
                        text.append("\"" + fname1.get(list.get(i)) + " " + fname2.get(list.get(i)) + "\"" + suffix + "、");
                    } else {
                        break;
                    }
                }
            } else {
                if (fname1.get(list.get(list.size() - 1)) != null && fname2.get(list.get(list.size() - 1)) != null) {
                    text.append("\"" + fname1.get(list.get(list.size() - 1)) + " " + fname2.get(list.get(list.size() - 1)) + "\"" + suffix + "、");
                    returnList.add(list.get(list.size() - 1));
                } else {
                    return returnList;
                }
                for (int i = list.size() - 2; i > 0; i--) {
                    if (fsort.get(list.get(i)) == null || fsort.get(list.get(i + 1)) == null) {
                        break;
                    }
                    if (Double.compare(Double.parseDouble(fsort.get(list.get(i)).toString()), Double.parseDouble(fsort.get(list.get(i + 1)).toString())) == 0) {
                        text.append("\"" + fname1.get(list.get(i)) + " " + fname2.get(list.get(i)) + "\"" + suffix + "、");
                    } else {
                        break;
                    }
                }
            }
            if (text.length() > 0) {
                text.deleteCharAt(text.length() - 1);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return returnList;
    }


    public static <T> List<T> formatName2(List<T> list, StringBuilder text, String sortField, String nameField, String sort, String suffix) {
        List<T> returnList = new ArrayList<>();
        if (list.size() <= 0) {
            return returnList;
        }

        try {
            Field fname = list.get(0).getClass().getDeclaredField(nameField);
            Field fsort = list.get(0).getClass().getDeclaredField(sortField);
            fname.setAccessible(true);
            fsort.setAccessible(true);

            if ("asc".equals(sort)) {
                if (fname.get(list.get(0)) != null) {
                    text.append(fname.get(list.get(0)) + suffix + "、");
                    returnList.add(list.get(0));
                } else {
                    return returnList;
                }
                for (int i = 1; i < list.size(); i++) {
                    if (fsort.get(list.get(i)) == null || fsort.get(list.get(i - 1)).toString() == null) {
                        break;
                    }
                    if (Double.compare(Double.parseDouble(fsort.get(list.get(i)).toString()), Double.parseDouble(fsort.get(list.get(i - 1)).toString())) == 0) {
                        text.append(fname.get(list.get(i)).toString() + suffix + "、");
                        returnList.add(list.get(i));
                    } else {
                        break;
                    }
                }
            } else {
                if (fname.get(list.get(list.size() - 1)) != null) {
                    text.append(fname.get(list.get(list.size() - 1)) + suffix + "、");
                    returnList.add(list.get(list.size() - 1));
                } else {
                    return returnList;
                }
                for (int i = list.size() - 2; i > 0; i--) {
                    if (fsort.get(list.get(i)) == null || fsort.get(list.get(i + 1)) == null) {
                        break;
                    }
                    if (Double.compare(Double.parseDouble(fsort.get(list.get(i)).toString()), Double.parseDouble(fsort.get(list.get(i + 1)).toString())) == 0) {
                        text.append(fname.get(list.get(i)).toString() + suffix + "、");
                        returnList.add(list.get(i));
                    } else {
                        break;
                    }
                }
            }
            if (text.length() > 0) {
                text.deleteCharAt(text.length() - 1);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return returnList;
    }

    public static void clearStr(StringBuilder str, StringBuilder... strs) {
        if (str == null) {
            str = new StringBuilder();
        } else {
            str.delete(0, str.length());
        }
        for (StringBuilder s : strs) {
            clearStr(s);
        }
    }

    /**
     * 重新填装list
     *
     * @param list
     * @param newList
     * @param <T>
     * @return
     */
    public static <T> List<T> resetList(List<T> list, List<T> newList) {
        newList.clear();
        newList.addAll(list);
        return newList;
    }

    /**
     * 将list按一定长度分成多个list
     *
     * @param list
     * @param subSize
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> subList(List<T> list, int subSize) {
        if (list != null && list.size() > 0 && subSize > 0) {
            List<List<T>> returnList = new ArrayList<>();
            List<T> tempList = new ArrayList<>();
            for (T t : list) {
                tempList.add(t);
                if (tempList.size() % subSize == 0) {
                    returnList.add(tempList);
                    tempList = new ArrayList<>();
                }
            }
            if (tempList.size() % subSize != 0) {
                returnList.add(tempList);
            }
            return returnList;
        }
        return null;
    }

    public static <T> boolean isNotBlank(List<T> list) {
        return list != null && list.size() > 0;
    }

    public static boolean isNotBlank(List<?>... lists) {
        if (lists.length > 0) {
            for (List<?> list : lists) {
                if (CollectionUtils.isEmpty(list))
                    return false;
            }
            return true;
        }
        return false;
    }

}
