<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.sys.mapper.SysAuditMapper">


    <select id="queryTotalAudit" resultType="java.util.Map">
        select
        audit.rule_code as ruleCode,
        audit.rule_name as ruleName,
        IF(sum(audit.detail_no = '0') = 0,0,1) as isCaseRule,
        count(*) as num
        from ${table}_audit audit
        where
        audit.hospital_id = #{hospitalId}
        and audit.no = #{no}
        and audit.violation_type = '1'
        group by audit.rule_code, rule_name

    </select>

    <select id="queryTotalAuditHis" resultType="java.util.Map">
        select
        audit.rule_code as ruleCode,
        audit.rule_name as ruleName,
        IF(sum(audit.detail_no = '0') = 0,0,1) as isCaseRule,
        count(*) as num
        from ${table}_audit_his audit
        where
        audit.hospital_id = #{hospitalId}
        and audit.no = #{no}
        and audit.batch_no = #{batchNo}
        and audit.violation_type = '1'
        group by audit.rule_code, rule_name

    </select>
    <select id="queryAuditResult" resultType="java.util.Map">
        SELECT
		head.`no`,
        audit.detail_no,
		head.patient_name,
        GROUP_CONCAT(audit.rule_code) as rule_code,
        GROUP_CONCAT(audit.rule_name) as rule_name,
        GROUP_CONCAT(audit.rule_reason) as rule_reason,
        GROUP_CONCAT(reason.reason_type) as reason_type,
        GROUP_CONCAT(reason.reason_des) as reason_des
        FROM ${table} head
		LEFT JOIN ${table}_audit audit on audit.`no` = head.`no`
        LEFT JOIN ${table}_reason reason on reason.`no` = audit.`no` and reason.detail_no = audit.detail_no
        WHERE head.`no` = #{no} and audit.detail_no = #{detailNo} and head.hospital_id = #{hospitalId}
        and audit.rule_code = #{ruleCode}
        GROUP BY
        head.`no`,
        audit.detail_no,
        head.patient_name


    </select>
    <select id="queryAuditResultHis" resultType="java.util.Map">
        SELECT
		head.`no`,
        audit.detail_no,
		head.patient_name,
        audit.rule_code,
        audit.rule_name,
        audit.rule_type,
        audit.rule_type_name,
        audit.rule_reason,
        reason.reason_type,
        reason.reason_des
        FROM ${table}_his head
		LEFT JOIN ${table}_audit_his audit on audit.`no` = head.`no` and audit.batch_no = #{batchNo}
        LEFT JOIN ${table}_reason_his reason on reason.`no` = audit.`no` and reason.detail_no = audit.detail_no and reason.batch_no = #{batchNo}
        WHERE head.`no` = #{no} and audit.detail_no = #{detailNo} and head.hospital_id = #{hospitalId}
        and audit.rule_code = #{ruleCode} and head.batch_no = #{batchNo}

    </select>

    <select id="queryCaseAuditResult" resultType="java.util.Map">
        SELECT
		head.`no`,
        audit.detail_no as detailNo,
		head.patient_name as patientName,
        audit.rule_code as ruleCode,
        audit.rule_name as ruleName,
        audit.rule_type as ruleType,
        audit.rule_type_name as ruleTypeName,
        audit.rule_reason as ruleReason,
        reason.reason_type as reasonType,
        reason.reason_des as reasonDes
        FROM ${table} head
		LEFT JOIN ${table}_audit audit on audit.`no` = head.`no`
        LEFT JOIN ${table}_reason reason on reason.`no` = audit.`no` and reason.detail_no = audit.detail_no
        WHERE head.`no` = #{no} and audit.detail_no = '0' and head.hospital_id = #{hospitalId}
    </select>
</mapper>

