<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <!-- 增加历史数据表索引 -->
    <changeSet author="zhaoyac (generated)" id="20240807-1">
        <validCheckSum>1:any</validCheckSum>
        <createIndex indexName="mqs_op_pt_hisU4" tableName="mqs_op_pt_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_pt_detail_hisU4" tableName="mqs_op_pt_detail_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_pt_audit_hisU4" tableName="mqs_op_pt_audit_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_pt_reason_hisU4" tableName="mqs_op_pt_reason_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_pt_orders_hisU4" tableName="mqs_op_pt_orders_his">
            <column name="last_updated_date"/>
        </createIndex>

        <createIndex indexName="mqs_op_rg_hisU4" tableName="mqs_op_rg_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_rg_detail_hisU4" tableName="mqs_op_rg_detail_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_rg_audit_hisU4" tableName="mqs_op_rg_audit_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_rg_reason_hisU4" tableName="mqs_op_rg_reason_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_rg_orders_hisU4" tableName="mqs_op_rg_orders_his">
            <column name="last_updated_date"/>
        </createIndex>

        <createIndex indexName="mqs_op_pct_hisU4" tableName="mqs_op_pct_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_pct_detail_hisU4" tableName="mqs_op_pct_detail_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_pct_audit_hisU4" tableName="mqs_op_pct_audit_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_pct_reason_hisU4" tableName="mqs_op_pct_reason_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_op_pct_orders_hisU4" tableName="mqs_op_pct_orders_his">
            <column name="last_updated_date"/>
        </createIndex>

        <createIndex indexName="mqs_hp_bk_hisU4" tableName="mqs_hp_bk_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_bk_detail_hisU4" tableName="mqs_hp_bk_detail_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_bk_audit_hisU4" tableName="mqs_hp_bk_audit_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_bk_reason_hisU4" tableName="mqs_hp_bk_reason_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_bk_orders_hisU4" tableName="mqs_hp_bk_orders_his">
            <column name="last_updated_date"/>
        </createIndex>

        <createIndex indexName="mqs_hp_do_hisU4" tableName="mqs_hp_do_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_do_detail_hisU4" tableName="mqs_hp_do_detail_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_do_audit_hisU4" tableName="mqs_hp_do_audit_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_do_reason_hisU4" tableName="mqs_hp_do_reason_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_do_orders_hisU4" tableName="mqs_hp_do_orders_his">
            <column name="last_updated_date"/>
        </createIndex>

        <createIndex indexName="mqs_hp_rg_hisU4" tableName="mqs_hp_rg_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_rg_detail_hisU4" tableName="mqs_hp_rg_detail_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_rg_audit_hisU4" tableName="mqs_hp_rg_audit_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_rg_reason_hisU4" tableName="mqs_hp_rg_reason_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_rg_orders_hisU4" tableName="mqs_hp_rg_orders_his">
            <column name="last_updated_date"/>
        </createIndex>

        <createIndex indexName="mqs_hp_tf_hisU4" tableName="mqs_hp_tf_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_tf_detail_hisU4" tableName="mqs_hp_tf_detail_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_tf_audit_hisU4" tableName="mqs_hp_tf_audit_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_tf_reason_hisU4" tableName="mqs_hp_tf_reason_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_tf_orders_hisU4" tableName="mqs_hp_tf_orders_his">
            <column name="last_updated_date"/>
        </createIndex>

        <createIndex indexName="mqs_hp_out_hisU4" tableName="mqs_hp_out_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_out_detail_hisU4" tableName="mqs_hp_out_detail_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_out_audit_hisU4" tableName="mqs_hp_out_audit_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_out_reason_hisU4" tableName="mqs_hp_out_reason_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_out_orders_hisU4" tableName="mqs_hp_out_orders_his">
            <column name="last_updated_date"/>
        </createIndex>

        <createIndex indexName="mqs_hp_pred_hisU4" tableName="mqs_hp_pred_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_pred_detail_hisU4" tableName="mqs_hp_pred_detail_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_pred_audit_hisU4" tableName="mqs_hp_pred_audit_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_pred_reason_hisU4" tableName="mqs_hp_pred_reason_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_pred_orders_hisU4" tableName="mqs_hp_pred_orders_his">
            <column name="last_updated_date"/>
        </createIndex>

        <createIndex indexName="mqs_hp_settle_hisU4" tableName="mqs_hp_settle_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_settle_detail_hisU4" tableName="mqs_hp_settle_detail_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_settle_audit_hisU4" tableName="mqs_hp_settle_audit_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_settle_reason_hisU4" tableName="mqs_hp_settle_reason_his">
            <column name="last_updated_date"/>
        </createIndex>
        <createIndex indexName="mqs_hp_settle_orders_hisU4" tableName="mqs_hp_settle_orders_his">
            <column name="last_updated_date"/>
        </createIndex>

    </changeSet>

    <!--  明细记录增加当次审核标记 -->
    <changeSet author="zhaoyac (generated)" id="20240807-2">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_hp_rg_detail">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_rg_detail_his">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail_his">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail_his">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail_his">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail_his">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail_his">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail_his">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail_his">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail_his">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail_his">
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>


    </changeSet>

    <!--  增加违规原因表索引 -->
    <changeSet author="zhaoyac (generated)" id="20240807-3">
        <validCheckSum>1:any</validCheckSum>
        <createIndex indexName="mqs_hp_rg_reasonU2" tableName="mqs_hp_rg_reason">
            <column name="admission_no"/>
        </createIndex>
    </changeSet>

    <!--  明细记录增加当次审核标记 -->
    <changeSet author="zhaoyac (generated)" id="20240808-1">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_hp_rg_reason">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_reason">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_reason">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_reason">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_reason">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_reason">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_reason">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_reason">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_reason">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_reason">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_rg_reason_his">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_reason_his">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_reason_his">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_reason_his">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_reason_his">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_reason_his">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_reason_his">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_reason_his">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_reason_his">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_reason_his">
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>