package com.crhms.cloud.mqs.mqs_kbm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.mqs_kbm.domain.ItemDrug;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 知识库管理-医保项目目录-国家医保药品分类与代码mapper接口
 *
 * @author: wangxingang
 * @date: 2023/02/03
 */

@Mapper
public interface ItemDrugMapper extends BaseMapper<ItemDrug> {
    /**
     * 查询市场状态下拉列表
     */
    List<String> queryMarketStatusList();
}
