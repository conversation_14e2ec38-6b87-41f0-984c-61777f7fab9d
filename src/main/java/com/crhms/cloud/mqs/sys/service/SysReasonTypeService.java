package com.crhms.cloud.mqs.sys.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.sys.domain.SysReasonType;
import com.crhms.cloud.mqs.sys.mapper.SysReasonTypeMapper;
import com.crhms.cloud.mqs.sys.service.SysReasonTypeService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 系统管理-反馈类型配置(SysReasonType)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-24 15:32:45
 */
@Service("sysReasonTypeService")
public class SysReasonTypeService extends ServiceImpl< SysReasonTypeMapper, SysReasonType> {

}
