package com.crhms.cloud.mqs.sys.init_engine.web;


import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.sys.init_engine.dto.InitEngineFileLog;
import com.crhms.cloud.mqs.sys.init_engine.manager.InitEngineManager;
import com.crhms.cloud.mqs.sys.service.SysAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 初始化引擎
 * <AUTHOR>
 * @date 2023/3/28
 **/
@RestController
@RequestMapping("/api/mqs/init_engine")
public class SysInitEngineController {

    @Autowired
    private InitEngineManager initEngineManager;

    /** 
     * 生成引擎初始化单据文件，返回任务ID
     * <AUTHOR>
     * @date 2023/3/28
     **/
    @GetMapping("/file/build")
    public ResponseEntity<Long> buildFile(){
        return ResponseEntity.ok(initEngineManager.buildFile());
    }

    /**
     * 清理任务
     * <AUTHOR>
     * @date 2023/3/28
     **/
    @GetMapping("/file/clearTask")
    public ResponseEntity clearTask(){
        initEngineManager.clearTask();
        return ResponseEntity.ok(HttpStatus.OK);
    }

    /**
     * 获取任务ID
     * <AUTHOR>
     * @date 2023/3/28
     **/
    @GetMapping("/file/getTaskId")
    public ResponseEntity<Long> getTaskId(){
        return ResponseEntity.ok(initEngineManager.getTaskId());
    }

    /**
     * 获取任务进度
     * <AUTHOR>
     * @date 2023/3/28
     **/
    @GetMapping("/file/getLog")
    public ResponseEntity<InitEngineFileLog> getTaskLog(@RequestParam(value = "taskId") Long taskId){
        return ResponseEntity.ok(initEngineManager.getTaskLog(taskId));
    }
}
