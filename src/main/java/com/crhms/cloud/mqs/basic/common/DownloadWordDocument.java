package com.crhms.cloud.mqs.basic.common;

import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

import static org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE;


@Slf4j
public class DownloadWordDocument {

    public static void generateAndDownloadWord(List<Map> list , String title ,HttpServletResponse response) throws IOException {
        // 创建一个新的Word文档
        XWPFDocument document = new XWPFDocument();

        String fileName = title;
        // 添加标题
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(title);
        titleRun.setBold(true);
        titleRun.setFontSize(16);

        if(CollectionUtils.isNotEmpty(list)){
            for (Map map : list) {
                // 添加项目信息
                addProjectInfo(document, map.get("title").toString());
                List<Map> child_map = (List<Map>) map.get("child");
                if(CollectionUtils.isNotEmpty(child_map)){
                    for (Map child : child_map) {
                        List<Object> listO = (List<Object>) child.get("table");
                        addMedicareTable(document, child.get("title").toString(),listO);
                    }
                }
            }
        }


        String encodedFileName = URLEncoder.encode(fileName, "UTF-8")
                .replaceAll("\\+", "%20"); // 替换+号为%20
        // 设置响应头，告诉浏览器这是一个文件下载
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setHeader("Content-Disposition", "attachment; filename="+encodedFileName+".docx");

        // 获取响应的输出流
        OutputStream outputStream = response.getOutputStream();

        // 将Word文档写入输出流
        document.write(outputStream);

        // 关闭输出流
        outputStream.close();
        document.close();
    }

    private static void addProjectInfo(XWPFDocument document, String projecInfo) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText(projecInfo);
        run.setFontSize(12);
    }

    // 启用列宽自动调整
    private static void enableAutoColumnWidth(XWPFTable table) {
        // 遍历所有单元格，清除固定宽度设置
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                CTTcPr tcPr = cell.getCTTc().isSetTcPr() ?
                        cell.getCTTc().getTcPr() : cell.getCTTc().addNewTcPr();
                tcPr.unsetTcW(); // 清除固定宽度设置
            }
        }
    }
    private static void addMedicareTable(XWPFDocument document, String tableName,List<Object> tableData) {
        // 添加表格标题
        XWPFParagraph tableTitleParagraph = document.createParagraph();
        XWPFRun tableTitleRun = tableTitleParagraph.createRun();
        tableTitleRun.setText(tableName);
        tableTitleRun.setBold(true);
        tableTitleRun.setFontSize(14);

        // 创建表格
        XWPFTable table = document.createTable(1, 14); // 1行14列


        // 3. 设置表格宽度为页面宽度的80%（示例值，可调整）
        long pageWidth = 9525; // 默认Word页面宽度（DXA），约6.5英寸
        long tableWidth = (long) (pageWidth * 1.2); // 80%宽度
        setTableWidth(table, tableWidth);
        // 4. 设置表格水平居中（关键！）
        table.setTableAlignment(TableRowAlign.CENTER); // 左右边距自动均等

        XWPFTableRow tableRow = table.getRow(0);

        // 设置表头
        String[] headers = {"序号", "跨单据", "时间", "项目编码", "项目名称", "规格", "单价", "数量", "科室", "用药天数", "每次用量", "频次", "项目类型", "总价"};
        int[] header_width = {1,3,5,3,3,2,2,2,2,2,2,2,2,2};
        for (int i = 0; i < headers.length; i++) {
            XWPFTableCell cell = tableRow.getCell(i);
            XWPFParagraph cellParagraph = cell.addParagraph();
            XWPFRun cellRun = cellParagraph.createRun();
            cellRun.setText(headers[i]);
//            cellRun.setBold(true);
            cellRun.setFontSize(12);




//            // 设置列宽（单位：TWIPS，1cm ≈ 567 TWIPS）
//            CTTblWidth width = cell.getCTTc().addNewTcPr().addNewTcW();
//            width.setW(BigInteger.valueOf(500*header_width[i])); // 设置宽度（示例：2000 TWIPS ≈ 3.5cm）
//            width.setType(STTblWidth.DXA); // 使用固定宽度
        }

        if (CollectionUtils.isNotEmpty(tableData)) {
            int rowIndex = 1;
            for (Object o : tableData) {
                XWPFTableRow newRow = table.createRow();
                BaseMedicalDetail detail = (BaseMedicalDetail) o;
                int columnIndex = 0;
                ceateCell(table, newRow, String.valueOf(rowIndex++), columnIndex++,1); // 序号
                ceateCell(table, newRow, detail.getAdmissionNo(), columnIndex++,3); // 跨单据
                ceateCell(table, newRow, DateFormatUtils.format(detail.getItemDate(), "yyyy-MM-dd HH:mm:ss"), columnIndex++,5); // 时间
                ceateCell(table, newRow, String.valueOf(detail.getItemId()), columnIndex++,3); // 项目编码
                ceateCell(table, newRow, String.valueOf(detail.getItemName()), columnIndex++,3); // 项目名称
                ceateCell(table, newRow, String.valueOf(detail.getSpecification()), columnIndex++,2); // 规格
                ceateCell(table, newRow, String.valueOf(detail.getPrice()), columnIndex++,2); // 单价
                ceateCell(table, newRow, String.valueOf(detail.getNumbers()), columnIndex++,2); // 数量
                ceateCell(table, newRow, String.valueOf(detail.getApplyDeptName()), columnIndex++,2); // 科室
                ceateCell(table, newRow, String.valueOf(detail.getUsageDays()), columnIndex++,2); // 用药天数
                ceateCell(table, newRow, String.valueOf(detail.getUsage()), columnIndex++,2); // 每次用量
                ceateCell(table, newRow, String.valueOf(detail.getFrequencyInterval()), columnIndex++,2); // 频次
                ceateCell(table, newRow, String.valueOf(detail.getItemTypeName()), columnIndex++,2); // 项目类型
                ceateCell(table, newRow, String.valueOf(detail.getCosts()), columnIndex++,2); // 总价
            }
        }

        // 2. 设置表格整体边框（灰色实线）
        setTableBorders(table, "808080", 4); // 灰色(#808080)，粗细4/8磅（0.5磅）

        // 3. 设置所有单元格边框（灰色实线）
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                setCellBorders(cell, "808080", 4);
            }
        }
        setCellWidth(table);

    }


    /**
     * 在指定表格中创建新行并在指定单元格索引位置添加文本内容。
     *
     * @param table     需要操作的Word表格对象，不能为空
     * @param value     要设置到单元格中的文本内容
     * @param cellIndex 目标单元格的索引位置（0-based），必须有效
     * @return 无返回值
     */
    private static void ceateCell(XWPFTable table, XWPFTableRow newRow,String value,int cellIndex,int colWidth){
        XWPFTableCell cell0 = newRow.getCell(cellIndex);
        XWPFParagraph cellParagraph = cell0.addParagraph();
        XWPFRun cellRun = cellParagraph.createRun();
        cellRun.setText(value);
        cellRun.setFontSize(12);

//        // 设置列宽（单位：TWIPS，1cm ≈ 567 TWIPS）
//        CTTblWidth width = cell0.getCTTc().addNewTcPr().addNewTcW();
//        width.setW(BigInteger.valueOf(500*colWidth)); // 设置宽度（示例：2000 TWIPS ≈ 3.5cm）
//        width.setType(STTblWidth.DXA); // 使用固定宽度
    }


    /**
     * 设置表格边框样式
     * @param colorHex 颜色代码（如 "808080" 表示灰色）
     * @param size     边框粗细（单位：1/8磅，4=0.5磅）
     */
    private static void setTableBorders(XWPFTable table, String colorHex, int size) {
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        if (tblPr == null) tblPr = table.getCTTbl().addNewTblPr();

        CTTblBorders borders = tblPr.addNewTblBorders();
        setBorderStyle(borders.addNewTop(), colorHex, size);     // 上边框
        setBorderStyle(borders.addNewBottom(), colorHex, size);  // 下边框
        setBorderStyle(borders.addNewLeft(), colorHex, size);    // 左边框
        setBorderStyle(borders.addNewRight(), colorHex, size);   // 右边框
        setBorderStyle(borders.addNewInsideH(), colorHex, size); // 内部横线
        setBorderStyle(borders.addNewInsideV(), colorHex, size); // 内部竖线
    }

    /**
     * 设置单元格边框样式
     */
    private static void setCellBorders(XWPFTableCell cell, String colorHex, int size) {
        CTTcPr tcPr = cell.getCTTc().getTcPr();
        if (tcPr == null) tcPr = cell.getCTTc().addNewTcPr();

        CTTcBorders borders = tcPr.addNewTcBorders();
        setBorderStyle(borders.addNewTop(), colorHex, size);     // 上边框
        setBorderStyle(borders.addNewBottom(), colorHex, size);  // 下边框
        setBorderStyle(borders.addNewLeft(), colorHex, size);    // 左边框
        setBorderStyle(borders.addNewRight(), colorHex, size);   // 右边框
    }

    /**
     * 通用边框样式设置
     */
    private static void setBorderStyle(CTBorder border, String colorHex, int size) {
        border.setVal(STBorder.Enum.forString("single")); // 实线
        border.setColor(colorHex);                        // 颜色
        border.setSz(BigInteger.valueOf(size));           // 粗细（4=0.5磅）
    }


    // 设置表格固定宽度（单位：DXA）
    private static void setTableWidth(XWPFTable table, long widthInDxa) {
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        if (tblPr == null) tblPr = table.getCTTbl().addNewTblPr();

        CTTblWidth tblWidth = tblPr.isSetTblW() ? tblPr.getTblW() : tblPr.addNewTblW();
        tblWidth.setType(STTblWidth.DXA); // 使用绝对单位
        tblWidth.setW(BigInteger.valueOf(widthInDxa));

        // 禁用自动调整（确保宽度固定）
//        table.setWidth("100%");
    }



    private static XWPFTable setCellWidth(XWPFTable table){
        setCellWidthFunc( table, 0, 280);
        setCellWidthFunc( table, 1, 500);
//        setCellWidthFunc( table, 1, 500);
//        setCellWidthFunc( table, 2, 1000);
//       setCellWidthFunc( table, 3, 1000);
//        setCellWidthFunc( table, 4, 1000);
//        setCellWidthFunc( table, 8, 500);
        setCellWidthFunc( table, 2, 0);
        setCellWidthFunc( table, 3, 0);
        setCellWidthFunc( table, 4, 0);
        setCellWidthFunc( table, 5, 0);
        setCellWidthFunc( table, 6, 0);
        setCellWidthFunc( table, 7, 0);
        setCellWidthFunc( table, 8, 0);



        setCellWidthFunc( table, 9, 500);
        setCellWidthFunc( table, 10, 500);
        setCellWidthFunc( table, 11, 280);
        setCellWidthFunc( table, 12, 280);
        table.getCTTbl().getTblPr().addNewTblLayout().setType(STTblLayoutType.FIXED);
        return table;
    }

    private static XWPFTable setCellWidthFunc(XWPFTable table,int colIndex, int widthInt){
        // 设置第一列宽度
        int firstColWidth = widthInt; // 单位: 1/20磅

        for (XWPFTableRow row : table.getRows()) {
            XWPFTableCell cell = row.getCell(colIndex); // 第colIndex列的所有单元格

            CTTcPr tcPr = cell.getCTTc().isSetTcPr()
                    ? cell.getCTTc().getTcPr()
                    : cell.getCTTc().addNewTcPr();

            CTTblWidth width = tcPr.isSetTcW()
                    ? tcPr.getTcW()
                    : tcPr.addNewTcW();

            width.setType(STTblWidth.DXA);
            if(firstColWidth==0){

            }else{
                width.setW(BigInteger.valueOf(firstColWidth));
            }
        }

        return table;
    }
}
