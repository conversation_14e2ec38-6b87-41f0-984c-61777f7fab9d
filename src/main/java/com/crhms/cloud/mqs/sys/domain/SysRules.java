package com.crhms.cloud.mqs.sys.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.List;


/**
 * 运营管理-规则管理(SysRules)实体类
 *
 * <AUTHOR>
 * @since 2022-12-19 09:40:49
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_rules")
public class SysRules extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -17082518771850258L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_RULE_CODE="rule_code";
    public static final String FIELD_BROAD_HEADING="broad_heading";
    public static final String FIELD_RULE_LEVEL="rule_level";
    public static final String FIELD_RULE_LEVEL_NAME="rule_level_name";
    public static final String FIELD_IS_HIS="is_his";
    public static final String FIELD_IS_MAIN="is_main";
    public static final String FIELD_IS_REUSE="is_reuse";
    public static final String FIELD_REMAKE="remake";
    public static final String FIELD_ENABLE="enable";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_RULE_NAME="rule_name";
    public static final String FIELD_RULE_TYPE="rule_type";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 规则编码
     */    
    @TableField
    private String ruleCode;
    /**
     * 规则大类
     */
    @DictTranslate(dictCode = "MQS_RULE_BROAD_HEADING")
    @TableField
    private String broadHeading;
    /**
     * 规则级别，可疑 违规 待核实
     */
    @TableField
    private String ruleLevel;
    /**
     * 是否查询历史数据
     */
    @TableField
    private String isHis;
    /**
     * 是否作用在主单
     */
    @TableField
    private String isMain;


    /**
     * 规则逻辑说明
     */
    @TableField
    private String ruleLogicDesc;


    /**
     * 规则数据说明
     */
    @TableField
    private String  ruleDataDesc;

    /**
     * 规则说明
     */    
    @TableField
    private String remake;

    /**
     * 是否启用1.启用 0.禁用
     */    
    @TableField
    private String enable;

    /**
     * 编码, 用于前端区分不同的数据设置页面
     * 重复挂号:REPEAT_REGISTRATION
     * 挂号未就诊:NO_VISIT_REGISTERED
     * 重复入院:REPEATED_ADMISSION
     * 住院限制选择诊断:HP_LIMIT_DIAG
     * <p>
     * 信用审核:CREDIT_REVIEW
     * <p>
     * 其他内置:OTHER_BUILT_IN
     * <p>
     * 用户自定义:CUSTOM
     *
     */
    @TableField
    private String dataConfigCode;

    /**
     * 规则名称
     */    
    @TableField
    private String ruleName;
    /**
        * 规则类型
        *   2：内置，林总黑盒引擎，前缀B
        *   1：自定义，郑斌引擎实现的规则，前缀C
        *   3：医保平台，代理转发省平台审核接口，前缀G
     */
    @DictTranslate(dictCode = "LOCAL_RULE_TYPE",dictText = "ruleTypeName")
    @TableField
    private String ruleType;
    @TableField(exist = false)
    private String ruleTypeName;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;

    /**
     * 逻辑删除字段 1是0否
     */
    @TableField
    private String deleted;
    /**
     * 规则一级分类编码
     */
    @TableField
    private String primaryCategoryCode;

    /**
     * 规则一级分类名称
     */
    @TableField
    private String primaryCategoryName;

    /**
     * 规则二级分类编码
     */
    @TableField
    private String secondaryCategoryCode;

    /**
     * 规则二级分类名称
     */
    @TableField
    private String secondaryCategoryName;

    // 非数据库字段

    /**
     * (查询标记使用)
     */
    @TableField(exist = false)
    private String ck;


    /**
     * 多选规则大类
     */
    @TableField(exist = false)
    private List<String> broadHeadingList;

    /**
     * 规则类型编码
     */
    @TableField(exist = false)
    private String ruleLevelName;

    @TableField(exist = false)
    private String levelConfig;
    @TableField(exist = false)
    private String isBlock;
    @TableField(exist = false)
    private String isRemind;
}

