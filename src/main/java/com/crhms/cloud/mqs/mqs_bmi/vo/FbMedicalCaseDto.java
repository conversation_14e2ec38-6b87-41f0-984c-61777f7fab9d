package com.crhms.cloud.mqs.mqs_bmi.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 *  医保反馈详情实体类
 *
 * <AUTHOR>
 * @since 2023-02-08 17:07:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
public class FbMedicalCaseDto implements Serializable {
    /**
     * 单据号
     */ 
    private String no;
    /**
     * 就诊流水号
     */ 
    private String admissionNo;
 
    /**
     * 医疗类别(原就医方式编码)
     */ 
    @DictTranslate(dictCode = "DIC_MEDICAL_TYPE")
    private String claimType;
    /**
     * 参保类型编码
     */ 
    @DictTranslate(dictCode = "DIC_INSUR_TYPE")
    private String benefitType;

    //1.住院 2.门诊
    private String visitType;

    /**
     * 出院诊断名称（在院时 为主诊断）
     */ 
    private String outDiagnosisName;
    /**
     * 次诊断
     */
    private String diagnosis;
  
    /**
     * 参保人编码
     */ 
    private String patientId;
    /**
     * 参保人姓名
     */ 
    private String patientName;
    /**
     * 性别
     */ 
    @DictTranslate(dictCode = "DIC_GENDER")
    private String patientGender;
    /**
     * 出生日期
     */ 
    private String patientBirthday;
    /**
     * 年龄
     */
    private int age;
    /**
     * 单据总金额
     */ 
    private BigDecimal totalAmount;
    /**
     * 人员类别编码
     */ 
    @DictTranslate(dictCode = "DIC_PSN_TYPE")
    private String personnelType;
   
    /**
     * 医保内金额
     */ 
    private BigDecimal bmiConveredAmount;

    /**
     * 单据扣除金额
     */
    private BigDecimal deductionAmount;
   

}

