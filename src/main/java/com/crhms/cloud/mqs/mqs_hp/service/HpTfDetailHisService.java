package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpTfDetailHis;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpTfDetailHisMapper;
import com.crhms.cloud.mqs.mqs_hp.service.HpTfDetailHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-转科室明细-历史表(HpTfDetailHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 10:04:33
 */
@Service("hpTfDetailHisService")
public class HpTfDetailHisService extends ServiceImpl< HpTfDetailHisMapper, HpTfDetailHis> {

}
