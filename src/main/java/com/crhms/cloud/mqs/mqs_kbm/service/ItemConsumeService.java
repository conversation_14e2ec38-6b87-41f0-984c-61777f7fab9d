package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.ItemConsume;
import com.crhms.cloud.mqs.mqs_kbm.mapper.ItemConsumeMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 知识库管理-医保项目目录-国家医保医用耗材分类与代码 service
 *
 * @author: wangxingang
 * @date: 2023/02/03
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class ItemConsumeService extends ServiceImpl<ItemConsumeMapper, ItemConsume> {
    /**
     * 查询列表 - 国家医保医用耗材分类与代码
     *
     * @param pageInfo        分页信息
     * @param consumeCode     耗材代码
     * @param classFirst      一级分类(学科、品类)
     * @param classSecond     二级分类(用途、品目)
     * @param classThird      三级分类(部位、功能、品种)
     * @param ybCommonName    医保通用名
     * @param materialQuality 材质
     * @param feature         特征
     */
    public List<ItemConsume> queryList(Page<ItemConsume> pageInfo, String consumeCode, String classFirst, String classSecond, String classThird, String ybCommonName, String materialQuality, String feature) {
        return baseMapper.selectPage(pageInfo, new LambdaQueryWrapper<ItemConsume>()
                .like(StrUtil.isNotEmpty(consumeCode), ItemConsume::getConsumeCode, consumeCode)
                .like(StrUtil.isNotEmpty(classFirst), ItemConsume::getClassFirst, classFirst)
                .like(StrUtil.isNotEmpty(classSecond), ItemConsume::getClassSecond, classSecond)
                .like(StrUtil.isNotEmpty(classThird), ItemConsume::getClassThird, classThird)
                .like(StrUtil.isNotEmpty(ybCommonName), ItemConsume::getYbCommonName, ybCommonName)
                .like(StrUtil.isNotEmpty(materialQuality), ItemConsume::getMaterialQuality, materialQuality)
                .like(StrUtil.isNotEmpty(feature), ItemConsume::getFeature, feature)
        ).getRecords();
    }
}
