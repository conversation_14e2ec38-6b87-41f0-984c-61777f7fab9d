package com.crhms.cloud.mqs.basic.domain;

import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;


/**
 * 反馈结果表(BaseAuditReason)实体类
 *
 * <AUTHOR>
 * @since 2023-01-18 15:53:11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class BaseAuditReason extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -65354220614909379L;

    public static final String FIELD_ID = "id";
    public static final String FIELD_DETAIL_NO = "detail_no";
    public static final String FIELD_NO = "no";
    public static final String FIELD_ITEM_ID = "item_id";
    public static final String FIELD_RULE_CODE = "rule_code";
    public static final String FIELD_REASON_TYPE = "reason_type";
    public static final String FIELD_REASON_DES = "reason_des";
    public static final String FIELD_BATCH_NO = "batch_no";
    public static final String FIELD_LAST_UPDATED_BY = "last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE = "last_updated_date";
    public static final String FIELD_CREATED_BY = "created_by";
    public static final String FIELD_CREATED_DATE = "created_date";
    public static final String FIELD_HOSPITAL_ID = "hospital_id";

    /**
     * 表id
     */
    private Integer id;

    /**
     * 明细流水号
     */
    private String detailNo;
    /**
     * 单据号
     */
    private String no;
    /**
     * 就诊流水号
     */
    private String admissionNo;
    /**
     * 项目
     */
    private String itemId;
    /**
     * 弹窗反馈自费标记
     */
    private String selfExpense;
    /**
     * 违规编码
     */
    private String ruleCode;
    /**
     * 反馈类型
     */
    private String reasonType;
    /**
     * 反馈理由
     */
    private String reasonDes;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 院区id
     */
    private String hospitalId;


}

