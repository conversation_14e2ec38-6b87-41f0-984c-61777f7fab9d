package com.crhms.cloud.mqs.sys.dto;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 门诊审核-处方审核-反馈结果表(SysReason)实体类
 *
 * <AUTHOR>
 * @since 2022-12-19 09:41:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class SysReason extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -73710342340669412L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_DETAIL_NO="detail_no";
    public static final String FIELD_NO="no";
    public static final String FIELD_ITEM_ID="item_id";
    public static final String FIELD_RULE_CODE="rule_code";
    public static final String FIELD_REASON_TYPE="reason_type";
    public static final String FIELD_REASON_DES="reason_des";
    public static final String FIELD_BATCH_NO="batch_no";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 明细流水号
     */    
    @TableField
    private String detailNo;
    /**
     * 单据号
     */    
    @TableField
    private String no;
    /**
     * 项目
     */    
    @TableField
    private String itemId;
    /**
     * 违规编码
     */    
    @TableField
    private String ruleCode;
    /**
     * 反馈类型
     */    
    @TableField
    private String reasonType;
    /**
     * 反馈理由
     */    
    @TableField
    private String reasonDes;
    /**
     * 批次号
     */    
    @TableField
    private String batchNo;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;


}

