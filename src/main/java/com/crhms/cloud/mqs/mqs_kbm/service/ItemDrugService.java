package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.ItemDrug;
import com.crhms.cloud.mqs.mqs_kbm.mapper.ItemDrugMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 知识库管理-医保项目目录-国家医保药品分类与代码 service
 *
 * @author: wangxingang
 * @date: 2023/02/03
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class ItemDrugService extends ServiceImpl<ItemDrugMapper, ItemDrug> {

    /**
     * 查询列表 - 国家医保药品分类与代码
     *
     * @param pageInfo         分页信息
     * @param drugCode         药品代码
     * @param registeredName   注册名称
     * @param drugEnterprise   药品企业
     * @param approvalNo       批准文号
     * @param marketStatusList 市场状态
     */
    public List<ItemDrug> queryList(Page<ItemDrug> pageInfo, String drugCode, String registeredName, String drugEnterprise, String approvalNo, List<String> marketStatusList) {
        return baseMapper.selectPage(pageInfo, new LambdaQueryWrapper<ItemDrug>()
                .like(StrUtil.isNotEmpty(drugCode), ItemDrug::getDrugCode, drugCode)
                .like(StrUtil.isNotEmpty(registeredName), ItemDrug::getRegisteredName, registeredName)
                .like(StrUtil.isNotEmpty(drugEnterprise), ItemDrug::getDrugEnterprise, drugEnterprise)
                .like(StrUtil.isNotEmpty(approvalNo), ItemDrug::getApprovalNo, approvalNo)
                .in(CollUtil.isNotEmpty(marketStatusList), ItemDrug::getMarketStatus, marketStatusList)
        ).getRecords();
    }

    /**
     * 查询市场状态下拉列表
     */
    public List<String> queryMarketStatusList() {
        return baseMapper.queryMarketStatusList();
    }
}
