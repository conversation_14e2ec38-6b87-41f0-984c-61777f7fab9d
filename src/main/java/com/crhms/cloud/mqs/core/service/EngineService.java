package com.crhms.cloud.mqs.core.service;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.mqs.basic.vo.EngAuditResultVo;
import com.crhms.cloud.mqs.basic.vo.EngAuditVo;
import com.crhms.cloud.mqs.core.engineService.engineService;
import com.crhms.cloud.mqs.sys.domain.SysConfig;
import com.crhms.cloud.mqs.sys.service.SysConfigService;
import lombok.extern.log4j.Log4j2;
import org.apache.thrift.protocol.TBinaryProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.TSocket;
import org.apache.thrift.transport.TTransport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

@Service
@Configuration
@Log4j2
public class EngineService {

//    @Value("${mqs.hospital-engine-mqs.url}")
    private static String mqsEng = "MQS_ENGINE_THRIFT_URL";
    @Lazy
    @Autowired
    private SysConfigService sysConfigService;

    @Value("${mqs.conf.engAudit.timeout}")
    private int ENGAUDIT_TIMEOUT;

    public EngAuditResultVo execRulesByCodes(String batch, EngAuditVo engAuditVo, List<Map<String,Object>> rules,String hospitalId){

        ExecutorService executor = Executors.newSingleThreadExecutor();

        String sysValue = sysConfigService.queryValueByKey(mqsEng, hospitalId);
        String[] groupUrlArr = sysValue.split(":");
        String s = JSONUtil.toJsonStr(engAuditVo);
        String s1 = JSONUtil.toJsonStr(rules);
        // 调用审核引擎
        Future<String> future = executor.submit(new Callable<String>() {
            @Override
            public String call() throws Exception {
                String engResult = null;
                TTransport transport = new TSocket(groupUrlArr[0], Integer.valueOf(groupUrlArr[1]));
                TProtocol protocol = new TBinaryProtocol(transport);
                engineService.Client client = new engineService.Client(protocol);
                transport.open();
                log.debug("======调用引擎审核 executor Start======" + batch + ":" + new java.sql.Timestamp(System.currentTimeMillis()).toString());
                log.debug("======调用引擎审核 参数======" + batch + ":");
                log.debug("======参数====data :" + batch + ":" + s);
                log.debug("======参数====rules :" + batch + ":" + s1);
                engResult = client.execRulesByCodes(s, s1);
                log.debug("======调用引擎审核 结果===" + batch + ":" + engResult);
                log.debug("======调用引擎审核 executor End======"  + batch + ":" + new java.sql.Timestamp(System.currentTimeMillis()).toString());

                transport.close();
                return engResult;
            }
        });

        String engResult = null;
        try{
            log.info("======调用引擎审核 Start======" + batch + ":" + new java.sql.Timestamp(System.currentTimeMillis()).toString());
            engResult = future.get(ENGAUDIT_TIMEOUT, TimeUnit.SECONDS);
            log.info("======调用引擎审核 End======"  + batch + ":" + new java.sql.Timestamp(System.currentTimeMillis()).toString());

        }catch (TimeoutException e){
            engResult = "{\"is_success\": true,\"total_time\": " + ENGAUDIT_TIMEOUT*1000 + ",\"local\": {\"success\": true}}";
            log.error("======调用引擎超时======" + batch + ":" + new java.sql.Timestamp(System.currentTimeMillis()).toString());
        }catch(Exception e){
            e.printStackTrace();
            engResult = "{\"is_success\": true,\"total_time\": " + ENGAUDIT_TIMEOUT*1000 + ",\"local\": {\"success\": true}}";
            log.error("======调用引擎异常======" + batch + ":"  + new java.sql.Timestamp(System.currentTimeMillis()).toString() + ":"+ e.getMessage());
        } finally {
            future.cancel(true); // 如果任务运行超过设定的超时时间，就会被取消
            executor.shutdown(); // 关闭ExecutorService
        }

        EngAuditResultVo engAuditResultVo = JSONUtil.toBean(JSONUtil.parseObj(engResult), EngAuditResultVo.class);

        return engAuditResultVo;
    }

    public EngAuditResultVo saveRecord(EngAuditVo engAuditVo,String hospitalId){
        String sysValue = sysConfigService.queryValueByKey(mqsEng, hospitalId);
        String[] groupUrlArr = sysValue.split(":");
        String s = JSONUtil.toJsonStr(engAuditVo);
        // 调用审核引擎
        String engResult = null;
        try{
            TTransport transport = new TSocket(groupUrlArr[0], Integer.valueOf(groupUrlArr[1]));
            TProtocol protocol = new TBinaryProtocol(transport);
            engineService.Client client = new engineService.Client(protocol);
            transport.open();
            engResult = client.saveRecord(s);
            transport.close();
        } catch (Exception e){
            e.printStackTrace();
            throw new BaseException(StrUtil.format("调用审核引擎异常:{}", ExceptionUtil.stacktraceToString(e)));
        }
        EngAuditResultVo engAuditResultVo = JSONUtil.toBean(JSONUtil.parseObj(engResult), EngAuditResultVo.class);
        return engAuditResultVo;
    }

    public Map deleteRecord(String patientId, String no, String detailNo,String hospitalId){
        String sysValue = sysConfigService.queryValueByKey(mqsEng, hospitalId);
        String[] groupUrlArr = sysValue.split(":");
        // 调用审核引擎
        String engResult = null;
        try{
            TTransport transport = new TSocket(groupUrlArr[0], Integer.valueOf(groupUrlArr[1]));
            TProtocol protocol = new TBinaryProtocol(transport);
            engineService.Client client = new engineService.Client(protocol);
            transport.open();
            if(ObjectUtil.isEmpty(detailNo)){
                engResult = client.deleteRecord(patientId,no);
            }else {
                engResult = client.deleteFeeList(patientId,no,detailNo);
            }
            transport.close();
        } catch (Exception e){
            e.printStackTrace();
            throw new BaseException(StrUtil.format("调用审核引擎异常:{}", ExceptionUtil.stacktraceToString(e)));
        }
        Map map = JSONUtil.toBean(engResult, Map.class);
        return map;
    }

    public Map deleteRule(String rules,String hospitalId){
        String sysValue = sysConfigService.queryValueByKey(mqsEng, hospitalId);
        String[] groupUrlArr = sysValue.split(":");
        // 调用审核引擎
        String engResult = null;
        try{
            TTransport transport = new TSocket(groupUrlArr[0], Integer.valueOf(groupUrlArr[1]));
            TProtocol protocol = new TBinaryProtocol(transport);
            engineService.Client client = new engineService.Client(protocol);
            transport.open();
            engResult = client.removeRules(rules);
            transport.close();
        } catch (Exception e){
            e.printStackTrace();
            throw new BaseException(StrUtil.format("调用审核引擎异常:{}", ExceptionUtil.stacktraceToString(e)));
        }
        Map map = JSONUtil.toBean(engResult, Map.class);
        return map;
    }

}
