package com.crhms.cloud.mqs.sys.controller;


import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.service.GlobalCache;
import com.crhms.cloud.mqs.sys.domain.SysConfig;
import com.crhms.cloud.mqs.sys.domain.SysRuleLevel;
import com.crhms.cloud.mqs.sys.domain.SysRuleLevelThird;
import com.crhms.cloud.mqs.sys.service.SysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统配置
 * <AUTHOR>
 * @since 2023-01-12 14:27:01
 */
@RestController
@RequestMapping("api/mqs/sysConfig")
public class SysConfigController {

    @Autowired
    SysConfigService sysConfigService;

    @Autowired
    GlobalCache globalCache;

    /**
     * 获取系统配置
     * @param key
     * @return
     */
    @GetMapping("/queryConfigByKey")
    public ResponseEntity<SysConfig> queryByKey(@RequestParam(value = "key") String key){

        return ResponseEntity.ok(sysConfigService.queryByKey(key,LoginContext.getHospitalId()));
    }

    /**
     * 获取系统配置列表
     * @param key
     * @return
     */
    @GetMapping("/queryConfigList")
    public ResponseEntity<List<SysConfig>> queryList(@RequestParam(value = "key",required = false) String key){

        return ResponseEntity.ok(sysConfigService.queryList(SysConfig.builder().sysKey(key).hospitalId(LoginContext.getHospitalId()).build()));
    }

    /**
     * 获取违规级别配置列表
     * @param platform
     * @return
     */
    @GetMapping("/queryRuleLevelList")
    public ResponseEntity<List<SysRuleLevel>> queryRuleLevelList(@RequestParam(value = "platform",required = false) String platform,
                                                                 @RequestParam(value = "levelConfig",required = false) String levelConfig){

        return ResponseEntity.ok(sysConfigService.queryRuleLevelList(SysRuleLevel.builder().platform(platform).levelConfig(levelConfig).hospitalId(LoginContext.getHospitalId()).build()));
    }

    /**
     * 查询规则级别下拉列表(从 mqs_sys_rule_level_third 表查)
     * @param platform 平台 1自定义2内置3医保平台
     * @return 规则级别集合
     */
    @GetMapping("/ruleLevelList")
    public ResponseEntity<List<SysRuleLevelThird>> ruleLevelList(@RequestParam(value = "platform", required = false) String platform) {
        List<SysRuleLevelThird> ruleLevelList = sysConfigService.ruleLevelList(platform);
        return new ResponseEntity<>(ruleLevelList, HttpStatus.OK);
    }



    /**
     * 更新规则级别配置
     * @param sysRuleLevels
     * @return
     */
    @PostMapping("/updateRuleLevel")
    public ResponseEntity updateRuleLevel(@RequestBody List<SysRuleLevel> sysRuleLevels){
        sysConfigService.updateRuleLevel(sysRuleLevels);
        return ResponseEntity.ok("更新成功！");

    }
    /**
     * 更新/新增系统配置
     * @param sysConfig
     * @return
     */
    @PostMapping("/updateConfig")
    public ResponseEntity updateConfig(@RequestBody SysConfig sysConfig){
        sysConfigService.updateConfig(sysConfig);
        return ResponseEntity.ok(HttpStatus.OK);

    }

    /**
     * 检查用户所在院区的初始化状态 未初始化将以 院区为1的数据为模板初始化
     * @return
     */
    @GetMapping("/check/initialization")
    public ResponseEntity compatibilityHospital(){
        sysConfigService.compatibilityHospital("99");
        return ResponseEntity.ok("send initialization!");
    }


    /**
     * 手动刷新所有缓存
     * @return
     */
    @GetMapping("/refreshRedis")
    public ResponseEntity refreshRedis(){
        globalCache.initCache();
        return ResponseEntity.ok("刷新成功");
    }

    /**
     * 手动刷新规则环境
     * @return
     */
    @GetMapping("/refreshRedis/reflashRulesCache")
    public ResponseEntity reflashRulesCache(){
        globalCache.reflashRulesCache(LoginContext.getHospitalId());
        return ResponseEntity.ok("刷新成功");
    }
    /**
     * 手动刷新场景规则缓存
     * @return
     */
    @GetMapping("/refreshRedis/reflashSceneRuleCache")
    public ResponseEntity reflashSceneRuleCache(){
        globalCache.reflashSceneRuleCache(LoginContext.getHospitalId());
        return ResponseEntity.ok("刷新成功");
    }
    /**
     * 手动刷新审核记数缓存
     * @return
     */
    @GetMapping("/refreshRedis/reflashAuditNumsCache")
    public ResponseEntity reflashAuditNumsCache(){
        globalCache.reflashAuditNumsCache(LoginContext.getHospitalId());
        return ResponseEntity.ok("刷新成功");
    }

    /**
     * 手动刷新系统配置
     * @return
     */
    @GetMapping("/refreshRedis/reflashSystemConfigCache")
    public ResponseEntity reflashSystemConfigCache(){
        globalCache.reflashSystemConfigCache(LoginContext.getHospitalId());
        return ResponseEntity.ok("刷新成功");
    }

}
