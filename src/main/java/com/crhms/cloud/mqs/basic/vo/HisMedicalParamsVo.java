package com.crhms.cloud.mqs.basic.vo;

import lombok.Data;

import java.util.List;

/**
 * 审核统一接口对象
 */
@Data
public class HisMedicalParamsVo {

    /**
     * 主单
     */
    public HisMedicalCaseVo medical_case;
    /**
     * 缴费明细
     */
    public List<HisFeeVo> fee_list;

    /**
     * 医嘱明细（医嘱项目、计价项目)
     */
    public List<HisOrdersVo> orders_list;

    /**
     * 是否增量标识
     */
    public Boolean is_increment;

    /**
     * 手术记录
     */
    public List<HisOperationMainVo> operation_list;
    /**
     * 诊断记录
     */
    public List<HisDiagnosisVo> diagnosis_list;
    /**
     * 未交费记录
     */
    public List<UnpaidRecordsVo> unpaid_list;

    /**
     * 未就诊记录
     */
    public List<UnVisitVo> unvisit_list;

}
