package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.CgPath;
import com.crhms.cloud.mqs.mqs_kbm.mapper.CgPathMapper;
import com.crhms.cloud.mqs.mqs_kbm.vo.CgPathQueryVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 临床指南-临床路径
 *
 * @author: wangxingang
 * @date: 2023.02.08
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class CgPathService extends ServiceImpl<CgPathMapper, CgPath> {
    /**
     * 新增
     *
     * @param deptNameList 适用科室列表
     * @param fileName     临床路径文件名
     * @param oid          临床路径文件oid
     * @param releaseTime  发布时间
     */
    public void saveCgPath(List<String> deptNameList, String fileName, String oid, Integer releaseTime) {
        // 来源：系统生成，包括院内和国家发布两类，系统内置的为国家发布，用户自己上传的为院内
        List<CgPath> batch = new ArrayList<>(deptNameList.size());
        for (String deptName : deptNameList) {
            batch.add(CgPath.builder()
                    .deptName(deptName)
                    .fileName(fileName)
                    .oid(oid)
                    .releaseTime(releaseTime)
                    .dataSource("院内")
                    .build());
        }
        saveBatch(batch);
    }


    /**
     * 查询列表
     *
     * @param filter 查询条件
     */
    public List<CgPath> queryList(CgPathQueryVo filter) {
        // 适用科室
        List<String> deptNameList = filter.getDeptNameList();
        // 临床路径
        String fileName = filter.getFileName();
        // 发布时间
        List<Integer> releaseTimeList = filter.getReleaseTimeList();
        // 来源
        List<String> dataSourceList = filter.getDataSourceList();

        return baseMapper.selectList(new LambdaQueryWrapper<CgPath>()
                .in(CollUtil.isNotEmpty(deptNameList), CgPath::getDeptName, deptNameList)
                .like(StrUtil.isNotEmpty(fileName), CgPath::getFileName, fileName)
                .in(CollUtil.isNotEmpty(releaseTimeList), CgPath::getReleaseTime, releaseTimeList)
                .in(CollUtil.isNotEmpty(dataSourceList), CgPath::getDataSource, dataSourceList)
                .select(CgPath::getId, CgPath::getDeptName, CgPath::getFileName, CgPath::getReleaseTime, CgPath::getDataSource)
        );

    }


    /**
     * 查询适用科室下拉列表
     *
     * @return 适用科室下拉列表
     */
    public List<String> queryDeptNameList() {
        return baseMapper.queryDeptNameList();
    }

    /**
     * 查询发布时间下拉列表
     *
     * @return 发布时间下拉列表
     */
    public List<String> queryReleaseTimeList() {
        return baseMapper.queryReleaseTimeList();
    }
}
