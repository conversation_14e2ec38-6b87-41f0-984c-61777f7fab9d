package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpPredDetail;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpPredDetailMapper;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-预出院审核明细(HpPredDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 09:48:16
 */
@Service("hpPredDetailService")
public class HpPredDetailService extends ServiceImpl< HpPredDetailMapper, HpPredDetail> {

}
