package com.crhms.cloud.mqs.mqs_op.mapper;

import com.crhms.cloud.mqs.mqs_op.domain.OpPtHis;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 门诊审核-门诊缴费审核-历史表(OpPtHis)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-12-19 14:25:46
 */

@Mapper
public interface OpPtHisMapper extends BaseMapper<OpPtHis> {

}

