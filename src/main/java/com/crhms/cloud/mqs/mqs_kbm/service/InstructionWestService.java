package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.mqs.mqs_kbm.mapper.InstructionWestMapper;
import com.crhms.cloud.mqs.mqs_kbm.vo.InstructionDrug;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 说明书-西药药品说明书 service
 *
 * @author: wangxingang
 * @date: 2023.02.09
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class InstructionWestService {

    @Autowired
    private InstructionWestMapper mapper;


    /**
     * 通用接口: 查询剂型下拉列表
     *
     * @param type 0:西药 1:中药
     */
    public List<String> queryDosageList(String type) {
        return mapper.queryDosageList(type);
    }

    /**
     * 通用接口: 查询药品列表
     *
     * @param pageInfo   分页信息
     * @param type       0:西药 1:中药
     * @param drugCode   药品编码
     * @param drugName   药品名称
     * @param dosageList 剂型列表
     */
    public List<InstructionDrug> queryDrugList(Page<InstructionDrug> pageInfo, String type, String drugCode, String drugName, List<String> dosageList) {
        return StrUtil.equals(type, "1") ? mapper.queryWestDrugList(pageInfo, type, drugCode, drugName, dosageList)
                : mapper.queryChineseDrugList(pageInfo, type, drugCode, drugName, dosageList);
    }

    /**
     * 通用接口: 查询特殊人群用药
     *
     * @param pageInfo 分页信息
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    public List<Map<String, String>> querySpecialGroup(Page<Object> pageInfo, String type, String drugCode) {
        return mapper.querySpecialGroup(pageInfo, type, drugCode);
    }

    /**
     * 通用接口: 查询药品配伍禁忌
     *
     * @param pageInfo 分页信息
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    public List<Map<String, String>> queryIncompatibility(Page<Object> pageInfo, String type, String drugCode) {
        return mapper.queryIncompatibility(pageInfo, type, drugCode);
    }

    /**
     * 通用接口: 查询药品适应症关系
     *
     * @param pageInfo 分页信息
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    public List<Map<String, String>> queryIndication(Page<Object> pageInfo, String type, String drugCode) {
        List<Map<String, String>> mapList = mapper.queryIndication(pageInfo, type, drugCode);
        // 处理适应症字段
        for (Map<String, String> map : mapList) {
            boolean bothNull = map.get("idication_idication_cn") == null && map.get("idication_B_idication_cn") == null;
            boolean firstNull = map.get("idication_idication_cn") == null && map.get("idication_B_idication_cn") != null;
            boolean secondNull = map.get("idication_idication_cn") != null && map.get("idication_B_idication_cn") == null;
            if (bothNull) {
                map.put("indication", "");
            } else if (firstNull) {
                map.put("indication", map.get("idication_idication_cn"));
            } else if (secondNull) {
                map.put("indication", map.get("idication_B_idication_cn"));
            } else {
                map.put("indication", map.get("idication_idication_cn") + "和" + map.get("idication_B_idication_cn"));
            }
            map.remove("idication_idication_cn");
            map.remove("idication_B_idication_cn");
        }
        return mapList;
    }

    /**
     * 通用接口: 查询药品禁忌症关系
     *
     * @param pageInfo 分页信息
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    public List<Map<String, String>> queryTaboos(Page<Object> pageInfo, String type, String drugCode) {
        return mapper.queryTaboos(pageInfo, type, drugCode);
    }

    /**
     * 西药接口: 查询药物相互作用
     *
     * @param pageInfo 分页信息
     * @param drugCode 药品编码
     */
    public List<Map<String, String>> queryInteraction(Page<Object> pageInfo, String drugCode) {
        return mapper.queryInteraction(pageInfo, drugCode);
    }

    /**
     * 西药接口: 查询过敏反应
     *
     * @param pageInfo 分页信息
     * @param drugCode 药品编码
     */
    public List<Map<String, String>> queryAllergy(Page<Object> pageInfo, String drugCode) {
        return mapper.queryAllergy(pageInfo, drugCode);
    }
}
