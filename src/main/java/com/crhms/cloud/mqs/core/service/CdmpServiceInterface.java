package com.crhms.cloud.mqs.core.service;

import com.crhms.cloud.mqs.basic.vo.BmiSaveResonVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

/**
 * @ClassName CdmpInterface
 * @description 数据平台服务间调用接口
 * <AUTHOR>
 * @date 2024/07/30 16:23
 */
@FeignClient(
        name = "cdmp",
        url = "${crhms.application.cdmp.url:}"
)
public interface CdmpServiceInterface {
    /**
     * 三方消息发送接口
     * @param request
     * @return
     */
    @PostMapping(
            value = {"/api/cdmp/bus/1/mqs/sendMessage"},
            produces = {"application/json"}
    )
    Object sendMessageToPlatform(Map<String,Object> request);

    /**
     * 转自费三方接口
     * 请求实例
     * {
     * 	"admissionNo":"11",
     *     "patientId":"142419",
     *
     *     "selfDetailNos": [
     * 		"001",
     * 		"002"
     * 	],
     * 	"unselfDetailNos": [
     * 		"003",
     * 		"004"
     * 	]
     * }
     *
     * 返回实例
     * {
     * 	"reqTimestamp": "1731641264963",
     * 	"data": {
     * 		"code": "100100", -- 100100表示业务成功 100200表示业务失败
     * 		"status": "success",
     * 		"desc": "转自费成功"
     * 	    },
     * 	"processTime": "114",
     * 	"errorMsg": "",
     * 	"requestId": "1857264194640695297",
     * 	"status": "SUCCESS"
     * }
     * @param request
     * @return
     */
    @PostMapping(
            value = {"/api/cdmp/bus/1/mqs/change/selfexpense"},
            produces = {"application/json"}
    )
    Object sendToChangeSelfexpense(Map<String,Object> request);


    /**
     * 医保审核接口 3101、3102
     * @param request
     * @return
     */
    @PostMapping(
            value = {"/api/cdmp/bus/1/mqs/bmiAudit/310x"},
            produces = {"application/json"}
    )
    Object sendToBmiAudit(Object request);
    /**
     * 医保反馈接口 3103
     * @param request
     * @return
     */
    @PostMapping(
            value = {"/api/cdmp/bus/1/mqs/bmiBack/3103"},
            produces = {"application/json"}
    )
    Object sendToBmiCallBack(BmiSaveResonVo request);
}
