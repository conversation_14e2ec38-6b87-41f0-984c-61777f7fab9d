package com.crhms.cloud.mqs.mqs_kbm.vo;

import lombok.Builder;
import lombok.Data;

/**
 * 临床指南-"一般治疗"页面信息
 *
 * @author: wangxingang
 * @date: 2023.02.08
 */

@Data
@Builder
public class CgGuideGeneralItem {

    /**
     * 编码
     */
    private String diseaseCode;

    /**
     * 治疗名称
     */
    private String serviceName;

    /**
     * 单日频次
     */
    private String serviceNumber;

    /**
     * 项目疗程
     */
    private String serviceProcess;

    /**
     * 项目内涵
     */
    private String serviceDetail;

    /**
     * 备注
     */
    private String remarks;
}
