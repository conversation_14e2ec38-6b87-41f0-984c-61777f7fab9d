package com.crhms.cloud.mqs.mqs_kbm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 知识库管理-分组合理性-编码有效性
 *
 * @author: wang<PERSON><PERSON>
 * @date: 2023.02.06
 */
@Data
@TableName("mqs_kbm_group_code_validity")
public class GroupCodeValidity {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 版本号
     */
    @TableField
    private String versionNo;

    /**
     * 类型
     */
    @TableField
    private String type;

    /**
     * 诊断代码
     */
    @TableField
    private String diagCode;

    /**
     * 诊断名称
     */
    @TableField
    private String diagName;

    /**
     * 是否有效:是否
     */
    @TableField
    private String validFlag;
}
