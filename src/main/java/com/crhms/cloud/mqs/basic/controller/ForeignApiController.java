package com.crhms.cloud.mqs.basic.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.mqs.basic.common.ClinicParams;
import com.crhms.cloud.mqs.basic.common.MqsResult;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.service.BaseAuditFactory;
import com.crhms.cloud.mqs.basic.service.BaseAuditService;
import com.crhms.cloud.mqs.basic.service.BaseMedicalService;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.basic.vo.ComprePopupQueryVO;
import com.crhms.cloud.mqs.basic.vo.HisBaseAuditDto;
import com.crhms.cloud.mqs.basic.vo.BaseAuditResonDto;
import com.crhms.cloud.mqs.basic.vo.BaseAuditResultDto;
import com.crhms.cloud.mqs.sys.domain.SysReasonType;
import com.crhms.cloud.mqs.sys.domain.SysRules;
import com.crhms.cloud.mqs.sys.domain.SysSceneFunction;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.service.SysReasonTypeService;
import com.crhms.cloud.mqs.sys.service.SysSceneService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * HIS审核 - 相关接口
 */
@RestController
@RequestMapping(value = "/api/mqs/ForeginHospitalAuditService")
@Slf4j
public class ForeignApiController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private BaseMedicalService baseMedicalService;

    @Autowired
    private SysReasonTypeService sysReasonTypeService;

    /**
     * 处方审核（11001）
     *
     * @param hisBaseAuditDto
     * @return
     */
    @PostMapping("/ClaimAuditHospital")
    public MqsResult ClaimAuditHospital(@RequestBody HisBaseAuditDto hisBaseAuditDto) {
        log.info("======审核START======" + new java.sql.Timestamp(System.currentTimeMillis()).toString() + "======");
        log.debug("===审核原始报文===" +  JSONUtil.toJsonStr(hisBaseAuditDto));
        String auditScenario = hisBaseAuditDto.getAudit_scenario();
        String hospitalId = hisBaseAuditDto.getHospital_id();
        //生成批次号
        String batch = MqsUtils.getUUBatch();
        hisBaseAuditDto.setBatch_no(batch);

        //基础数据校验
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            return MqsResult.ERROR("1", "不支持的审核场景");
        }
        if (Objects.isNull(hisBaseAuditDto.getData())) {
            return MqsResult.ERROR("请传入审核内容！");
        }
        //获取场景下规则
        List<Map<String, Object>> auditRules = genAuditRules(auditScenario, hospitalId);
        hisBaseAuditDto.setAuditRules(auditRules);
        //获取场景综合配置
        SysSceneFunction sysSceneFunction = SpringUtil.getBean(SysSceneService.class).getSceneFunctionCache(auditScenario, hospitalId);
        //获取审核实现工厂
        BaseAuditService auditService = BaseAuditFactory.createAuditService(auditScenario);

        log.info("======审核批次======" + batch + "======");
        //审核结果
        BaseAuditResultDto baseAuditResultDto = auditService.sentToAudit(batch, auditScenario, hisBaseAuditDto, sysSceneFunction);
        log.debug("===审核原始返回===" +  JSONUtil.toJsonStr(baseAuditResultDto));
        log.info("======审核END======" + new java.sql.Timestamp(System.currentTimeMillis()).toString() + "======");

        return MqsResult.OK(baseAuditResultDto);
    }


    /**
     * 处方审核 - 诊间弹窗 - 查询单据信息（11001 - 1）
     *
     * @return
     */
    @GetMapping("/audit/queryMedicalCase")
    public MqsResult auditQueryMedicalCase(@RequestParam(value = "admission_no") String admissionNo,
                                           @RequestParam(value = "batch") String batch,
                                           @RequestParam(value = "audit_scenario") String auditScenario,
                                           @RequestParam(value = "hospital_id") String hospitalId) {
        return MqsResult.OK(baseMedicalService.auditQueryMedicalCase(admissionNo, batch, auditScenario, hospitalId));
    }

    /**
     * 处方审核 - 诊间弹窗 - 查询单据违规明细（11001 - 2）
     *
     * @return
     */
    @GetMapping("/audit/queryMedicalDetailCase")
    public MqsResult queryMedicalDetailCase(@RequestParam(value = "admission_no") String admissionNo,
                                            @RequestParam(value = "batch") String batch,
                                            @RequestParam(value = "audit_scenario") String auditScenario,
                                            @RequestParam(value = "hospital_id") String hospitalId) {
        return MqsResult.OK(baseMedicalService.queryMedicalDetailCase(admissionNo, batch, auditScenario, hospitalId));
    }

    /**
     * 处方审核 - 诊间弹窗 - 查询单据明细违规详情（11001 - 3）
     *
     * @return
     */
    @GetMapping("/audit/queryMedicalAuditRules")
    public MqsResult queryMedicalAuditRules(@RequestParam(value = "admission_no") String admissionNo,
                                            @RequestParam(value = "batch") String batch,
                                            @RequestParam(value = "detail_no") String detailNo,
                                            @RequestParam(value = "audit_scenario",required = false) String auditScenario,
                                            @RequestParam(value = "hospital_id") String hospitalId,
                                            @RequestParam(value = "ruleOrigin",required = false) String ruleOrigin) {
        return MqsResult.OK(baseMedicalService.queryMedicalAuditRules(detailNo, admissionNo, batch, auditScenario,ruleOrigin, hospitalId));
    }


    /**
     * 处方审核 - 诊间弹窗 - 强制保存接口（11001 - 4）
     *
     * @param baseAuditResonDto
     * @return
     */
    @PostMapping("/audit/ClaimAuditSaveHospital")
    public MqsResult saveClaimAuditHospital(@RequestBody BaseAuditResonDto baseAuditResonDto) {
        //基础数据校验
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), baseAuditResonDto.getAudit_scenario())) {
            return MqsResult.ERROR("不支持的审核场景！");
        }

        //保存当前批次数据
        //1 删除业务正式表 审核结果数据 2 复制业务 和审核 到正式表  3 反馈原因表 转到历史表，覆盖到正式表
        MqsResult mqsResult = baseMedicalService.saveClaimAuditHospital(baseAuditResonDto);

        return mqsResult;

    }

    /**
     * 处方审核 - 诊间弹窗 - 返回修改记录（11001 - 5）
     * @param admissionNo
     * @param batch
     * @param auditScenario
     * @param personnelTypeId
     * @param hospitalId
     * @return
     */
    @GetMapping("/audit/ClaimAuditBackHospital")
    public MqsResult ClaimAuditBackHospital(@RequestParam(value = "admission_no") String admissionNo,
                                            @RequestParam(value = "totalCosts") BigDecimal totalCosts,
                                            @RequestParam(value = "batch") String batch,
                                            @RequestParam(value = "personnelTypeId") String personnelTypeId,
                                            @RequestParam(value = "audit_scenario") String auditScenario,
                                            @RequestParam(value = "hospital_id") String hospitalId) {
        baseMedicalService.claimAuditBackHospital(admissionNo, totalCosts, batch, personnelTypeId,auditScenario, hospitalId);
        return MqsResult.OK("success");

    }

    /**
     * 处方审核 - 诊间弹窗 - 自费协议导出（11002 - 6）
     *
     * @param map
     * @return
     */
    @PostMapping("/audit/exportSelfAgreement")
    public ResponseEntity exportSelfAgreement(@RequestParam Map<String,String> map, HttpServletResponse response) {

        String no = map.get("no");
        String hospital_id = map.get("hospital_id");
        String audit_scenario =  map.get("audit_scenario");
        String batch = map.get("batch");
        String detailNos = map.get("detail_nos");
        List<String> detail_nos = Arrays.asList(detailNos.split(","));

        //导出自费协议
        baseMedicalService.exportSelfAgreement(response, no, detail_nos, audit_scenario, batch, hospital_id,null);
        return ResponseEntity.ok("success");

    }

    /**
     * 处方审核 - 诊间弹窗 - 查询字典（11001 - 7）(目前版本只有反馈类型值集)
     * @param auditScenario
     * @param hospitalId
     * @return
     */
    @GetMapping("/audit/getMqsDict")
    public MqsResult ClaimAuditBackHospital(@RequestParam(value = "audit_scenario") String auditScenario,
                                            @RequestParam(value = "dict_code") String dictCode,
                                            @RequestParam(value = "hospital_id") String hospitalId) {
        List<SysReasonType> list = sysReasonTypeService.list(new QueryWrapper<SysReasonType>()
                .select(SysReasonType.FIELD_REASON_TYPE, SysReasonType.FIELD_REASON_DES)
                .eq(SysReasonType.FIELD_SCENE_CODE, auditScenario).eq(SysReasonType.FIELD_HOSPITAL_ID, hospitalId).eq(SysReasonType.FIELD_ENABLE, "1"));
        return MqsResult.OK(list);
    }


    /**
     * 处方保存(11002)
     *
     * @param hisBaseAuditDto
     * @return
     */
    @PostMapping("/ClaimAuditSave4Hospital")
    public MqsResult ClaimAuditSave4Hospital(@RequestBody HisBaseAuditDto hisBaseAuditDto) {
        log.info("======审核START======" + new java.sql.Timestamp(System.currentTimeMillis()).toString() + "======");
        log.debug("===审核原始报文===" +  JSONUtil.toJsonStr(hisBaseAuditDto));
        String auditScenario = hisBaseAuditDto.getAudit_scenario();
        String hospitalId = hisBaseAuditDto.getHospital_id();
        //生成批次号
        String batch = MqsUtils.getUUBatch();
        hisBaseAuditDto.setBatch_no(batch);

        //基础数据校验
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            return MqsResult.ERROR("1", "不支持的审核场景");
        }
        if (Objects.isNull(hisBaseAuditDto.getData())) {
            return MqsResult.ERROR("请传入审核内容！");
        }
        //获取场景下规则
        List<Map<String, Object>> auditRules = genAuditRules(auditScenario, hospitalId);

        hisBaseAuditDto.setAuditRules(auditRules);
        //获取场景综合配置
        SysSceneFunction sysSceneFunction = SpringUtil.getBean(SysSceneService.class).getSceneFunctionCache(auditScenario, hospitalId);
        //获取审核实现工厂
        BaseAuditService auditService = BaseAuditFactory.createAuditService(auditScenario);

        //调用审核 -》 保存入表
        BaseAuditResultDto baseAuditResultDto = auditService.saveToRecord(batch, auditScenario, hisBaseAuditDto, sysSceneFunction);
        log.debug("===审核原始返回===" +  JSONUtil.toJsonStr(baseAuditResultDto));
        log.info("======审核END======" + new java.sql.Timestamp(System.currentTimeMillis()).toString() + "======");
        return MqsResult.OK(baseAuditResultDto);
    }

    /**
     * 查询强制保存状态 (11003)
     */
    @GetMapping("/querySaveStatus")
    public MqsResult GetHospitalSaveStatus(@RequestParam(value = "admission_no") String admission_no,
                                           @RequestParam(value = "batch") String batch,
                                           @RequestParam(value = "audit_scenario") String audit_scenario,
                                           @RequestParam(value = "hospital_id") String hospital_id,
                                           @RequestParam(value = "bmi_code", required = false) String bmi_code) {

        List<BaseMedicalCase> caseList = baseMedicalService.getHospitalSaveStatus(admission_no, batch, audit_scenario, hospital_id);
        if (CollectionUtil.isNotEmpty(caseList)) {
            return MqsResult.OK("1", "单据已保存成功");
        } else {
            return MqsResult.OK("0", "单据未保存");
        }
    }

    /**
     * 删除已保存的单据(11004)
     */
    @GetMapping("/DelClaimAuditHospital")
    public MqsResult DelClaimAuditHospital(@RequestParam(value = "admission_no") String admissionNo,
                                           @RequestParam(value = "no") String no,
                                           @RequestParam(value = "patient_id", required = false) String patientId,
                                           @RequestParam(value = "audit_scenario", required = false) String auditScenario,
                                           @RequestParam(value = "hospital_id") String hospitalId,
                                           @RequestParam(value = "bmi_code", required = false) String bmiCode) {
        try {
            baseMedicalService.delClaimAuditHospital(patientId,null, no, admissionNo, auditScenario, hospitalId);
        } catch (Exception e) {
            e.printStackTrace();
            return MqsResult.ERROR("单据删除异常", e.getMessage());
        }
        return MqsResult.OK("1", "单据已删除成功");

    }

    /**
     * 删除已保存的单据明细(11005)
     */
    @GetMapping("/DelClaimAuditHospital/fee")
    public MqsResult DelClaimAuditDetailHospital(@RequestParam(value = "admission_no") String admissionNo,
                                                 @RequestParam(value = "no") String no,
                                                 @RequestParam(value = "patient_id", required = false) String patientId,
                                                 @RequestParam(value = "detail_no") String detailNo,
                                                 @RequestParam(value = "audit_scenario", required = false) String auditScenario,
                                                 @RequestParam(value = "hospital_id") String hospitalId,
                                                 @RequestParam(value = "bmi_code", required = false) String bmiCode) {
        try {
            List<String> detailNos = StrUtil.isEmpty(detailNo) ? new ArrayList<>() : Arrays.asList(detailNo.split(","));
            baseMedicalService.delClaimAuditHospital(patientId, detailNos, no, admissionNo, auditScenario, hospitalId);
        } catch (Exception e) {
            e.printStackTrace();
            return MqsResult.ERROR("单据删除异常", e.getMessage());
        }

        return MqsResult.OK("1", "单据明细已删除");

    }

    /**
     * 更新住院状态
     */
    @GetMapping("/UpdateClaimDischargeFlag")
    public MqsResult UpdateClaimDischargeFlag(@RequestParam(value = "admission_no") String admissionNo,
                                              @RequestParam(value = "audit_scenario") String auditScenario,
                                              @RequestParam(value = "no") String no,
                                              @RequestParam(value = "is_discharge") String isDischarge,
                                              @RequestParam(value = "hospital_id") String hospitalId,
                                              @RequestParam(value = "bmi_code", required = false) String bmiCode) {
        try {
            baseMedicalService.updateClaimDischargeFlag(no,admissionNo,isDischarge,auditScenario,hospitalId);
        } catch (Exception e) {
            MqsResult.ERROR("0", "单据住院状态更新异常!");
        }
        return MqsResult.OK("1", no + "单据住院状态更新成功！");

    }
    /**
     * 更新计费标记(11006)
     */
    @GetMapping("/UpdateClaimChargingFlag")
    public MqsResult UpdateClaimChargingFlag(@RequestParam(value = "admission_no") String admissionNo,
                                             @RequestParam(value = "audit_scenario") String auditScenario,
                                             @RequestParam(value = "no") String no,
                                             @RequestParam(value = "detail_no") String detailNo,
                                             @RequestParam(value = "charging_flag") String chargingFlag,
                                             @RequestParam(value = "hospital_id") String hospitalId,
                                             @RequestParam(value = "bmi_code", required = false) String bmiCode) {
        try {
            baseMedicalService.updateClaimChargingFlag(detailNo,no,admissionNo,chargingFlag,auditScenario,hospitalId);
        } catch (Exception e) {
            MqsResult.ERROR("0", "更新异常!");
        }
        return MqsResult.OK("1", "计费标记更新成功！");

    }


    /**
     * 明细自费状态查询(11007)
     */
    @GetMapping("/GetHospitalSelfExpense")
    public MqsResult GetHospitalSelfExpense(@RequestParam(value = "admission_no") String admissionNo,
                                            @RequestParam(value = "audit_scenario") String auditScenario,
                                            @RequestParam(value = "no", required = false) String no,
                                            @RequestParam(value = "detail_no", required = false) String detailNo,
                                            @RequestParam(value = "hospital_id") String hospitalId,
                                            @RequestParam(value = "bmi_code", required = false) String bmiCode) {

        return MqsResult.OK(baseMedicalService.getHospitalSelfExpense(detailNo,no,admissionNo,auditScenario,hospitalId), "自费状态结果！");

    }


    /**
     * 获取单据违规信息(11008)
     */
    @GetMapping("/GetClaimRes")
    public MqsResult GetClaimRes(@RequestParam(value = "admission_no") String admissionNo,
                                 @RequestParam(value = "audit_scenario") String auditScenario,
                                 @RequestParam(value = "no") String no,
                                 @RequestParam(value = "hospital_id") String hospitalId,
                                 @RequestParam(value = "bmi_code", required = false) String bmiCode) {

        return MqsResult.OK(baseMedicalService.getClaimRes(no, admissionNo, auditScenario, hospitalId));

    }

    /**
     * 单据重审接口 (11009)
     */
    @GetMapping("/ReClaimAuditHospital")
    public MqsResult ReClaimAuditHospital(@RequestParam(value = "admission_no") String admissionNo,
                                          @RequestParam(value = "audit_scenario") String auditScenario,
                                          @RequestParam(value = "no",required = false) String no,
                                          @RequestParam(value = "batch",required = false) String oldBatch,
                                          @RequestParam(value = "saved", defaultValue = "0") String saved,
                                          @RequestParam(value = "hospital_id") String hospitalId,
                                          @RequestParam(value = "bmi_code", required = false) String bmiCode) {

        //基础数据校验
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            return MqsResult.ERROR("1", "不支持的审核场景");
        }

        //获取场景下规则
        List<Map<String, Object>> auditRules = genAuditRules(auditScenario, hospitalId);
        //获取场景综合配置
        SysSceneFunction sysSceneFunction = SpringUtil.getBean(SysSceneService.class).getSceneFunctionCache(auditScenario, hospitalId);

        //生成批次号
        String batch = MqsUtils.getUUBatch();
        HisBaseAuditDto baseAuditDto = new HisBaseAuditDto();
        baseAuditDto.setAdmission_no(admissionNo);
        baseAuditDto.setAudit_scenario(auditScenario);
        baseAuditDto.setIs_pretrial("0");
        baseAuditDto.setHospital_id(hospitalId);
        baseAuditDto.setBatch_no(batch);
        baseAuditDto.setAuditRules(auditRules);

        //获取审核实现工厂
        BaseAuditService auditService = BaseAuditFactory.createAuditService(auditScenario);

        log.info("重审批次======" + batch + "======");
        //审核结果
        BaseAuditResultDto baseAuditResultDto = auditService.reSentToAudit(batch, auditScenario, baseAuditDto, no, saved, sysSceneFunction, oldBatch);

        log.info("重审 END======" + new java.sql.Timestamp(System.currentTimeMillis()).toString() + "======");

        return MqsResult.OK(baseAuditResultDto);

    }

    List<Map<String, Object>> genAuditRules(String auditScenario,String hospitalId){
        List<Map<String, Object>> auditRules = new ArrayList<>();
        Map<Object, Object> sceneRuleMap = redisTemplate.opsForHash().entries(StrUtil.format(GloablData.GLOBAL_SYS_SCENERULE_KEY, hospitalId));
        Map<Object, Object> ruleMap = redisTemplate.opsForHash().entries(StrUtil.format(GloablData.GLOBAL_SYS_RULES_KEY, hospitalId));

        List<String> ruleCodes = (List<String>) sceneRuleMap.get(auditScenario);
        if (CollectionUtil.isEmpty(ruleCodes)) {
            throw new BaseException("当前审核场景未维护审核规则！");
        }
        for (String ruleCode : ruleCodes) {
            Map<String, Object> map = new HashMap<>();
            Map<String, String> rule = (Map<String, String>) ruleMap.get(ruleCode);
            if(Objects.isNull(rule)){
                continue;
            }
            map.put("ruleCode", rule.get(SysRules.FIELD_RULE_CODE));
            if(!Objects.isNull(rule.get(SysRules.FIELD_RULE_TYPE))) {
                map.put("ruleType", Integer.valueOf(rule.get(SysRules.FIELD_RULE_TYPE)));
            }
            if(!Objects.isNull(rule.get(SysRules.FIELD_RULE_LEVEL))){
                map.put("resultType",Integer.valueOf(rule.get(SysRules.FIELD_RULE_LEVEL)));
            }
            map.put("resultTypeName",rule.get(SysRules.FIELD_RULE_LEVEL_NAME));
            auditRules.add(map);
        }
        return auditRules;
    }


    /**
     * 综合弹窗------相关记录导出
     *
     * @return
     */
    @PostMapping("/audit/exportRelatedRecords")
    public void exportRelatedRecords(@RequestBody ClinicParams clinicParams, HttpServletResponse response){
        ComprePopupQueryVO comprePopupQueryVO = new ComprePopupQueryVO();
        comprePopupQueryVO.setAdmissionNo(clinicParams.getAdmission_no());
        comprePopupQueryVO.setHospitalId(clinicParams.getHospitalId());
        comprePopupQueryVO.setAuditScenario(clinicParams.getAuditScenario());
        comprePopupQueryVO.setBatchNo(clinicParams.getBatch());
        comprePopupQueryVO.setIsIncrement(clinicParams.getIsIncrement());
        comprePopupQueryVO.setClinicalStatus(clinicParams.getClinicalStatus());
        comprePopupQueryVO.setViolationFlag("1");
        comprePopupQueryVO.setAuditSource(clinicParams.getAuditSource());
        comprePopupQueryVO.setSelfExpense(clinicParams.getSelfExpense());
        comprePopupQueryVO.setItemName(clinicParams.getItemName());
        comprePopupQueryVO.setItemTypeCode(clinicParams.getItemTypeCode());
        comprePopupQueryVO.setCategoryName(clinicParams.getCategoryName());
        comprePopupQueryVO.setOutpatientMedication(clinicParams.getOutpatientMedication());
        comprePopupQueryVO.setRuleTypes(clinicParams.getRuleTypes());
        baseMedicalService.exportRelatedRecords(comprePopupQueryVO, clinicParams.getDetail_no(), clinicParams.getAdmission_no(), clinicParams.getBatch(),clinicParams.getAuditScenario(),clinicParams.getRuleOrigin(),clinicParams.getHospitalId(),response);
    }
}
