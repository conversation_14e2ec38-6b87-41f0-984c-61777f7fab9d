package com.crhms.cloud.mqs.mqs_op.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_op.domain.OpPctDetailHis;
import com.crhms.cloud.mqs.mqs_op.mapper.OpPctDetailHisMapper;
import org.springframework.stereotype.Service;

/**
 * 门诊审核-处方费用明细(OpPctDetailHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-19 14:40:10
 */
@Service("opPctDetailHisService")
public class OpPctDetailHisService extends ServiceImpl< OpPctDetailHisMapper, OpPctDetailHis> {

}
