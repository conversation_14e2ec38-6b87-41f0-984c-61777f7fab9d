package com.crhms.cloud.mqs.sys.enums;

import java.util.List;
import java.util.Map;

public class SceneTemplate {
    public static final Integer opSize = 2;
    public static final String[] sceneNameArray = {"门诊挂号审核","门诊处方审核","门诊缴费审核","住院登记审核","医嘱审核","每日记账审核","转科室审核","预出院审核","出院审核","住院结算审核"};
    public static final String[] sceneCodeArray = {"opRg","opPct","opPt","hpRg","hpDo","hpBk","hpTf","hpPred","hpOut","hpSettle"};
    public static final String[] enableArray = {"1","1","1","1","1","1","1","1","1","1"};
    public static final String[] miReviewApiArray = {"2","0","1","0","0","2","1","1","2","2"};
    public static final String[] manualReviewArray = {"2","0","1","2","0","0","0","1","1","1"};
    public static final String[] showDimensionsArray = {"0","0","0","0","0","0","0","0","0","0"};
    public static final String[] showFormatArray = {"0","0","0","0","0","0","0","0","0","0"};
    public static final String[] functionCodeArray = {"0","0","0","0","0","2","1","1","1","1"};
    public static final String[] isSelfArray = {"0","1","1","0","1","","1","1","1","1"};
    public static final String[] selfPaidConditionArray = {"","0,1","0,1","","0,1","","0,1","0,1","0,1","0,1"};
    public static final String[] isSelfAgreementArray = {"0","1","1","0","1","","1","1","1","1"};
    public static final String[] isIgnoreArray = {"1","1","1","1","1","1","1","1","1","1"};
    public static final String opName = "门诊审核";
    public static final String hpName = "住院审核";
}
