package com.crhms.cloud.mqs.sys.disruptor;

import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.YieldingWaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.lmax.disruptor.util.DaemonThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName DisruptorQueue
 * @Description 内存队列
 * <AUTHOR>
 * @Date 2024-11-01
 **/
@Slf4j
@Component
@Order(-1)
public class DisruptorQueue implements ApplicationRunner {

    @Autowired
    private SysLogHandler sysLogHandler;

    @Autowired
    private MrCaseHandler mrCaseHandler;

    // 系统日志队列
    public static Disruptor<SysLogDTO> SYS_LOGS_QUEUE = null;

    // 人工质控队列
    public static Disruptor<MrCaseHandlerDTO> MR_CASE_QUEUE = null;

    @Override
    public void run(ApplicationArguments args) throws Exception {

        // 初始化日志内存队列
        SYS_LOGS_QUEUE = new Disruptor<SysLogDTO>(() -> new SysLogDTO(), 2048*1024 , DaemonThreadFactory.INSTANCE, ProducerType.MULTI, new BlockingWaitStrategy());
        // 绑定消费者
        SYS_LOGS_QUEUE.handleEventsWith(sysLogHandler);

        SYS_LOGS_QUEUE.setDefaultExceptionHandler(new SysLogsHandlerException());

        SYS_LOGS_QUEUE.start();

        // 初始化日志内存队列
        MR_CASE_QUEUE = new Disruptor<MrCaseHandlerDTO>(() -> new MrCaseHandlerDTO(), 2048*1024 , DaemonThreadFactory.INSTANCE, ProducerType.MULTI, new YieldingWaitStrategy());
        // 绑定消费者
        MR_CASE_QUEUE.handleEventsWith(mrCaseHandler);

        MR_CASE_QUEUE.setDefaultExceptionHandler(new MrCaseHandlerException());

        MR_CASE_QUEUE.start();

    }
}
