package com.crhms.cloud.mqs.sys.config;

import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.vo.*;
import com.googlecode.jmapper.JMapper;

public class JMapperSingleton {

    private static JMapper<BaseMedicalParam, HisMedicalParamsVo> hisToMqsInstance;

    private static JMapper<EngBillVo, BaseMedicalParam> mqsToEngInstance;

    private static JMapper<EngFeeVo, BaseMedicalDetail> mqsToEngFeeInstance;

    private static JMapper<EngMedicalCaseVo, BaseMedicalCase> mqsToEngMedCaseInstance;

    private static JMapper<EngOperationDetailVo, HisOperationDetailVo> mqsOperationToEngInstance;


    private JMapperSingleton() {
        // 私有构造函数
    }

    public static synchronized JMapper<BaseMedicalParam, HisMedicalParamsVo> getHisToMqsInstance() {
        if (hisToMqsInstance == null) {
            hisToMqsInstance = new JMapper<>(BaseMedicalParam.class, HisMedicalParamsVo.class);
        }
        return hisToMqsInstance;
    }
    public static synchronized JMapper<EngBillVo, BaseMedicalParam> getMqsToEngInstance() {
        if (mqsToEngInstance == null) {
            mqsToEngInstance = new JMapper<>(EngBillVo.class, BaseMedicalParam.class);
        }
        return mqsToEngInstance;
    }
    public static synchronized JMapper<EngFeeVo, BaseMedicalDetail> getMqsToEngFeeInstance() {
        if (mqsToEngFeeInstance == null) {
            mqsToEngFeeInstance = new JMapper<>(EngFeeVo.class, BaseMedicalDetail.class);
        }
        return mqsToEngFeeInstance;
    }
    public static synchronized JMapper<EngMedicalCaseVo, BaseMedicalCase> getMqsToEngMedCaseInstance() {
        if (mqsToEngMedCaseInstance == null) {
            mqsToEngMedCaseInstance = new JMapper<>(EngMedicalCaseVo.class, BaseMedicalCase.class);
        }
        return mqsToEngMedCaseInstance;
    }

    public static synchronized JMapper<EngOperationDetailVo, HisOperationDetailVo> getMqsOperationToEngInstance() {
        if (mqsOperationToEngInstance == null) {
            mqsOperationToEngInstance = new JMapper<>(EngOperationDetailVo.class, HisOperationDetailVo.class);
        }
        return mqsOperationToEngInstance;
    }
}

