package com.crhms.cloud.mqs.mqs_bmi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFb;
import com.crhms.cloud.mqs.mqs_bmi.vo.BmiQueryVo;
import com.crhms.cloud.mqs.mqs_bmi.vo.FbMedicalCaseDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 医保管理-申诉审核表(BmiFb)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-10 10:40:08
 */

@Mapper
public interface BmiFbMapper extends BaseMapper<BmiFb> {

    /**
     * 查询指定行数据
     * @return 对象列表
     */
    List<BmiFb> queryByPage(@Param("queryVo") BmiQueryVo bmiQueryVo, Page myPage);

    /**
     * 查询指定行数据 - 权限
     * @return 对象列表
     */
    List<BmiFb> queryByPageWithAuth(@Param("queryVo") BmiQueryVo bmiQueryVo, Page myPage);
    /**
     * 查询单据详情数据
     *
     */
    FbMedicalCaseDto queryHeadWithNo( @Param("no") String no,@Param("hospitalId") String hospitalId);

    List<Map<String, String>> selectDeptList(@Param("dept") String dept, @Param("hospitalId") String hospitalId);

    List<Map<String, String>> selectDoctorList(@Param("doctor") String doctor, @Param("hospitalId") String hospitalId);

    List<String> selectClaimTypeList(@Param("claimType") String claimType, @Param("hospitalId") String hospitalId);
}

