package com.crhms.cloud.mqs.basic.service;

import cn.hutool.extra.spring.SpringUtil;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.vo.BaseMedicalParam;
import com.crhms.cloud.mqs.basic.vo.HisBaseAuditDto;
import com.crhms.cloud.mqs.basic.vo.EngAuditResultVo;
import com.crhms.cloud.mqs.mqs_op.service.OpPctService;
import com.crhms.cloud.mqs.mqs_op.service.OpPtService;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 审核工厂类
 *
 * <AUTHOR>
 * @since 2022-12-16 10:19:56
 */
@Slf4j
@Service
public class BaseAuditFactory {


    public static BaseAuditService createAuditService(String auditScenario) {

        switch (AuditScenarioEnum.valueOf(auditScenario)) {
            case opPt:
                return SpringUtil.getBean(OpPtService.class);
            case opPct:
                return SpringUtil.getBean(OpPctService.class);
            default:
                return new BaseAuditService() {

                    @Override
                    public void beforeAudit(String batch, HisBaseAuditDto hisBaseAuditDto) {

                    }

                    @Override
                    public void afterAudit(String batch, EngAuditResultVo result, HisBaseAuditDto hisBaseAuditDto, List<BaseMedicalParam> data) {

                    }
                };
        }
    }

}
