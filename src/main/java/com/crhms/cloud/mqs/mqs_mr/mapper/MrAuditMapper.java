package com.crhms.cloud.mqs.mqs_mr.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.mqs_mr.domain.MrAudit;
import com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface MrAuditMapper extends BaseMapper<MrAudit> {

    @Select({
            "<script>",
            "SELECT ",
                    "mrAudit.`no`,",
                    "mrAudit.detail_no,",
                    "detail.self_expense ",
                    "FROM mqs_mr_audit mrAudit ",
                    "JOIN mqs_mr_base_detail detail on detail.detail_no = mrAudit.detail_no ",
                    "WHERE mrAudit.no in ",
                    "<foreach collection=\"nos\" separator=\",\" open=\"(\" close=\")\" item=\"no\">\n",
                    "     #{no}\n",
                    "</foreach>",
                    "and mrAudit.hospital_id = #{hospitalId}",
            "</script>"
    })
    List<MrMedicalDetail> queryMrAudit(List<String> nos, String hospitalId);
}
