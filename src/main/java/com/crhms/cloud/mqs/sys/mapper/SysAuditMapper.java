package com.crhms.cloud.mqs.sys.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 审核结果查询数据访问层
 *
 * <AUTHOR>
 * @since 2022-12-19 09:40:47
 */

@Mapper
public interface SysAuditMapper {
    /**
     * 查询违反规则和数量
     *
     * @param hospitalId
     * @return
     */
    List<Map<String, Object>> queryTotalAudit(@Param("table") String table, @Param("no") String no, @Param("hospitalId") String hospitalId);

    /**
     * 查询违反规则和数量
     *
     * @param no
     * @param hospitalId
     * @return
     */
    List<Map<String, Object>> queryTotalAuditHis(@Param("table") String table, @Param("no") String no, @Param("batchNo") String batchNo, @Param("hospitalId") String hospitalId);

    Map<String, Object> queryAuditResult(@Param("table") String tableName, @Param("no") String no, @Param("ruleCode") String ruleCode, @Param("detailNo") String detailNo, @Param("hospitalId") String hospitalId);

    Map<String, Object> queryAuditResultHis(@Param("table") String tableName, @Param("no") String no, @Param("ruleCode") String ruleCode, @Param("detailNo") String detailNo, @Param("batchNo") String batchNo, @Param("hospitalId") String hospitalId);

    List<Map<String, Object>> queryCaseAuditResult(@Param("table") String tableName, @Param("no") String no, @Param("hospitalId") String hospitalId);
}

