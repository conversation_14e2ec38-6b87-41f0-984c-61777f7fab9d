package com.crhms.cloud.mqs.mqs_kbm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.mqs_kbm.domain.ItemDrugTcms;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 知识库管理-医保项目目录-国家医保药品分类与代码(中药饮片)mapper接口
 *
 * @author: wangxingang
 * @date: 2023/02/03
 */

@Mapper
public interface ItemDrugTcmsMapper extends BaseMapper<ItemDrugTcms> {
    /**
     * 查询功效分类下拉列表
     */
    List<String> selectEfficacyClassifyList();
}
