package com.crhms.cloud.mqs.mqs_pm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.excel.dto.ExcelExport;
import com.crhms.cloud.core.excel.dto.SheetData;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdConsume;
import com.crhms.cloud.mqs.mqs_pm.mapper.PmStdConsumeMapper;
import com.crhms.cloud.mqs.mqs_pm.vo.ConsumeImportVo;
import com.crhms.cloud.mqs.sys.utils.DictUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 物价管理-市级标准物价-耗材
 *
 * @author: wangxingang
 * @date: 2023/01/31
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class PmStdConsumeService extends ServiceImpl<PmStdConsumeMapper, PmStdConsume> {

    /**
     * 新增或修改
     *
     * @param consume 耗材信息
     * <AUTHOR>
     * @date 2023.01.31
     */
    public void submitConsume(PmStdConsume consume) {
        saveOrUpdate(consume);
    }


    /**
     * 分页查询
     *
     * @param productCode 产品编码
     * @param productName 产品名称
     * @param startPeriod 时间周期-开始时间
     * @param endPeriod   时间周期-结束时间
     * @param pageInfo    分页信息
     * <AUTHOR>
     * @date 2023.01.31
     */
    public List<PmStdConsume> queryPage(String productCode, String productName, String startPeriod, String endPeriod, Page<PmStdConsume> pageInfo) {
        Page<PmStdConsume> result = page(pageInfo, new LambdaQueryWrapper<PmStdConsume>()
                .like(StrUtil.isNotEmpty(productCode), PmStdConsume::getProductCode, productCode)
                .like(StrUtil.isNotEmpty(productName), PmStdConsume::getProductName, productName)
                .between(StrUtil.isNotEmpty(startPeriod) && StrUtil.isNotEmpty(endPeriod), PmStdConsume::getEffectTime, startPeriod, endPeriod)
        );

        return result.getRecords();
    }




    /**
     * 根据id批量删除
     *
     * @param ids id集合
     * <AUTHOR>
     * @date 2023.01.30
     */
    public void deleteByIds(List<Long> ids) {
        removeByIds(ids);
    }


    /**
     * 导出Excel
     *
     * @param response    响应
     * @param productCode 产品编码
     * @param productName 产品名称
     * @param startPeriod 时间周期-开始时间
     * @param endPeriod   时间周期-结束时间
     * @param isModel     是否导出模板?true是false否(默认是否)
     * <AUTHOR>
     * @date 2023.01.30
     */
    public void exportExcel(HttpServletResponse response, String productCode, String productName, String startPeriod, String endPeriod, boolean isModel) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        StringBuilder fileName = new StringBuilder();
        fileName.append("物价管理-市级标准物价-耗材");



        // 导出模板时isModel是ture
        if (isModel) {
            // 表头
            heads.add(CollUtil.list(true, "PRODUCT_CODE", "PRODUCT_NAME", "SPECIFICATION", "MODEL", "REGISTER_NUMBER", "PROD_ENTERPRISE", "PACK_SPECIFICATION", "PRICE"));
            heads.add(CollUtil.list(true, "产品编码", "产品名称", "规格", "型号", "注册证号", "生产企业", "包装规格", "价格(元)"));
        }
        else {
            // 表头
            heads.add(CollUtil.list(true, "EFFECT_TIME", "PRODUCT_CODE", "PRODUCT_NAME", "SPECIFICATION", "MODEL", "REGISTER_NUMBER", "PROD_ENTERPRISE", "PACK_SPECIFICATION", "PRICE"));
            heads.add(CollUtil.list(true, "生效时间", "产品编码", "产品名称", "规格", "型号", "注册证号", "生产企业", "包装规格", "价格(元)"));

            // 查询勾选的数据
            List<PmStdConsume> consumes = baseMapper.selectList(new LambdaQueryWrapper<PmStdConsume>()
                    .like(StrUtil.isNotEmpty(productCode), PmStdConsume::getProductCode, productCode)
                    .like(StrUtil.isNotEmpty(productName), PmStdConsume::getProductName, productName)
                    .between(StrUtil.isNotEmpty(startPeriod) && StrUtil.isNotEmpty(endPeriod), PmStdConsume::getEffectTime, startPeriod, endPeriod));
            // 字典值转换
            DictUtils.translateDict(consumes);
            // 构建数据
            for (PmStdConsume consume : consumes) {
                datas.add(CollUtil.list(true, DateUtil.format(consume.getEffectTime(), "yyyy.MM.dd"), consume.getProductCode(), consume.getProductName(), consume.getSpecification(), consume.getModel(), consume.getRegisterNumber(), consume.getProdEnterprise(), consume.getPackSpecification(), consume.getPrice()));
            }
        }

        MqsUtils.buildExportFileNameSuffix(fileName, startPeriod, endPeriod);

        // 导出
        List<ExcelExport> exports = new ArrayList<>();
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }

    /**
     * 导入Excel, 返回重复的数据
     *
     */
    public List<PmStdConsume> importExcel(ConsumeImportVo importVo) {
        MultipartFile file = importVo.getFile();
        Date effectTime = importVo.getEffectTime();
        List<String> productCodeList = importVo.getProductCodeList();
        // 获取sheet页
        SheetData sheetData = BigExcelUtil.parseExcel(file).getSheets().get(0);

        // 校验Excel
        checkExcel(sheetData);

        // 解析Excel数据
        List<PmStdConsume> importData = parseExcel(sheetData, effectTime);

        List<PmStdConsume> duplicateData = new ArrayList<>();
        if (productCodeList == null) {
            // 第一次调用, 返回重复数据

            // 获取已存在的数据
            List<PmStdConsume> existData = baseMapper.selectList(null);

            // 根据项目编码和时间周期判断已存在
            for (PmStdConsume importDatum : importData) {
                for (PmStdConsume existDatum : existData) {
                    if (DateUtil.isSameDay(importDatum.getEffectTime(), existDatum.getEffectTime())
                            && StrUtil.equals(importDatum.getProductCode(), existDatum.getProductCode())) {
                        duplicateData.add(importDatum);
                    }
                }
            }

        } else {
            if (CollUtil.isNotEmpty(productCodeList)) {
                baseMapper.delete(new LambdaQueryWrapper<PmStdConsume>().eq(PmStdConsume::getEffectTime, effectTime).in(PmStdConsume::getProductCode, productCodeList));
            }
            for (PmStdConsume importDatum : importData) {
                importDatum.setId(IdWorker.getId());
            }
            saveBatch(importData);
        }


        // 返回重复数据
        return duplicateData;

    }


    /**
     * 解析Excel数据
     *
     * @param sheetData  sheet页
     * @param effectTime 生效时间
     * @return 去除表头后的Excel数据(List < 实体类 > 形式)
     */
    private List<PmStdConsume> parseExcel(SheetData sheetData, Date effectTime) {

        // 获取数据
        List<PmStdConsume> datas = new ArrayList<>();
        List<Object> line;
        for (int i = 0; i < sheetData.getLines().size(); i++) {
            if (i <= 1) {
                continue;
            }
            line = sheetData.getLines().get(i);

            String price = BigExcelUtil.getItemStr(line, 7) == null ? "0" : BigExcelUtil.getItemStr(line, 7);

            datas.add(PmStdConsume.builder()
                    .effectTime(effectTime)
                    .productCode(BigExcelUtil.getItemStr(line, 0))
                    .productName(BigExcelUtil.getItemStr(line, 1))
                    .specification(BigExcelUtil.getItemStr(line, 2))
                    .model(BigExcelUtil.getItemStr(line, 3))
                    .registerNumber(BigExcelUtil.getItemStr(line, 4))
                    .prodEnterprise(BigExcelUtil.getItemStr(line, 5))
                    .packSpecification(BigExcelUtil.getItemStr(line, 6))
                    .price(NumberUtil.toBigDecimal(price))
                    .build());
        }

        return datas;
    }


    /**
     * 校验Excel表头及数据是否重复
     *
     * @param sheetData sheet页
     */
    private void checkExcel(SheetData sheetData) {

        // 校验表头
        String checkHead = BigExcelUtil.checkHead(sheetData, CollUtil.list(true, "PRODUCT_CODE", "PRODUCT_NAME", "SPECIFICATION", "MODEL", "REGISTER_NUMBER", "PROD_ENTERPRISE", "PACK_SPECIFICATION", "PRICE"));
        if (StrUtil.isNotEmpty(checkHead)) {
            throw new BaseException(checkHead);
        }

        // 校验项目编码是否重复
        /*Set<String> projectCodeSet = new HashSet<>();
        List<Object> line;
        String projectCode;
        List<String> errorList = new ArrayList<>();
        for (int i = 0; i < sheetData.getLines().size(); i++) {
            if (i <= 1) {
                continue;
            }
            line = sheetData.getLines().get(i);

            projectCode = BigExcelUtil.getItemStr(line, 0);
            if (CollUtil.contains(projectCodeSet, projectCode)) {
                // 提示重复
                errorList.add(StrUtil.format("第{}行数据中, 项目编码'{}'已存在", i, projectCode));
            } else {
                projectCodeSet.add(projectCode);
            }
        }

        if (CollUtil.isNotEmpty(errorList)) {
            throw new BaseException(errorList.toString());
        }*/
    }
}
