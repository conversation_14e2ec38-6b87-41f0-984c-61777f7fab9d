package com.crhms.cloud.mqs.basic.vo;

import com.googlecode.jmapper.annotations.JMap;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EngOperationDetailVo implements Serializable {

    @JMap("no")
    private String admission_no;
    //手术记录明细id主键
    @JMap("detail_id")
    private String id;
    // 医院的手术记录序号	String(256)	当前明细所属的手术记录序号 	是
    @JMap("operation_id")
    private String operation_record_no;
    // 指本条记录的是第几个手术	String(256)	指本条记录的是第几个手术 	是
    @JMap("operation_no")
    private String operation_no;
    // 手术编码	String(256)	当地医疗结构获取的ICD9字典数据（医保版）	是
    @JMap("operation_code")
    private String operation_code;
    // 手术名称	String(256)		是
    @JMap("operation_name")
    private String operation_name;
    // 参考手术等级字典表	String(1)	见数据字典5.8手术等级（是否设立字典表要看后期是否要加规则）明细和病案首页的校验规则	是
    @JMap("opera_level_code")
    private String operation_level;
    // 参考手术切口分类	String(16)	见数据字典5.9手术切口分类（是否设立字典表要看后期是否要加规则）明细和病案首页的校验规则	是
    @JMap("inc_type_code")
    private String operation_incision_class;
    // 参考手术切口愈合等级字典	String(16)	见数据字典5.10手术切口愈合等级（是否设立字典表要看后期是否要加规则）明细和病案首页的校验规则	是
    @JMap("inc_heal_level_code")
    private String operation_heal_class;
    // 主次标识	String(1)	0-非主手术；1-主手术	是	    一次住院（一份病案），有且只有一个主手术
    @JMap("main_operation")
    private String is_major_iden;
    // 是否医源性手术	String(1)	0：否；1：是	否
    @JMap("is_iatrogenic")
    private String is_iatrogenic;
    // 手术医师编码	String(256)	医师库编码	是		C	Y	确定
    @JMap("opera_doctor_code")
    private String operation_doctor_code;
    // 手术医师姓名	String(256)	医师库名称	是	门诊单据用实际结算日期；住院单据，如已经出院结算，则用结算日期，如尚未出院结算，用当天费用增量或医嘱增量日期,格式：1950-01-01	C	Y	确定
    @JMap("opera_doctor_name")
    private String operation_doctor_name;
    // 手术医师I助编码	String(256)	医师库编码	否	医保分配的医疗机构ID	C	Y	确定
    @JMap("assistant1_code")
    private String first_operdoctorcode;
    // 手术医师I助姓名	String(256)	医师库名称	否	见数据字典5.4定点机构等级	C	Y	字典对照
    @JMap("assistant1_name")
    private String first_operdoctorname;
    // 手术医师II助编码	String(256)	医师库编码	否	见数据字典5.5定点机构类型	C	Y	新增
    @JMap("assistant2_code")
    private String second_operdoctorcode;
    // 手术医师II助姓名	String(256)	医师库名称	否	见数据字典5.1就医方式	C	Y	扩增字典标
    @JMap("assistant2_name")
    private String second_operdoctorname;
    // 麻醉师编码	String(256)	医师库编码	否		C	Y	确定
    @JMap("anst_docotr_code")
    private String anesthesiologist_code;
    // 麻醉师姓名	String(256)	医师库名称	否	诊断编码	C	入院诊断、出院诊断、出院其他诊断字符串至少一项不为空，为确保数据准确率，建议全部写入数据	必填性联合判断
    @JMap("anst_docotr_name")
    private String anesthesiologist_name;
    // 手术执行日期时间	String	yyyy-MM-dd HH:mm:ss	是	诊断编码，如果是门诊可以和入院诊断一样	C	入院诊断、出院诊断、出院其他诊断字符串至少一项不为空，为确保数据准确率，建议全部写入数据	必填性联合判断
    @JMap("opera_start_date")
    private String operation_date;
    // 手术执行结束时间	String	yyyy-MM-dd HH:mm:ss	是	2340|2342|1212|	C	入院诊断、出院诊断、出院其他诊断字符串至少一项不为空，为确保数据准确率，建议全部写入数据，最多16个其他诊断	必填性联合判断；数据转换分发
    @JMap("opera_end_date")
    private String operation_finish_date;
    // 麻醉方式	String(16)	值域参考数据字典（是否设立字典表要看后期是否要加规则）明细和病案首页的校验规则	否	格式：1950-01-01	C	Y	字段类型转换
    @JMap("anst_type_code")
    private String anaesthesia_type;
    // 是否有手术并发症	String(1)	0否，1是	是	门诊单据用实际结算日期；住院单据，如已经出院结算，则用结算日期，如尚未出院结算，用当天费用增量或医嘱增量日期格式：1950-01-01	C	Y	字段类型转换
    @JMap("is_complication")
    private String is_complication;
    // 手术并发症编码	String(256)	ICD-10编码	否	1:男 0:女: -1:未知	C	Y	字典对照
    @JMap("complication_code")
    private String complication_code;
    // 手术并发症名称	String(256)	ICD-10名称	否	格式：1950-01-01	C	Y	确定
    @JMap("complication_name")
    private String complication_name;
    // 手术记录	clob	记录过程	否	1 是0 否	C	Y	必填性冲突
    @JMap("opera_process_record")
    private String operation_record;
    // 手术记录医师编码	String(256)	医师库编码	否	1 是0 否	C	Y	必填性冲突
    @JMap("record_doctor_code")
    private String record_doctor_code;
    // 手术记录医师名字	String(256)	医师库名称	否	不能为空，默认为0	C	Y	确定
    @JMap("record_doctor_name")
    private String record_doctor_name;
    // 手术类别	String(1)	1.择期手术　2.急诊手术	否	字典表。默认值-99999	C	Y	待定
    @JMap("operation_type_code")
    private String operation_type;

}
