package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.TcmSyndrome;
import com.crhms.cloud.mqs.mqs_kbm.mapper.TcmSyndromeMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 知识库管理-中医诊断-中医证候分类与代码 service
 *
 * @author: wangxingang
 * @date: 2023.02.03
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class TcmSyndromeService extends ServiceImpl<TcmSyndromeMapper, TcmSyndrome> {
    /**
     * 查询中医证候分类与代码
     *
     * @param pageInfo     分页信息
     * @param syndromeCode 证候名编码
     * @param syndromeName 证候名
     */
    public List<TcmSyndrome> queryList(Page<TcmSyndrome> pageInfo, String syndromeCode, String syndromeName) {
        return baseMapper.selectPage(pageInfo, new LambdaQueryWrapper<TcmSyndrome>()
                .like(StrUtil.isNotEmpty(syndromeCode), TcmSyndrome::getSyndromeCode, syndromeCode)
                .like(StrUtil.isNotEmpty(syndromeName), TcmSyndrome::getSyndromeName, syndromeName)
        ).getRecords();
    }
}
