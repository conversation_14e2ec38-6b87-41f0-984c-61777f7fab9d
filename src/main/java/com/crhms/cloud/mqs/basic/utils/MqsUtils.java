package com.crhms.cloud.mqs.basic.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDiag;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalOrders;
import com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper;
import com.crhms.cloud.mqs.basic.vo.*;
import com.crhms.cloud.mqs.sys.config.JMapperSingleton;
import com.crhms.cloud.mqs.sys.domain.SysSceneFunction;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.service.SysSceneService;
import com.googlecode.jmapper.JMapper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
public class MqsUtils {

    /**
     * 获取批次号
     *
     * @return
     */
    public static String getUUBatch() {
        return UUID.randomUUID().toString().replace("-", "").toLowerCase();
    }

    /**
     * 判断枚举值是否存在于指定枚举数组中
     *
     * @param enums 枚举数组
     * @param value 枚举值
     * @return boolean
     */
    public static boolean isScenarioExist(Enum[] enums, String value) {
        if (value == null) {
            return false;
        }
        for (Enum e : enums) {
            if (value.equals(e.name())) {
                return true;
            }
        }
        return false;
    }

    @SuppressWarnings("unchecked")
    /**
     * 将String类型的xml转换成对象
     */
    public static Object convertXmlStrToObject(Class clazz, String xmlStr) {
        Object xmlObject = null;
        try {
            JAXBContext context = JAXBContext.newInstance(clazz);
            // 进行将Xml转成对象的核心接口
            Unmarshaller unmarshaller = context.createUnmarshaller();
            StringReader sr = new StringReader(xmlStr);
            xmlObject = unmarshaller.unmarshal(sr);
        } catch (JAXBException e) {
            e.printStackTrace();
        }
        return xmlObject;
    }

    /**
     * BigDecimal 转换
     * @param value
     * @return
     */
    public static BigDecimal getBigDecimal( Object value ) {
        BigDecimal val = null;
        if( value != null ) {
            if( value instanceof BigDecimal ) {
                val = (BigDecimal) value;
            } else if( value instanceof String ) {
                val = new BigDecimal( (String) value );
            } else if( value instanceof BigInteger) {
                val = new BigDecimal( (BigInteger) value );
            } else if( value instanceof Number ) {
                val = new BigDecimal( ((Number)value).doubleValue() );
            } else {
                throw new ClassCastException("Not possible to coerce ["+value+"] from class "+value.getClass()+" into a BigDecimal.");
            }
        }else {
            val = BigDecimal.ZERO;
        }
        return val;
    }

    /**
     * 出生日期 - 截止时间 计算年龄
     * @param birth
     * @return
     */
    public static int getAge(Date birth, Date stopDate) {
        Calendar cal = Calendar.getInstance();
        if(!Objects.isNull(stopDate)){
            cal.setTime(stopDate);
        }
        int thisYear = cal.get(Calendar.YEAR);
        int thisMonth = cal.get(Calendar.MONTH);
        int dayOfMonth = cal.get(Calendar.DAY_OF_MONTH);

        cal.setTime(birth);
        int birthYear = cal.get(Calendar.YEAR);
        int birthMonth = cal.get(Calendar.MONTH);
        int birthdayOfMonth = cal.get(Calendar.DAY_OF_MONTH);

        int age = thisYear - birthYear;

        // 未足月
        if (thisMonth <= birthMonth) {
            // 当月
            if (thisMonth == birthMonth) {
                // 未足日
                if (dayOfMonth < birthdayOfMonth) {
                    age--;
                }
            } else {
                age--;
            }
        }
        return age;
    }
    /**
     * 出生日期计算年龄
     * @param birth
     * @return
     */
    public static int getAge(Date birth) {
        Calendar cal = Calendar.getInstance();
        int thisYear = cal.get(Calendar.YEAR);
        int thisMonth = cal.get(Calendar.MONTH);
        int dayOfMonth = cal.get(Calendar.DAY_OF_MONTH);

        cal.setTime(birth);
        int birthYear = cal.get(Calendar.YEAR);
        int birthMonth = cal.get(Calendar.MONTH);
        int birthdayOfMonth = cal.get(Calendar.DAY_OF_MONTH);

        int age = thisYear - birthYear;

        // 未足月
        if (thisMonth <= birthMonth) {
            // 当月
            if (thisMonth == birthMonth) {
                // 未足日
                if (dayOfMonth < birthdayOfMonth) {
                    age--;
                }
            } else {
                age--;
            }
        }
        return age;
    }

    /**
     * 系统单据 -》 引擎数据
     * @param baseMedicalParams 转换前数据
     * @return 引擎调用数据
     */
    public static EngAuditVo transformationToEng(String isPretrial , List<BaseMedicalParam> baseMedicalParams, String auditScenario,SysSceneFunction sysSceneFunction) {
        log.info("======系统单据 -》 引擎数据 START======" + new java.sql.Timestamp(System.currentTimeMillis()).toString());

        JMapper<EngBillVo, BaseMedicalParam> mqsToEngInstance = JMapperSingleton.getMqsToEngInstance();

        JMapper<EngOperationDetailVo, HisOperationDetailVo> mqsOperationToEngInstance = JMapperSingleton.getMqsOperationToEngInstance();

        EngAuditVo engAuditVo = new EngAuditVo();
        List<EngBillVo> billVos = new ArrayList<>();
        for (BaseMedicalParam baseMedicalParam : baseMedicalParams) {
            EngBillVo engBillVo = mqsToEngInstance.getDestination(baseMedicalParam);
            engBillVo.getMedical_case().setRecord_type(auditScenario);
            engBillVo.getMedical_case().setPatient_birthday(StrUtil.sub(engBillVo.getMedical_case().getPatient_birthday(), 0, 10));

            //手术记录结构处理
            if(CollectionUtil.isNotEmpty(baseMedicalParam.getOperation_list())){
                List<EngOperationDetailVo> engOperationDetailVos = new ArrayList<>();
                for (HisOperationMainVo hisOperationMainVo : baseMedicalParam.getOperation_list()) {
                    if(CollectionUtil.isNotEmpty(hisOperationMainVo.getOperation_detail_list())){
                        for (HisOperationDetailVo hisOperationDetailVo : hisOperationMainVo.getOperation_detail_list()) {
                            BeanUtil.copyProperties(hisOperationMainVo,hisOperationDetailVo);
                            EngOperationDetailVo destination = mqsOperationToEngInstance.getDestination(hisOperationDetailVo);
                            engOperationDetailVos.add(destination);
                        }
                    }
                }
                engBillVo.setOperation_list(engOperationDetailVos);
            }

            billVos.add(engBillVo);
        }
        engAuditVo.setBill_list(billVos);
        engAuditVo.setIs_pretrial(Integer.valueOf(isPretrial));
        //医保审核配置
        String miReviewApi = sysSceneFunction.getMiReviewApi();
        String trigScen = null;
        if("0".equals(miReviewApi) || "1".equals(miReviewApi)){
            trigScen = ("0".equals(miReviewApi) ? "3101" : "1".equals(miReviewApi) ? "3102" : null)
                    + "|" + sysSceneFunction.getMiReviewApiSelect();
        }
        engAuditVo.setTrig_scen(trigScen);

        log.info("======系统单据 -》 引擎数据 END======" + new java.sql.Timestamp(System.currentTimeMillis()).toString());
        return engAuditVo;
    }

    /**
     *  审核数据 -》 系统数据
     *  （内涵数据冲销逻辑）
     * @param hisBaseAuditDto 转换前审核数据
     * @return 入表数据
     */
    public static List<BaseMedicalParam> transformationToCase(HisBaseAuditDto hisBaseAuditDto) {
        log.info("======审核数据 -》 系统数据 Start======" + new java.sql.Timestamp(System.currentTimeMillis()).toString());
        BaseMedicalMapper baseMedicalMapper = SpringUtil.getBean(BaseMedicalMapper.class);
        JMapper<BaseMedicalParam, HisMedicalParamsVo> hisToMqsInstance = JMapperSingleton.getHisToMqsInstance();
        List<BaseMedicalParam> baseMedicalParams = new ArrayList<>();
        String hospital_id = hisBaseAuditDto.getHospital_id();
        String batch = hisBaseAuditDto.getBatch_no();
        String tableName = AuditScenarioEnum.valueOf(hisBaseAuditDto.getAudit_scenario()).getTableName();


        for (HisMedicalParamsVo datum : hisBaseAuditDto.getData()) {
            BaseMedicalParam medicalParams = hisToMqsInstance.getDestination(datum);
            baseMedicalParams.add(medicalParams);
            BaseMedicalCase medicalCase = medicalParams.getMedical_case();
            String admission_no = ObjectUtil.isNotEmpty(hisBaseAuditDto.getAdmission_no())? hisBaseAuditDto.getAdmission_no():medicalCase.getAdmissionNo();
            medicalCase.setAdmissionNo(admission_no);
            medicalCase.setBatchNo(batch);
            medicalCase.setLastUpdatedBy(1L);
            medicalCase.setCreatedBy(1L);
            medicalCase.setAuditTime(new Date());
            medicalCase.setMrStatus("0");
            medicalCase.setIsIncrement(datum.getIs_increment().toString());
            medicalCase.setHospitalId(hospital_id);
            //未传当前科室取入院科室/转入科室赋值
            if(StringUtils.isEmpty(medicalCase.getPresentDeptCode())){
                //组装当前科室 (如果有则用转入科室，没有则入院科室)
                if(StringUtils.isNotBlank(medicalCase.getTfToDeptName()) && StringUtils.isNotBlank(medicalCase.getTfToDeptCode())){
                    medicalCase.setPresentDeptName(medicalCase.getTfToDeptName());
                    medicalCase.setPresentDeptCode(medicalCase.getTfToDeptCode());
                } else {
                    medicalCase.setPresentDeptName(medicalCase.getDeptName());
                    medicalCase.setPresentDeptCode(medicalCase.getDeptCode());
                }
            }
            //其它诊断处理
            List<BaseMedicalDiag> diagnosisList = CollectionUtil.isEmpty(medicalParams.getDiagnosis_list())? new ArrayList<>():medicalParams.getDiagnosis_list();

            //继2.2.0 前旧版本兼容 主单上诊断合并至诊断表
            if(StrUtil.isNotEmpty(medicalCase.getInDiagnosisCode())){
                Optional<BaseMedicalDiag> first = diagnosisList.stream().filter(x -> "1".equals(x.getMainFlag()) && "1".equals(x.getDiagInoutType()) && medicalCase.getInDiagnosisCode().equals(x.getDiagnosisCode())).findFirst();
                if(!first.isPresent()){
                    diagnosisList.add(BaseMedicalDiag.builder().diagnosisNo(medicalCase.getNo()+ "-1-" + medicalCase.getInDiagnosisCode())
                            .diagnosisCode(medicalCase.getInDiagnosisCode())
                            .diagnosisName(medicalCase.getInDiagnosisName())
                            .mainFlag("1").diagInoutType("1").build());
                }
            } else {
                //补充入院主诊断名称以及编码
                Optional<BaseMedicalDiag> first = diagnosisList.stream().filter(x -> "1".equals(x.getMainFlag()) && "1".equals(x.getDiagInoutType()) ).findFirst();
                if(first.isPresent()){
                    medicalCase.setInDiagnosisCode(first.get().getDiagnosisCode());
                    medicalCase.setInDiagnosisName(first.get().getDiagnosisName());
                }
            }
            if(StrUtil.isNotEmpty(medicalCase.getOutDiagnosisCode())){
                Optional<BaseMedicalDiag> first = diagnosisList.stream().filter(x -> "1".equals(x.getMainFlag()) && "2".equals(x.getDiagInoutType()) && medicalCase.getOutDiagnosisCode().equals(x.getDiagnosisCode())).findFirst();
                if(!first.isPresent()){
                    diagnosisList.add(BaseMedicalDiag.builder().diagnosisNo(medicalCase.getNo()+ "-2-" + medicalCase.getOutDiagnosisCode())
                            .diagnosisCode(medicalCase.getOutDiagnosisCode())
                            .diagnosisName(medicalCase.getOutDiagnosisName())
                            .mainFlag("1").diagInoutType("2").build());
                }
            } else {
                //补充出院主诊断名称以及编码
                Optional<BaseMedicalDiag> first = diagnosisList.stream().filter(x -> "1".equals(x.getMainFlag()) && "2".equals(x.getDiagInoutType())).findFirst();
                if(first.isPresent()){
                    medicalCase.setOutDiagnosisCode(first.get().getDiagnosisCode());
                    medicalCase.setOutDiagnosisName(first.get().getDiagnosisName());
                }
            }

            if(CollectionUtil.isNotEmpty(diagnosisList)){
                //诊断默认信息补充
                for (BaseMedicalDiag baseMedicalDiag : diagnosisList) {
                    baseMedicalDiag.setNo(medicalCase.getNo());
                    baseMedicalDiag.setAdmissionNo(admission_no);
                    baseMedicalDiag.setPatientId(medicalCase.getPatientId());
                    baseMedicalDiag.setPatientName(medicalCase.getPatientName());
                    baseMedicalDiag.setMainFlag(StrUtil.isEmpty(baseMedicalDiag.getMainFlag())? "0":baseMedicalDiag.getMainFlag());
                    baseMedicalDiag.setDiagInoutType(StrUtil.isEmpty(baseMedicalDiag.getDiagInoutType())? "2":baseMedicalDiag.getDiagInoutType());
                    if(StrUtil.isEmpty(baseMedicalDiag.getDiagnosisNo())){
                        baseMedicalDiag.setDiagnosisNo(medicalCase.getNo() + "-" + baseMedicalDiag.getDiagInoutType() + "-" + baseMedicalDiag.getDiagnosisCode());
                    }
                    baseMedicalDiag.setDiagnosisOrder(StrUtil.isEmpty(baseMedicalDiag.getDiagnosisOrder())? "":baseMedicalDiag.getDiagnosisOrder());
                    baseMedicalDiag.setHospitalId(hospital_id);
                    baseMedicalDiag.setBatchNo(batch);
                    baseMedicalDiag.setLastUpdatedBy(1L);
                    baseMedicalDiag.setCreatedBy(1L);
                }
                //按照 出院次诊断排序前16位次诊断用于快速展示
                List<BaseMedicalDiag> collect = diagnosisList.stream().filter(x -> "0".equals(x.getMainFlag()) && "2".equals(x.getDiagInoutType())).sorted(Comparator.comparing(BaseMedicalDiag::getDiagnosisOrder).reversed()).collect(Collectors.toList());
                for (int i = 0; i < collect.size(); i++) {
                    if(i >= 16){
                        break;
                    }
                    BaseMedicalDiag x = collect.get(i);
                    switch (i){
                        case 0:
                            medicalCase.setDiagnosisCode1(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName1(x.getDiagnosisName());
                            break;
                        case 1:
                            medicalCase.setDiagnosisCode2(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName2(x.getDiagnosisName());
                            break;
                        case 2:
                            medicalCase.setDiagnosisCode3(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName3(x.getDiagnosisName());
                            break;
                        case 3:
                            medicalCase.setDiagnosisCode4(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName4(x.getDiagnosisName());
                            break;
                        case 4:
                            medicalCase.setDiagnosisCode5(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName5(x.getDiagnosisName());
                            break;
                        case 5:
                            medicalCase.setDiagnosisCode6(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName6(x.getDiagnosisName());
                            break;
                        case 6:
                            medicalCase.setDiagnosisCode7(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName7(x.getDiagnosisName());
                            break;
                        case 7:
                            medicalCase.setDiagnosisCode8(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName8(x.getDiagnosisName());
                            break;
                        case 8:
                            medicalCase.setDiagnosisCode9(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName9(x.getDiagnosisName());
                            break;
                        case 9:
                            medicalCase.setDiagnosisCode10(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName10(x.getDiagnosisName());
                            break;
                        case 10:
                            medicalCase.setDiagnosisCode11(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName11(x.getDiagnosisName());
                            break;
                        case 11:
                            medicalCase.setDiagnosisCode12(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName12(x.getDiagnosisName());
                            break;
                        case 12:
                            medicalCase.setDiagnosisCode13(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName13(x.getDiagnosisName());
                            break;
                        case 13:
                            medicalCase.setDiagnosisCode14(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName14(x.getDiagnosisName());
                            break;
                        case 14:
                            medicalCase.setDiagnosisCode15(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName15(x.getDiagnosisName());
                            break;
                        case 15:
                            medicalCase.setDiagnosisCode16(x.getDiagnosisCode());
                            medicalCase.setDiagnosisName16(x.getDiagnosisName());
                            break;
                    }
                }

                medicalParams.setDiagnosis_list(diagnosisList);
            }
            if(CollectionUtil.isNotEmpty(medicalParams.getFee_list())){
                // 费用集
                List<BaseMedicalDetail> feelist = medicalParams.getFee_list().stream().filter(x -> StrUtil.isEmpty(x.getRefundNo())).collect(Collectors.toList());
                // 退费集
                List<BaseMedicalDetail> refundFeelist =  medicalParams.getFee_list().stream().filter(x -> StrUtil.isNotEmpty(x.getRefundNo())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(refundFeelist)){
                    // 退费历史费用集
                    Set<String> uncoverDet = new HashSet<>();
                    refundFeelist.stream().forEach(x -> {
                        Optional<BaseMedicalDetail> first = feelist.stream().filter(y -> x.getRefundNo().equals(y.getDetailNo())).findFirst();
                        if(!first.isPresent()){
                            uncoverDet.add(x.getRefundNo());
                        }
                    });
                    //补全退费集费用数据
                    if(CollUtil.isNotEmpty(uncoverDet)){
                        List<BaseMedicalDetail> oldDetailList = baseMedicalMapper.queryAuditDetailByDetailNo(tableName, new ArrayList<>(uncoverDet));
                        //补全的被冲明细
                        // OperationType:1 属于当次明细标记
                        // IsCurrent:1 属于增量数据
                        // LastUpdatedBy:99区分接口传入还是逻辑补全
                        // BatchNo 赋值当次待确认批次
                        // reverseFlag:0 非删除数据
                        oldDetailList.stream().forEach(x -> {x.setOperationType("1"); x.setIsCurrent("1");x.setLastUpdatedBy(99L);x.setBatchNo(batch);x.setReverseFlag("0");});
                        feelist.addAll(oldDetailList);
                    }
                    //冲销处理，取冲销行的绝对值 扣减被冲销行的费用/数量 （冲销金额为 0 保留行）
                    processRefunds(feelist, refundFeelist);
                }

                for (BaseMedicalDetail baseMedicalDetail : feelist) {
                    baseMedicalDetail.setBatchNo(batch);
                    baseMedicalDetail.setHospitalId(hospital_id);
                    baseMedicalDetail.setAdmissionNo(admission_no);
                    baseMedicalDetail.setNo(medicalCase.getNo());
                    //自费明细 医保内金额固定为0
                    baseMedicalDetail.setBmiConveredAmount("0".equals(baseMedicalDetail.getSelfExpense())
                            ? baseMedicalDetail.getBmiConveredAmount(): BigDecimal.ZERO);
                    //增加丙类项目逻辑 若审核时自费为1 则为丙类项目
                    baseMedicalDetail.setSelfOriginal(baseMedicalDetail.getSelfExpense());
                    baseMedicalDetail.setLastUpdatedBy(ObjectUtil.isEmpty(medicalCase.getLastUpdatedBy())? 1L:medicalCase.getLastUpdatedBy());
                    baseMedicalDetail.setCreatedBy(1L);
                }
                medicalParams.setFee_list(feelist);
            }
            if(CollectionUtil.isNotEmpty(medicalParams.getOrders_list())){
                //医嘱明细处理
                for (BaseMedicalOrders baseMedicalOrders : medicalParams.getOrders_list()) {
                    baseMedicalOrders.setBatchNo(batch);
                    baseMedicalOrders.setHospitalId(hospital_id);
                    baseMedicalOrders.setAdmissionNo(admission_no);
                    baseMedicalOrders.setNo(medicalCase.getNo());
                    baseMedicalOrders.setLastUpdatedBy(1L);
                    baseMedicalOrders.setCreatedBy(1L);
                }
            }
            if(CollectionUtil.isNotEmpty(medicalParams.getOperation_list())){
                List<HisOperationMainVo> operation_list = medicalParams.getOperation_list();
                for (HisOperationMainVo hisOperationMainVo : operation_list) {
                    hisOperationMainVo.setAdmission_no(admission_no);
                    hisOperationMainVo.setPatient_id(medicalCase.getPatientId());
                    hisOperationMainVo.setPatient_name(medicalCase.getPatientName());
                    hisOperationMainVo.setNo(medicalCase.getNo());
                    hisOperationMainVo.setHospital_id(hospital_id);
                    hisOperationMainVo.setBatch_no(batch);
                    if(CollectionUtil.isNotEmpty(hisOperationMainVo.getOperation_detail_list())){
                        for (HisOperationDetailVo hisOperationDetailVo : hisOperationMainVo.getOperation_detail_list()) {
                            hisOperationDetailVo.setAdmission_no(admission_no);
                            hisOperationDetailVo.setNo(medicalCase.getNo());
                            hisOperationDetailVo.setHospital_id(hospital_id);
                            hisOperationDetailVo.setBatch_no(batch);
                        }
                    }
                }
            }
        }
        log.info("======审核数据 -》 系统数据 END======" + new java.sql.Timestamp(System.currentTimeMillis()).toString());

        return baseMedicalParams;
    }


    /**
     * 传入年月日, 构造"年-月-日"
     * @param filterYear  年 2023
     * @param filterMonth 月 03
     * @param filterDay   日 14
     * @return 2023-03-14
     */
    public static Map<String, String> buildDate(String filterYear, String filterMonth, String filterDay) {
        if (StrUtil.isEmpty(filterYear)) {
            throw new BaseException("参数错误, 至少应该传年份");
        }

        Date startTime;
        Date endTime;

        // 年月日都传了
        if (StrUtil.isNotEmpty(filterDay)) {
            startTime = DateUtil.beginOfDay(DateUtil.parseDate(filterYear + "-" + filterMonth + "-" + filterDay));
            endTime = DateUtil.endOfDay(DateUtil.parseDate(filterYear + "-" + filterMonth + "-" + filterDay));
        } else {
            // 只传了年月
            if (StrUtil.isNotEmpty(filterMonth)) {
                startTime = DateUtil.beginOfMonth(DateUtil.parseDate(filterYear + "-" + filterMonth + "-" + 1));
                endTime = DateUtil.endOfMonth(DateUtil.parseDate(filterYear + "-" + filterMonth + "-" + 1));
            } else {
                // 只传了年
                startTime = DateUtil.beginOfYear(DateUtil.parseDate(filterYear + "-" + 1 + "-" + 1));
                endTime = DateUtil.endOfYear(DateUtil.parseDate(filterYear + "-" + 1 + "-" + 1));
            }
        }

        return new HashMap<String, String>(){{
            put("startTime", startTime.toString());
            put("endTime", endTime.toString());
        }};
    }

    public static Map<String, String> buildDate(String filterYear) {
        return buildDate(filterYear, null, null);
    }

    /**
     * 返回12个月份
     * @param filterYear    年
     * @param filterMonth   月
     * @param filterDay     日
     * @return 12个月份集合
     */
    public static List<String> buildMonths(String filterYear, String filterMonth, String filterDay) {
        // 根据传入的时间参数解析出sql查询需要用到的时间参数
        DateTime endTime;
        DateTime startTime;

        if (StrUtil.isNotEmpty(filterYear)) {
            // 传了年份
            if (StrUtil.isNotEmpty(filterMonth)) {
                // 还传了月份
                endTime = DateUtil.parseDate(filterYear + "-" + filterMonth + "-" + 1);
                startTime = DateUtil.offset(endTime, DateField.MONTH, -11);
            } else {
                // 只传了年份
                endTime = DateUtil.parseDate(filterYear + "-" + 12 + "-" + 1);
                startTime = DateUtil.parseDate(filterYear + "-" + 1 + "-" + 1);
            }
        } else {
            // 没传年份
            throw new BaseException("必须至少传年月");
        }

        List<DateTime> monthList = DateUtil.rangeToList(startTime, endTime, DateField.MONTH);
        List<String> months = monthList.stream().map(month -> DateUtil.format(month, "yyyy-MM")).collect(Collectors.toList());

        return months;
    }

    /**
     * 文件名后边拼上查询起止时间和此刻时间(年月日时分秒)
     * @param fileName  文件名
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    public static void buildExportFileNameSuffix(StringBuilder fileName, String startTime,String endTime) {
        if (StrUtil.isNotEmpty(startTime) && StrUtil.isNotEmpty(endTime)) {
            // 文件名拼接上导出的起止时间
            String admissionDateFrom = StrUtil.removeAll(startTime, "-");
            String admissionDateTo = StrUtil.removeAll(endTime, "-");
            fileName.append("(").append(admissionDateFrom).append("_").append(admissionDateTo).append(")");
        }
        // 文件名拼上导出时间(年月日时分秒)
        fileName.append("-").append(DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss"));
    }

    /**
     * 冲销方法
     * @param feelist 被冲销费用行
     * @param reFeelist 冲销费用
     */
    private static void processRefunds(List<BaseMedicalDetail> feelist, List<BaseMedicalDetail> reFeelist) {
         if(CollUtil.isEmpty(feelist) || CollUtil.isEmpty(reFeelist)){
             return;
         }

        // 将费用详情编号与费用金额关联
        Map<String, BaseMedicalDetail> feeMap = new HashMap<>();
        for (BaseMedicalDetail fee : feelist) {
            feeMap.put(fee.getDetailNo(), fee);
        }

        // 处理冲销逻辑
        for (BaseMedicalDetail refund : reFeelist) {
            if (feeMap.containsKey(refund.getRefundNo())) {
                BaseMedicalDetail fee = feeMap.get(refund.getRefundNo());
                //校验冲销原规格是否一致，近支持单一规格冲销
                if(StrUtil.compare(fee.getSpecification(),refund.getSpecification(),false) != 0) {
                    throw new BaseException(StrUtil.format("冲销失败，原{}项目【{}】规格与冲销【{}】规格不一致！", fee.getItemName(), fee.getSpecification(), refund.getSpecification()));
                }
                fee.setNumbers(fee.getNumbers() != null ? fee.getNumbers().subtract(refund.getNumbers() != null ? refund.getNumbers().abs() : BigDecimal.ZERO).max(BigDecimal.ZERO) : BigDecimal.ZERO);
                fee.setCosts(fee.getCosts() != null ? fee.getCosts().subtract(refund.getCosts() != null ? refund.getCosts().abs() : BigDecimal.ZERO).max(BigDecimal.ZERO) : BigDecimal.ZERO);
                fee.setBmiConveredAmount(fee.getBmiConveredAmount() != null ? fee.getBmiConveredAmount().subtract(refund.getBmiConveredAmount() != null ? refund.getBmiConveredAmount().abs() : BigDecimal.ZERO).max(BigDecimal.ZERO) : BigDecimal.ZERO);
                fee.setBmiOverallAmount(fee.getBmiOverallAmount() != null ? fee.getBmiOverallAmount().subtract(refund.getBmiOverallAmount() != null ? refund.getBmiOverallAmount().abs() : BigDecimal.ZERO).max(BigDecimal.ZERO) : BigDecimal.ZERO);
                fee.setCostCosts(fee.getCostCosts() != null ? fee.getCostCosts().subtract(refund.getCostCosts() != null ? refund.getCostCosts().abs() : BigDecimal.ZERO).max(BigDecimal.ZERO) : BigDecimal.ZERO);
                fee.setCostNumber(fee.getCostNumber() != null ? fee.getCostNumber().subtract(refund.getCostNumber() != null ? refund.getCostNumber().abs() : BigDecimal.ZERO).max(BigDecimal.ZERO) : BigDecimal.ZERO);
                //若冲销后数量结果为0 则该行需要被清华粗
                if(BigDecimal.ZERO.compareTo(fee.getNumbers()) == 0 ) {
                    fee.setReverseFlag("1");
                }
            }
        }
    }
}
