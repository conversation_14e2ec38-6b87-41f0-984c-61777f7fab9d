package com.crhms.cloud.mqs.mqs_pm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.excel.dto.ExcelExport;
import com.crhms.cloud.core.excel.dto.SheetData;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdServiceItem;
import com.crhms.cloud.mqs.mqs_pm.mapper.PmStdServiceItemMapper;
import com.crhms.cloud.mqs.mqs_pm.vo.ServiceItemImportVo;
import com.crhms.cloud.mqs.sys.utils.DictUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 物价管理-市级标准物价-服务项目service
 *
 * @author: 王新刚
 * @date: 2023/01/30
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class PmStdServiceItemService extends ServiceImpl<PmStdServiceItemMapper, PmStdServiceItem> {


    /**
     * 分页查询
     *
     * @param financialClassificationList 财务分类
     * @param projectName                 项目名称
     * @param startPeriod                 时间周期-开始时间
     * @param endPeriod                   时间周期-结束时间
     * @param pageInfo                    分页信息
     * <AUTHOR>
     * @date 2023.01.30
     */
    public List<PmStdServiceItem> queryPage(List<String> financialClassificationList, String projectName, String startPeriod, String endPeriod, Page<PmStdServiceItem> pageInfo) {
        Page<PmStdServiceItem> result = page(pageInfo, new LambdaQueryWrapper<PmStdServiceItem>()
                .in(CollUtil.isNotEmpty(financialClassificationList), PmStdServiceItem::getFinancialClassification, financialClassificationList)
                .like(StrUtil.isNotEmpty(projectName), PmStdServiceItem::getProjectName, projectName)
                .between(StrUtil.isNotEmpty(startPeriod) && StrUtil.isNotEmpty(endPeriod), PmStdServiceItem::getEffectTime, startPeriod, endPeriod)
        );
        DictUtils.translateDict(result.getRecords());
        return result.getRecords();
    }

    /**
     * 新增
     *
     * @param serviceItem 服务项目信息
     * <AUTHOR>
     * @date 2023.01.30
     */
    public void submitServiceItem(PmStdServiceItem serviceItem) {
        save(serviceItem);
    }


    /**
     * 根据id批量删除
     *
     * @param ids id集合
     * <AUTHOR>
     * @date 2023.01.30
     */
    public void deleteByIds(List<Long> ids) {
        removeByIds(ids);
    }


    /**
     * 导出Excel
     *
     * @param response                    响应
     * @param financialClassificationList 财务分类
     * @param projectName                 项目名称
     * @param startPeriod                 时间周期-开始时间
     * @param endPeriod                   时间周期-结束时间
     * @param isModel                     是否导出模板?true是false否(默认是否)
     * <AUTHOR>
     * @date 2023.01.30
     */
    public void exportExcel(HttpServletResponse response, List<String> financialClassificationList, String projectName, String startPeriod, String endPeriod, boolean isModel) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        StringBuilder fileName = new StringBuilder();
        fileName.append("物价管理-市级标准物价-服务项目");



        // 导出模板时isModel是ture
        if (isModel) {
            // 表头
            heads.add(CollUtil.list(true, "FINANCIAL_CLASSIFICATION", "PROJECT_CODE", "PROJECT_NAME", "PROJECT_CONNOTATION", "OTHER_CONTENT", "UNIT", "DESCRIPTION", "PRICE"));
            heads.add(CollUtil.list(true, "财务分类", "项目编码", "项目名称", "项目内涵", "除外内容", "计价单位", "说明", "价格(元)"));
        }
        else {
            // 表头
            heads.add(CollUtil.list(true, "EFFECT_TIME", "FINANCIAL_CLASSIFICATION", "PROJECT_CODE", "PROJECT_NAME", "PROJECT_CONNOTATION", "OTHER_CONTENT", "UNIT", "DESCRIPTION", "PRICE"));
            heads.add(CollUtil.list(true, "生效时间", "财务分类", "项目编码", "项目名称", "项目内涵", "除外内容", "计价单位", "说明", "价格(元)"));

            // 查询导出数据
            List<PmStdServiceItem> pmStdServiceItems = list(new LambdaQueryWrapper<PmStdServiceItem>()
                    .in(CollUtil.isNotEmpty(financialClassificationList), PmStdServiceItem::getFinancialClassification, financialClassificationList)
                    .like(StrUtil.isNotEmpty(projectName), PmStdServiceItem::getProjectName, projectName)
                    .between(StrUtil.isNotEmpty(startPeriod) && StrUtil.isNotEmpty(endPeriod), PmStdServiceItem::getEffectTime, startPeriod, endPeriod)
            );
            // 字典值转换
            DictUtils.translateDict(pmStdServiceItems);
            // 构建数据
            for (PmStdServiceItem serviceItem : pmStdServiceItems) {
                datas.add(CollUtil.list(true, DateUtil.format(serviceItem.getEffectTime(), "yyyy.MM.dd"), serviceItem.getFinancialClassification(), serviceItem.getProjectCode(), serviceItem.getProjectName(), serviceItem.getProjectConnotation(), serviceItem.getOtherContent(), serviceItem.getUnit(), serviceItem.getDescription(), serviceItem.getPrice()));
            }
        }

        MqsUtils.buildExportFileNameSuffix(fileName, startPeriod, endPeriod);

        // 导出
        List<ExcelExport> exports = new ArrayList<>();
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }


    /**
     * 导入Excel, 返回重复的数据
     *
     * @param importVo 参数
     */
    public List<PmStdServiceItem> importExcel(ServiceItemImportVo importVo) {
        Date effectTime = importVo.getEffectTime();
        MultipartFile file = importVo.getFile();
        List<String> projectCodeList = importVo.getProjectCodeList();


        // 获取sheet页
        SheetData sheetData = BigExcelUtil.parseExcel(file).getSheets().get(0);

        // 校验Excel
        checkExcel(sheetData);

        // 解析Excel数据
        List<PmStdServiceItem> importData = parseExcel(sheetData, effectTime);


        List<PmStdServiceItem> duplicateData = new ArrayList<>();
        if (projectCodeList == null) {
            // 第一次调用, 返回重复数据

            // 获取已存在的数据
            List<PmStdServiceItem> existData = baseMapper.selectList(null);

            // 根据项目编码和时间周期判断已存在
            for (PmStdServiceItem importDatum : importData) {
                for (PmStdServiceItem existDatum : existData) {
                    if (DateUtil.isSameDay(importDatum.getEffectTime(), existDatum.getEffectTime())
                            && StrUtil.equals(importDatum.getProjectCode(), existDatum.getProjectCode())) {
                        duplicateData.add(importDatum);
                    }
                }
            }

        } else {
            if (CollUtil.isNotEmpty(projectCodeList)) {
                baseMapper.delete(new LambdaQueryWrapper<PmStdServiceItem>().eq(PmStdServiceItem::getEffectTime, effectTime).in(PmStdServiceItem::getProjectCode, projectCodeList));
            }
            for (PmStdServiceItem importDatum : importData) {
                importDatum.setId(IdWorker.getId());
            }
            saveBatch(importData);
        }


        // 返回重复数据
        return duplicateData;
    }


    /**
     * 解析Excel数据
     *
     * @param sheetData  sheet页
     * @param timePeriod 时间周期
     * @return 去除表头后的Excel数据(List < 实体类 > 形式)
     */
    private List<PmStdServiceItem> parseExcel(SheetData sheetData, Date timePeriod) {

        // 获取数据
        List<PmStdServiceItem> datas = new ArrayList<>();
        List<Object> line;
        for (int i = 0; i < sheetData.getLines().size(); i++) {
            if (i <= 1) {
                continue;
            }
            line = sheetData.getLines().get(i);

            String price = BigExcelUtil.getItemStr(line, 7) == null ? "0" : BigExcelUtil.getItemStr(line, 7);

            datas.add(PmStdServiceItem.builder()
                    .effectTime(timePeriod)
                    .financialClassification(BigExcelUtil.getItemStr(line, 0))
                    .projectCode(BigExcelUtil.getItemStr(line, 1))
                    .projectName(BigExcelUtil.getItemStr(line, 2))
                    .projectConnotation(BigExcelUtil.getItemStr(line, 3))
                    .otherContent(BigExcelUtil.getItemStr(line, 4))
                    .unit(BigExcelUtil.getItemStr(line, 5))
                    .description(BigExcelUtil.getItemStr(line, 6))
                    .price(NumberUtil.toBigDecimal(price))
                    .build());
        }

        return datas;
    }

    /**
     * 校验Excel表头及数据是否重复
     *
     * @param sheetData sheet页
     */
    private void checkExcel(SheetData sheetData) {

        // 校验表头
        String checkHead = BigExcelUtil.checkHead(sheetData, CollUtil.list(true, "FINANCIAL_CLASSIFICATION", "PROJECT_CODE", "PROJECT_NAME", "PROJECT_CONNOTATION", "OTHER_CONTENT", "UNIT", "DESCRIPTION", "PRICE"));
        if (StrUtil.isNotEmpty(checkHead)) {
            throw new BaseException(checkHead);
        }

        // 校验项目编码是否重复
        /*Set<String> projectCodeSet = new HashSet<>();
        List<Object> line;
        String projectCode;
        List<String> errorList = new ArrayList<>();
        for (int i = 0; i < sheetData.getLines().size(); i++) {
            if (i <= 1) {
                continue;
            }
            line = sheetData.getLines().get(i);

            projectCode = BigExcelUtil.getItemStr(line, 1);
            if (CollUtil.contains(projectCodeSet, projectCode)) {
                // 提示重复
                errorList.add(StrUtil.format("第{}行数据的项目编码'{}'在前边的数据中已存在", i + 1, projectCode));
            } else {
                projectCodeSet.add(projectCode);
            }
        }

        if (CollUtil.isNotEmpty(errorList)) {
            throw new BaseException(errorList.toString());
        }*/
    }
}
