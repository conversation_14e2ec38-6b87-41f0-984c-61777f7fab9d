package com.crhms.cloud.mqs.sys.domain;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 系统配置表(SysConfig)实体类
 *
 * <AUTHOR>
 * @since 2023-01-12 15:40:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_config")
public class SysConfig extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -44837151279601295L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_ENABLE="enable";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";
    public static final String FIELD_REMAKE="remake";
    public static final String FIELD_SYS_KEY="sys_key";
    public static final String FIELD_SYS_VALUE="sys_value";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * key
     */
    @TableField
    private String sysKey;
    /**
     * value
     */
    @TableField
    private String sysValue;

    /**
     * 描述
     */
    @TableField
    private String remake;

    /**
     * 是否启用1.启用 0.禁用
     */    
    @TableField
    private String enable;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;


}

