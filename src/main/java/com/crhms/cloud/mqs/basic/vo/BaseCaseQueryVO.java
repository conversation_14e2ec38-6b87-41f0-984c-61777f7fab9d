package com.crhms.cloud.mqs.basic.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.Date;
import java.util.List;


/**
 * 住院查询实体类
 */


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class BaseCaseQueryVO {
    //审核场景
    private String auditScenario;
    //审核场景集
    private List<String> auditScenarios;
    //入院日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String admissionDateFrom;
    //入院日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String admissionDateTo;
    //审核日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String auditTimeFrom;
    //审核日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String auditTimeTo;

    //人工审核日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String mrTimeFrom;
    //人工审核日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String mrTimeTo;
    //单据号
    private String no;
    //住院号/就诊流水号
    private String admissionNo;
    //挂号id
    private String registerId;
    //参保类型
    private String benefitTypeId;
    //参保人
    private String patient;
    //科室
    private String deptCode;
    //医生id
    private String docId;
    //科室集
    private List<String> deptCodes;
    //医生id集
    private List<String> docIds;
    //规则集
    private List<String> ruleCodes;
    //转出科室
    private String tfFromDeptCode;
    //人员类别
    private String personnelType;
    //医疗类别
    private String claimTypeId;
    //是否违规 （1-违规、0-不违规）
    private String violationFlag;
    //是否需要人工审核
    private String mrFlag;
    //院区id
    private String hospitalId;
    //规则名称
    private String ruleCode;
    //住院状态
    private String isDischarge;
    //病区
    private String inpatientArea;
    //病区
    private List<String> inpatientAreas;
    //批次号
    private String batchNo;
    //就诊日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String itemDateFrom;
    //就诊日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String itemDateTo;
    //挂号日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String registerTimeFrom;
    //挂号日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String registerTimeTo;

    //当前科室编码
    private List<String> presentDeptCodes;

    //审核状态
    private String mrStatus;
    //分页条件
    private int page;
    private int pageSize;

    //参保人编码
    private String patientId;
    //结算日期开始
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String startBillDate;
    //结算日期结束
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endBillDate;
    //出院日期开始
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String startDischargeDate;
    //出院日期结束
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endDischargeDate;
    //违规级别
    private List<String> ruleType;



    //标记(1-已标记，0-未标记)
    private String lables;
    //最新质控人
    private List<String> mrBys;
    //分配质控人
    private List<String> mrTos;
    //险种类型编码集合
    private List<String> benefitTypeIds;
    //人员类别编码集合
    private List<String> personnelTypes;
    //医疗类别集合
    private List<String> claimTypeIds;
    //在院状态（1-出院，0-在院）
    private String dischargeState;
    //结算状态（1-已结算，0-未结算
    private String billState;
    //人工审核状态（2-审核完成，1-已审核，0-未审核）
    private String toDoAudit;
    //临床处理状态 （0-未处理、1-处理中、2-处理完成、3-无需处理、4-忽略、5-转自费）
    private String clinicalStatus;
    //预出院状态（1-已开预出院，0-未开预出院）
    private String preDischarge;
    //预计出院时间从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String preDischargeDateFrom;
    //预计出院时间至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String preDischargeDateTo;
    //开出院医嘱时间从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String dischargeOrderDateFrom;
    //开出院医嘱时间至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String dischargeOrderDateTo;
    //是否今日出院 (是否今日出院 1是 0否)
    private String dischargeToday;
    //机审日期开始
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String startAuditTime;
    //机审日期结束
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endAuditTime;

}

