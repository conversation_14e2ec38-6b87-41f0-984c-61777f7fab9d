package com.crhms.cloud.mqs.basic.domain;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import com.googlecode.jmapper.annotations.JMapConversion;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * 住院/门诊审核-通用(BaseMedicalDiag)实体类
 *
 * <AUTHOR>
 * @since 2023-02-08 17:07:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
public class BaseMedicalDiag extends BaseDomain implements Serializable {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 诊断号
     */
    @TableField
    private String diagnosisNo;

    /**
     * 单据号
     */
    @TableField
    private String no;
    /**
     * 就诊流水号
     */
    @TableField
    private String admissionNo;

    /**
     * 患者编码
     */
    @TableField
    private String patientId;
    /**
     * 患者姓名
     */
    @TableField
    private String patientName;

    /**
     * 诊断编码
     */
    @TableField
    private String diagnosisCode;
    /**
     * 诊断名称
     */
    @TableField
    private String diagnosisName;

    /**
     * 诊断科室编码
     */
    @TableField
    private String diagDeptCode;

    /**
     * 诊断科室名称
     */
    @TableField
    private String diagDeptName;

    /**
     * 主诊断标志;0:否 1:是
     */
    @TableField
    private String mainFlag;

    /**
     * 出入院诊断类型 1.入院 2.出院
     */
    @TableField
    private String diagInoutType;
    /**
     * 诊断类别
     */
    @TableField
    @DictTranslate(dictCode = "DIC_DIAG_TYPE")
    private String diagType;
    /**
     * 诊断排序号
     */
    @TableField
    private String diagnosisOrder;

    /**
     * 诊断时间
     */
    @TableField
    private Date diagnosisTime;

    /**
     * 入院病情
     */
    @TableField
    private String diagnosisSituation;


    /**
     * 批次号
     */
    @TableField
    private String batchNo;

    /**
     * 医院id
     */
    @TableField
    private String hospitalId;


    /**
     * jmapper类型转换
     *
     */
    @JMapConversion(from = {"diagnosis_time"},
                    to = {"diagnosisTime"})
    public Date convertDateFields(String dateString) throws ParseException {
        if (StrUtil.isBlank(dateString)) {
            return null;
        }
        for (String format : GloablData.DATE_FORMATS) {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            try {
                return sdf.parse(dateString);
            } catch (ParseException e) {
                // 忽略该异常，尝试下一个格式
            }
        }
        throw new RuntimeException("无法解析日期: " + dateString);
    }



}

