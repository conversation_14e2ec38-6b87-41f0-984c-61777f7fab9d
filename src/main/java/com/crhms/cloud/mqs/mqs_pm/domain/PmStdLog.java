package com.crhms.cloud.mqs.mqs_pm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.*;

/**
 * 物价管理-市级标准物价-更新日志
 *
 * @author: wangxing<PERSON>
 * @date: 2023/02/01
 */


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("mqs_pm_std_log")
public class PmStdLog extends BaseDomain {

    @TableField
    private Long id;

    /**
     * 更新大类
     */
    @TableField
    private String category;

    /**
     * 更新日志
     */
    @TableField
    private String log;

    @TableField(exist = false)
    private String updatedBy;
}
