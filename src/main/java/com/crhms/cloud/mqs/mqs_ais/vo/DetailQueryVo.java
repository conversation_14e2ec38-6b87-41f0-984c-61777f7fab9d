package com.crhms.cloud.mqs.mqs_ais.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class DetailQueryVo {
    //审核场景
    private String auditScenario;
    //项目日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String itemDateFrom;
    //项目日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String itemDateTo;
    // no单据号
    private String no;
    //住院号/就诊流水号
    private String admissionNo;
    //参保人
    private String patient;

    //医疗类别
    private String claimTypeId;
    //结算日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String billDateFrom;
    //结算日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String billDateTo;
    //医生
    private List<String> docIds;
    //科室
    private List<String> deptCodes;

    //是否违规
    private String violationFlag;

    //查询条件 规则 模糊
    private String rule;

    //查询条件 项目类型
    private String itemTypeCode;

    //查询条件 项目名称 模糊
    private String item;

    //审核结果
    private String mrStatus;
    private String hospitalId;
    //分页条件
    private int page;
    private int pageSize;

    private String relatedRecords;
}
