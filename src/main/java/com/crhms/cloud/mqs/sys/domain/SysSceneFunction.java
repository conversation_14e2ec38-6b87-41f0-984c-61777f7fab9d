package com.crhms.cloud.mqs.sys.domain;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 系统管理-审核场景-功能配置(SysSceneFunction)实体类
 *
 * <AUTHOR>
 * @since 2023-04-06 09:24:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_scene_function")
public class SysSceneFunction extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -43197751052891191L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_SCENE_CODE="scene_code";
    public static final String FIELD_FUNCTION_CODE="function_code";
    public static final String FIELD_ENABLE="enable";
    public static final String FIELD_IS_MR="is_mr";
    public static final String FIELD_IS_SELF="is_self";
    public static final String FIELD_IS_SELF_AGREEMENT="is_self_agreement";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 审核场景
     */
    @TableField
    private String sceneCode;
    /**
     * 审核场景名称
     */
    @TableField(exist = false)
    private String sceneName;
    /**
     * 功能编码 0 审核实施弹窗 1 综合弹窗 2 无弹窗
     */    
    @TableField
    private String functionCode;
    /**
     * 是否启用功能
     */    
    @TableField
    private String enable;
    /**
     * 是否展示人工审核结果
     */    
    @TableField
    private String isMr;
    /**
     * 是否可以转自费
     */    
    @TableField
    private String isSelf;
    /**
     * 是否可以生成自费协议书
     */    
    @TableField
    private String isSelfAgreement;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;

    /**
     * 审核弹窗 0-实时审核弹窗，1-综合弹窗，2-无弹窗
     */
    /*@TableField
    private String reviewPop;*/

    /**
     * 医保审核接口 0-事前接口，1-事中接口，2-不对接医保接口
     */
    @TableField
    private String miReviewApi;

    /**
     * 医保审核接口下拉
     */
    @TableField
    private String miReviewApiSelect;

    /**
     * 人工审核 0-查看，1-审核，2-不人工审核
     */
    @TableField
    private String manualReview;

    /**
     * 展示维度 0-按主单展示，1-按明细展示，2-不展示
     */
    @TableField
    private String showDimensions;

    /**
     * 展示形式 0-分TAB页展示，1-合并单页展示
     */
    @TableField
    private String showFormat;

    /**
     * 转自费不可用条件 0-人工审核通过，1-结算完成
     */
    @TableField
    private String selfPaidCondition;

    /**
     * 子场景id
     */
    @TableField
    private Long sceneId;

    /**
     * 医保审核接口（反馈服务） 1-是，0-否
     */
    @TableField
    private String miReviewApiBack;

    /**
     * 是否忽略（1-是，0-否）
     */
    @TableField
    private String isIgnore;

}

