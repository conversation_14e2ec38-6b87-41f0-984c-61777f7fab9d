package com.crhms.cloud.mqs.mqs_bmi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFb;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFbTemp;
import com.crhms.cloud.mqs.mqs_bmi.service.BmiFbService;
import com.crhms.cloud.mqs.mqs_bmi.vo.BmiImportVo;
import com.crhms.cloud.mqs.mqs_bmi.vo.BmiQueryVo;
import com.crhms.cloud.mqs.mqs_bmi.vo.FbMedicalCaseDto;
import com.crhms.cloud.mqs.mqs_bmi.vo.FbMedicalDetailDto;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 医保管理-申院内反馈控制层
 *
 * <AUTHOR>
 * @since 2023-02-10 10:40:08
 */
@RestController
@RequestMapping("api/mqs/bmiFb")
public class BmiFbController {
    /**
     * 服务对象
     */
    @Resource
    private BmiFbService bmiFbService;


    /**
     * 医保管理分页查询 - 带权限
     *
     * @param bmiQueryVo
     * @return
     */
    @PostMapping("/queryByPage")
    public ResponseEntity<List<BmiFb>> queryByPage(@RequestBody BmiQueryVo bmiQueryVo) {

        Page myPage = PageUtil.getPage(bmiQueryVo.getPage(), bmiQueryVo.getPageSize());

        return new ResponseEntity(this.bmiFbService.queryByPageWithAuth(bmiQueryVo, LoginContext.getHospitalId(), myPage), PageUtil.getTotalHeader(myPage), HttpStatus.OK);
    }

    /**
     * 查询详情页参保数据
     */
    @GetMapping("/queryCase")
    public ResponseEntity<FbMedicalCaseDto> queryFbMedicalCaseDto(@RequestParam(value = "no") String no) {
        return ResponseEntity.ok(this.bmiFbService.queryFbMedicalCaseDto(no, LoginContext.getHospitalId()));
    }

    //查询院内数据
    @GetMapping("/queryCaseDetail")
    public ResponseEntity<List<FbMedicalDetailDto>> queryFbMedicalDetailDto(@RequestParam(value = "no") String no,
                                                                            @RequestParam(value = "detailNo", required = false) String detailNo,
                                                                            @RequestParam(value = "item", required = false) String item,
                                                                            @RequestParam(value = "violationFlag", required = false) String violationFlag,
                                                                            @RequestParam(value = "visitType", required = false) String visitType,
                                                                            @RequestParam(value = "relatedRecords", required = false) String relatedRecords) {
        return ResponseEntity.ok(this.bmiFbService.queryFbMedicalDetailDto(no, detailNo, item, violationFlag, visitType, LoginContext.getHospitalId(),relatedRecords));

    }

    //查询医保反馈数据
    @GetMapping("/queryFbCaseDetail")
    public ResponseEntity<List<BmiFb>> queryFbCaseDetail(@RequestParam(value = "no") String no, @RequestParam(value = "statuss") String statuss) {
        return ResponseEntity.ok(this.bmiFbService.queryFbCaseDetail(no, statuss, LoginContext.getHospitalId()));
    }

    /**
     * 填写反馈意见
     *
     * @param fbStauts
     * @param fbReason
     * @param ids
     * @return
     */
    @PostMapping("/fbBmiReason")
    public ResponseEntity fbBmiReason(@RequestParam(value = "fbStauts") String fbStauts, @RequestParam(value = "fbReason") String fbReason, @RequestBody List<String> ids) {
        this.bmiFbService.fbBmiReason(fbStauts, fbReason, ids, LoginContext.getHospitalId());
        return ResponseEntity.ok("反馈成功！");
    }

    @PostMapping("/appeal/batchReturn")
    public ResponseEntity batchReturn(@RequestBody List<String> ids) {
        this.bmiFbService.batchChangeStatus(ids, "3", LoginContext.getHospitalId());
        return ResponseEntity.ok("退回成功！");
    }

    @PostMapping("/appeal/batchConfirm")
    public ResponseEntity batchConfirm(@RequestBody List<String> ids) {
        this.bmiFbService.batchChangeStatus(ids, "4", LoginContext.getHospitalId());
        return ResponseEntity.ok("确认成功！");
    }

    @PostMapping("/appeal/batchAppeal")
    public ResponseEntity batchAppeal(@RequestBody List<String> ids) {
        this.bmiFbService.batchChangeStatus(ids, "6", LoginContext.getHospitalId());
        return ResponseEntity.ok("申诉成功！");
    }

    /**
     * 处理已反馈数据
     *
     * @param status
     * @param id
     * @param fbStauts
     * @param fbReason
     * @return
     */
    @GetMapping("/appeal/checkDetail")
    public ResponseEntity checkDetail(@RequestParam(value = "status") String status,
                                      @RequestParam(value = "id") String id,
                                      @RequestParam(value = "fbStauts", required = false) String fbStauts,
                                      @RequestParam(value = "fbReason", required = false) String fbReason) {
        this.bmiFbService.checkDetail(status, id, fbStauts, fbReason, LoginContext.getHospitalId());
        return ResponseEntity.ok("操作成功！");
    }

    /**
     * 获取科室列表
     *
     * @param dept
     * @return
     */
    @GetMapping("/selectDeptList")
    public ResponseEntity<List<Map<String, String>>> selectDeptList(@RequestParam(value = "dept", required = false) String dept) {
        return ResponseEntity.ok(bmiFbService.selectDeptList(dept, LoginContext.getHospitalId()));
    }

    /**
     * 获取医生列表
     *
     * @param doctor
     * @return
     */
    @GetMapping("/selectDoctorList")
    public ResponseEntity<List<Map<String, String>>> selectDoctorList(@RequestParam(value = "doctor", required = false) String doctor) {
        return ResponseEntity.ok(bmiFbService.selectDoctorList(doctor, LoginContext.getHospitalId()));
    }
    /**
     * 获取就医方式列表
     * @param claimType
     * @return
     */
    @GetMapping("/selectClaimType")
    public ResponseEntity<List<String>> selectClaimType(@RequestParam(value = "claimType" ,required = false) String claimType){
        return ResponseEntity.ok(bmiFbService.selectClaimTypeList(claimType, LoginContext.getHospitalId()));
    }

    /**
     * 导出院内反馈
     *
     * @param response   响应
     * @param bmiQueryVo 查询条件
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody BmiQueryVo bmiQueryVo) {
        bmiFbService.exportWithAuth(response, bmiQueryVo);
    }


}

