package com.crhms.cloud.mqs.mqs_ais.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
public class DetailDto{

    private String detailNo;
    /**
     * 单据号
     */
    @TableField
    private String no;

    /**
     * 就诊流水号
     */
    @TableField
    private String admissionNo;
    /**
     * 参保人编码
     */
    @TableField
    private String patientId;
    /**
     * 参保人姓名
     */
    @TableField
    private String patientName;
    /**
     * 医疗类别(原就医方式编码)
     */
    @TableField
    @DictTranslate(dictCode = "DIC_MEDICAL_TYPE")
    private String claimTypeId;
    /**
     * 参保类型编码
     */
    @TableField
    @DictTranslate(dictCode = "DIC_INSUR_TYPE")
    private String benefitTypeId;
    /**
     * 性别
     */
    @TableField
    @DictTranslate(dictCode = "DIC_GENDER")
    private String patientGender;
    /**
     * 出生日期
     */
    @TableField
    private String patientBirthday;

    /**
     * 出院诊断编码
     */
    @TableField
    private String outDiagnosisCode;
    /**
     * 出院诊断名称
     */
    @TableField
    private String outDiagnosisName;
    /**
     * 入院诊断编码
     */
    @TableField
    private String inDiagnosisCode;
    /**
     * 入院诊断名称
     */
    @TableField
    private String inDiagnosisName;
    /**
     * 门诊次诊断编码
     */
    @TableField(exist = false)
    private String secondaryDiseaseId;
    /**
     * 门诊次诊断名称
     */
    @TableField(exist = false)
    private String secondaryDiseaseZh;
    /**
     * 入院日期
     */
    @TableField
    private Date admissionDate;
    /**
     * 出院日期
     */
    @TableField
    private Date dischargeDate;
    /**
     * 结算日期
     */
    @TableField
    private Date billDate;
    /**
     * 开单医生编码
     */
    @TableField
    private String applyDoctorCode;
    /**
     * 开单医生名称
     */
    @TableField
    private String applyDoctorName;
    /**
     * 开单科室编码
     */
    @TableField
    private String applyDeptCode;
    /**
     * 开单科室名称
     */
    @TableField
    private String applyDeptName;

    /**
     * 违规名称集
     */
    @TableField
    private String ruleNames;
    /**
     * 违规原因集
     */
    @TableField
    private String ruleReasons;
    /**
     * 反馈类型集
     */
    @TableField
    private String reasonTypes;
    /**
     * 反馈原因集
     */
    @TableField
    private String reasonDess;
    /**
     * 项目类型编码(A-Z大类)
     */
    @TableField
    private String itemTypeCode;
    /**
     * 项目类型名称
     */
    @TableField
    private String itemTypeName;
    /**
     * 项目日期
     */
    @TableField
    private String itemId;
    /**
     * 项目日期
     */
    @TableField
    private Date itemDate;
    /**
     * 项目名称
     */
    @TableField
    private String itemName;
    /**
     * 规格
     */
    @TableField
    private String specification;
    /**
     * 每次用量
     */
    @TableField
    private String usage;
    /**
     * 数量
     */
    @TableField
    private BigDecimal numbers;
    /**
     * 单价
     */
    @TableField
    private BigDecimal price;
    /**
     * 包装单位
     */
    @TableField
    private String usageUnit;
    /**
     * 总费用
     */
    @TableField
    private BigDecimal costs;

    private BigDecimal bmiConveredAmount;

    private String bmiOverallAmount;
    /**
     * 违规数量
     */
    @TableField
    private BigDecimal violationNum;

    /**
     * 违规金额
     */
    @TableField
    private BigDecimal violationAmt;

    private String relatedRecords;

/*
    private String hospitalId;
    private String batchNo;
 */

}
