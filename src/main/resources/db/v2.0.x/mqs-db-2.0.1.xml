<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <!-- 数据初始化 -->
    <changeSet author="zhaoyac (generated)" id="1681702350954-2">
        <!-- 删除将要初始化的原始数据 -->
        <sql> DELETE FROM mqs_sys_monitor_personal WHERE id = 1 </sql>
        <insert tableName="mqs_sys_monitor_personal">
            <column name="id" valueNumeric="1"/>
            <column name="user_id" value="1"/>
            <column name="is_cds" value="1"/>
            <column name="is_ci" value="1"/>
            <column name="is_sd" value="1"/>
            <column name="is_td" value="1"/>
            <column name="is_si" value="1"/>
            <column name="is_cds_ci" value="0"/>
            <column name="hospital_id" value="1"/>
        </insert>
    </changeSet>
    <changeSet author="zhaoyac (generated)" id="1681702350954-3">
        <!-- 删除将要初始化的原始数据 -->
        <sql> DELETE FROM mqs_sys_reason_type WHERE id IN (1639227966872592386,1639227966880980994,1639227966885175298,1647804149542457345,1647804149622149121,1647804149626343425,1647804302047350786,1647804302055739394,1647804302055739395,1647804362260779009,1647804362264973314,1647804362273361922,1647804669061533697,1647804669065728002,1647804669069922306,1647804722056564737,1647804722064953346,1647804722064953347,1647804767036280833,1647804767040475138,1647804767040475139,1647804807855247362,1647804807859441666,1647804807859441667,1647804856496590849,1647804856504979457,1647804856504979458,1647804907469967361,1647804907474161665,1647804907474161666) </sql>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1639227966872592386"/>
            <column name="scene_code" value="hpRg"/>
            <column name="reason_type" value="1"/>
            <column name="reason_des" value="患者要求"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-24T19:29:37"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-24T19:29:37"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1639227966880980994"/>
            <column name="scene_code" value="hpRg"/>
            <column name="reason_type" value="2"/>
            <column name="reason_des" value="病情需要"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-24T19:29:37"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-24T19:29:37"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1639227966885175298"/>
            <column name="scene_code" value="hpRg"/>
            <column name="reason_type" value="3"/>
            <column name="reason_des" value="其他"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-24T19:29:37"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-24T19:29:37"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804149542457345"/>
            <column name="scene_code" value="opRg"/>
            <column name="reason_type" value="1"/>
            <column name="reason_des" value="患者要求"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:28:19"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:28:19"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804149622149121"/>
            <column name="scene_code" value="opRg"/>
            <column name="reason_type" value="2"/>
            <column name="reason_des" value="病情需要"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:28:19"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:28:19"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804149626343425"/>
            <column name="scene_code" value="opRg"/>
            <column name="reason_type" value="3"/>
            <column name="reason_des" value="其它"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:28:19"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:28:19"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804302047350786"/>
            <column name="scene_code" value="opPct"/>
            <column name="reason_type" value="1"/>
            <column name="reason_des" value="患者要求"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:28:55"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:28:55"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804302055739394"/>
            <column name="scene_code" value="opPct"/>
            <column name="reason_type" value="2"/>
            <column name="reason_des" value="病情需要"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:28:55"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:28:55"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804302055739395"/>
            <column name="scene_code" value="opPct"/>
            <column name="reason_type" value="3"/>
            <column name="reason_des" value="其它"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:28:55"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:28:55"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804362260779009"/>
            <column name="scene_code" value="opPt"/>
            <column name="reason_type" value="1"/>
            <column name="reason_des" value="患者要求"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:29:09"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:29:09"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804362264973314"/>
            <column name="scene_code" value="opPt"/>
            <column name="reason_type" value="2"/>
            <column name="reason_des" value="病情需要"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:29:09"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:29:09"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804362273361922"/>
            <column name="scene_code" value="opPt"/>
            <column name="reason_type" value="3"/>
            <column name="reason_des" value="其它"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:29:09"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:29:09"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804669061533697"/>
            <column name="scene_code" value="hpDo"/>
            <column name="reason_type" value="1"/>
            <column name="reason_des" value="患者要求"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:22"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:22"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804669065728002"/>
            <column name="scene_code" value="hpDo"/>
            <column name="reason_type" value="2"/>
            <column name="reason_des" value="病情需要"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:22"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:22"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804669069922306"/>
            <column name="scene_code" value="hpDo"/>
            <column name="reason_type" value="3"/>
            <column name="reason_des" value="其它"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:22"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:22"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804722056564737"/>
            <column name="scene_code" value="hpBk"/>
            <column name="reason_type" value="1"/>
            <column name="reason_des" value="患者要求"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:35"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804722064953346"/>
            <column name="scene_code" value="hpBk"/>
            <column name="reason_type" value="2"/>
            <column name="reason_des" value="病情需要"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:35"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804722064953347"/>
            <column name="scene_code" value="hpBk"/>
            <column name="reason_type" value="3"/>
            <column name="reason_des" value="其它"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:35"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804767036280833"/>
            <column name="scene_code" value="hpTf"/>
            <column name="reason_type" value="1"/>
            <column name="reason_des" value="患者要求"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:46"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:46"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804767040475138"/>
            <column name="scene_code" value="hpTf"/>
            <column name="reason_type" value="2"/>
            <column name="reason_des" value="病情需要"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:46"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:46"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804767040475139"/>
            <column name="scene_code" value="hpTf"/>
            <column name="reason_type" value="3"/>
            <column name="reason_des" value="其它"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:46"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:46"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804807855247362"/>
            <column name="scene_code" value="hpPred"/>
            <column name="reason_type" value="1"/>
            <column name="reason_des" value="患者要求"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:56"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804807859441666"/>
            <column name="scene_code" value="hpPred"/>
            <column name="reason_type" value="2"/>
            <column name="reason_des" value="病情需要"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:56"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804807859441667"/>
            <column name="scene_code" value="hpPred"/>
            <column name="reason_type" value="3"/>
            <column name="reason_des" value="其它"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:30:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:30:56"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804856496590849"/>
            <column name="scene_code" value="hpOut"/>
            <column name="reason_type" value="1"/>
            <column name="reason_des" value="患者要求"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:31:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:31:07"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804856504979457"/>
            <column name="scene_code" value="hpOut"/>
            <column name="reason_type" value="2"/>
            <column name="reason_des" value="患者病情需要"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:31:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:31:07"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804856504979458"/>
            <column name="scene_code" value="hpOut"/>
            <column name="reason_type" value="3"/>
            <column name="reason_des" value="其它"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:31:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:31:07"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804907469967361"/>
            <column name="scene_code" value="hpSettle"/>
            <column name="reason_type" value="1"/>
            <column name="reason_des" value="患者要求"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:31:19"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:31:19"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804907474161665"/>
            <column name="scene_code" value="hpSettle"/>
            <column name="reason_type" value="2"/>
            <column name="reason_des" value="病情需要"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:31:19"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:31:19"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_reason_type">
            <column name="id" valueNumeric="1647804907474161666"/>
            <column name="scene_code" value="hpSettle"/>
            <column name="reason_type" value="3"/>
            <column name="reason_des" value="其它"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-17T11:31:19"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-17T11:31:19"/>
            <column name="hospital_id" value="1"/>
        </insert>
    </changeSet>
    <changeSet author="zhaoyac (generated)" id="1681702350954-4">
        <!-- 删除将要初始化的原始数据 -->
        <sql> DELETE FROM mqs_sys_rule_level WHERE id IN (1, 2, 3, 4, 5, 6) </sql>
        <insert tableName="mqs_sys_rule_level">
            <column name="id" valueNumeric="1"/>
            <column name="platform" value="zdy"/>
            <column name="code" value="1"/>
            <column name="default_name" value="严重违规"/>
            <column name="name" value="严重违规"/>
            <column name="enable" value="1"/>
            <column name="is_block" value="0"/>
            <column name="is_remind" value="0"/>
            <column name="level_config" value="3"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-01-12T17:18:21"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-01-12T17:18:27"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level">
            <column name="id" valueNumeric="2"/>
            <column name="platform" value="zdy"/>
            <column name="code" value="2"/>
            <column name="default_name" value="一般违规"/>
            <column name="name" value="一般违规"/>
            <column name="enable" value="1"/>
            <column name="is_block" value="0"/>
            <column name="is_remind" value="0"/>
            <column name="level_config" value="2"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-01-12T17:18:21"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-01-12T17:18:27"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level">
            <column name="id" valueNumeric="3"/>
            <column name="platform" value="zdy"/>
            <column name="code" value="3"/>
            <column name="default_name" value="高度可疑"/>
            <column name="name" value="高度可疑"/>
            <column name="enable" value="1"/>
            <column name="is_block" value="0"/>
            <column name="is_remind" value="0"/>
            <column name="level_config" value="3"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-01-12T17:18:21"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-01-12T17:18:27"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level">
            <column name="id" valueNumeric="4"/>
            <column name="platform" value="zdy"/>
            <column name="code" value="4"/>
            <column name="default_name" value="轻度可疑"/>
            <column name="name" value="轻度可疑"/>
            <column name="enable" value="1"/>
            <column name="is_block" value="0"/>
            <column name="is_remind" value="0"/>
            <column name="level_config" value="3"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-01-12T17:18:21"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-01-12T17:18:27"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level">
            <column name="id" valueNumeric="5"/>
            <column name="platform" value="zdy"/>
            <column name="code" value="5"/>
            <column name="default_name" value="提醒1"/>
            <column name="name" value="一级提醒"/>
            <column name="enable" value="1"/>
            <column name="is_block" value="0"/>
            <column name="is_remind" value="0"/>
            <column name="level_config" value="4"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-01-12T17:18:21"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-01-12T17:18:27"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level">
            <column name="id" valueNumeric="6"/>
            <column name="platform" value="zdy"/>
            <column name="code" value="6"/>
            <column name="default_name" value="提醒2"/>
            <column name="name" value="二级提醒"/>
            <column name="enable" value="1"/>
            <column name="is_block" value="0"/>
            <column name="is_remind" value="0"/>
            <column name="level_config" value="4"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-01-12T17:18:21"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-01-12T17:18:27"/>
            <column name="hospital_id" value="1"/>
        </insert>
    </changeSet>
    <changeSet author="zhaoyac (generated)" id="1681702350954-5">
        <!-- 删除将要初始化的原始数据 -->
        <sql> DELETE FROM mqs_sys_rule_level_third WHERE id IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12) </sql>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="1"/>
            <column name="platform" value="2"/>
            <column name="code" value="1"/>
            <column name="default_name" value="违规"/>
            <column name="sys_code" value="3"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="2"/>
            <column name="platform" value="2"/>
            <column name="code" value="2"/>
            <column name="default_name" value="可疑"/>
            <column name="sys_code" value="2"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="3"/>
            <column name="platform" value="2"/>
            <column name="code" value="3"/>
            <column name="default_name" value="待核实"/>
            <column name="sys_code" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="4"/>
            <column name="platform" value="3"/>
            <column name="code" value="1"/>
            <column name="default_name" value="明确违规"/>
            <column name="sys_code" value="2"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="5"/>
            <column name="platform" value="3"/>
            <column name="code" value="2"/>
            <column name="default_name" value="高度可疑"/>
            <column name="sys_code" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="6"/>
            <column name="platform" value="3"/>
            <column name="code" value="3"/>
            <column name="default_name" value="轻度可疑"/>
            <column name="sys_code" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="7"/>
            <column name="platform" value="1"/>
            <column name="code" value="1"/>
            <column name="default_name" value="严重违规"/>
            <column name="sys_code" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="8"/>
            <column name="platform" value="1"/>
            <column name="code" value="2"/>
            <column name="default_name" value="一般违规"/>
            <column name="sys_code" value="2"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="9"/>
            <column name="platform" value="1"/>
            <column name="code" value="3"/>
            <column name="default_name" value="高度可疑"/>
            <column name="sys_code" value="3"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="10"/>
            <column name="platform" value="1"/>
            <column name="code" value="4"/>
            <column name="default_name" value="轻度可疑"/>
            <column name="sys_code" value="4"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="11"/>
            <column name="platform" value="1"/>
            <column name="code" value="5"/>
            <column name="default_name" value="提醒1"/>
            <column name="sys_code" value="5"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_rule_level_third">
            <column name="id" valueNumeric="12"/>
            <column name="platform" value="1"/>
            <column name="code" value="6"/>
            <column name="default_name" value="提醒2"/>
            <column name="sys_code" value="6"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-03-21T18:57:07"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-03-21T18:57:11"/>
            <column name="hospital_id" value="1"/>
        </insert>
    </changeSet>

    <changeSet author="zhaoyac (generated)" id="1681702350954-7">
        <!-- 删除将要初始化的原始数据 -->
        <sql> DELETE FROM mqs_sys_scene WHERE id IN (11, 22, 33, 44, 55, 66, 77, 88, 99, 110)</sql>
        <insert tableName="mqs_sys_scene">
            <column name="id" valueNumeric="11"/>
            <column name="scene_code" value="opRg"/>
            <column name="scene_name" value="门诊挂号审核"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-10T17:12:45"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-02-07T18:31:52"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene">
            <column name="id" valueNumeric="22"/>
            <column name="scene_code" value="opPct"/>
            <column name="scene_name" value="门诊处方审核"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-10T17:12:45"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-02-07T18:31:52"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene">
            <column name="id" valueNumeric="33"/>
            <column name="scene_code" value="opPt"/>
            <column name="scene_name" value="门诊缴费审核"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-10T17:12:45"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-02-07T18:31:52"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene">
            <column name="id" valueNumeric="44"/>
            <column name="scene_code" value="hpRg"/>
            <column name="scene_name" value="住院登记审核"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-10T17:12:45"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-02-07T18:31:52"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene">
            <column name="id" valueNumeric="55"/>
            <column name="scene_code" value="hpDo"/>
            <column name="scene_name" value="医嘱审核"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-10T17:12:45"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-02-07T18:31:52"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene">
            <column name="id" valueNumeric="66"/>
            <column name="scene_code" value="hpBk"/>
            <column name="scene_name" value="每日记账审核"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-10T17:12:45"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-02-07T18:31:52"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene">
            <column name="id" valueNumeric="77"/>
            <column name="scene_code" value="hpTf"/>
            <column name="scene_name" value="转科室审核"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-10T17:12:45"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-02-07T18:31:52"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene">
            <column name="id" valueNumeric="88"/>
            <column name="scene_code" value="hpPred"/>
            <column name="scene_name" value="预出院审核"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-10T17:12:45"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-02-07T18:31:52"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene">
            <column name="id" valueNumeric="99"/>
            <column name="scene_code" value="hpOut"/>
            <column name="scene_name" value="出院审核"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-10T17:12:45"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-02-07T18:31:52"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene">
            <column name="id" valueNumeric="110"/>
            <column name="scene_code" value="hpSettle"/>
            <column name="scene_name" value="结算审核"/>
            <column name="enable" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-10T17:12:45"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-02-07T18:31:52"/>
            <column name="hospital_id" value="1"/>
        </insert>
    </changeSet>
    <changeSet author="zhaoyac (generated)" id="1681702350954-8">
        <!-- 删除将要初始化的原始数据 -->
        <sql> DELETE FROM mqs_sys_scene_function WHERE id IN (1 ,2 ,3 ,4 ,5 ,6 ,7 ,8 ,9 ,10 ,11 ,12 ,13 ,14 ,15 ,16 ,17 ,18 ,19 ,20)</sql>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="1"/>
            <column name="scene_code" value="opPt"/>
            <column name="function_code" value="1"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:31"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:04:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="2"/>
            <column name="scene_code" value="opPt"/>
            <column name="function_code" value="2"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:05"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="3"/>
            <column name="scene_code" value="opPct"/>
            <column name="function_code" value="1"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:31"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:04:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="4"/>
            <column name="scene_code" value="opPct"/>
            <column name="function_code" value="2"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:05"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="5"/>
            <column name="scene_code" value="opRg"/>
            <column name="function_code" value="1"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="0"/>
            <column name="is_self" value="0"/>
            <column name="is_self_agreement" value="0"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:31"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:04:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="6"/>
            <column name="scene_code" value="opRg"/>
            <column name="function_code" value="2"/>
            <column name="enable" value="0"/>
            <column name="is_mr" value="0"/>
            <column name="is_self" value="0"/>
            <column name="is_self_agreement" value="0"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:05"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="7"/>
            <column name="scene_code" value="hpRg"/>
            <column name="function_code" value="1"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:31"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:04:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="8"/>
            <column name="scene_code" value="hpRg"/>
            <column name="function_code" value="2"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:05"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="9"/>
            <column name="scene_code" value="hpDo"/>
            <column name="function_code" value="1"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:31"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:04:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="10"/>
            <column name="scene_code" value="hpDo"/>
            <column name="function_code" value="2"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:05"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="11"/>
            <column name="scene_code" value="hpBk"/>
            <column name="function_code" value="1"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:31"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:04:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="12"/>
            <column name="scene_code" value="hpBk"/>
            <column name="function_code" value="2"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:05"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="13"/>
            <column name="scene_code" value="hpTf"/>
            <column name="function_code" value="1"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:31"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:04:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="14"/>
            <column name="scene_code" value="hpTf"/>
            <column name="function_code" value="2"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:05"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="15"/>
            <column name="scene_code" value="hpPred"/>
            <column name="function_code" value="1"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:31"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:04:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="16"/>
            <column name="scene_code" value="hpPred"/>
            <column name="function_code" value="2"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:05"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="17"/>
            <column name="scene_code" value="hpOut"/>
            <column name="function_code" value="1"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:31"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:04:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="18"/>
            <column name="scene_code" value="hpOut"/>
            <column name="function_code" value="2"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:05"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="19"/>
            <column name="scene_code" value="hpSettle"/>
            <column name="function_code" value="1"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:31"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:04:35"/>
            <column name="hospital_id" value="1"/>
        </insert>
        <insert tableName="mqs_sys_scene_function">
            <column name="id" valueNumeric="20"/>
            <column name="scene_code" value="hpSettle"/>
            <column name="function_code" value="2"/>
            <column name="enable" value="1"/>
            <column name="is_mr" value="1"/>
            <column name="is_self" value="1"/>
            <column name="is_self_agreement" value="1"/>
            <column name="last_updated_by" valueNumeric="1"/>
            <column name="last_updated_date" value="2023-04-06T10:04:56"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="created_date" value="2023-04-06T10:05"/>
            <column name="hospital_id" value="1"/>
        </insert>
    </changeSet>


    <changeSet author="zhaoyac (generated)" id="1681702350954-9101">
        <validCheckSum>1:any</validCheckSum>
        <createIndex indexName="mqs_op_rg_ordersU1" tableName="mqs_op_rg_orders">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="order_detail_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_rg_ordersU2" tableName="mqs_op_rg_orders" unique="true">
            <column name="order_detail_no"/>
        </createIndex>

        <createIndex indexName="mqs_op_pt_ordersU1" tableName="mqs_op_pt_orders">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="order_detail_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_pt_ordersU2" tableName="mqs_op_pt_orders" unique="true">
            <column name="order_detail_no"/>
        </createIndex>

        <createIndex indexName="mqs_op_pct_ordersU1" tableName="mqs_op_pct_orders">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="order_detail_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_pct_ordersU2" tableName="mqs_op_pct_orders" unique="true">
            <column name="order_detail_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_do_ordersU1" tableName="mqs_hp_do_orders">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="order_detail_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_do_ordersU2" tableName="mqs_hp_do_orders" unique="true">
            <column name="order_detail_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_bk_ordersU1" tableName="mqs_hp_bk_orders">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="order_detail_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_bk_ordersU2" tableName="mqs_hp_bk_orders" unique="true">
            <column name="order_detail_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_out_ordersU1" tableName="mqs_hp_out_orders">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="order_detail_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_out_ordersU2" tableName="mqs_hp_out_orders" unique="true">
            <column name="order_detail_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_pred_ordersU1" tableName="mqs_hp_pred_orders">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="order_detail_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_pred_ordersU2" tableName="mqs_hp_pred_orders" unique="true">
            <column name="order_detail_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_tf_ordersU1" tableName="mqs_hp_tf_orders">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="order_detail_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_tf_ordersU2" tableName="mqs_hp_tf_orders" unique="true">
            <column name="order_detail_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_rg_ordersU1" tableName="mqs_hp_rg_orders">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="order_detail_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_rg_ordersU2" tableName="mqs_hp_rg_orders" unique="true">
            <column name="order_detail_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_settle_ordersU1" tableName="mqs_hp_settle_orders">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="order_detail_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_settle_ordersU2" tableName="mqs_hp_settle_orders" unique="true">
            <column name="order_detail_no"/>
        </createIndex>
    </changeSet>

    <!-- 主单增加字段 单据院区与系统hospital_id 区分 -->
    <changeSet author="zhaoyac (generated)" id="1681702350954-11">
        <validCheckSum>1:any</validCheckSum>
        <sql>
            ALTER TABLE `mqs_op_rg`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_op_rg_his`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_op_pct`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_op_pct_his`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_op_pt`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_op_pt_his`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_rg`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_rg_his`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_bk`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_bk_his`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_do`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_do_his`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_tf`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_tf_his`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_out`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_out_his`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_pred`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_pred_his`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_settle`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
            ALTER TABLE `mqs_hp_settle_his`
            ADD COLUMN `hospital_code` varchar(100) NULL COMMENT '医院编码';
        </sql>

    </changeSet>

    <changeSet author="zhaoyac (generated)" id="1681702350954-13">
        <validCheckSum>1:any</validCheckSum>
        <modifyDataType tableName="mqs_mr_log" columnName="id" newDataType="bigint"/>
        <modifyDataType tableName="mqs_mr_plan" columnName="id" newDataType="bigint"/>
    </changeSet>
    <!-- 增加定时任务配置-->
    <changeSet author="zhaoyac (generated)" id="20231108-1">
        <sql>
            INSERT INTO `mqs_sys_config`(`id` ,`sys_key`, `sys_value`, `remake`, `enable`, `last_updated_by`, `last_updated_date`, `created_by`, `created_date`, `hospital_id`) VALUES ('17','CLEAR_HISDATA_TIME', '0 50 23 * * ? ', '数据清理', '1', 1, '2023-11-08 17:36:27', 1, '2023-11-08 17:36:31', '1');
        </sql>
    </changeSet>

    <!-- 增加历史数据表索引 -->
    <changeSet author="zhaoyac (generated)" id="20231108-2">
        <validCheckSum>1:any</validCheckSum>

        <createIndex indexName="mqs_op_rg_hisU3" tableName="mqs_op_rg_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_rg_detail_hisU3" tableName="mqs_op_rg_detail_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_rg_audit_hisU3" tableName="mqs_op_rg_audit_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_rg_reason_hisU3" tableName="mqs_op_rg_reason_his">
            <column name="batch_no"/>
        </createIndex>

        <createIndex indexName="mqs_op_pt_hisU3" tableName="mqs_op_pt_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_pt_detail_hisU3" tableName="mqs_op_pt_detail_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_pt_audit_hisU3" tableName="mqs_op_pt_audit_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_pt_reason_hisU3" tableName="mqs_op_pt_reason_his">
            <column name="batch_no"/>
        </createIndex>

        <createIndex indexName="mqs_op_pct_hisU3" tableName="mqs_op_pct_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_pct_detail_hisU3" tableName="mqs_op_pct_detail_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_pct_audit_hisU3" tableName="mqs_op_pct_audit_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_op_pct_reason_hisU3" tableName="mqs_op_pct_reason_his">
            <column name="batch_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_rg_hisU3" tableName="mqs_hp_rg_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_rg_detail_hisU3" tableName="mqs_hp_rg_detail_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_rg_audit_hisU3" tableName="mqs_hp_rg_audit_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_rg_reason_hisU3" tableName="mqs_hp_rg_reason_his">
            <column name="batch_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_do_hisU3" tableName="mqs_hp_do_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_do_detail_hisU3" tableName="mqs_hp_do_detail_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_do_audit_hisU3" tableName="mqs_hp_do_audit_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_do_reason_hisU3" tableName="mqs_hp_do_reason_his">
            <column name="batch_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_bk_hisU3" tableName="mqs_hp_bk_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_bk_detail_hisU3" tableName="mqs_hp_bk_detail_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_bk_audit_hisU3" tableName="mqs_hp_bk_audit_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_bk_reason_hisU3" tableName="mqs_hp_bk_reason_his">
            <column name="batch_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_tf_hisU3" tableName="mqs_hp_tf_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_tf_detail_hisU3" tableName="mqs_hp_tf_detail_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_tf_audit_hisU3" tableName="mqs_hp_tf_audit_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_tf_reason_hisU3" tableName="mqs_hp_tf_reason_his">
            <column name="batch_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_out_hisU3" tableName="mqs_hp_out_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_out_detail_hisU3" tableName="mqs_hp_out_detail_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_out_audit_hisU3" tableName="mqs_hp_out_audit_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_out_reason_hisU3" tableName="mqs_hp_out_reason_his">
            <column name="batch_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_settle_hisU3" tableName="mqs_hp_settle_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_settle_detail_hisU3" tableName="mqs_hp_settle_detail_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_settle_audit_hisU3" tableName="mqs_hp_settle_audit_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_settle_reason_hisU3" tableName="mqs_hp_settle_reason_his">
            <column name="batch_no"/>
        </createIndex>

        <createIndex indexName="mqs_hp_pred_hisU3" tableName="mqs_hp_pred_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_pred_detail_hisU3" tableName="mqs_hp_pred_detail_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_pred_audit_hisU3" tableName="mqs_hp_pred_audit_his">
            <column name="batch_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_pred_reason_hisU3" tableName="mqs_hp_pred_reason_his">
            <column name="batch_no"/>
        </createIndex>
    </changeSet>

    <changeSet author="zhaoyac (generated)" id="20231124-1">
        <validCheckSum>1:any</validCheckSum>
        <modifyDataType tableName="mqs_sys_config" columnName="sys_value" newDataType="varchar(2000)"/>
    </changeSet>

    <!-- 增加标题个性化配置 -->
    <changeSet author="zhaoyac (generated)" id="20231124-2">
        <sql>
            INSERT INTO `mqs_sys_config`(`id`, `sys_key`, `sys_value`, `remake`, `enable`, `last_updated_by`, `last_updated_date`, `created_by`, `created_date`, `hospital_id`) VALUES (18, 'MQS_SYS_TITLE', '医院诊间审核管理系统', '系统名称', '1', 1, '2023-11-08 17:36:27', 1, '2023-11-08 17:36:31', '1');
        </sql>
    </changeSet>

</databaseChangeLog>