package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpBkDetailHis;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpBkDetailHisMapper;
import com.crhms.cloud.mqs.mqs_hp.service.HpBkDetailHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-每日记账明细-历史表(HpBkDetailHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 10:04:33
 */
@Service("hpBkDetailHisService")
public class HpBkDetailHisService extends ServiceImpl< HpBkDetailHisMapper, HpBkDetailHis> {

}
