package com.crhms.cloud.mqs.mqs_hp.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 审核费用明细(HpSettleDetail)实体类
 *
 * <AUTHOR>
 * @since 2023-02-09 13:16:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_hp_settle_detail")
public class HpSettleDetail extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 609895937154036293L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_DETAIL_NO="detail_no";
    public static final String FIELD_NO="no";
    public static final String FIELD_ADMISSION_NO="admission_no";
    public static final String FIELD_BILL_DATE="bill_date";
    public static final String FIELD_ITEM_ID="item_id";
    public static final String FIELD_ITEM_NAME="item_name";
    public static final String FIELD_PTYPE="ptype";
    public static final String FIELD_ITEM_TYPE_CODE="item_type_code";
    public static final String FIELD_ITEM_TYPE_NAME="item_type_name";
    public static final String FIELD_ITEM_DATE="item_date";
    public static final String FIELD_NUMBERS="numbers";
    public static final String FIELD_PRICE="price";
    public static final String FIELD_USAGE_UNIT="usage_unit";
    public static final String FIELD_COSTS="costs";
    public static final String FIELD_COST_NUMBER="cost_number";
    public static final String FIELD_COST_COSTS="cost_costs";
    public static final String FIELD_BMI_CONVERED_AMOUNT="bmi_convered_amount";
    public static final String FIELD_BMI_OVERALL_AMOUNT="bmi_overall_amount";
    public static final String FIELD_SPECIFICATION="specification";
    public static final String FIELD_USAGE="usage";
    public static final String FIELD_APPLY_DOCTOR_CODE="apply_doctor_code";
    public static final String FIELD_APPLY_DOCTOR_NAME="apply_doctor_name";
    public static final String FIELD_APPLY_DOCTOR_LEVEL="apply_doctor_level";
    public static final String FIELD_APPLY_DEPT_CODE="apply_dept_code";
    public static final String FIELD_APPLY_DEPT_NAME="apply_dept_name";
    public static final String FIELD_EXEC_DOCTOR_CODE="exec_doctor_code";
    public static final String FIELD_EXEC_DOCTOR_NAME="exec_doctor_name";
    public static final String FIELD_EXEC_DEPT_CODE="exec_dept_code";
    public static final String FIELD_EXEC_DEPT_NAME="exec_dept_name";
    public static final String FIELD_RT_DOCTOR_CODE="rt_doctor_code";
    public static final String FIELD_RT_DOCTOR_NAME="rt_doctor_name";
    public static final String FIELD_RP_NURSE_CODE="rp_nurse_code";
    public static final String FIELD_RP_NURSE_NAME="rp_nurse_name";
    public static final String FIELD_CHARGING_FLAG="charging_flag";
    public static final String FIELD_SELF_EXPENSE="self_expense";
    public static final String FIELD_FREQUENCY_INTERVAL="frequency_interval";
    public static final String FIELD_APPROVAL_NUMBER="approval_number";
    public static final String FIELD_Z_PHYSICIANAP="z_physicianap";
    public static final String FIELD_POSTS_NUMBER="posts_number";
    public static final String FIELD_PAY_RATIO="pay_ratio";
    public static final String FIELD_ABROAD_DRUG_FLAG="abroad_drug_flag";
    public static final String FIELD_OUT_HOSPITAL_FLAG="out_hospital_flag";
    public static final String FIELD_RULE_CODES="rule_codes";
    public static final String FIELD_RULE_NAMES="rule_names";
    public static final String FIELD_RULE_REASONS="rule_reasons";
    public static final String FIELD_REASON_TYPES="reason_types";
    public static final String FIELD_REASON_DESS="reason_dess";
    public static final String FIELD_BATCH_NO="batch_no";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 明细流水号
     */    
    @TableField
    private String detailNo;
    /**
     * 单据号
     */    
    @TableField
    private String no;
    /**
     * 就诊流水号
     */    
    @TableField
    private String admissionNo;
    /**
     * 结算日期
     */    
    @TableField
    private Date billDate;
    /**
     * 项目编码
     */    
    @TableField
    private String itemId;
    /**
     * 项目名称
     */    
    @TableField
    private String itemName;
    /**
     * 项目类型名称
     */    
    @TableField
    private String ptype;
    /**
     * 项目类型编码(A-Z大类)
     */    
    @TableField
    private String itemTypeCode;
    /**
     * 项目类型名称
     */    
    @TableField
    private String itemTypeName;
    /**
     * 项目日期
     */    
    @TableField
    private Date itemDate;
    /**
     * 数量
     */    
    @TableField
    private BigDecimal numbers;
    /**
     * 单价
     */    
    @TableField
    private BigDecimal price;
    /**
     * 包装单位
     */    
    @TableField
    private String usageUnit;
    /**
     * 总费用
     */    
    @TableField
    private BigDecimal costs;
    /**
     * 计费数量
     */    
    @TableField
    private BigDecimal costNumber;
    /**
     * 计费金额
     */    
    @TableField
    private BigDecimal costCosts;
    /**
     * 医保内金额
     */    
    @TableField
    private String bmiConveredAmount;
    /**
     * 医保统筹金额
     */    
    @TableField
    private String bmiOverallAmount;
    /**
     * 规格
     */    
    @TableField
    private String specification;
    /**
     * 每次用量
     */    
    @TableField
    private String usage;
    /**
     * 用药天数
     */
    @TableField
    private String usageDays;
    /**
     * 开单医生编码
     */    
    @TableField
    private String applyDoctorCode;
    /**
     * 开单医生名称
     */    
    @TableField
    private String applyDoctorName;
    /**
     * 开单医生级别
     */    
    @TableField
    private String applyDoctorLevel;
    /**
     * 开单科室编码
     */    
    @TableField
    private String applyDeptCode;
    /**
     * 开单科室名称
     */    
    @TableField
    private String applyDeptName;
    /**
     * 受单医生编码
     */    
    @TableField
    private String execDoctorCode;
    /**
     * 受单医生名称
     */    
    @TableField
    private String execDoctorName;
    /**
     * 受单科室编码
     */    
    @TableField
    private String execDeptCode;
    /**
     * 受单科室名称
     */    
    @TableField
    private String execDeptName;
    /**
     * 住院医师编码
     */    
    @TableField
    private String rtDoctorCode;
    /**
     * 住院医师名称
     */    
    @TableField
    private String rtDoctorName;
    /**
     * 责任护士编码
     */    
    @TableField
    private String rpNurseCode;
    /**
     * 责任护士名称
     */    
    @TableField
    private String rpNurseName;
    /**
     * 计费标记
     */    
    @TableField
    private String chargingFlag;
    /**
     * 是否自费
     */    
    @TableField
    private String selfExpense;
    /**
     * 使用频次
     */    
    @TableField
    private String frequencyInterval;
    /**
     * 备案审批号
     */    
    @TableField
    private String approvalNumber;
    /**
     * 医师行政职务
     */    
    @TableField
    private String zPhysicianap;
    /**
     * 帖数
     */    
    @TableField
    private String postsNumber;
    /**
     * 自付比例
     */    
    @TableField
    private BigDecimal payRatio;
    /**
     * 出国带药标志
     */    
    @TableField
    private String abroadDrugFlag;
    /**
     * 外院费用标志
     */    
    @TableField
    private String outHospitalFlag;
    /**
     * 是否违规
     */
    @TableField
    private String violationFlag;
    /**
     * 违规编码集
     */    
    @TableField
    private String ruleCodes;
    /**
     * 违规名称集
     */    
    @TableField
    private String ruleNames;
    /**
     * 违规原因集
     */    
    @TableField
    private String ruleReasons;
    /**
     * 反馈类型集
     */    
    @TableField
    private String reasonTypes;
    /**
     * 反馈原因集
     */    
    @TableField
    private String reasonDess;
    /**
     * 批次号
     */    
    @TableField
    private String batchNo;
    /**
     * 更新人
     */    
    @TableField
    private Long lastUpdatedBy;
    /**
     * 更新时间
     */    
    @TableField
    private Date lastUpdatedDate;
    /**
     * 创建人
     */    
    @TableField
    private Long createdBy;
    /**
     * 创建时间
     */    
    @TableField
    private Date createdDate;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;


}

