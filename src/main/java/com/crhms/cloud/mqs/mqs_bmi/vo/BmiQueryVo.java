package com.crhms.cloud.mqs.mqs_bmi.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@Data
public class BmiQueryVo {

    //结算日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String billDateFrom;
    //结算日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String billDateTo;
    //申诉截止时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String appealDate;
    //单据号
    private String no;
    //交易流水号
    private String detailNo;
    //住院号/就诊流水号
    private String admissionNo;
    //就医方式
    private List<String> claimTypes;
    //参保人/个人编号
    private String patient;
    //是否匹配
    private String isMatch;
    //科室名称
    private List<String> deptCodes;
    //反馈科室
    private List<String> fbDeptCodes;
    //处理状态
    private List<String> statuss;
    //扣费意见
    private String fbStauts;
    //医生名称
    private List<String> docIds;
    //项目名称
    private String item;
    //规则名称 模糊查询
    private String rule;
    //规则名称
    private List<String> rules;
    //医院id
    private String hospitalId;

    //用户拥有的科室权限
    private List<String> authDeptCodes;
    //分页条件
    private int page;
    private int pageSize;
}
