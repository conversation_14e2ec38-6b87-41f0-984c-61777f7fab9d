package com.crhms.cloud.mqs.sys.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.sys.domain.SysFunds;
import com.crhms.cloud.mqs.sys.domain.SysFundsAlone;
import com.crhms.cloud.mqs.sys.domain.SysFundsDept;
import com.crhms.cloud.mqs.sys.domain.SysFundsZone;
import com.crhms.cloud.mqs.sys.dto.FundAloneCodesVO;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.mapper.SysFundsMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 运营管理-基金管理(SysFunds)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-15 16:43:03
 */
@Log4j2
@Service("sysFundsService")
public class SysFundsService extends ServiceImpl<SysFundsMapper, SysFunds> {

    @Autowired
    SysFundsDeptService sysFundsDeptService;
    @Autowired
    SysFundsZoneService sysFundsZoneService;
    @Autowired
    SysFundsAloneService sysFundsAloneService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    public SysFunds queryById(Integer id) {
        return this.baseMapper.selectById(id);
    }

    /**
     * 查询年度计划列表
     *
     * @return 查询结果
     */
    public List<SysFunds> queryList() {

        List<SysFunds> list = this.list(new QueryWrapper<SysFunds>().orderByDesc(SysFunds.FIELD_YEARS, SysFunds.FIELD_VISIT_TYPE));
        //比例计算
        for (SysFunds sysFunds : list) {
            sysFunds.setTotalWarnAmountRatio(BigDecimal.ZERO.compareTo(sysFunds.getTotalAmount()) == 0 ? BigDecimal.ZERO :sysFunds.getTotalWarnAmount().divide(sysFunds.getTotalAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        }
        return list;
    }

    /**
     * 查询年度计划明细
     *
     * @param id 主键
     * @return 实例对象
     */
    public SysFunds queryDetail(Long id) {
        SysFunds sysFunds = this.baseMapper.selectById(id);
        if(!Objects.isNull(sysFunds)){
            List<SysFundsDept> deptList = sysFundsDeptService.list(new QueryWrapper<SysFundsDept>().eq(SysFundsDept.FIELD_FUNDS_ID, id));
            List<SysFundsZone> zoneList = sysFundsZoneService.list(new QueryWrapper<SysFundsZone>().eq(SysFundsZone.FIELD_FUNDS_ID, id));
            List<SysFundsAlone> aloneList = sysFundsAloneService.list(new QueryWrapper<SysFundsAlone>().eq(SysFundsAlone.FIELD_FUNDS_ID, id));
            for (SysFundsDept sysFundsDept : deptList) {
                sysFundsDept.setYearsWarnRatio(BigDecimal.ZERO.compareTo(sysFundsDept.getYearsAmount()) == 0 ? BigDecimal.ZERO :sysFundsDept.getYearsWarn().divide(sysFundsDept.getYearsAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            }
            for (SysFundsZone sysFundsZone : zoneList) {
                sysFundsZone.setYearsWarnRatio(BigDecimal.ZERO.compareTo(sysFundsZone.getYearsAmount()) == 0 ? BigDecimal.ZERO :sysFundsZone.getYearsWarn().divide(sysFundsZone.getYearsAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            }
            for (SysFundsAlone sysFundsAlone : aloneList) {
                sysFundsAlone.setYearsWarnRatio(BigDecimal.ZERO.compareTo(sysFundsAlone.getAmount()) == 0 ? BigDecimal.ZERO :sysFundsAlone.getWarn().divide(sysFundsAlone.getAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            }
            sysFunds.setDeptList(deptList);
            sysFunds.setZoneList(zoneList);
            sysFunds.setAloneList(aloneList);
        }
        return sysFunds;
    }

    /**
     * 新增编辑数据
     *
     * @param sysFunds 实例对象
     * @return 实例对象
     */
    @Transactional
    public SysFunds editData(SysFunds sysFunds) {

        String hospitalId = LoginContext.getHospitalId();
        sysFunds.setHospitalId(hospitalId);

        //校验配置规范
        checkSysFunds(sysFunds);

        if (Objects.isNull(sysFunds.getId())) {
            //新增
            Long count = this.baseMapper.selectCount(new QueryWrapper<SysFunds>().eq(SysFunds.FIELD_HOSPITAL_ID, hospitalId).eq(SysFunds.FIELD_YEARS, sysFunds.getYears()).eq(SysFunds.FIELD_VISIT_TYPE, sysFunds.getVisitType()));
            if (count > 0) {
                throw new BaseException("已存在" + sysFunds.getYears() + "重复数据,请勿重复添加！");
            }
            //插入数据
            this.save(sysFunds);
        } else {
            //更新
            Long count = this.baseMapper.selectCount(new QueryWrapper<SysFunds>().eq(SysFunds.FIELD_HOSPITAL_ID, hospitalId).eq(SysFunds.FIELD_YEARS, sysFunds.getYears()).eq(SysFunds.FIELD_VISIT_TYPE, sysFunds.getVisitType()).ne(SysFunds.FIELD_ID, sysFunds.getId()));
            if (count > 0) {
                throw new BaseException("已存在" + sysFunds.getYears() + "重复数据,请勿重复添加！");
            }
            //更新数据
            this.updateById(sysFunds);
        }
        List<Future> futureList = new ArrayList<>();
        Long userId = LoginContext.getUserId();
        //部门配置新增
        if (CollectionUtil.isNotEmpty(sysFunds.getDeptList())) {
            List<SysFundsDept> deptList = sysFunds.getDeptList();
            for (SysFundsDept sysFundsDept : deptList) {
                sysFundsDept.setFundsId(sysFunds.getId());
                sysFundsDept.setHospitalId(hospitalId);
                sysFundsDept.setLastUpdatedBy(userId);
            }
            List<SysFundsDept> updates = deptList.stream().filter(x -> !Objects.isNull(x.getId())).collect(Collectors.toList());
            List<SysFundsDept> inserts = deptList.stream().filter(x -> Objects.isNull(x.getId())).collect(Collectors.toList());
            futureList.add(sysFundsDeptService.updateBatchById(updates,1000));
            sysFundsDeptService.saveBatch(inserts);
        }
        //更新院区配置
        if (CollectionUtil.isNotEmpty(sysFunds.getZoneList())) {
            List<SysFundsZone> zoneList = sysFunds.getZoneList();
            for (SysFundsZone sysFundsZone : zoneList) {
                sysFundsZone.setFundsId(sysFunds.getId());
                sysFundsZone.setHospitalId(hospitalId);
                sysFundsZone.setLastUpdatedBy(userId);
            }
            List<SysFundsZone> updates = zoneList.stream().filter(x -> !Objects.isNull(x.getId())).collect(Collectors.toList());
            List<SysFundsZone> inserts = zoneList.stream().filter(x -> Objects.isNull(x.getId())).collect(Collectors.toList());
            futureList.add(sysFundsZoneService.updateBatchById(updates,1000));
            sysFundsZoneService.saveBatch(inserts);
        }
        if(CollectionUtil.isNotEmpty(sysFunds.getAloneList())){
            List<SysFundsAlone> zoneList = sysFunds.getAloneList();
            for (SysFundsAlone sysFundsAlone : zoneList) {
                sysFundsAlone.setYears(sysFunds.getYears());
                sysFundsAlone.setFundsId(sysFunds.getId());
                sysFundsAlone.setHospitalId(hospitalId);
                sysFundsAlone.setLastUpdatedBy(userId);
            }
            List<SysFundsAlone> updates = zoneList.stream().filter(x -> !Objects.isNull(x.getId())).collect(Collectors.toList());
            List<SysFundsAlone> inserts = zoneList.stream().filter(x -> Objects.isNull(x.getId())).collect(Collectors.toList());
            sysFundsAloneService.updateBatchById(updates,1000);
            sysFundsAloneService.saveBatch(inserts);
        }
        //等待线程执行完毕后 刷新年度基金额度
        for (Future future : futureList) {
            while (true) {
                //获取future成功完成状态，如果想要限制每个任务的超时时间，取消本行的状态判断+future.get(1000*1, TimeUnit.MILLISECONDS)+catch超时异常使用即可。
                if(future.isDone() && !future.isCancelled()){
                    break;
                }else {
                    try {
                        log.info("===============等待" + future.toString());
                        Thread.sleep(1);//每次轮询休息1毫秒（CPU纳秒级），避免CPU高速轮循耗空CPU
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        break;
                    }
                }
            }
        }
        this.baseMapper.syncTotalAmount(sysFunds.getId(),hospitalId);
        return sysFunds;
    }

    private void checkSysFunds(SysFunds sysFunds) {
        List<SysFundsZone> zoneList = sysFunds.getZoneList();
        List<SysFundsDept> deptList = sysFunds.getDeptList();
        List<SysFundsAlone> aloneList = sysFunds.getAloneList();
        if (CollectionUtil.isNotEmpty(zoneList)) {
            List<String> collect = zoneList.stream().filter(x -> x.getYearsWarn().compareTo(x.getYearsAmount()) > 0
                    || x.getWarn1().compareTo(x.getAmount1()) > 0
                    || x.getWarn2().compareTo(x.getAmount2()) > 0
                    || x.getWarn3().compareTo(x.getAmount3()) > 0
                    || x.getWarn4().compareTo(x.getAmount4()) > 0
                    || x.getWarn5().compareTo(x.getAmount5()) > 0
                    || x.getWarn6().compareTo(x.getAmount6()) > 0
                    || x.getWarn7().compareTo(x.getAmount7()) > 0
                    || x.getWarn8().compareTo(x.getAmount8()) > 0
                    || x.getWarn9().compareTo(x.getAmount9()) > 0
                    || x.getWarn10().compareTo(x.getAmount10()) > 0
                    || x.getWarn11().compareTo(x.getAmount11()) > 0
                    || x.getWarn12().compareTo(x.getAmount12()) > 0
            ).map(SysFundsZone::getName).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                throw new BaseException("【" + collect.toString() + "】预警金额不能大于基金金额！");
            }
        }
        if (CollectionUtil.isNotEmpty(deptList)) {
            List<String> collect = deptList.stream().filter(x -> x.getYearsWarn().compareTo(x.getYearsAmount()) > 0
                    || x.getWarn1().compareTo(x.getAmount1()) > 0
                    || x.getWarn2().compareTo(x.getAmount2()) > 0
                    || x.getWarn3().compareTo(x.getAmount3()) > 0
                    || x.getWarn4().compareTo(x.getAmount4()) > 0
                    || x.getWarn5().compareTo(x.getAmount5()) > 0
                    || x.getWarn6().compareTo(x.getAmount6()) > 0
                    || x.getWarn7().compareTo(x.getAmount7()) > 0
                    || x.getWarn8().compareTo(x.getAmount8()) > 0
                    || x.getWarn9().compareTo(x.getAmount9()) > 0
                    || x.getWarn10().compareTo(x.getAmount10()) > 0
                    || x.getWarn11().compareTo(x.getAmount11()) > 0
                    || x.getWarn12().compareTo(x.getAmount12()) > 0
            ).map(SysFundsDept::getName).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                throw new BaseException("【" + collect.toString() + "】预警金额不能大于基金金额！");
            }
        }
        if (CollectionUtil.isNotEmpty(aloneList)) {
            List<String> collect = aloneList.stream().filter(x -> x.getWarn().compareTo(x.getAmount()) > 0
            ).map(SysFundsAlone::getAloneName).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                throw new BaseException("【" + collect.toString() + "】预警金额不能大于基金金额！");
            }
        }
    }


    /**
     * 统计更新年度基金使用情况
     *
     * @param year
     */
    public void upDateUsedAmountByYear(String year, String hospital) {
        List<SysFunds> funds = new ArrayList<>();
        QueryWrapper<SysFunds> eq = new QueryWrapper<SysFunds>().eq(SysFunds.FIELD_YEARS, year);
        if(StrUtil.isNotEmpty(hospital)){
            eq.eq(SysFunds.FIELD_HOSPITAL_ID, hospital);
        }
        funds = this.baseMapper.selectList(eq);

        Map<String, List<SysFunds>> fundsGroup = funds.stream().collect(Collectors.groupingBy(SysFunds::getHospitalId));
        for (Map.Entry<String, List<SysFunds>> stringListEntry : fundsGroup.entrySet()) {
            String hospitalId = stringListEntry.getKey();
            List<SysFunds> sysFunds = stringListEntry.getValue();

            if (sysFunds.size() <= 2) {
                List<SysFundsDept> SysFundsDeptList = new ArrayList<>();
                List<SysFundsZone> SysFundsZoneList = new ArrayList<>();
                List<SysFundsAlone> sysFundsAloneList = new ArrayList<>();

                //查询慢特病数据
                List<Map> csDiseases = this.baseMapper.queryCsDisease(hospitalId);
                //查询大病数据
                List<Map> siDieasesMap = this.baseMapper.querySiDisease(hospitalId);
                List<String> siDieasess = siDieasesMap.stream().map(x -> x.get("code").toString()).collect(Collectors.toList());
                //查询靶向药数据
                List<Map> tdDieasesMap = this.baseMapper.queryTdDisease(hospitalId);
                List<String> tdDieasess = tdDieasesMap.stream().map(x -> x.get("item_id").toString()).collect(Collectors.toList());
                //单独管理排除集(特殊病种基金单独管理)
                Set<String> ignoreCsDiseaseCode = new HashSet<>();
                Set<String> ignoreSingleDiseaseCode = new HashSet<>();
                Set<String> ignoreSiDiagnosisCode = new HashSet<>();
                Set<String> ignoreTdDieasess = new HashSet<>();

                Map<String, String> csDiseasesMap = csDiseases.stream().collect(Collectors.toMap(x -> x.get("cs_disease_code").toString(), x -> x.get("type").toString()));

                for (SysFunds sysFund : sysFunds) {
                    String table = "1".equals(sysFund.getVisitType()) ? AuditScenarioEnum.opPt.getAuditScenario() : AuditScenarioEnum.hpSettle.getAuditScenario();
                    //计算统计维度 开启标识
                    boolean isAlone = "1".equals(sysFund.getIsAlone());
                    if (isAlone) {
                        //开启单病种基金管理
                        List<SysFundsAlone> aloneList = sysFundsAloneService.list(new QueryWrapper<SysFundsAlone>().eq(SysFundsAlone.FIELD_HOSPITAL_ID, hospitalId).eq(SysFundsAlone.FIELD_FUNDS_ID, sysFund.getId()));
                        for (SysFundsAlone sysFundsAlone : aloneList) {
                            sysFundsAlone.setUsedAmount(BigDecimal.ZERO);
                            sysFundsAlone.setUsedRatio(BigDecimal.ZERO);
                        }
                        sysFundsAloneList.addAll(aloneList);
                        Map<String, SysFundsAlone> aloneMap = aloneList.stream().collect(Collectors.toMap(SysFundsAlone::getAloneCode, x -> x));
                        boolean isCdsCi = !Objects.isNull(aloneMap.get("CdsCi"));
                        boolean isCds = !Objects.isNull(aloneMap.get("Cds"));
                        boolean isCi = !Objects.isNull(aloneMap.get("Ci"));
                        boolean isSd = !Objects.isNull(aloneMap.get("Sd"));
                        boolean isSi = !Objects.isNull(aloneMap.get("Si"));
                        boolean isTd = !Objects.isNull(aloneMap.get("Td"));

                        //统计慢特病/单病种/大病占用金额
                        List<Map> maps = this.baseMapper.queryUsedAmountByYear(table, year, hospitalId, isCdsCi, isSd, isSi, isTd);

                        //总金额
                        BigDecimal used_amount = BigDecimal.ZERO;

                        //特病管理按照权重优先级占用基金池
//                    aloneList.sort(Comparator.comparing(x -> {
//                        x.setUsedAmount(BigDecimal.ZERO);
//                        return x.getWeight();
//                    }));

                        for (Map x : maps) {
                            BigDecimal total = MqsUtils.getBigDecimal(x.get("total"));
                            used_amount = used_amount.add(total);

                            for (SysFundsAlone sysFundsAlone : aloneList) {
                                String aloneCode = sysFundsAlone.getAloneCode();
                                // 判断当前金额 是否需要计入本基金  是 =》计入基金 且单独管理的基金是重复累计的
                                boolean flag = false;
                                switch (aloneCode) {
                                    case "CdsCi":
                                        flag = !StrUtil.isBlankIfStr(x.get("cs_disease_code")) && ObjectUtil.isNotNull(csDiseasesMap.get(x.get("cs_disease_code")));
                                        if(flag){
                                            ignoreCsDiseaseCode.add(x.get("cs_disease_code").toString());
                                        }
                                        break;
                                    case "Cds":
                                        flag = !StrUtil.isBlankIfStr(x.get("cs_disease_code")) && "1".equals(csDiseasesMap.get(x.get("cs_disease_code")));
                                        if(flag){
                                            ignoreCsDiseaseCode.add(x.get("cs_disease_code").toString());
                                        }
                                        break;
                                    case "Ci":
                                        flag = !StrUtil.isBlankIfStr(x.get("cs_disease_code")) && "2".equals(csDiseasesMap.get(x.get("cs_disease_code")));
                                        if(flag){
                                            ignoreCsDiseaseCode.add(x.get("cs_disease_code").toString());
                                        }
                                        break;
                                    case "Sd":
                                        flag = !StrUtil.isBlankIfStr(x.get("single_disease_code")) && !"0".equals(x.get("single_disease_code"));
                                        if(flag){
                                            ignoreSingleDiseaseCode.add(x.get("single_disease_code").toString());
                                        }
                                        break;
                                    case "Si":
                                        flag = !StrUtil.isBlankIfStr(x.get("out_diagnosis_code")) && siDieasess.contains(x.get("out_diagnosis_code"));
                                        if(flag){
                                            ignoreSiDiagnosisCode.add(x.get("out_diagnosis_code").toString());
                                        }
                                        break;
                                    case "Td":
                                        flag = !StrUtil.isBlankIfStr(x.get("item_id")) && tdDieasess.contains(x.get("item_id"));
                                        if(flag){
                                            ignoreTdDieasess.add(x.get("item_id").toString());
                                        }
                                        break;
                                }
                                if (flag) {
                                    sysFundsAlone.setUsedAmount(sysFundsAlone.getUsedAmount().add(total));
                                    sysFundsAlone.setUsedRatio(BigDecimal.ZERO.compareTo(sysFundsAlone.getAmount()) == 0 ? BigDecimal.ZERO : sysFundsAlone.getUsedAmount().divide(sysFundsAlone.getAmount(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
//                                break;
                                }
                            }
                        }
                    }
                    List<SysFundsDept> sysFundsDepts = sysFundsDeptService.list(new QueryWrapper<SysFundsDept>().eq(SysFundsDept.FIELD_FUNDS_ID, sysFund.getId()).eq(SysFundsDept.FIELD_HOSPITAL_ID, hospitalId));
                    if (sysFundsDepts.size() > 0) {
                        //按部门统计
                        List<Map> deptTotal = this.baseMapper.queryUsedAmountByDept(table, year, ignoreCsDiseaseCode, ignoreSingleDiseaseCode, ignoreSiDiagnosisCode, ignoreTdDieasess, hospitalId);
                        Map<String, List<Map>> collect = deptTotal.stream().filter(x -> !Objects.isNull(x.get("discharge_dept_code"))).collect(Collectors.groupingBy(x -> x.get("discharge_dept_code").toString()));
                        for (SysFundsDept sysFundsDept : sysFundsDepts) {
                            List<Map> depList = collect.get(sysFundsDept.getCode());
                            if (CollectionUtil.isNotEmpty(depList)) {
                                BigDecimal total = depList.stream().map(x -> MqsUtils.getBigDecimal(x.get("total"))).reduce(BigDecimal.ZERO, BigDecimal::add);
                                Map<String, BigDecimal> collect1 = depList.stream().collect(Collectors.toMap(x -> x.get("month").toString(), e -> MqsUtils.getBigDecimal(e.get("total")), (k1, k2) -> k1));
                                sysFundsDept.setUsedAmount(total);
                                sysFundsDept.setUsedRatio(BigDecimal.ZERO.compareTo(sysFundsDept.getYearsAmount()) == 0 ? BigDecimal.ZERO : sysFundsDept.getUsedAmount().divide(sysFundsDept.getYearsAmount(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
                                sysFundsDept.setUsedAmountMonth1(collect1.get("1") == null ? BigDecimal.ZERO : collect1.get("1"));
                                sysFundsDept.setUsedAmountMonth2(collect1.get("2") == null ? BigDecimal.ZERO : collect1.get("2"));
                                sysFundsDept.setUsedAmountMonth3(collect1.get("3") == null ? BigDecimal.ZERO : collect1.get("3"));
                                sysFundsDept.setUsedAmountMonth4(collect1.get("4") == null ? BigDecimal.ZERO : collect1.get("4"));
                                sysFundsDept.setUsedAmountMonth5(collect1.get("5") == null ? BigDecimal.ZERO : collect1.get("5"));
                                sysFundsDept.setUsedAmountMonth6(collect1.get("6") == null ? BigDecimal.ZERO : collect1.get("6"));
                                sysFundsDept.setUsedAmountMonth7(collect1.get("7") == null ? BigDecimal.ZERO : collect1.get("7"));
                                sysFundsDept.setUsedAmountMonth8(collect1.get("8") == null ? BigDecimal.ZERO : collect1.get("8"));
                                sysFundsDept.setUsedAmountMonth9(collect1.get("9") == null ? BigDecimal.ZERO : collect1.get("9"));
                                sysFundsDept.setUsedAmountMonth10(collect1.get("10") == null ? BigDecimal.ZERO : collect1.get("10"));
                                sysFundsDept.setUsedAmountMonth11(collect1.get("11") == null ? BigDecimal.ZERO : collect1.get("11"));
                                sysFundsDept.setUsedAmountMonth12(collect1.get("12") == null ? BigDecimal.ZERO : collect1.get("12"));
                            }
                        }
                        SysFundsDeptList.addAll(sysFundsDepts);
                    }
                    List<SysFundsZone> sysFundsZones = sysFundsZoneService.list(new QueryWrapper<SysFundsZone>().eq(SysFundsZone.FIELD_FUNDS_ID, sysFund.getId()).eq(SysFundsZone.FIELD_HOSPITAL_ID, hospitalId));
                    if (sysFundsZones.size() > 0) {
                        //按病区统计
                        List<Map> zoneTotal = this.baseMapper.queryUsedAmountByZone(table, year,ignoreCsDiseaseCode, ignoreSingleDiseaseCode, ignoreSiDiagnosisCode, ignoreTdDieasess, hospitalId);
                        Map<String, List<Map>> collect = zoneTotal.stream().filter(x -> !Objects.isNull(x.get("out_zone_code"))).collect(Collectors.groupingBy(x -> x.get("out_zone_code").toString()));
                        for (SysFundsZone sysFundsZone : sysFundsZones) {
                            List<Map> znoeList = collect.get(sysFundsZone.getCode());
                            if (CollectionUtil.isNotEmpty(znoeList)) {
                                BigDecimal total = znoeList.stream().map(x -> MqsUtils.getBigDecimal(x.get("total"))).reduce(BigDecimal.ZERO, BigDecimal::add);
                                Map<String, BigDecimal> collect1 = znoeList.stream().collect(Collectors.toMap(x -> x.get("month").toString(), e -> MqsUtils.getBigDecimal(e.get("total")), (k1, k2) -> k1));
                                sysFundsZone.setUsedAmount(total);
                                sysFundsZone.setUsedRatio(BigDecimal.ZERO.compareTo(sysFundsZone.getYearsAmount()) == 0 ? BigDecimal.ZERO : sysFundsZone.getUsedAmount().divide(sysFundsZone.getYearsAmount(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
                                sysFundsZone.setUsedAmountMonth1(collect1.get("1") == null ? BigDecimal.ZERO : collect1.get("1"));
                                sysFundsZone.setUsedAmountMonth2(collect1.get("2") == null ? BigDecimal.ZERO : collect1.get("2"));
                                sysFundsZone.setUsedAmountMonth3(collect1.get("3") == null ? BigDecimal.ZERO : collect1.get("3"));
                                sysFundsZone.setUsedAmountMonth4(collect1.get("4") == null ? BigDecimal.ZERO : collect1.get("4"));
                                sysFundsZone.setUsedAmountMonth5(collect1.get("5") == null ? BigDecimal.ZERO : collect1.get("5"));
                                sysFundsZone.setUsedAmountMonth6(collect1.get("6") == null ? BigDecimal.ZERO : collect1.get("6"));
                                sysFundsZone.setUsedAmountMonth7(collect1.get("7") == null ? BigDecimal.ZERO : collect1.get("7"));
                                sysFundsZone.setUsedAmountMonth8(collect1.get("8") == null ? BigDecimal.ZERO : collect1.get("8"));
                                sysFundsZone.setUsedAmountMonth9(collect1.get("9") == null ? BigDecimal.ZERO : collect1.get("9"));
                                sysFundsZone.setUsedAmountMonth10(collect1.get("10") == null ? BigDecimal.ZERO : collect1.get("10"));
                                sysFundsZone.setUsedAmountMonth11(collect1.get("11") == null ? BigDecimal.ZERO : collect1.get("11"));
                                sysFundsZone.setUsedAmountMonth12(collect1.get("12") == null ? BigDecimal.ZERO : collect1.get("12"));
                            }
                        }
                        SysFundsZoneList.addAll(sysFundsZones);
                    }

                }
                //批量更新入表
                this.updateBatchById(sysFunds);
                sysFundsDeptService.updateBatchById(SysFundsDeptList, 1000);
                sysFundsZoneService.updateBatchById(SysFundsZoneList, 1000);
                sysFundsAloneService.updateBatchById(sysFundsAloneList);
            } else {
                throw new BaseException("只能维护门诊 住院两种基金！");
            }
        }
    }

    public List<Map<String,String>> aloneTypeList() {
        List<Map<String,String> > list = new ArrayList<>();
        for (Map.Entry<String, String> stringStringEntry : GloablData.LOCAL_DICT.LOCAL_FUNDS_ALONE_CODE.dicts.entrySet()) {
            Map<String,String> map = new HashMap();
            map.put("code",stringStringEntry.getKey());
            map.put("name",stringStringEntry.getValue());
            list.add(map);
        }
        return list;
    }

    public List<Map<String, Object>> getAmountByMonth(Date startDate, Date endDate, List<String> fundVisitType, String hospitalId) {

        String startYear = DateUtil.format(startDate, "yyyy");
        String endYear = DateUtil.format(endDate, "yyyy");

        List<SysFunds> funds = this.list(new QueryWrapper<SysFunds>().select(SysFunds.FIELD_ID,SysFunds.FIELD_WARN_TYPE)
                .eq(SysFunds.FIELD_HOSPITAL_ID, hospitalId)
                .in(SysFunds.FIELD_VISIT_TYPE, fundVisitType)
                .between(SysFunds.FIELD_YEARS, startYear,endYear));
        List<Long> deptIds = funds.stream().filter(x -> "1".equals(x.getWarnType())).map(SysFunds::getId).collect(Collectors.toList());
        List<Long> zoneIds = funds.stream().filter(x -> "2".equals(x.getWarnType())).map(SysFunds::getId).collect(Collectors.toList());
        return this.baseMapper.getAmountByMonth(deptIds,zoneIds,hospitalId);

    }

    public void deleteData(Long id) {
        this.baseMapper.deleteById(id);
        sysFundsAloneService.remove(new QueryWrapper<SysFundsAlone>().eq(SysFundsAlone.FIELD_FUNDS_ID,id));
        sysFundsDeptService.remove(new QueryWrapper<SysFundsDept>().eq(SysFundsDept.FIELD_FUNDS_ID,id));
        sysFundsZoneService.remove(new QueryWrapper<SysFundsZone>().eq(SysFundsZone.FIELD_FUNDS_ID,id));
    }

    /**
     * 查询当前年份基金配置，单独管理条件集
     * @param year
     * @param hospitalId
     * @return
     */
    public FundAloneCodesVO genIgnore(String year, String hospitalId){

        FundAloneCodesVO result = new FundAloneCodesVO();
        result.setIgnoreOpCsDiseaseCode(new ArrayList<>());
        result.setIgnoreOpSiDiagnosisCode(new ArrayList<>());
        result.setIgnoreOpSingleDisease(false);
        result.setIgnoreOpTdDieasess(new ArrayList<>());
        result.setIgnoreHpCsDiseaseCode(new ArrayList<>());
        result.setIgnoreHpSiDiagnosisCode(new ArrayList<>());
        result.setIgnoreHpSingleDisease(false);
        result.setIgnoreHpTdDieasess(new ArrayList<>());
        //查询慢特病数据
        List<Map> csDiseases = this.baseMapper.queryCsDisease(hospitalId);
        //查询大病数据
        List<Map> siDieasesMap = this.baseMapper.querySiDisease(hospitalId);
        //查询靶向药数据
        List<Map> tdDieasesMap = this.baseMapper.queryTdDisease(hospitalId);

        List<String> CdsCiDieasess = csDiseases.stream().map(x -> x.get("cs_disease_code").toString()).collect(Collectors.toList());
        List<String> CdsDieasess = csDiseases.stream().filter(x -> "1".equals(x.get("type"))).map(x -> x.get("cs_disease_code").toString()).collect(Collectors.toList());
        List<String> CiDieasess = csDiseases.stream().filter(x -> "2".equals(x.get("type"))).map(x -> x.get("cs_disease_code").toString()).collect(Collectors.toList());

        List<String> siDieasess = siDieasesMap.stream().map(x -> x.get("code").toString()).collect(Collectors.toList());
        List<String> tdDieasess = tdDieasesMap.stream().map(x -> x.get("item_id").toString()).collect(Collectors.toList());

        QueryWrapper<SysFunds> wrapper = new QueryWrapper<SysFunds>().eq(SysFunds.FIELD_HOSPITAL_ID, hospitalId).eq(SysFunds.FIELD_YEARS, year);
        //基金配置
        List<SysFunds> fundsList = this.baseMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(fundsList)) {
            List<Long> fundIds = fundsList.stream().map(SysFunds::getId).collect(Collectors.toList());
            //查询单独管理额度
            List<SysFundsAlone> sysFundsAlones = this.baseMapper.queryAloneAmount(fundIds, hospitalId);

            if (CollUtil.isNotEmpty(sysFundsAlones)) {
                Set<Map.Entry<String, List<SysFundsAlone>>> entries = sysFundsAlones.stream().collect(Collectors.groupingBy(SysFundsAlone::getVisitType)).entrySet();
                for (Map.Entry<String, List<SysFundsAlone>> entry : entries) {

                    //单独管理排除集(特殊病种基金单独管理)
                    List<String> ignoreCsDiseaseCode = new ArrayList<>();
                    boolean ignoreSingleDiseaseCode = false;
                    List<String> ignoreSiDiagnosisCode = new ArrayList<>();
                    List<String> ignoreTdDieasess = new ArrayList<>();
                    //门诊/住院
                    String visitType = entry.getKey();
                    List<SysFundsAlone> sysFundsAloneList = entry.getValue();
                    for (SysFundsAlone sysFundsAlone : sysFundsAloneList) {
                        switch (sysFundsAlone.getAloneCode()) {
                            case "CdsCi":
                                ignoreCsDiseaseCode.addAll(CdsCiDieasess);
                                break;
                            case "Cds":
                                ignoreCsDiseaseCode.addAll(CdsDieasess);
                                break;
                            case "Ci":
                                ignoreCsDiseaseCode.addAll(CiDieasess);
                                break;
                            case "Sd":
                                ignoreSingleDiseaseCode = true;
                                break;
                            case "Si":
                                ignoreSiDiagnosisCode.addAll(siDieasess);
                                break;
                            case "Td":
                                ignoreTdDieasess.addAll(tdDieasess);
                                break;
                        }
                    }
                    if ("1".equals(visitType)) {
                        result.setIgnoreOpCsDiseaseCode(ignoreCsDiseaseCode);
                        result.setIgnoreOpSiDiagnosisCode(ignoreSiDiagnosisCode);
                        result.setIgnoreOpSingleDisease(ignoreSingleDiseaseCode);
                        result.setIgnoreOpTdDieasess(ignoreTdDieasess);
                    } else {
                        result.setIgnoreHpCsDiseaseCode(ignoreCsDiseaseCode);
                        result.setIgnoreHpSiDiagnosisCode(ignoreSiDiagnosisCode);
                        result.setIgnoreHpSingleDisease(ignoreSingleDiseaseCode);
                        result.setIgnoreHpTdDieasess(ignoreTdDieasess);
                    }
                }
            }
        }
        return result;
    }

}
