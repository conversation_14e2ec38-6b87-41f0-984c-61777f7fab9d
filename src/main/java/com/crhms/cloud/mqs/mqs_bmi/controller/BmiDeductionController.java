package com.crhms.cloud.mqs.mqs_bmi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiDeduction;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiDeductionTemp;
import com.crhms.cloud.mqs.mqs_bmi.service.BmiDeductionService;
import com.crhms.cloud.mqs.mqs_bmi.vo.BmiQueryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 医保管理-医保扣费(BmiDeduction)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-14 10:43:09
 */
@RestController
@RequestMapping("api/mqs/bmiDeduction")
public class BmiDeductionController {
    /**
     * 服务对象
     */
    @Resource
    private BmiDeductionService bmiDeductionService;


    /**
     * 医保管理分页查询
     *
     * @param bmiQueryVo
     * @return
     */
    @PostMapping("/queryByPage")
    public ResponseEntity<List<BmiDeduction>> queryByPage(@RequestBody BmiQueryVo bmiQueryVo) {

        Page myPage = PageUtil.getPage(bmiQueryVo.getPage(), bmiQueryVo.getPageSize());

        return new ResponseEntity(this.bmiDeductionService.queryByPage(bmiQueryVo, LoginContext.getHospitalId(), myPage), PageUtil.getTotalHeader(myPage), HttpStatus.OK);
    }


    /**
     * 医保扣费导入
     */
    @PostMapping("/import")
    public ResponseEntity<Map> importFbExcel(MultipartFile file) {
        return ResponseEntity.ok(this.bmiDeductionService.importFbExcel(file, LoginContext.getHospitalId()));
    }

    /**
     * 查询重复的导入列表
     *
     * @param batchNo
     * @return
     */
    @GetMapping("/import/queryByPage")
    public ResponseEntity<List<BmiDeductionTemp>> queryImportByPage(@RequestParam(value = "batchNo") String batchNo,
                                                                    @RequestParam(value = "page") int page,
                                                                    @RequestParam(value = "pageSize") int pageSize) {
        Page myPage = PageUtil.getPage(page, pageSize);

        return new ResponseEntity(this.bmiDeductionService.queryImportByPage(batchNo, LoginContext.getHospitalId(), myPage), PageUtil.getTotalHeader(myPage), HttpStatus.OK);
    }

    /**
     * 载入数据至 医保扣费
     *
     * @param batchNo
     * @return
     */
    @PostMapping("import/loadIn")
    public ResponseEntity loadIn(@RequestParam(value = "batchNo") String batchNo) {
        this.bmiDeductionService.loadIn(batchNo, LoginContext.getHospitalId());
        return ResponseEntity.ok("导入成功！");
    }
    /**
     * 导入操作 - 忽略
     *
     * @param batchNo
     * @return
     */
    @PostMapping("import/ignoreData")
    public ResponseEntity ignoreData(@RequestParam(value = "batchNo") String batchNo, @RequestBody List<String> ignoreNos) {
        this.bmiDeductionService.ignoreData(batchNo, ignoreNos, LoginContext.getHospitalId());
        return ResponseEntity.ok("操作成功！");
    }
    /**
     * 导入操作 - 覆盖
     *
     * @param batchNo
     * @return
     */
    @PostMapping("import/coverData")
    public ResponseEntity coverData(@RequestParam(value = "batchNo") String batchNo, @RequestBody List<String> coverNos) {
        this.bmiDeductionService.coverData(batchNo, coverNos, LoginContext.getHospitalId());
        return ResponseEntity.ok("操作成功！");
    }

    /**
     * 批量删除数据
     *
     * @param ids 主键
     * @return 删除是否成功
     */
    @DeleteMapping("/delete")
    public ResponseEntity<Boolean> deleteById(@RequestBody List<Long> ids) {
        String hospitalId = LoginContext.getHospitalId();
        return ResponseEntity.ok(this.bmiDeductionService.remove(new QueryWrapper<BmiDeduction>().eq(BmiDeduction.FIELD_HOSPITAL_ID, hospitalId).in(BmiDeduction.FIELD_ID, ids)));
    }

    /**
     * 获取科室列表
     *
     * @param dept
     * @return
     */
    @GetMapping("/selectDeptList")
    public ResponseEntity<List<Map<String, String>>> selectDeptList(@RequestParam(value = "dept", required = false) String dept) {
        return ResponseEntity.ok(bmiDeductionService.selectDeptList(dept, LoginContext.getHospitalId()));
    }

    /**
     * 获取医生列表
     *
     * @param doctor
     * @return
     */
    @GetMapping("/selectDoctorList")
    public ResponseEntity<List<Map<String, String>>> selectDoctorList(@RequestParam(value = "doctor", required = false) String doctor) {
        return ResponseEntity.ok(bmiDeductionService.selectDoctorList(doctor, LoginContext.getHospitalId()));
    }

    /**
     * 获取就医方式列表
     * @param claimType
     * @return
     */
    @GetMapping("/selectClaimType")
    public ResponseEntity<List<String>> selectClaimType(@RequestParam(value = "claimType" ,required = false) String claimType){
        return ResponseEntity.ok(bmiDeductionService.selectClaimTypeList(claimType, LoginContext.getHospitalId()));
    }
    /**
     * 获取已有规则列表
     * @param rule
     * @return
     */
    @GetMapping("/selectRules")
    public ResponseEntity<List<String>> selectRules(@RequestParam(value = "rule" ,required = false) String rule){
        return ResponseEntity.ok(bmiDeductionService.selectRules(rule, LoginContext.getHospitalId()));
    }

    /**
     * 指标统计
     *
     * @param itemDateFrom
     * @param itemDateTo
     * @param dept
     * @return
     */
    @GetMapping("/statistics/ind")
    public ResponseEntity<Map> indStatistics(@RequestParam(value = "itemDateFrom") String itemDateFrom,
                                             @RequestParam(value = "itemDateTo") String itemDateTo,
                                             @RequestParam(value = "dept", required = false) String dept) {
        return ResponseEntity.ok(bmiDeductionService.indStatistics(itemDateFrom, itemDateTo, dept, LoginContext.getHospitalId()));
    }

    /**
     * 违规Top10 (规则/项目排名)
     *
     * @return
     */
    @GetMapping("/statistics/getViolationTop")
    public ResponseEntity<List<Map>> genViolationTop(@RequestParam(value = "itemDateFrom") String itemDateFrom,
                                                     @RequestParam(value = "itemDateTo") String itemDateTo,
                                                     @RequestParam(value = "dept", required = false) String dept,
                                                     @RequestParam(value = "orderBy", defaultValue = "rules") String orderBy) {
        return ResponseEntity.ok(bmiDeductionService.genViolationTop(itemDateFrom, itemDateTo, dept, orderBy, LoginContext.getHospitalId()));
    }

    /**
     * 统计分析	 （科室/医生维度）
     *
     * @return
     */
    @GetMapping("/statistics/getAnalysisDetails")
    public ResponseEntity<List<Map>> getAnalysisDetails(@RequestParam(value = "itemDateFrom") String itemDateFrom,
                                                        @RequestParam(value = "itemDateTo") String itemDateTo,
                                                        @RequestParam(value = "dept", required = false) String dept,
                                                        @RequestParam(value = "orderBy", defaultValue = "dept") String orderBy) {
        return ResponseEntity.ok(bmiDeductionService.getAnalysisDetails(itemDateFrom, itemDateTo, dept, orderBy, LoginContext.getHospitalId()));
    }

    /**
     *  扣费时间趋势
     *
     * @return
     */
    @GetMapping("/statistics/byMonth")
    public ResponseEntity<List<Map>> getAnalysisByMonth(@RequestParam(value = "itemDateFrom") String itemDateFrom,
                                                        @RequestParam(value = "itemDateTo") String itemDateTo,
                                                        @RequestParam(value = "dept", required = false) String dept) {
        return ResponseEntity.ok(bmiDeductionService.getAnalysisByMonth(itemDateFrom, itemDateTo, dept,LoginContext.getHospitalId()));
    }


    /**
     * 导出医保扣费
     * @param response   响应
     * @param bmiQueryVo 请求参数
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody BmiQueryVo bmiQueryVo) {
        bmiDeductionService.export(response, bmiQueryVo);
    }

}

