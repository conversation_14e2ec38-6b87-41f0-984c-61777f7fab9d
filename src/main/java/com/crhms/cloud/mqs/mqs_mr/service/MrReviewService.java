package com.crhms.cloud.mqs.mqs_mr.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.excel.dto.ExcelExport;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.user.dto.CustomUser;
import com.crhms.cloud.core.utils.ApplicationContextUtil;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDiag;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalStats;
import com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper;
import com.crhms.cloud.mqs.basic.mapper.BaseMedicalStatsMapper;
import com.crhms.cloud.mqs.basic.mapper.HpDiagMapper;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.basic.vo.BaseCaseQueryVO;
import com.crhms.cloud.mqs.mqs_mr.domain.*;
import com.crhms.cloud.mqs.mqs_mr.enums.MrRuleEnum;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrReviewMapper;
import com.crhms.cloud.mqs.mqs_mr.vo.*;
import com.crhms.cloud.mqs.sys.config.SysParamConfig;
import com.crhms.cloud.mqs.sys.domain.IppZone;
import com.crhms.cloud.mqs.sys.domain.SysLogs;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.service.IppService;
import com.crhms.cloud.mqs.sys.service.SysLogsService;
import com.crhms.cloud.mqs.sys.utils.DictUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 住院审核-每日记账审核(HpBk)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 09:48:16
 */
@Slf4j
@Service("MaReviewService")
public class MrReviewService {
    @Autowired
    private MrReviewMapper mrReviewMapper;
    @Autowired
    private BaseMedicalMapper baseMedicalMapper;
    @Autowired
    private MrPlanService mrPlanService;
    @Autowired
    private MrLogService mrLogService;
    @Autowired
    private HpDiagMapper hpDiagMapper;
    @Autowired
    private MrAuditService mrAuditService;
    @Autowired
    private IppService ippService;
    @Autowired
    private SysLogsService sysLogsService;
    @Autowired
    private MrCaseService mrCaseService;
    @Autowired
    private BaseMedicalStatsMapper baseMedicalStatsMapper;

    /**
     * 分页查询人工审核主单
     *
     * @param queryVO    筛选条件
     * @param page       分页对象
     * @param hospitalId
     * @return 查询结果
     */
    public List<BaseMedicalCase> queryByPage(BaseCaseQueryVO queryVO, Page page, String hospitalId) {
        //获取人工审核配置权限
        boolean authResult = mrPlanService.getAuth(hospitalId,queryVO);
        if(!authResult){
            return Collections.emptyList();
        }
        //查询mqs_mr_base主单信息
        List<BaseMedicalCase> maReviews = mrReviewMapper.queryMrCaseListByPage(queryVO, page,hospitalId);
        //查询最高违规级别
        if(CollectionUtil.isNotEmpty(maReviews)){
            //获取全部主单单据号
            List<String> nos = maReviews.stream().map(mCase-> mCase.getNo()).collect(Collectors.toList());
            Map<String,List<BaseMedicalDetail>> detailMap = null;
            List<BaseMedicalDetail> details = mrReviewMapper.queryMinRuleTypeByNo(nos,hospitalId);
            if(CollectionUtil.isNotEmpty(details)){
                detailMap = details.stream().collect(Collectors.groupingBy(BaseMedicalDetail::getNo));
            }
            //遍历转换特殊字段
            for(BaseMedicalCase baseMedicalCase : maReviews){
                convert (baseMedicalCase,detailMap);
            }
            //字典翻译
            DictUtils.translateDict(maReviews);
        }
        return maReviews;
    }

    /**
     * 查询人工审核详情- 费用明细
     * @param baseDetailQueryVO
     * @param page
     * @return
     */
    public MrMedicalDetailVO queryBaseMedicalDetail(MrDetailQueryVO baseDetailQueryVO, Page page) {
        // ------------------------------------查询主单字段的信息--------------------------------------------
        String hospitalId  = LoginContext.getHospitalId();
        String no = baseDetailQueryVO.getNo();
        MrMedicalDetailVO mrMedicalDetailVO = queryBaseMedicalCaseByNo(no,hospitalId);
        if(Objects.isNull(mrMedicalDetailVO)){
            return null;
        }
        //字典翻译
        DictUtils.translateDict(mrMedicalDetailVO);
        // ------------------------------------查询明细字段的信息并对特殊字段进行转换--------------------------------------------
        List<MrMedicalDetail> details = queryBaseMedicalDetails (baseDetailQueryVO,page,hospitalId);
        //字典翻译
        DictUtils.translateDict(details);
        mrMedicalDetailVO.setDetails(details);
        return mrMedicalDetailVO;
    }

    /*
    * 查询人工审核详情- 费用明细- 按照项目分组
    * */
    public MrMedicalDetailVO queryBaseMedicalDetailByItemIds (MrDetailQueryVO baseDetailQueryVO, Page page) {
        // ------------------------------------查询主单字段的信息--------------------------------------------
        String hospitalId  = LoginContext.getHospitalId();
        String no = baseDetailQueryVO.getNo();
        MrMedicalDetailVO mrMedicalDetailVO = queryBaseMedicalCaseByNo(no,hospitalId);
        if(Objects.isNull(mrMedicalDetailVO)){
            return null;
        }
        //字典翻译
        DictUtils.translateDict(mrMedicalDetailVO);
        // ------------------------------------查询明细字段的信息并对特殊字段进行转换--------------------------------------------
        List<MrMedicalDetail> detailItemIds = mrReviewMapper.queryMrDetailByItemId(MrCase.BASE_TABLE,baseDetailQueryVO,page,hospitalId);
        if(CollectionUtil.isEmpty(detailItemIds)){
            return mrMedicalDetailVO;
        }
        //获取itemIds + detailNos
        List<String> groupItemIds = detailItemIds.stream().map(MrMedicalDetail::getItemId).collect(Collectors.toList());
        List<String> groupDetailNos = detailItemIds.stream().map(MrMedicalDetail::getDetailNo).collect(Collectors.toList());
        baseDetailQueryVO.setDetailNos(groupDetailNos);

        List<MrMedicalDetail> details = queryBaseMedicalDetails (baseDetailQueryVO,page,hospitalId);
        if(CollectionUtil.isNotEmpty(details)){
            //字典翻译(父明细)
            DictUtils.translateDict(details);
            //获取项目ids
            //根据项目ids重新获取明细
            baseDetailQueryVO.setDetailNos(null);
            baseDetailQueryVO.setItemIds(groupItemIds);
            List<MrMedicalDetail> childDetails = queryBaseMedicalDetails (baseDetailQueryVO,null,hospitalId);
            if(CollectionUtil.isNotEmpty(childDetails)){
                //字典翻译（子明细）
                DictUtils.translateDict(childDetails);
                Map<String,List<MrMedicalDetail>> childDetailMap = childDetails.stream().collect(Collectors.groupingBy(MrMedicalDetail::getItemId));
                for(MrMedicalDetail detail : details){
                    //根据明细对特殊字段进行处理
                    List<MrMedicalDetail> fileterDetails = childDetailMap.get(detail.getItemId());
                    if(CollectionUtil.isNotEmpty(fileterDetails) && fileterDetails.size() != 1){
                        MrDetailAggregation result = processDetailList(fileterDetails);
                        detail.setDetailNum(String.valueOf(fileterDetails.size()));      //如果不为空则对当前detail进行重新赋值、
                        detail.setViolationFlag(result.getViolationFlag());              //是否违规
                        detail.setViolationFlagCh(result.getViolationFlagCh());
                        detail.setSelfExpense(result.getSelfExpense());                  //自费状态
                        detail.setSelfExpenseCh(result.getSelfExpenseCh());
                        detail.setAuditSource(result.getAuditSource());                  //审核渠道
                        detail.setAuditSourceName(result.getAuditSourceName());
                        detail.setClinicalStatus(result.getClinicalStatus());            //临床处理
                        detail.setRuleType(result.getRuleType());                        //违规级别
                        detail.setRuleTypeName(result.getRuleTypeName());                //规则级别名称
                        detail.setRuleNames(result.getRuleNames());                      //规则名称
                        detail.setRuleReasons(result.getRuleReasons());                  //违规原因
                        detail.setItemDate(null);                                        //项目时间
                        detail.setReasonTypes(null);                                     //反馈类型
                        detail.setNumbers(result.getNumbers());                          //数量
                        detail.setCosts(result.getCosts());                              //金额
                        detail.setCategoryName(result.getCategoryName());                //甲乙类
                        detail.setOutpatientMedication(result.getOutpatientMedication()); //是否出院带药
                        detail.setRouteAdministration(result.getRouteAdministration());  //用药途径
                        detail.setChild(fileterDetails);
                    }
                }
            }
        }
        mrMedicalDetailVO.setDetails(details);
        return mrMedicalDetailVO;
    }

    /**
     * 转违规
     * @param mrChangeDTO
     * @param hospitalId
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeViolationFlag(MrChangeDTO mrChangeDTO, String hospitalId) {
        //首先验证该条人工违规是否已经存在于mqs_mr_audit表，如果已经存在旧纪录则不允许进行新增操作
        if(Objects.nonNull(mrChangeDTO)){
            //验证转违规的明细的本身违规状态（这样就能排除又审一遍且变成了机审违规）
            List<String> detailNos = mrChangeDTO.getDetailNos();
            List<BaseMedicalDetail> details = baseMedicalMapper.queryAuditDetailByDetailNo(SysParamConfig.BASE_TABLE,detailNos);
            //验证人工违规表也不存在当前记录
            List<MrAudit> mrAudits = mrAuditService.list(new LambdaQueryWrapper<MrAudit>().in(MrAudit::getDetailNo,detailNos).eq(MrAudit::getHospitalId,hospitalId));
            //如果存在违规状态等于1的则证明已经被机身违规，则此次提交被禁止
            boolean result = details.stream().anyMatch(detail -> "1".equals(detail.getViolationFlag())) || mrAudits.size() != 0;
            boolean selfResult = details.stream().anyMatch(detail -> "1".equals(detail.getSelfExpense()));
            if(result){
                throw new BaseException("当前所选明细存在机审违规项或已存在人工违规记录，此次转违规操作失败！！！");
            } else if (selfResult) {
                throw new BaseException("当前所选明细为自费状态，禁止转违规操作！！！");
            }else {
                List<MrAudit> mrAudit = new ArrayList<>(20);
                detailNos.forEach(detailNo -> {
                    String ruleCode = MrRuleEnum.getRuleCode(mrChangeDTO.getRuleType());

                    mrAudit.add(MrAudit.builder().no(mrChangeDTO.getNo())
                            .detailNo(detailNo)
                            .admissionNo(mrChangeDTO.getAdmissionNo())
                            .ruleType(mrChangeDTO.getRuleType())
                            .ruleTypeName(mrChangeDTO.getRuleTypeName())
                            .ruleCode(ruleCode)
                            .ruleName("人工-"+mrChangeDTO.getRuleTypeName())
                            .violationFlag("1")
                            .ruleReason(mrChangeDTO.getMrOpinion())
                            .hospitalId(hospitalId).build());
                });
                mrAuditService.saveBatch(mrAudit);
                //改变人工审核状态   mqs_mr_case
                mrChangeDTO.setMrStatus("1");
                reviewPass(Arrays.asList(mrChangeDTO),hospitalId,false);

                //异步更新人工质控临床处理状态
                mrCaseService.updateCaseClinicalStatus(Arrays.asList(mrChangeDTO.getNo()),hospitalId);
                //记录操作日志
                try{
                    sendLogs (detailNos,null,hospitalId,"mr","（批量）转违规项");
                } catch (Exception e) {
                    log.info("异常信息：{}，（批量）转违规项日志记录失败！！！",e);
                }
            }
        }
    }

    /**
     * 撤销转违规
     * @param detailNos
     * @param hospitalId
     */
    @Transactional(rollbackFor = Exception.class)
    public void unDoChangeViolationFlag (List<String> detailNos, String hospitalId) {
        if(CollectionUtil.isNotEmpty(detailNos)){
            List<MrAudit> mrAudits = mrAuditService.list(new LambdaQueryWrapper<MrAudit>().in(MrAudit::getDetailNo,detailNos).eq(MrAudit::getHospitalId,hospitalId));
            boolean result = CollectionUtil.isEmpty(mrAudits) || detailNos.size() != mrAudits.size();
            if(result){
                throw new BaseException("当前所选明细有未转人工违规记录，此次撤销操作失败！！！");
            } else {
                mrAuditService.removeByIds(mrAudits);
                //改变人工审核状态   mqs_mr_case
                MrAudit mrAudit = mrAudits.get(0);
                unDoReviewPass(mrAudit.getNo(),mrAudit.getAdmissionNo(),hospitalId,false);

                //异步更新人工质控临床处理状态
                mrCaseService.updateCaseClinicalStatus(Arrays.asList(mrAudit.getNo()),hospitalId);
                //记录操作日志
                try{
                    sendLogs (detailNos,null,hospitalId,"mr","（批量）撤销转违规项");
                } catch (Exception e) {
                    log.info("异常信息：{}，（批量）撤销转违规项日志记录失败！！！",e);
                }
            }
        }
    }

    /**
    * 审核
    * @param mrChangeDTOs
     */
    @Transactional(rollbackFor = Exception.class)
    public void reviewPass (List<MrChangeDTO> mrChangeDTOs,String hospitalId,boolean flag) {
        CustomUser user = LoginContext.getUser();
        String userName = user.getUsername();
        String realName = user.getName();
        if(CollectionUtil.isNotEmpty(mrChangeDTOs)){
            // 将当前主单的审核状态改为审核通过
            mrChangeDTOs.forEach(mrChangeDTO ->{
                mrReviewMapper.updateReviewData(mrChangeDTO.getNo(),mrChangeDTO.getAdmissionNo(),mrChangeDTO.getMrStatus(),mrChangeDTO.getLables(),mrChangeDTO.getMrOpinion(),hospitalId,userName,realName);
                if(flag){
                    //记录操作日志
                    try{
                        String lables = mrChangeDTO.getLables();
                        String no = mrChangeDTO.getNo();
                        if(StringUtils.isNotBlank(lables) && "1".equals(lables) ){
                            sendLogs (null,no,hospitalId,"mr","标记");
                        } else if (StringUtils.isNotBlank(lables) && "0".equals(lables)) {
                            sendLogs (null,no,hospitalId,"mr","取消标记");
                        } else {
                            sendLogs (null,no,hospitalId,"mr","审核完成");
                        }
                    } catch (Exception e) {
                        log.info("异常信息：{}，日志记录失败！！！",e);
                    }
                }
            });
        }
    }

    /**
    *  撤销审核
     * @param
     */
    public void unDoReviewPass (String no,String admissionNo,String hospitalId,boolean flag) {
        CustomUser user = LoginContext.getUser();
        String userName = user.getUsername();
        String realName = user.getName();
        //首先查询单据是否存在人工审核违规记录，不存在则人工审核状态改为0 未审核，存在则改为1 已审核
        List<MrAudit> mrAudits = mrAuditService.list(new LambdaQueryWrapper<MrAudit>().eq(MrAudit::getNo,no).eq(MrAudit::getAdmissionNo,admissionNo).eq(MrAudit::getHospitalId,hospitalId));
        if(CollectionUtil.isEmpty(mrAudits)){
            mrReviewMapper.updateReviewData(no,admissionNo,"0",null,null,hospitalId,userName,realName);
        } else {
            mrReviewMapper.updateReviewData(no,admissionNo,"1",null,null,hospitalId,userName,realName);
        }

        if(flag){
            //记录操作日志
            try{
                sendLogs (null,no,hospitalId,"mr","撤销审核完成");
            } catch (Exception e) {
                log.info("异常信息：{}，撤销审核完成日志记录失败！！！",e);
            }
        }
    }

    /*
     *  查询人工审核明细信息
     * */
    public List<MrMedicalDetail> queryBaseMedicalDetails (MrDetailQueryVO baseDetailQueryVO, Page page,String hospitalId) {
        //获取明细列表   自费（0-医保，1-自费 2-转自费）/ 是否自费（0-否，1-是，2-转自费）
        List<MrMedicalDetail> details = mrReviewMapper.queryMrDetailByPage(MrCase.BASE_TABLE,baseDetailQueryVO,page,hospitalId);
        if(CollectionUtil.isNotEmpty(details)){
            Map<String,String> meAuditMap = null;
            Map<String,MrMedicalDetail> detailClinicalMap = null;
            List<String> detailNos = details.stream().map(detail -> detail.getDetailNo()).collect(Collectors.toList());
            //查询明细的最高违规级别
            List<MrAudit> mrAudits = mrAuditService.list(new LambdaQueryWrapper<MrAudit>().eq(MrAudit::getHospitalId,hospitalId).in(MrAudit::getDetailNo,detailNos));
            //查询明细的审核渠道以及违规级别
            List<MrMedicalDetail> detailClinicals = mrReviewMapper.queryDetailSourceAndClinical(detailNos,hospitalId);
            if(CollectionUtil.isNotEmpty(mrAudits)){
                meAuditMap = mrAudits.stream().collect(Collectors.toMap(MrAudit::getDetailNo,MrAudit::getRuleType));
            }
            if(CollectionUtil.isNotEmpty(detailClinicals)){
                detailClinicalMap = detailClinicals.stream().collect(Collectors.toMap(MrMedicalDetail::getDetailNo,detail->detail));
            }
            //遍历赋值特殊字段
            for(MrMedicalDetail detail : details){
                //违规级别 + 是否违规
                if(Objects.nonNull(meAuditMap)){
                    String reuleType = meAuditMap.get(detail.getDetailNo());
                    if(StringUtils.isNotBlank(reuleType)){
                        detail.setRuleType(reuleType);
                        detail.setViolationFlag("1");
                    }
                }
                //审核渠道 + 临床处理 + 违规原因
                MrMedicalDetail detailClinical = detailClinicalMap.get(detail.getDetailNo());
                if(Objects.nonNull(detailClinical)){
                    detail.setAuditSource(detailClinical.getAuditSource());
                    detail.setClinicalStatus(detailClinical.getClinicalStatus());
                    detail.setRuleReasons(detailClinical.getRuleReasons());
                    detail.setRuleTypeName(detailClinical.getRuleTypeName());
                    detail.setRuleNames(detailClinical.getRuleNames());
                }
                //明细个数
                detail.setDetailNum("1");
            }
        }
        return details;
    }

    /*
     *  根据单据号no查询主单信息
     *
     * */
    public MrMedicalDetailVO queryBaseMedicalCaseByNo (String no,String hospitalId) {
        //获取主单信息
        BaseMedicalCase baseMedicalCase = mrReviewMapper.queryMrCase(no,hospitalId);
        if(Objects.isNull(baseMedicalCase)){
            return null;
        }
        //查询预出院日期以及预出院状态
        BaseMedicalStats baseMedicalStats = baseMedicalStatsMapper.selectOne(new LambdaQueryWrapper<BaseMedicalStats>().eq(BaseMedicalStats::getNo,no).eq(BaseMedicalStats::getHospitalId,hospitalId));
        if(Objects.nonNull(baseMedicalStats)){
            baseMedicalCase.setPreDischarge(baseMedicalStats.getPreDischarge());
            baseMedicalCase.setPreDischargeDate(baseMedicalStats.getPreDischargeDate());
        }
        //违规来源 (查询人工违规和机审违规)
        Long mrAuditCount = mrAuditService.getBaseMapper().selectCount(new LambdaQueryWrapper<MrAudit>().eq(MrAudit::getNo,no).eq(MrAudit::getHospitalId,hospitalId));
        List<BaseMedicalCase> mrBaseAudits = mrReviewMapper.queryMrBaseAuditByNo(no,hospitalId);
        if(mrAuditCount > 0 && CollectionUtil.isNotEmpty(mrBaseAudits)){
            baseMedicalCase.setAuditSource("1,2");
        } else if (mrAuditCount > 0 && CollectionUtil.isEmpty(mrBaseAudits)) {
            baseMedicalCase.setAuditSource("2");
        } else if (mrAuditCount == 0 && CollectionUtil.isNotEmpty(mrBaseAudits)) {
            baseMedicalCase.setAuditSource("1");
        } else if (mrAuditCount == 0 && CollectionUtil.isEmpty(mrBaseAudits)) {
            baseMedicalCase.setAuditSource("1");
        } else {
            baseMedicalCase.setAuditSource(null);
        }
        String admissionNo = baseMedicalCase.getAdmissionNo();
        //转换特殊字段
        convert(baseMedicalCase,null);
        MrMedicalDetailVO mrMedicalDetailVO = BeanUtil.copyProperties(baseMedicalCase,MrMedicalDetailVO.class);
        //获取诊断信息
        List<BaseMedicalDiag> medicalDiags = hpDiagMapper.queryDiagList(admissionNo,no,hospitalId);
        if(CollectionUtil.isNotEmpty(medicalDiags)){
            //获取全部出院诊断
            //主诊断在最前边，然后按照diagnosisOrder  诊断排序号进行倒序排列
            String result = medicalDiags.stream()
                    .filter(medicalDiag-> "2".equals(medicalDiag.getDiagInoutType()))
                    .sorted(Comparator.comparing(BaseMedicalDiag::getMainFlag).reversed()
                            .thenComparing(Comparator.comparing(BaseMedicalDiag::getDiagnosisOrder).reversed()))
                    .map(BaseMedicalDiag::getDiagnosisName)
                    .collect(Collectors.joining("-"));
            mrMedicalDetailVO.setDiagnosisName(result);
        }
        //审核状态
        mrMedicalDetailVO.setMrStatus(StringUtils.isBlank(mrMedicalDetailVO.getMrStatus())?"0":mrMedicalDetailVO.getMrStatus());
        return mrMedicalDetailVO;
    }

    /*
     *  转换类（用来处理列表以及明细的某些特殊字段的值）
     *  @param baseMedicalCase 主单信息
     * */
    public void convert (BaseMedicalCase baseMedicalCase,Map<String,List<BaseMedicalDetail>> detailMap) {
        //标记
        if(StringUtils.isBlank(baseMedicalCase.getLables())){
            baseMedicalCase.setLables("0");
        }
        //机审场景
        if(StringUtils.isNotBlank(baseMedicalCase.getSourceId())){
            String auditScenario = AuditScenarioEnum.valueOf(baseMedicalCase.getSourceId()).getDesc();
            baseMedicalCase.setAuditScenario(auditScenario);
        }
        //年龄
        baseMedicalCase.setAge(DateUtil.age(DateUtil.parse(baseMedicalCase.getPatientBirthday()), DateUtil.date(baseMedicalCase.getAdmissionDate())));
        //最高违规级别
        if(Objects.nonNull(detailMap) && CollectionUtil.isNotEmpty(detailMap.get(baseMedicalCase.getNo()))){
            BaseMedicalDetail baseMedicalDetail = detailMap.get(baseMedicalCase.getNo()).get(0);
            String ruleType = baseMedicalDetail.getRuleType();
            String ruleTypeName = baseMedicalDetail.getRuleTypeName();
            if(StringUtils.isNotBlank(ruleType)){
                baseMedicalCase.setRuleType(Integer.valueOf(ruleType));
                baseMedicalCase.setRuleTypeName(ruleTypeName);
                baseMedicalCase.setViolationFlag("1");
            } else {
                baseMedicalCase.setRuleType(null);
                baseMedicalCase.setViolationFlag("0");
            }
        }
        //是否今日出院
        Date dischargeDate = baseMedicalCase.getDischargeDate();
        boolean result = false;
        if(Objects.nonNull(dischargeDate)){
            //如果出院时间不为空则判断出院时间是否是当天
            result = DateUtil.isSameDay(dischargeDate,new Date());
        } else {
            //如果出院时间为空则按照与出院时间来进行过滤
            Date preDischargeDate = baseMedicalCase.getPreDischargeDate();
            if(Objects.isNull(preDischargeDate)){
                result = false;
            } else {
                result = DateUtil.isSameDay(preDischargeDate,new Date());
            }
        }
        baseMedicalCase.setDischargeToday(result?"1":"0");
        //在院状态  (1-出院，0-在院) （根据出院时间判断，有出院时间则为出院，无则为在院）
        boolean dischargeState = Objects.nonNull(baseMedicalCase.getDischargeDate());
        //入院天数
        Date admissionDate = Objects.nonNull(baseMedicalCase.getAdmissionDate()) ? baseMedicalCase.getAdmissionDate() : new Date();
        long days = 0;
        if(dischargeState){
            //已出院
            days = DateUtil.betweenDay(baseMedicalCase.getDischargeDate(),admissionDate,true) + 1;
        } else {
            // 在院
            days = DateUtil.betweenDay(new Date(),admissionDate,true) + 1;
        }
        baseMedicalCase.setInhospitalDays(String.valueOf(days));
    }

    /*
     * 聚合明细各项属性
     * */
    public MrDetailAggregation processDetailList(List<MrMedicalDetail> fileterDetails) {
        //聚合结果
        return  fileterDetails.stream()
                .map(bean -> new MrDetailAggregation(
                        bean.getViolationFlag(),
                        bean.getViolationFlagCh(),
                        bean.getSelfExpense(),
                        bean.getSelfExpenseCh(),
                        bean.getAuditSource(),
                        bean.getAuditSourceName(),
                        bean.getClinicalStatus(),
                        bean.getRuleType(),
                        bean.getRuleTypeName(),
                        bean.getRuleReasons(),
                        bean.getNumbers(),
                        bean.getCosts(),
                        bean.getRuleNames(),
                        bean.getCategoryName(),
                        bean.getOutpatientMedication(),
                        bean.getRouteAdministration()
                ))
                .reduce(new MrDetailAggregation("0","不违规", "","","","","","","","",BigDecimal.ZERO,BigDecimal.ZERO,"","","",""), (acc, curr) -> {
                    // 判断是否违规
                    Set<String> codes = new HashSet<>();
                    codes.add(acc.getViolationFlag());
                    codes.add(curr.getViolationFlag());
                    boolean containsCode = codes.contains("1");
                    String violationFlagCh = "违规".equals(acc.getViolationFlagCh()) || "违规".equals(curr.getViolationFlagCh()) ? "违规" : "未违规";
                    //拼装自费状态
                    String selfExpense = String.join("/", mergeStringArrays(acc.getSelfExpense(),curr.getSelfExpense(),"/"));
                    String selfExpenseCh = String.join("/", mergeStringArrays(acc.getSelfExpenseCh(),curr.getSelfExpenseCh(),"/"));
                    //审核渠道
                    String auditSource = String.join("、", mergeStringArrays(acc.getAuditSource(),curr.getAuditSource(),"、"));
                    String auditSourceName = String.join("、", mergeStringArrays(acc.getAuditSourceName(),curr.getAuditSourceName(),"、"));
                    //临床处理
                    String clinicalStatus = String.join("/", mergeStringArrays(acc.getClinicalStatus(),curr.getClinicalStatus(),"/"));
                    // 违规级别 + 违规级别说明
                    String ruleType = "";
                    String ruleTypeName = acc.getRuleTypeName();
                    if (!StringUtils.isBlank(acc.getRuleType()) || !StringUtils.isBlank(curr.getRuleType())) {
                        int accRuleType = StringUtils.isBlank(acc.getRuleType()) ? Integer.MAX_VALUE : Integer.parseInt(acc.getRuleType());
                        int currRuleType = StringUtils.isBlank(curr.getRuleType()) ? Integer.MAX_VALUE : Integer.parseInt(curr.getRuleType());
                        if (currRuleType < accRuleType) {
                            ruleType = String.valueOf(currRuleType);
                            ruleTypeName = curr.getRuleTypeName();
                        } else {
                            ruleType = String.valueOf(accRuleType);
                        }
                    }
                    //规则名称
                    String ruleNames = String.join(";", mergeStringArrays(acc.getRuleNames(),curr.getRuleNames(),";"));
                    // 违规原因
                    String ruleReasons = String.join(";", mergeStringArrays(acc.getRuleReasons(),curr.getRuleReasons(),";"));
                    //数量
                    BigDecimal numbers = acc.getNumbers().add(curr.getNumbers());
                    //金额
                    BigDecimal costs = acc.getCosts().add(curr.getCosts());
                    //甲乙类
                    String categoryName = String.join("/", mergeStringArrays(acc.getCategoryName(),curr.getCategoryName(),"/"));
                    //是否出院带药
                    String outpatientMedication = String.join("/", mergeStringArrays(acc.getOutpatientMedication(),curr.getOutpatientMedication(),"/"));
                    //用药途径
                    String  routeAdministration = String.join("/", mergeStringArrays(acc.getRouteAdministration(),curr.getRouteAdministration(),"/"));

                    return new MrDetailAggregation(
                            containsCode ? "1" : "0",
                            violationFlagCh,
                            selfExpense,
                            selfExpenseCh,
                            auditSource,
                            auditSourceName,
                            clinicalStatus,
                            ruleType,
                            ruleTypeName,
                            ruleReasons,
                            numbers,
                            costs,
                            ruleNames,
                            categoryName,
                            outpatientMedication,
                            routeAdministration
                    );
                }, (left, right) -> left);
    }

    /*
     * 抽取的公共数据处理方法
     * */
    private Set<String> mergeStringArrays(String str1, String str2, String separator) {
        return Stream.concat(
                        Arrays.stream(Optional.ofNullable(str1).orElse("").split(separator)),
                        Arrays.stream(Optional.ofNullable(str2).orElse("").split(separator))
                )
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }


    /**
     * 异步记录日志
     * */
    public void sendLogs (List<String> detailNos,String no,String hospitalId,String operatorSource,String operatorText) {
        CustomUser customUser = LoginContext.getUser();
        String operatorId = String.valueOf(customUser.getUsername());
        String operatorName = customUser.getName();
        ThreadPoolTaskExecutor threadPoolTaskExecutor = ApplicationContextUtil.getBean(ThreadPoolTaskExecutor.class);
        CompletableFuture.runAsync(() -> {
            List<SysLogs> logs = new ArrayList<>();
            //如果有明细就查明细，没有就查主单信息
            if(StringUtils.isBlank(no)) {
                List<MrMedicalDetail> details = mrReviewMapper.queryMrDetailByDetailNos(SysParamConfig.BASE_TABLE,detailNos, hospitalId);
                if (CollectionUtil.isEmpty(details)) {
                    return;
                }
                MrMedicalDetail detail = details.get(0);
                String orderNo = detail.getNo();
                String orderAdmissionNo = detail.getAdmissionNo();
                BaseMedicalCase baseMedicalCase = mrReviewMapper.queryMrCase(orderNo,hospitalId);
                for(MrMedicalDetail mrMedicalDetail : details){
                    logs.add(SysLogs.builder()
                            .operatorId(operatorId)
                            .operatorName(operatorName)
                            .operatorTime(new Date())
                            .operatorText(operatorText)
                            .admissionNo(orderAdmissionNo)
                            .patientId(baseMedicalCase.getPatientId())
                            .patientName(baseMedicalCase.getPatientName())
                            .detailNo(mrMedicalDetail.getDetailNo())
                            .itemId(mrMedicalDetail.getItemId())
                            .itemName(mrMedicalDetail.getItemName())
                            .itemDate(mrMedicalDetail.getItemDate())
                            .operatorSource(operatorSource)
                            .hospitalId(hospitalId).build());
                }
            } else {
                BaseMedicalCase baseMedicalCase = mrReviewMapper.queryMrCase(no,hospitalId);
                logs.add(SysLogs.builder()
                        .operatorId(operatorId)
                        .operatorName(operatorName)
                        .operatorTime(new Date())
                        .operatorText(operatorText)
                        .admissionNo(baseMedicalCase.getAdmissionNo())
                        .patientId(baseMedicalCase.getPatientId())
                        .patientName(baseMedicalCase.getPatientName())
                        .operatorSource(operatorSource)
                        .hospitalId(hospitalId).build());
            }
            sysLogsService.recordLogs(logs);
        },threadPoolTaskExecutor);
    }

































    /**
     * 查询审核主单详情
     * @param no
     * @param auditScenario
     * @param hospitalId
     * @return
     */
    public BaseMedicalCase queryBaseMedicalCase(String no, String auditScenario, String hospitalId) {
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        BaseMedicalCase baseMedicalCase = mrReviewMapper.queryMrBaseMedicalCase(tableName,no,auditScenario,hospitalId);
        if(!Objects.isNull(baseMedicalCase.getAdmissionDate())){
            if(StrUtil.isNotEmpty(baseMedicalCase.getPatientBirthday())){
                String patientBirthday = baseMedicalCase.getPatientBirthday();
                DateTime parse = DateUtil.parse(patientBirthday, "yyyy-MM-dd");
                baseMedicalCase.setAge(MqsUtils.getAge(parse,baseMedicalCase.getAdmissionDate()));
            }
        }
        if(baseMedicalCase.getIsDischarge() == null){
            baseMedicalCase.setIsDischarge(auditScenario.contains("op") ? "2" : baseMedicalCase.getDischargeDate() == null ? "0" : "1");
        }

        //补充获取单据下的出院诊断
        List<BaseMedicalDiag> medicalDiagList = hpDiagMapper.queryMedicalDiags(BaseMedicalDiag.builder().hospitalId(hospitalId).no(no).diagInoutType("2").build());
        Set<String> collect = medicalDiagList.stream().map(BaseMedicalDiag::getDiagnosisName).collect(Collectors.toSet());
        if(CollectionUtil.isNotEmpty(collect)){
            baseMedicalCase.setSecondaryDiseaseZh(StringUtils.join(collect, "-"));
        }

        DictUtils.translateDict(baseMedicalCase);
        return baseMedicalCase;
    }

    /**
     * 人工审核
     *
     * @param auditScenario;
     * @param mrStatus
     * @param mrOpinion
     * @param mrList
     */
    @Transactional
    public void review(String auditScenario, String mrStatus, String mrOpinion, List<Map> mrList) {
        String hospitalId = LoginContext.getHospitalId();
        if (CollectionUtil.isEmpty(mrList)) {
            throw new BaseException("请选择审批的单据！");
        }
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        List<Long> ids = mrList.stream().map(e -> Long.valueOf((String) e.get("id"))).collect(Collectors.toList());
        //更新人工审核到业务字段
        //mrReviewMapper.updateReviewData(tableName, ids, mrStatus, mrOpinion, hospitalId);
        //插入到更新日志
        ArrayList<MrLog> logs = new ArrayList<>();
        for (Map mrReview : mrList) {
            logs.add(MrLog.builder().mrTime(new Date()).no((String) mrReview.get("no")).admissionNo((String) mrReview.get("admissionNo")).mrOpinion(mrOpinion).mrStatus(mrStatus).userId(Long.toString(LoginContext.getUser().getId())).userName(LoginContext.getUser().getName() + "(" + LoginContext.getUser().getLoginname() + ")").hospitalId(hospitalId).build());
        }
        mrLogService.saveBatch(logs, 1000);
    }


    public List<MrLog> queryLog(String no, String admissionNo, String userId, String userName, Page page) {
        return mrReviewMapper.queryLogByPage(no, admissionNo, userId, userName, LoginContext.getHospitalId(), page);
    }


    /**
     * 导出excel
     *
     * @param response 响应
     * @param queryVO  筛选条件
     */
    public void exportExcel(HttpServletResponse response, BaseCaseQueryVO queryVO) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        // 查询数据
        List<BaseMedicalCase> list = queryByPage(queryVO, null, LoginContext.getHospitalId());
        List<IppZone> ippZones = ippService.selectZoneList(null, LoginContext.getHospitalId());
        Map<String, String> collect = ippZones.stream().collect(Collectors.toMap(IppZone::getCode, IppZone::getName, (key1, key2) -> key2));
        for (BaseMedicalCase x : list) {
            x.setOutZoneName(collect.get(x.getOutZoneCode()));
        }

        // 文件名称: 人工审核-挂号审核(20230220_20230320)-20230320100744
        StringBuilder fileName = new StringBuilder();

        // 组装数据
        switch (queryVO.getAuditScenario()) {
            case "opPt":
                // 门诊缴费审核
                exportOpPt(heads, datas, list);
                fileName.append("人工审核-缴费审核");
                break;
            case "opPct":
                // 门诊处方审核
                exportOpPct(heads, datas, list);
                fileName.append("人工审核-处方审核");
                break;
            case "opRg":
                // 门诊挂号审核
                exportOpRg(heads, datas, list);
                fileName.append("人工审核-挂号审核");
                break;
            case "hpRg":
                // 住院登记审核
                exportHpRg(heads, datas, list);
                fileName.append("人工审核-住院登记审核");
                break;
            case "hpDo":
                // 医嘱审核
                exportHpDo(heads, datas, list);
                fileName.append("人工审核-医嘱审核");
                break;
            case "hpBk":
                // 每日记账审核
                exportHpBk(heads, datas, list);
                fileName.append("人工审核-每日记账审核");
                break;
            case "hpTf":
                // 转科室审核
                exportHpTf(heads, datas, list);
                fileName.append("人工审核-转科室审核");
                break;
            case "hpPred":
                // 预出院审核
                exportHpPred(heads, datas, list);
                fileName.append("人工审核-预出院审核");
                break;
            case "hpOut":
                // 出院审核
                exportHpOut(heads, datas, list);
                fileName.append("人工审核-出院审核");
                break;
            case "hpSettle":
                // 结算审核
                exportSettle(heads, datas, list);
                fileName.append("人工审核-结算审核");
                break;
            default:
                throw new BaseException("不存在的审核场景！");
        }

        MqsUtils.buildExportFileNameSuffix(fileName, queryVO.getAdmissionDateFrom(), queryVO.getAdmissionDateTo());

        // 导出excel
        List<ExcelExport> exports = new ArrayList<>();
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }


    /**
     * 门诊缴费审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportOpPt(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 和出院审核一样
        exportHpOut(heads, datas, list);
    }

    /**
     * 门诊处方审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportOpPct(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 和出院审核一样
        exportHpOut(heads, datas, list);

    }

    /**
     * 门诊挂号审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportOpRg(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 和出院审核一样
        exportHpOut(heads, datas, list);
    }

    /**
     * 住院登记审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpRg(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 和出院审核一样
        exportHpOut(heads, datas, list);
    }

    /**
     * 医嘱审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpDo(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 和出院审核一样
        exportHpOut(heads, datas, list);

    }

    /**
     * 每日记账审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpBk(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 和出院审核一样
        exportHpOut(heads, datas, list);
    }

    /**
     * 转科室审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpTf(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 和出院审核一样
        exportHpOut(heads, datas, list);

    }

    /**
     * 预出院审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpPred(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 和出院审核一样
        exportHpOut(heads, datas, list);

    }

    /**
     * 出院审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpOut(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 表头
        heads.add(CollectionUtil.list(true, "MR_STATUS", "MR_TIME", "VIOLATION_FLAG", "AUDIT_TIME", "NO", "PATIENT_NAME",
                "ADMISSION_NO", "DEPT_NAME", "OUT_ZONE_NAME", "PATIENT_ID", "BENEFIT_TYPE_ID", "PERSONNEL_TYPE", "IS_DISCHARGE", "CLAIM_TYPE_ID", "PATIENT_GENDER", "AGE"));
        heads.add(CollectionUtil.list(true, "审核状态", "审核日期", "是否机审违规", "机审时间", "单据号", "参保人姓名",
                "住院号", "科室", "病区", "参保人编码", "参保类型", "人员类别", "住院状态", "医疗类别", "性别", "年龄"));

        // 数据
        for (BaseMedicalCase item : list) {
            datas.add(CollectionUtil.list(true, "0".equals(item.getMrStatus())?"未审核":"已审核", DateUtil.format(item.getMrTime(), "yyyy-MM-dd HH:mm:ss"), item.getViolationFlag(), DateUtil.format(item.getAuditTime(), "yyyy-MM-dd HH:mm:ss"), item.getNo(), item.getPatientName(),
                    item.getAdmissionNo(), item.getDeptName(), item.getOutZoneName(), item.getPatientId(), item.getBenefitTypeId(), item.getPersonnelType(), item.getIsDischarge(), item.getClaimTypeId(), item.getPatientGender(), item.getAge()));
        }

    }

    /**
     * 结算审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportSettle(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 和出院审核一样
        exportHpOut(heads, datas, list);

    }




    /**
     * 转自费
     * @param mrChangeDTO
     * @param hospitalId
     */
    public void changeSelfExpense(MrChangeDTO mrChangeDTO, String hospitalId) {
        String tableName = AuditScenarioEnum.valueOf(mrChangeDTO.getAuditScenario()).getTableName();


        baseMedicalMapper.updateDetailSelfExpense(tableName
                ,mrChangeDTO.getDetailNo()
                ,mrChangeDTO.getNo()
                ,mrChangeDTO.getAdmissionNo()
                ,"1"
                ,mrChangeDTO.getMrOpinion()
                ,hospitalId
                ,LoginContext.getUserId());
    }


    public MrMedicalDetail queryCaseAudit(String no, String admissionNo, String hospitalId) {
        // 查询主单违规信息
        List<MrMedicalDetail> mrMedicalDetail = mrReviewMapper.queryCaseAudit(no, admissionNo, hospitalId);
        MrMedicalDetail medicialDetail = new MrMedicalDetail();
        medicialDetail.setViolationFlag("0");

        if (CollectionUtil.isNotEmpty(mrMedicalDetail)) {

            Set<String> ruleCodesSet = new HashSet<>();
            Set<String> ruleNamesSet = new HashSet<>();
            Set<String> reasonTypesSet = new HashSet<>();
            Set<String> reasonDessSet = new HashSet<>();
            Set<String> ruleReasonSet = new HashSet<>();
            Set<String> ruleOriginSet = new HashSet<>();
            String ruleType = "99";
            String ruleTypeName = "";

            for (MrMedicalDetail medicalDetail : mrMedicalDetail) {
                if(StrUtil.isNotEmpty(medicalDetail.getRuleOrigin())){
                    ruleOriginSet.add(medicalDetail.getRuleOrigin());
                }
                if(StrUtil.isNotEmpty(medicalDetail.getReasonTypes())) {
                    reasonTypesSet.add(medicalDetail.getReasonTypes());
                }
                if(StrUtil.isNotEmpty(medicalDetail.getRuleReasons())) {
                    ruleReasonSet.add(medicalDetail.getRuleReasons());
                }
                if(StrUtil.isNotEmpty(medicalDetail.getRuleCodes())) {
                    ruleCodesSet.add(medicalDetail.getRuleCodes());
                }
                if(StrUtil.isNotEmpty(medicalDetail.getRuleNames())) {
                    ruleNamesSet.add(medicalDetail.getRuleNames());
                }
                if(StrUtil.isNotEmpty(medicalDetail.getReasonDess())) {
                    reasonDessSet.add(medicalDetail.getReasonDess());
                }
                if(StrUtil.isNotEmpty(medicalDetail.getRuleType())) {
                    if (ruleType.compareTo(medicalDetail.getRuleType()) > 0) {
                        ruleType = medicalDetail.getRuleType();
                        ruleTypeName = medicalDetail.getRuleTypeName();
                    }
                }
            }

            medicialDetail.setViolationFlag("1");
            medicialDetail.setRuleCodes(StrUtil.join(";", ruleCodesSet));
            medicialDetail.setRuleNames(StrUtil.join(";", ruleNamesSet));
            medicialDetail.setReasonTypes(StrUtil.join(";", reasonTypesSet));
            medicialDetail.setReasonDess(StrUtil.join(";", reasonDessSet));
            medicialDetail.setRuleReasons(StrUtil.join(";", ruleReasonSet));
            medicialDetail.setRuleOrigin(StrUtil.join(";", ruleOriginSet));
            medicialDetail.setRuleType(ruleType);
            medicialDetail.setRuleTypeName(ruleTypeName);
        }
        DictUtils.translateDict(medicialDetail);

        return medicialDetail;
    }
}
