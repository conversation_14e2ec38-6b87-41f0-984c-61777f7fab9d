package com.crhms.cloud.mqs.sys.init_engine.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.crhms.cloud.core.utils.ApplicationContextUtil;
import com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpSettleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * @ClassName InitColDataRunner
 * @Description 初始化列数据任务线程
 * <AUTHOR>
 * @Date 2023/3/28
 **/
@Slf4j
public class InitColDataRunner implements Runnable{

    private HpSettleMapper hpSettleMapper;

    private RedisTemplate redisTemplate;

    // 任务ID
    private Long taskId;

    // 表名
    private String tableName;

    // 表字段(可能为多个)
    private String tableColNames;

    // 引擎属性名(去掉_转小写),同时也是列数据文件名
    private String engColName;

    public InitColDataRunner(Long taskId, String tableName, String engColName, String tableColNames) {
        this.taskId = taskId;
        this.tableName = tableName;
        this.tableColNames = tableColNames;
        this.engColName = engColName;

        hpSettleMapper = ApplicationContextUtil.getBean(HpSettleMapper.class);
        redisTemplate = ApplicationContextUtil.getBean(StringRedisTemplate.class);
    }

    @Override
    public void run() {
        List<Object> values;
        LinkedHashMap<Object, Integer> valueMap = new LinkedHashMap<>();

        // 拆分要查询多少字段
        List<String> cols = StrUtil.split(tableColNames, ",");
        if(cols.size() > 1){
            values = hpSettleMapper.distinctColsValue(tableName, cols);
        }else {
            values = hpSettleMapper.distinctColValue(tableName, tableColNames);
        }

        // 放到Map<值, 从0开始的索引>
        valueMap.put("", 0);
        valueMap.put(null, 0);
        if(CollUtil.isNotEmpty(values)){
            int index = 1;
            for(Object value : values){
                if(null==value || StrUtil.isEmpty(String.valueOf(value))){
                    continue;
                }
                valueMap.put(value, index);
                index ++;
            }
        }

        if("mqs_hp_settle".equals(tableName)){
            InitEngineCache.medCaseColDataCache.put(engColName, valueMap);

        }else if("mqs_hp_settle_detail".equals(tableName)){
            InitEngineCache.feeColDataCache.put(engColName, valueMap);
        }


        writeLog(taskId, StrUtil.format("   {}: 表{}字段{}共加载{}条不重复数据", Thread.currentThread().getName(), tableName, engColName, valueMap.size()));
    }

    /**
     * 记录日志
     * <AUTHOR>
     * @date 2023/3/28
     * @param msg       日志信息
     * @param taskId    任务ID
     **/
    public void writeLog(Long taskId, String msg){
        String logKey = StrUtil.format(InitEngineCache.INIT_ENGINE_TASK_LOGS, taskId);

        redisTemplate.opsForList().rightPush(logKey, msg);
    }
}
