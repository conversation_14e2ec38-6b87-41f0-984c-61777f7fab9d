package com.crhms.cloud.mqs.mqs_bmi.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiDeductionTemp;
import com.crhms.cloud.mqs.mqs_bmi.mapper.BmiDeductionTempMapper;
import com.crhms.cloud.mqs.mqs_bmi.service.BmiDeductionTempService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 医保管理(BmiDeductionTemp)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-14 14:23:05
 */
@Service("bmiDeductionTempService")
public class BmiDeductionTempService extends ServiceImpl< BmiDeductionTempMapper, BmiDeductionTemp> {
    @Resource
    private BmiDeductionTempMapper bmiDeductionTempMapper;
}
