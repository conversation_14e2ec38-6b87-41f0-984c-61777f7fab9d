package com.crhms.cloud.mqs.sys.init_engine.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName InitEngineCache
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/28
 **/
public class InitEngineCache<a> {

    // 生成引擎初始化单据文件，缓存任务ID
    public static final String INIT_ENGINE_TASK_ID = "CRHMS:MQS:INIT_ENGINE:TASK_ID";

    // 生成引擎初始化单据文件日志记录
    public static final String INIT_ENGINE_TASK_LOGS = "CRHMS:MQS:INIT_ENGINE:{}:TASK_LOGS";

    // 引擎初始化进度
    public static final String INIT_ENGINE_TASK_PROCESS = "CRHMS:MQS:INIT_ENGINE:{}:TASK_PROCESS";

    /** 主单列数据区数据缓存, Map<引擎属性去_小写, Map<值, 坐标> **/
    public static Map<String, LinkedHashMap<Object, Integer>> medCaseColDataCache = MapUtil.newConcurrentHashMap();

    /** 费用明细表列数据区数据缓存, Map<引擎属性去_小写, Map<值, 坐标> **/
    public static Map<String, LinkedHashMap<Object, Integer>> feeColDataCache = MapUtil.newConcurrentHashMap();
}