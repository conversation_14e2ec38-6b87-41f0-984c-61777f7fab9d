package com.crhms.cloud.mqs.sys.service;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.sys.domain.SysFundsDept;
import com.crhms.cloud.mqs.sys.mapper.SysFundsDeptMapper;
import org.apache.ibatis.binding.MapperMethod;
import org.springframework.data.redis.connection.FutureResult;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.Future;

/**
 * 运营管理-基金管理-部门(SysFundsDept)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-15 16:43:03
 */
@Service("sysFundsDeptService")
public class SysFundsDeptService extends ServiceImpl< SysFundsDeptMapper, SysFundsDept> {

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    public boolean deleteById(Long id) {
        return this.deleteById(id);
    }


    @Async
    @Transactional(rollbackFor = Exception.class)
    public Future updateBatchById(List<SysFundsDept> entityList, int batchSize) {
        if(batchSize > 1000){
            batchSize = 1000;
        }
        List<List<SysFundsDept>> partition = ListUtil.partition(entityList, batchSize);
        for (List<SysFundsDept> deptList : partition) {
            this.baseMapper.batchUpdate(deptList);
        }
        return new AsyncResult(true);
    }
}
