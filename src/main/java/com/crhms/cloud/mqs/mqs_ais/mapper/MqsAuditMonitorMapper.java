package com.crhms.cloud.mqs.mqs_ais.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 运营监控-审核监控 mapper接口
 *
 * @author: 王新刚
 * @date: 2023/03/14
 */
@Mapper
@DS("clickhouse")
public interface MqsAuditMonitorMapper {




    /**
     * 查询审核明细数和审核费用
     *
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param auditScenario 业务
     * @param psnTypes      人员类别
     * @return nAuditDetail:审核明细数   nAuditFee:审核费用
     */
    Map<String, Object> nAuditDetailAndAuditFee(@Param("startTime") String startTime,
                                                @Param("endTime") String endTime,
                                                @Param("auditScenario") String auditScenario,
                                                @Param("psnTypes") String psnTypes,
                                                @Param("hospitalId") String hospitalId);

    /**
     * 查询强制保存的明细单量和强制保存的项目费用
     *
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param auditScenario 业务
     * @param psnTypes      人员类别
     * @return nForcedSave:强制保存的明细单量   costForcedSave:强制保存的项目费用
     */
    Map<String, Object> nForcedSaveAndProjectCost(@Param("startTime") String startTime,
                                                  @Param("endTime") String endTime,
                                                  @Param("auditScenario") String auditScenario,
                                                  @Param("psnTypes") String psnTypes,
                                                  @Param("hospitalId") String hospitalId);
    /**
     * 违规规则: 查询违规规则排名
     *
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param auditScenarioList 业务
     * @param psnTypes          人员类别
     * @param rankFlag          number:人次  amount:金额
     * @return nForcedSave:强制保存的明细单量   costForcedSave:强制保存的项目费用
     */
    @MapKey("")
    List<Map<String, Object>> rankRule(@Param("startTime") String startTime,
                                       @Param("endTime") String endTime,
                                       @Param("auditScenarioList") List<String> auditScenarioList,
                                       @Param("psnTypes") String psnTypes,
                                       @Param("rankFlag") String rankFlag);

    /**
     * 违规规则: 查询所有违规规则的违规金额
     *
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param auditScenarioList 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes          人员类别编码
     * @param hospitalId        院区id
     * @return 所有违规规则的违规金额
     */
    BigDecimal ruleTotalAmount(@Param("startTime") String startTime,
                               @Param("endTime") String endTime,
                               @Param("auditScenarioList") List<String> auditScenarioList,
                               @Param("psnTypes") String psnTypes,
                               @Param("hospitalId") String hospitalId);


    /**
     * 违规项目: 查询违规项目排名
     *
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param auditScenarioList 业务
     * @param psnTypes          人员类别
     * @param rankFlag          number:人次  amount:金额
     * @return nForcedSave:强制保存的明细单量   costForcedSave:强制保存的项目费用
     */
    @MapKey("")
    List<Map<String, Object>> rankProject(@Param("startTime") String startTime,
                                          @Param("endTime") String endTime,
                                          @Param("auditScenarioList") List<String> auditScenarioList,
                                          @Param("psnTypes") String psnTypes,
                                          @Param("rankFlag") String rankFlag,
                                          @Param("hospitalId") String hospitalId);

    /**
     * 违规项目: 查询所有违规项目的违规金额
     *
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param auditScenarioList 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes          人员类别编码
     * @return 所有违规规则的违规金额
     */
    BigDecimal projectTotalAmount(@Param("startTime") String startTime,
                                  @Param("endTime") String endTime,
                                  @Param("auditScenarioList") List<String> auditScenarioList,
                                  @Param("psnTypes") String psnTypes,
                                  @Param("hospitalId") String hospitalId);


    /**
     * 科室违规明细
     *
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param auditScenarioList 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes          人员类别编码
     */
    @MapKey("")
    List<Map<String, Object>> deptViolationDetails(@Param("startTime") String startTime,
                                                   @Param("endTime") String endTime,
                                                   @Param("auditScenarioList") List<String> auditScenarioList,
                                                   @Param("psnTypes") String psnTypes,
                                                   @Param("hospitalId") String hospitalId);

    /**
     * 科室违规费用
     *
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param auditScenarioList 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes          人员类别编码
     */
    @MapKey("")
    List<Map<String, Object>> deptViolationFees(@Param("startTime") String startTime,
                                                @Param("endTime") String endTime,
                                                @Param("auditScenarioList") List<String> auditScenarioList,
                                                @Param("psnTypes") String psnTypes,
                                                @Param("hospitalId") String hospitalId);

    /**
     * 查询违规明细趋势
     *
     * @param auditScenarioList 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes          人员类别编码
     */
    @MapKey("")
    List<Map<String, Object>> detailsTendency(@Param("startTime") String startTime,
                                              @Param("endTime") String endTime,
                                              @Param("auditScenarioList") List<String> auditScenarioList,
                                              @Param("psnTypes") String psnTypes,
                                              @Param("hospitalId") String hospitalId);

    /**
     * 查询违规费用趋势
     *
     * @param months            查询的月份
     * @param auditScenarioList 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes          人员类别编码
     */
    @MapKey("")
    List<Map<String, Object>> feesTendency(@Param("months") List<String> months,
                                           @Param("auditScenarioList") List<String> auditScenarioList,
                                           @Param("psnTypes") String psnTypes,
                                           @Param("hospitalId") String hospitalId);


    /**
     * 查询日期内审核记录数
     * @param scenarioList
     * @param now
     * @param hospitalId
     * @return
     */
    @DS("mysql")
    BigDecimal selectTotalAuditTimes(@Param("scenarioList") List<String> scenarioList, @Param("now") String now, @Param("psnTypes") String psnTypes, @Param("hospitalId") String hospitalId);

    /**
     * 统计当前在院人数
     * @param scenarioList
     * @param hospitalId
     * @return
     */
    @DS("mysql")
    BigDecimal selectTotalInNums(@Param("scenarioList") List<String> scenarioList, @Param("psnTypes") String psnTypes, @Param("hospitalId") String hospitalId);

    /**
     * 查询费用结构异常占比
     *
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param auditScenarioList 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes          人员类别编码
     */
    @MapKey("")
    List<Map<String, Object>> costStructure(@Param("startTime") String startTime,
                                            @Param("endTime") String endTime,
                                            @Param("auditScenarioList") List<String> auditScenarioList,
                                            @Param("psnTypes") String psnTypes,
                                            @Param("hospitalId") String hospitalId);

    /**
     * 查询场景审核人次
     *
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param auditScenarioList 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes          人员类别编码
     */
    @MapKey("")
    List<Map<String, Object>> nAuditScenario(@Param("startTime") String startTime,
                                             @Param("endTime") String endTime,
                                             @Param("auditScenarioList") List<String> auditScenarioList,
                                             @Param("psnTypes") String psnTypes);

    /**
     * 查询每个违规主单的规则名称、规则分类、违规主单数量、违规金额
     *
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param auditScenarioList 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes          人员类别编码
     * @param rankFlag          排序字段: 主单数量或者金额
     */
    @MapKey("")
    List<Map<String, Object>> violationHead(@Param("startTime") String startTime,
                                            @Param("endTime") String endTime,
                                            @Param("auditScenarioList") List<String> auditScenarioList,
                                            @Param("psnTypes") String psnTypes,
                                            @Param("rankFlag") String rankFlag,
                                            @Param("hospitalId") String hospitalId);
    /**
     * 统计当前出院人数
     * @param scenarioList
     * @param now
     * @param hospitalId
     * @return
     */
    @DS("mysql")
    BigDecimal selectTotalOutNums(@Param("scenarioList") List<String> scenarioList, @Param("now") String now, @Param("psnTypes") String psnTypes, @Param("hospitalId") String hospitalId);

    /**
     * 查询返回修改
     * @param scenarioList
     * @param startTime
     * @param endTime
     * @param psnTypes
     * @param hospitalId
     * @return
     */
    @DS("mysql")
    Map selectAuditReturn(@Param("scenarioList") List<String> scenarioList, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("psnTypes") String psnTypes, @Param("hospitalId") String hospitalId);
}
