package com.crhms.cloud.mqs.sys.mapper;

import com.crhms.cloud.mqs.sys.domain.SysConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.sys.domain.SysRuleLevel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 系统配置表(SysConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-12 14:38:52
 */

@Mapper
public interface SysConfigMapper extends BaseMapper<SysConfig> {

    /**
     * 查询系统配置
     * @param sysConfig
     * @return
     */
    List<SysConfig> queryList(SysConfig sysConfig);

    /**
     * 查询系统规则级别配置
     * @param sysRuleLevel
     * @return
     */
    List<SysRuleLevel> queryRuleLevelList(SysRuleLevel sysRuleLevel);
}

