package com.crhms.cloud.mqs.sys.dto;

import com.crhms.cloud.mqs.sys.domain.SysScene;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 场景配置页面信息
 *
 * @author: wangxing<PERSON>
 * @date: 2023.02.17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SceneConfigVO {
    /**
     * 每日记账时间设置
     */
    private String dailyPostingTime;

    /**
     * 场景启用配置
     */
    private List<SysScene> sceneList;
}
