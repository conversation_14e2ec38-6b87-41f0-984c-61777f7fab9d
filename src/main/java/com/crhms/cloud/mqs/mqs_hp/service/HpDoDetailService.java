package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpDoDetail;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpDoDetailMapper;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-医嘱审核明细(HpDoDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 09:48:17
 */
@Service("hpDoDetailService")
public class HpDoDetailService extends ServiceImpl< HpDoDetailMapper, HpDoDetail> {

}
