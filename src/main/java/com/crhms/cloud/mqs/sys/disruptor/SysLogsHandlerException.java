package com.crhms.cloud.mqs.sys.disruptor;

import com.crhms.cloud.mqs.sys.domain.SysLogs;
import com.lmax.disruptor.ExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 数据管道消费异常全局处理器
 * <AUTHOR>
 * @date 2024-11-14 16:38:11
 */
@Slf4j
@Component
public class SysLogsHandlerException implements ExceptionHandler<SysLogDTO> {

    @Override
    public void handleEventException(Throwable ex, long sequence, SysLogDTO event) {
        log.error("消费日志事件时发生异常");
    }

    @Override
    public void handleOnStartException(Throwable ex) {
        ex.printStackTrace();
    }

    @Override
    public void handleOnShutdownException(Throwable ex) {
        ex.printStackTrace();
    }
}
