package com.crhms.cloud.mqs.mqs_ais.controller;

import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.mqs_ais.service.MqsAuditMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 运营监控-审核监控
 *
 * <AUTHOR>
 * @date 2023/3/6
 **/
@RestController
@RequestMapping("api/mqs/audit/monitor")
public class MqsAuditMonitorController {

    @Autowired
    private MqsAuditMonitorService monitorService;


    /**
     * 今日实时
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     * <AUTHOR>
     * @date 2023/03/14
     */
    @GetMapping("/realTime")
    public ResponseEntity<Map<String, Object>> realTime(@RequestParam(value = "auditScenario") String auditScenario,
                                                        @RequestParam(value = "psnTypes",required = false) String psnTypes) {
        Map<String, Object> result = monitorService.realTime(auditScenario, psnTypes,LoginContext.getHospitalId());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    /**
     * 年度使用情况
     *
     * @param filterYear    前端只传年份
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     * <AUTHOR>
     * @date 2023/03/15
     */
    @GetMapping("/annualUsage")
    public ResponseEntity<Map<String, Object>> annualUsage(@RequestParam(value = "filterYear") String filterYear,
                                                           @RequestParam(value = "auditScenario") String auditScenario,
                                                           @RequestParam(value = "psnTypes",required = false) String psnTypes) {
        Map<String, Object> result = monitorService.annualUsage(filterYear, auditScenario, psnTypes, LoginContext.getHospitalId());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    /**
     * 违规规则排名
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     * @param rankFlag      number:人次  amount:金额
     * <AUTHOR>
     * @date 2023/03/15
     */
    @GetMapping("/rank/rule")
    public ResponseEntity<List<Map<String, Object>>> rankRule(@RequestParam(value = "filterYear") String filterYear,
                                                              @RequestParam(value = "filterMonth", required = false) String filterMonth,
                                                              @RequestParam(value = "filterDay", required = false) String filterDay,
                                                              @RequestParam(value = "auditScenario") String auditScenario,
                                                              @RequestParam(value = "psnTypes", required = false) String psnTypes,
                                                              @RequestParam(value = "rankFlag", defaultValue = "number") String rankFlag) {
        List<Map<String, Object>> result = monitorService.rankRule(filterYear, filterMonth, filterDay, auditScenario, psnTypes, rankFlag);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    /**
     * 违规项目排名
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     * @param rankFlag      number:人次  amount:金额
     * <AUTHOR>
     * @date 2023/03/15
     */
    @GetMapping("/rank/project")
    public ResponseEntity<List<Map<String, Object>>> rankProject(@RequestParam(value = "filterYear") String filterYear,
                                                                 @RequestParam(value = "filterMonth", required = false) String filterMonth,
                                                                 @RequestParam(value = "filterDay", required = false) String filterDay,
                                                                 @RequestParam(value = "auditScenario") String auditScenario,
                                                                 @RequestParam(value = "psnTypes", required = false) String psnTypes,
                                                                 @RequestParam(value = "rankFlag", defaultValue = "number") String rankFlag) {
        List<Map<String, Object>> result = monitorService.rankProject(filterYear, filterMonth, filterDay, auditScenario, psnTypes, rankFlag);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    /**
     * 科室违规明细 和 科室违规费用
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     */
    @GetMapping("/violation/dept")
    public ResponseEntity<Map<String, List<Map<String, Object>>>> deptViolationDetailsAndFees(@RequestParam(value = "filterYear") String filterYear,
                                                                                              @RequestParam(value = "filterMonth", required = false) String filterMonth,
                                                                                              @RequestParam(value = "filterDay", required = false) String filterDay,
                                                                                              @RequestParam(value = "auditScenario") String auditScenario,
                                                                                              @RequestParam(value = "psnTypes", required = false) String psnTypes) {
        return new ResponseEntity<>(monitorService.deptViolationDetailsAndFees(filterYear, filterMonth, filterDay, auditScenario, psnTypes), HttpStatus.OK);
    }


    /**
     * 违规明细趋势 和 违规费用趋势
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     */
    @GetMapping("/violation/tendency")
    public ResponseEntity<Map<String, List<Map<String, Object>>>> violationTendency(@RequestParam(value = "filterYear") String filterYear,
                                                                                    @RequestParam(value = "filterMonth", required = false) String filterMonth,
                                                                                    @RequestParam(value = "filterDay", required = false) String filterDay,
                                                                                    @RequestParam(value = "auditScenario") String auditScenario,
                                                                                    @RequestParam(value = "psnTypes", required = false) String psnTypes) {
        return new ResponseEntity<>(monitorService.violationTendency(filterYear, filterMonth, filterDay, auditScenario, psnTypes), HttpStatus.OK);
    }


    /**
     * 费用结构异常占比
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     */
    @GetMapping("/costStructure")
    public ResponseEntity<List<Map<String, Object>>> costStructure(@RequestParam(value = "filterYear") String filterYear,
                                                                   @RequestParam(value = "filterMonth", required = false) String filterMonth,
                                                                   @RequestParam(value = "filterDay", required = false) String filterDay,
                                                                   @RequestParam(value = "auditScenario") String auditScenario,
                                                                   @RequestParam(value = "psnTypes", required = false) String psnTypes) {
        return new ResponseEntity<>(monitorService.costStructure(filterYear, filterMonth, filterDay, auditScenario, psnTypes), HttpStatus.OK);
    }


    /**
     * 场景审核人次
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     */
    @GetMapping("/nAuditScenario")
    public ResponseEntity<List<Map<String, Object>>> nAuditScenario(@RequestParam(value = "filterYear") String filterYear,
                                                                    @RequestParam(value = "filterMonth", required = false) String filterMonth,
                                                                    @RequestParam(value = "filterDay", required = false) String filterDay,
                                                                    @RequestParam(value = "auditScenario") String auditScenario,
                                                                    @RequestParam(value = "psnTypes", required = false) String psnTypes) {
        return new ResponseEntity<>(monitorService.nAuditScenario(filterYear, filterMonth, filterDay, auditScenario, psnTypes), HttpStatus.OK);
    }


    /**
     * 违规主单统计
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     * @param rankFlag      排序字段: 主单数量或者金额
     */
    @GetMapping("/violationHead")
    public ResponseEntity<List<Map<String, Object>>> violationHead(@RequestParam(value = "filterYear") String filterYear,
                                                                   @RequestParam(value = "filterMonth") String filterMonth,
                                                                   @RequestParam(value = "filterDay") String filterDay,
                                                                   @RequestParam(value = "auditScenario") String auditScenario,
                                                                   @RequestParam(value = "psnTypes", required = false) String psnTypes,
                                                                   @RequestParam(value = "rankFlag", defaultValue = "number") String rankFlag) {
        return new ResponseEntity<>(monitorService.violationHead(filterYear, filterMonth, filterDay, auditScenario, psnTypes, rankFlag), HttpStatus.OK);
    }
}

