package com.crhms.cloud.mqs.basic.domain;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.io.Serializable;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import com.googlecode.jmapper.annotations.JMap;
import com.googlecode.jmapper.annotations.JMapConversion;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 住院/门诊审核费用明细-通用(BaseMedicalDetail)实体类
 *
 * <AUTHOR>
 * @since 2023-02-08 17:07:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
public class BaseMedicalDetail extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -27158024103753257L;

    public static final String FIELD_ID="id";
    public static final String FIELD_DETAIL_NO="detSail_no";
    public static final String FIELD_NO="no";
    public static final String FIELD_BILL_DATE="bill_date";
    public static final String FIELD_ITEM_ID="item_id";
    public static final String FIELD_ITEM_NAME="item_name";
    public static final String FIELD_PTYPE="ptype";
    public static final String FIELD_ITEM_TYPE_CODE="item_type_code";
    public static final String FIELD_ITEM_TYPE_NAME="item_type_name";
    public static final String FIELD_ITEM_DATE="item_date";
    public static final String FIELD_NUMBERS="numbers";
    public static final String FIELD_PRICE="price";
    public static final String FIELD_COSTS="costs";
    public static final String FIELD_COST_NUMBER="cost_number";
    public static final String FIELD_COST_COSTS="cost_costs";
    public static final String FIELD_BMI_CONVERED_AMOUNT="bmi_convered_amount";
    public static final String FIELD_BMI_OVERALL_AMOUNT="bmi_overall_amount";
    public static final String FIELD_SPECIFICATION="specification";
    public static final String FIELD_USAGE="usage";
    public static final String FIELD_APPLY_DOCTOR_CODE="apply_doctor_code";
    public static final String FIELD_APPLY_DOCTOR_NAME="apply_doctor_name";
    public static final String FIELD_APPLY_DOCTOR_LEVEL="apply_doctor_level";
    public static final String FIELD_APPLY_DEPT_CODE="apply_dept_code";
    public static final String FIELD_APPLY_DEPT_NAME="apply_dept_name";
    public static final String FIELD_EXEC_DOCTOR_CODE="exec_doctor_code";
    public static final String FIELD_EXEC_DOCTOR_NAME="exec_doctor_name";
    public static final String FIELD_EXEC_DEPT_CODE="exec_dept_code";
    public static final String FIELD_EXEC_DEPT_NAME="exec_dept_name";
    public static final String FIELD_RT_DOCTOR_CODE="rt_doctor_code";
    public static final String FIELD_RT_DOCTOR_NAME="rt_doctor_name";
    public static final String FIELD_RP_NURSE_CODE="rp_nurse_code";
    public static final String FIELD_RP_NURSE_NAME="rp_nurse_name";
    public static final String FIELD_CHARGING_FLAG="charging_flag";
    public static final String FIELD_SELF_EXPENSE="self_expense";
    public static final String FIELD_FREQUENCY_INTERVAL="frequency_interval";
    public static final String FIELD_APPROVAL_NUMBER="approval_number";
    public static final String FIELD_Z_PHYSICIANAP="z_physicianap";
    public static final String FIELD_POSTS_NUMBER="posts_number";
    public static final String FIELD_PAY_RATIO="pay_ratio";
    public static final String FIELD_PAY_AMOUNT="pay_amount";
    public static final String FIELD_ABROAD_DRUG_FLAG="abroad_drug_flag";
    public static final String FIELD_OUT_HOSPITAL_FLAG="out_hospital_flag";
    public static final String FIELD_RULE_CODES="rule_codes";
    public static final String FIELD_RULE_NAMES="rule_names";
    public static final String FIELD_RULE_REASONS="rule_reasons";
    public static final String FIELD_REASON_TYPES="reason_types";
    public static final String FIELD_REASON_DESS="reason_dess";
    public static final String FIELD_BATCH_NO="batch_no";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */
    @TableId
    private Long id;

    /**
     * 明细流水号
     */
    @TableField
    private String detailNo;

    /**
     * 冲销明细流水号
     */
    @TableField
    private String refundNo;

    /**
     * 就诊流水号
     */
    @TableField
    private String admissionNo;
    /**
     * 单据号
     */
    @TableField
    private String no;
    /**
     * 结算日期
     */
    @TableField
    private Date billDate;
    /**
     * 项目编码
     */
    @TableField
    private String itemId;
    /**
     * 项目名称
     */
    @TableField
    private String itemName;
    /**
     * 院内项目编码
     */
    @TableField
    private String hisItemId;
    /**
     * 院内项目名称
     */
    @TableField
    private String hisItemName;

    /**
     * 项目类型名称
     */
    @TableField
    private String ptype;
    /**
     * 项目类型编码(A-Z大类)
     */
    @TableField
    private String itemTypeCode;
    /**
     * 项目类型名称
     */
    @TableField
    private String itemTypeName;
    /**
     * 项目日期
     */
    @TableField
    private Date itemDate;

    /**
     * 是否当次明细 1是 0否
     */
    @TableField
    private String isCurrent;

    /**
     * 数量
     */
    @TableField
    private BigDecimal numbers;
    /**
     * 单价
     */
    @TableField
    private BigDecimal price;
    /**
     * 包装单位
     */
    @TableField
    private String usageUnit;
    /**
     * 总费用
     */
    @TableField
    private BigDecimal costs;
    /**
     * 计费数量
     */
    @TableField
    private BigDecimal costNumber;
    /**
     * 计费金额
     */
    @TableField
    private BigDecimal costCosts;
    /**
     * 医保内金额
     */
    @TableField
    private BigDecimal bmiConveredAmount;
    /**
     * 医保统筹金额
     */
    @TableField
    private BigDecimal bmiOverallAmount;

    /**
     * 违规数量
     */
    @TableField
    private BigDecimal violationNum;

    /**
     * 违规金额
     */
    @TableField
    private BigDecimal violationAmt;

    /**
     * 规格
     */
    @TableField
    private String specification;
    /**
     * 每次用量
     */
    @TableField
    private String usage;
    /**
     * 用药天数
     */
    @TableField
    private String usageDays;
    /**
     * 开单医生编码
     */
    @TableField
    private String applyDoctorCode;
    /**
     * 开单医生名称
     */
    @TableField
    private String applyDoctorName;
    /**
     * 开单医生级别
     */
    @TableField
    private String applyDoctorLevel;
    /**
     * 开单科室编码
     */
    @TableField
    private String applyDeptCode;
    /**
     * 开单科室名称
     */
    @TableField
    private String applyDeptName;
    /**
     * 受单医生编码
     */
    @TableField
    private String execDoctorCode;
    /**
     * 受单医生名称
     */
    @TableField
    private String execDoctorName;
    /**
     * 受单科室编码
     */
    @TableField
    private String execDeptCode;
    /**
     * 受单科室名称
     */
    @TableField
    private String execDeptName;
    /**
     * 住院医师编码
     */
    @TableField
    private String rtDoctorCode;
    /**
     * 住院医师名称
     */
    @TableField
    private String rtDoctorName;
    /**
     * 责任护士编码
     */
    @TableField
    private String rpNurseCode;
    /**
     * 责任护士名称
     */
    @TableField
    private String rpNurseName;
    /**
     * 计费标记
     */
    @TableField
    @DictTranslate(dictCode = "LOCAL_YN_CODE")
    private String chargingFlag;
    /**
     * 是否自费
     */
    @TableField
    @DictTranslate(dictCode = "LOCAL_YN_CODE")
    private String selfExpense;
    /**
     * 是否丙类项目( 自费标记 原始值 )
     */
    @TableField
    private String selfOriginal;
    /**
     * 使用频次
     */
    @TableField
    @DictTranslate(dictCode = "DIC_FREQUENCY_INTERVAL")
    private String frequencyInterval;
    /**
     * 备案审批号
     */
    @TableField
    private String approvalNumber;
    /**
     * 医师行政职务
     */
    @TableField
    private String zPhysicianap;
    /**
     * 帖数
     */
    @TableField
    private String postsNumber;
    /**
     * 自付比例
     */
    @TableField
    private BigDecimal payRatio;
    /**
     * 出国带药标志
     */
    @TableField
    private String abroadDrugFlag;
    /**
     * 外院费用标志
     */
    @TableField
    private String outHospitalFlag;
    /**
     * 是否违规
     */
    @TableField
    @DictTranslate(dictCode = "LOCAL_VIOLATION_FLAG")
    private String violationFlag;

    /**
     * 违规计费标记/违规类型 0未违规 1违规，默认1
     */
    @TableField
    private String violationType;

    /**
     * 违规结果类型（一般显示最高违规级别)
     */
    @TableField
    private String ruleType;
    /**
     * 风险级别(1.阻断 2.需说明 3.无需说明 4.仅提醒)
     */
    @TableField(exist = false)
    private String riskLevel;

    /**
     * 违规编码集
     */
    @TableField
    private String ruleTypeName;

    /**
     * 违规编码集
     */
    @TableField
    private String ruleCodes;
    /**
     * 违规名称集
     */
    @TableField
    private String ruleNames;
    /**
     * 违规原因集
     */
    @TableField
    private String ruleReasons;
    /**
     * 反馈类型集
     */
    @TableField
    private String reasonTypes;
    /**
     * 反馈原因集
     */
    @TableField
    private String reasonDess;
    /**
     * 违规类型集
     */
    @DictTranslate(dictCode = "LOCAL_RULE_ORIGIN", dictSplit = "|")
    @TableField
    private String ruleOrigin;
    /**
     * 批次号
     */
    @TableField
    private String batchNo;
    /**
     * 院区id
     */
    @TableField
    private String hospitalId;

    // 非数据库字段
    //高亮标识
    private boolean highLight;

    @TableField(exist = false)
    private String memo;

    /**
     * 自付金额
     */
    @TableField(exist = false)
    private BigDecimal payAmount;

    /**
     * 险种类型
     */
    @TableField(exist = false)
    private String benefitTypeId;

    /**
     * 出院日期
     */
    @TableField(exist = false)
    private Date dischargeDate;

    /**
     * 人工审核时间
     */
    @TableField(exist = false)
    private Date mrTime;

    /**
     * 审核时间
     */
    @TableField(exist = false)
    private Date auditTime;

    /**
     * 参保人编码
     */
    @TableField(exist = false)
    private String patientId;

    @TableField(exist = false)
    private String patientName;

    @TableField(exist = false)
    @DictTranslate(dictCode = "DIC_MEDICAL_TYPE")
    private String claimTypeId;

    @TableField(exist = false)
    @DictTranslate(dictCode = "DIC_GENDER")
    private String patientGender;

    @TableField(exist = false)
    private String patientBirthday;

    @TableField(exist = false)
    private Date admissionDate;

    @TableField(exist = false)
    private String inDiagnosisCode;

    @TableField(exist = false)
    private String inDiagnosisName;

    @TableField(exist = false)
    private String outDiagnosisCode;

    @TableField(exist = false)
    private String outDiagnosisName;

    @TableField(exist = false)
    private String secondaryDiseaseId;

    @TableField(exist = false)
    private String secondaryDiseaseZh;

    //甲乙类
    @TableField
    private String categoryName;

    //是否长期医嘱
    @TableField
    private String longDrordFlag;
    //目录类别
    @TableField
    private String hilistType;
    //收费类别
    @TableField
    private String chrgType;
    //医嘱行为
    @TableField
    private String drordBhvr;
    //医保目录等级
    @TableField
    private String hilistLv;
    //医保目录价格
    @TableField
    private BigDecimal hilistPric;
    //自费金额
    @TableField
    private BigDecimal ownpayAmt;
    //自付金额
    @TableField
    private BigDecimal selfpayAmt;

    //是否操作行（0-否,1-是），用于标记明细是否为新增或修改操作行，默认值为1
    @TableField
    private String operationType;

    //是否被删除行(（0-否,1-是），用于标记明细是否被删除行，默认值为0
    @TableField
    private String reverseFlag;

    //是否出院带药（0-否,1-是）
    @TableField
    @DictTranslate(dictCode = "LOCAL_YN_CODE")
    private String outpatientMedication;

    //用药途径
    @TableField
    private String routeAdministration;

    //病区编码
    @TableField(exist = false)
    private String outZoneCode;

    //病区名称
    @TableField(exist = false)
    private String outZoneName;

    @TableField(exist = false)
    private String relatedRecords;

    /**
     * jmapper类型转换
     *
     */
    @JMapConversion(from = {"bill_date","item_date"},
            to = {"billDate","itemDate"})
    public Date convertDateFields(String dateString) throws ParseException {
        if (StrUtil.isBlank(dateString)) {
            return null;
        }
        for (String format : GloablData.DATE_FORMATS) {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            try {
                return sdf.parse(dateString);
            } catch (ParseException e) {
                // 忽略该异常，尝试下一个格式
            }
        }
        throw new RuntimeException("无法解析日期: " + dateString);
    }

    @JMapConversion(from = {"pay_ratio"},
            to = {"payRatio"})
    public BigDecimal convertToBigDecimalFields(String amount) throws ParseException {

        return StrUtil.isEmpty(amount) ? null : MqsUtils.getBigDecimal(amount);
    }
    @JMapConversion(from = {"is_current","operation_type"},
            to = {"isCurrent","operationType"})
    public String defauleY(String flag) throws ParseException {
        return StrUtil.isEmpty(flag) ? "1" : flag;
    }
    @JMapConversion(from = {"reverse_flag","self_expense"},
            to = {"reverseFlag","selfExpense"})
    public String defauleN(String flag) throws ParseException {
        return StrUtil.isEmpty(flag) ? "0" : flag;
    }
}

