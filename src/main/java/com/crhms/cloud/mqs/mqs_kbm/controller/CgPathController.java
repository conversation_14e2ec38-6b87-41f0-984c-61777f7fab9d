package com.crhms.cloud.mqs.mqs_kbm.controller;

import com.crhms.cloud.mqs.mqs_kbm.domain.CgPath;
import com.crhms.cloud.mqs.mqs_kbm.service.CgPathService;
import com.crhms.cloud.mqs.mqs_kbm.vo.CgPathQueryVo;
import com.crhms.cloud.mqs.mqs_kbm.vo.CgPathSaveVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 临床指南-临床路径
 *
 * @author: wangxingang
 * @date: 2023.02.08
 */

@RestController
@RequestMapping("/api/mqs/cgPath")
public class CgPathController {

    @Autowired
    private CgPathService service;


    /**
     * 新增
     *
     * @param saveVo 新增信息
     */
    @PostMapping("/save")
    public ResponseEntity<Object> save(@RequestBody CgPathSaveVo saveVo) {
        service.saveCgPath(saveVo.getDeptNameList(), saveVo.getFileName(), saveVo.getOid(), saveVo.getReleaseTime());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * 查询列表
     *
     * @param filter 查询条件
     */
    @PostMapping("/list")
    public ResponseEntity<List<CgPath>> queryList(@RequestBody(required = false) CgPathQueryVo filter) {
        List<CgPath> result = service.queryList(filter);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    /**
     * 获取oid - 用于预览/下载临床路径
     *
     * @param id 临床路径信息id
     */
    @GetMapping("/oid")
    public ResponseEntity<String> preview(Long id) {
        return new ResponseEntity<>(service.getById(id).getOid(), HttpStatus.OK);
    }


    /**
     * 查询适用科室下拉列表
     *
     * @return 适用科室下拉列表
     */
    @GetMapping("/deptName")
    public ResponseEntity<List<String>> queryDeptNameList() {
        return new ResponseEntity<>(service.queryDeptNameList(), HttpStatus.OK);
    }

    /**
     * 查询发布时间下拉列表
     *
     * @return 发布时间下拉列表
     */
    @GetMapping("/releaseTime")
    public ResponseEntity<List<String>> queryReleaseTimeList() {
        return new ResponseEntity<>(service.queryReleaseTimeList(), HttpStatus.OK);
    }
}
