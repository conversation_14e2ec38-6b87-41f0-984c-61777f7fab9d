package com.crhms.cloud.mqs.sys.domain;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 运营管理-基金管理(SysFunds)实体类
 *
 * <AUTHOR>
 * @since 2023-02-20 15:04:07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_funds")
public class SysFunds extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -29083695755748451L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_YEARS="years";
    public static final String FIELD_VISIT_TYPE="visit_type";
    public static final String FIELD_TOTAL_AMOUNT="total_amount";
    public static final String FIELD_USED_AMOUNT="used_amount";
    public static final String FIELD_USED_RATIO="used_ratio";
    public static final String FIELD_WARN_TYPE="warn_type";
    public static final String FIELD_IS_CDS_CI="is_alone";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 年度
     */    
    @TableField
    private String years;
    /**
     * 就诊类型1.门诊 2 住院
     */    
    @TableField
    private String visitType;
    /**
     * 基金额度
     */    
    @TableField
    private BigDecimal totalAmount;

    /**
     * 基金额度
     */
    @TableField
    private BigDecimal totalWarnAmount;

    /**
     * 基金额度
     */
    @TableField(exist = false)
    private BigDecimal totalWarnAmountRatio;

    /**
     * 已使用金额(定时更新)
     */    
    @TableField
    private BigDecimal usedAmount;
    /**
     * 已使用百分比
     */
    @TableField
    private BigDecimal usedRatio;
    /**
     * 预警维度1.科室 2病区
     */    
    @TableField
    private String warnType;
    /**
     * 是否单独管理
     */    
    @TableField
    private String isAlone;


    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;

    @TableField(exist =  false)
    List<SysFundsDept> deptList;
    @TableField(exist =  false)
    List<SysFundsZone> zoneList;
    @TableField(exist =  false)
    List<SysFundsAlone> aloneList;

    public BigDecimal getUsedAmount() {
        return ObjectUtil.isNull(usedAmount) ? BigDecimal.ZERO : usedAmount;
    }

    public BigDecimal getUsedRatio() {
        return ObjectUtil.isNull(usedRatio) ? BigDecimal.ZERO : usedRatio;
    }

}

