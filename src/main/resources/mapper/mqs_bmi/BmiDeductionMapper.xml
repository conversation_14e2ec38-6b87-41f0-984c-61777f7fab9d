<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_bmi.mapper.BmiDeductionMapper">


  <sql id="baseSql">
          id, detail_no, no, admission_no, bmi_number, claim_type, bill_date, patient_id, patient_name, patient_gender, patient_birthday, benefit_type, personnel_type, dept_code, dept_name, doc_id, doc_name, item_date, item_id, item_name, proposal_code, numbers, price, specification, usages, ptype, rules, bmi_convered_amount, deduction_amount, deduction_reason, deduction_amount1, deduction_amount2, deduction_amount3, version, last_updated_by, last_updated_date, created_by, created_date, hospital_id, org, fee_org_code, fee_org_name, return_reason, fb_complain
    </sql>

    <select id="queryByPage" resultType="com.crhms.cloud.mqs.mqs_bmi.domain.BmiDeduction">
        select
        <include refid="baseSql"/>
        from mqs_bmi_deduction
        where hospital_id = #{queryVo.hospitalId}
        <if test="queryVo.detailNo != null and queryVo.detailNo != ''">
            and detail_no = #{queryVo.detailNo}
        </if>
        <if test="queryVo.no != null and queryVo.no != ''">
            and no = #{queryVo.no}
        </if>
        <if test="queryVo.admissionNo != null and queryVo.admissionNo != ''">
            and admission_no = #{queryVo.admissionNo}
        </if>
        <if test="queryVo.claimTypes != null and queryVo.claimTypes.size() >0">
            and claim_type in
            <foreach collection="queryVo.claimTypes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.billDateFrom != null and queryVo.billDateFrom != ''">
            and bill_date &gt;= concat(#{queryVo.billDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.billDateTo != null and queryVo.billDateTo != ''">
            and bill_date &lt;= concat(#{queryVo.billDateTo},' 23:59:59')
        </if>

        <if test="queryVo.patient != null and queryVo.patient != ''">
            and ( patient_id like CONCAT('%',#{queryVo.patient,jdbcType = VARCHAR},'%')
            or patient_name like CONCAT('%',#{queryVo.patient,jdbcType = VARCHAR},'%'))
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() >0">
            and dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() >0">
            and doc_Id in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.rules != null and queryVo.rules.size() >0">
            and rules in
            <foreach collection="queryVo.rules" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.item != null and queryVo.item != ''">
            and ( item_id like CONCAT('%',#{queryVo.item,jdbcType = VARCHAR},'%')
            or item_name like CONCAT('%',#{queryVo.item,jdbcType = VARCHAR},'%'))
        </if>

        <if test="queryVo.rule != null and queryVo.rule != ''">
            and rules like CONCAT('%',#{queryVo.rule,jdbcType = VARCHAR},'%')
        </if>

    </select>

    <select id="selectDeptList" resultType="java.util.Map">
        SELECT
        dept_code as code,
        dept_name as name
        from mqs_bmi_deduction
        WHERE hospital_id = #{hospitalId}
        <if test="dept != null and dept != ''">
            and ( dept_code like CONCAT('%',#{dept,jdbcType = VARCHAR},'%')
            or dept_name like CONCAT('%',#{dept,jdbcType = VARCHAR},'%'))
        </if>
        group by dept_code,dept_name
    </select>
    <select id="selectDoctorList" resultType="java.util.Map">
        SELECT
        doc_id as code,
        doc_name as name
        from mqs_bmi_deduction
        WHERE hospital_id = #{hospitalId}
        <if test="doctor != null and doctor != ''">
            and ( doc_id like CONCAT('%',#{doctor,jdbcType = VARCHAR},'%')
            or doc_name like CONCAT('%',#{doctor,jdbcType = VARCHAR},'%'))
        </if>
        group by doc_id,doc_name
    </select>

    <select id="selectRules" resultType="java.lang.String">
        SELECT
        rules AS rule
        FROM
        mqs_bmi_deduction
        WHERE hospital_id = #{hospitalId}
        <if test="rule != null and rule != ''">
            and rules like CONCAT('%',#{rule,jdbcType = VARCHAR},'%')
        </if>
        GROUP BY
        rules

    </select>

    <select id="indStatistics" resultType="java.util.Map">
        select
        COUNT( id ) AS total,
        COUNT( DISTINCT no ) AS totalCase,
        COUNT( DISTINCT item_id ) AS totalProject,
        IFNULL( sum( deduction_amount ), 0 ) AS totalAmount,
        COUNT( DISTINCT dept_code ) AS totalDept,
        COUNT( DISTINCT doc_id ) AS totalDoctor
        from mqs_bmi_deduction
        where hospital_id = #{hospitalId}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and dept_code = #{dept}
        </if>
    </select>
    <select id="genViolationTop" resultType="java.util.Map">
        select
        COUNT(id) AS total,
        ${orderBy} AS name
        from mqs_bmi_deduction
        where hospital_id = #{hospitalId}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and dept_code = #{dept}
        </if>
        GROUP BY
        ${orderBy}
        ORDER BY
        COUNT(id) DESC
        LIMIT 10
    </select>
    <select id="getAnalysisDetails" resultType="java.util.Map">
        select
        ${orderBy},
        COUNT( id ) AS total,
        IFNULL( sum( deduction_amount ), 0 ) AS totalAmount,
        COUNT( DISTINCT NO ) AS totalCase
        from mqs_bmi_deduction
        where hospital_id = #{hospitalId}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and dept_code = #{dept}
        </if>
        GROUP BY
        ${orderBy}
        ORDER BY
        totalAmount DESC
        LIMIT 10
    </select>
    <select id="getAnalysisTotal" resultType="java.math.BigDecimal">
        select
        COUNT( id ) AS total
        from mqs_bmi_deduction
        where hospital_id = #{hospitalId}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and dept_code = #{dept}
        </if>

    </select>
    <select id="getAnalysisByMonth" resultType="java.util.Map">
        SELECT
            DATE_FORMAT( item_date, '%Y-%m' ) monthDay,
            COUNT( id ) AS total,
            IFNULL( sum( deduction_amount ), 0 ) AS totalAmount
        FROM
            mqs_bmi_deduction
        WHERE
            hospital_id = '1'
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and dept_code = #{dept}
        </if>
        GROUP BY
            DATE_FORMAT(item_date, '%Y-%m')

    </select>
    <select id="selectClaimTypeList" resultType="java.lang.String">
        SELECT
        claim_type
        from mqs_bmi_deduction
        WHERE hospital_id = #{hospitalId}
        <if test="claimType != null and claimType != ''">
            and claim_type like CONCAT('%',#{claimType,jdbcType = VARCHAR},'%')
        </if>
        group by claim_type
    </select>

</mapper>

