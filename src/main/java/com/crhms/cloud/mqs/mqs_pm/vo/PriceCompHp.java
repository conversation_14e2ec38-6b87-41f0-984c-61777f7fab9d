package com.crhms.cloud.mqs.mqs_pm.vo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 物价对比-院内物价
 *
 * @author: wangxingang
 * @date: 2023.02.03
 */

@Data
@Builder
public class PriceCompHp {

    /**
     * 项目日期
     */
    private Date itemDate;

    /**
     * 院内物价-编码
     */
    private String hpCode;

    /**
     * 院内物价-名称
     */
    private String hpName;

    /**
     * 院内物价-分类
     */
    private String hpClassify;

    /**
     * 院内物价-计价单位
     */
    private String hpUnit;

    /**
     * 院内物价-价格(元)
     */
    private BigDecimal hpPrice;
}
