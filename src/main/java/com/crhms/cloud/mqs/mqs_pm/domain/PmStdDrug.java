package com.crhms.cloud.mqs.mqs_pm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 物价管理-市级标准物价-药品实体类
 *
 * @author: wangxingang
 * @date: 2023/01/31
 */

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("mqs_pm_std_drug")
public class PmStdDrug extends BaseDomain {
    @TableId
    private Long id;

    /**
     * 生效时间
     */
    @TableField
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private Date effectTime;

    /**
     * 药品编码
     */
    @TableField
    private String drugCode;

    /**
     * 药品名称
     */
    @TableField
    private String drugName;

    /**
     * 剂型
     */
    @TableField
    private String dosageForm;

    /**
     * 规格
     */
    @TableField
    private String specification;

    /**
     * 分类
     */
    @TableField
    private String classify;

    /**
     * 价格
     */
    @TableField
    private BigDecimal price;

    /**
     * 产地/制药厂家
     */
    @TableField
    private String origin;


}
