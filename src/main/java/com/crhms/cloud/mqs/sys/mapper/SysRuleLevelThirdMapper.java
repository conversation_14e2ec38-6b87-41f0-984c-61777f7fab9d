package com.crhms.cloud.mqs.sys.mapper;

import com.crhms.cloud.mqs.sys.domain.SysRuleLevelThird;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 三方规则级别映射(SysRuleLevelThird)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-21 17:53:36
 */

@Mapper
public interface SysRuleLevelThirdMapper extends BaseMapper<SysRuleLevelThird> {
    /**
     * 查询规则类型对应的规则级别列表
     *
     * @param platform   规则类型
     * @param hospitalId 院区ID
     * @return 规则级别列表
     */
    List<SysRuleLevelThird> ruleLevelList(@Param("platform") String platform,
                                          @Param("hospitalId") String hospitalId);
}

