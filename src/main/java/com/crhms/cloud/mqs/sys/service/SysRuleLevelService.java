package com.crhms.cloud.mqs.sys.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.stream.CollectorUtil;
import com.alibaba.nacos.shaded.org.checkerframework.checker.units.qual.A;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.sys.domain.SysRuleLevel;
import com.crhms.cloud.mqs.sys.domain.SysRuleLevelThird;
import com.crhms.cloud.mqs.sys.mapper.SysRuleLevelMapper;
import com.crhms.cloud.mqs.sys.service.SysRuleLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 规则级别配置表(SysRuleLevel)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-12 16:23:39
 */
@Service("sysRuleLevelService")
public class SysRuleLevelService extends ServiceImpl< SysRuleLevelMapper, SysRuleLevel> {
    @Autowired
    private SysRuleLevelMapper sysRuleLevelMapper;
    @Autowired
    private SysRuleLevelThirdService sysRuleLevelThirdService;

    /**
     * 查询系统规则级别配置
     * @param hospitalId
     * @return
     */
    public List<SysRuleLevel> queryRuleLevelList(String hospitalId) {

        return this.list(new QueryWrapper<SysRuleLevel>().eq(SysRuleLevel.FIELD_HOSPITAL_ID, hospitalId).orderByAsc(SysRuleLevel.FIELD_CODE));

    }

    /**
     * 查询三方规则级别配置
     * @param platform
     * @param hospitalId
     * @return
     */
    public List<SysRuleLevelThird> queryThirdLevelList(String platform, String hospitalId) {
        return sysRuleLevelMapper.queryThirdLevelList(platform,hospitalId);
    }


    public Map queryAllLevelList(String hospitalId) {

        List<SysRuleLevel> collect1 = this.list(new QueryWrapper<SysRuleLevel>().eq(SysRuleLevel.FIELD_HOSPITAL_ID, hospitalId).orderByAsc(SysRuleLevel.FIELD_CODE));
        List<SysRuleLevelThird> ThirdLevels = sysRuleLevelMapper.queryThirdLevelList(null, hospitalId);
        List<SysRuleLevelThird> collect2 = ThirdLevels.stream().filter(x -> "2".equals(x.getPlatform())).collect(Collectors.toList());
        List<SysRuleLevelThird> collect3 = ThirdLevels.stream().filter(x -> "3".equals(x.getPlatform())).collect(Collectors.toList());
        Map<String,Object> reslut = new HashMap<>();
        reslut.put("collect1",collect1);
        reslut.put("collect2",collect2);
        reslut.put("collect3",collect3);

        return reslut;
    }

    public void editConnfig(List<SysRuleLevel> collect1, List<SysRuleLevelThird> collect2, List<SysRuleLevelThird> collect3) {
        if(CollectionUtil.isNotEmpty(collect1)){
            for (SysRuleLevel sysRuleLevel : collect1) {
                if("1".equals(sysRuleLevel.getConfig1())){
                    sysRuleLevel.setLevelConfig("1");
                }
                if("1".equals(sysRuleLevel.getConfig2())){
                    sysRuleLevel.setLevelConfig("2");
                }
                if("1".equals(sysRuleLevel.getConfig3())){
                    sysRuleLevel.setLevelConfig("3");
                }
                if("1".equals(sysRuleLevel.getConfig4())){
                    sysRuleLevel.setLevelConfig("4");
                }
            }
            this.updateBatchById(collect1);
        }
        if(CollectionUtil.isNotEmpty(collect2)){
            sysRuleLevelThirdService.updateBatchById(collect2);
        }
        if(CollectionUtil.isNotEmpty(collect3)){
            sysRuleLevelThirdService.updateBatchById(collect3);
        }
    }
}
