package com.crhms.cloud.mqs.mqs_pm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.excel.dto.ExcelExport;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.mqs_hp.domain.HpSettleDetail;
import com.crhms.cloud.mqs.mqs_hp.service.HpSettleDetailService;
import com.crhms.cloud.mqs.mqs_op.domain.OpPtDetail;
import com.crhms.cloud.mqs.mqs_op.mapper.OpPtDetailMapper;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdConsume;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdDrug;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdServiceItem;
import com.crhms.cloud.mqs.mqs_pm.mapper.PmPriceCompMapper;
import com.crhms.cloud.mqs.mqs_pm.vo.PmPriceCompQueryVo;
import com.crhms.cloud.mqs.mqs_pm.vo.PriceCompHp;
import com.crhms.cloud.mqs.mqs_pm.vo.PriceCompStd;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 物价管理-院内物价对比 service
 *
 * @author: wangxingang
 * @date: 2023/02/02
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class PmPriceCompService {

    /**
     * 物价对比Mapper
     */
    @Autowired
    private PmPriceCompMapper mapper;

    /**
     * 门诊缴费审核mapper
     */
    @Autowired
    private OpPtDetailMapper opPtDetailMapper;

    /**
     * 住院结算审核service
     */
    @Autowired
    private HpSettleDetailService settleService;


    /**
     * 分页查询
     *
     * @param queryVo  查询条件
     * @param pageInfo 分页信息
     */
    public List<Map<String, Object>> queryPage(PmPriceCompQueryVo queryVo, Page<Object> pageInfo) {
        // 查询条件
        List<String> categoryList = queryVo.getCategoryList();
        String projectCode = queryVo.getProjectCode();
        String startPeriod = queryVo.getStartPeriod();
        String endPeriod = queryVo.getEndPeriod();
        List<String> compResult = queryVo.getCompResult();
        List<String> diffCategory = queryVo.getDiffCategory();


        // 所有的标准物价
        List<PriceCompStd> stdLists = getStdPriceList(categoryList, projectCode, startPeriod, endPeriod);

        // 分页查询
        List<List<PriceCompStd>> split = CollUtil.split(stdLists, (int) pageInfo.getSize());
        if (pageInfo.getCurrent() > split.size()) {
            return new ArrayList<>();
        }

        // 只要这一个分页的结果
        List<PriceCompStd> stdList = split.get((int) pageInfo.getCurrent() - 1);

        List<Map<String, Object>> resultList = new ArrayList<>();
        // 根据每一条标准物价找到对应的院内物价
        for (PriceCompStd stdPrice : stdList) {
            // 标准物价和院内物价是一对多
            List<PriceCompHp> hpPriceList = getHpPriceList(stdPrice.getStdCode());
            Map<String, Object> resultItem = getResultItem(stdPrice, hpPriceList);
            resultList.add(resultItem);
        }

        // 院内物价 - 列表中间部分列
//        List<PriceCompHp> hpList = getHpPriceList(projectCode);

        // 根据标准物价的时间周期和物价编码, 找到周期内最新的院内数据
//        List<Map<String, Object>> resultList = getResultList(stdList, hpList);

        // 根据查询条件过滤出符合条件的返回结果
        List<Map<String, Object>> finalResult = getFinalResult(resultList, compResult, diffCategory);

        pageInfo.setTotal(stdLists.size());
        return finalResult;
    }

    private Map<String, Object> getResultItem(PriceCompStd stdPrice, List<PriceCompHp> hpPriceList) {
        Map<String, Object> resultItem = new HashMap<>();
        resultItem.put("EFFECT_TIME", stdPrice.getEffectTime());
        resultItem.put("CATEGORY", stdPrice.getCategory());
        resultItem.put("STD_CODE", stdPrice.getStdCode());
        resultItem.put("STD_NAME", stdPrice.getStdName());
        resultItem.put("STD_CLASSIFY", stdPrice.getStdClassify());
        resultItem.put("STD_UNIT", stdPrice.getStdUnit());
        resultItem.put("STD_PRICE", stdPrice.getStdPrice());

        PriceCompHp latestHp = findLatestHp(stdPrice.getStdCode(), hpPriceList);

        if (latestHp == null) {
            // 差异大类 = 新增
            resultItem.put("COMPARE_RESULT", "有差异");
            resultItem.put("DIFF_CATEGORY", "新增");
            resultItem.put("DIFF_CONTENT", "-");
        } else {
            resultItem.put("HP_CODE", latestHp.getHpCode());
            resultItem.put("HP_NAME", latestHp.getHpName());
            resultItem.put("HP_CLASSIFY", latestHp.getHpClassify());
            resultItem.put("HP_UNIT", latestHp.getHpUnit());
            resultItem.put("HP_PRICE", latestHp.getHpPrice());

            if (stdPrice.getStdPrice().compareTo(latestHp.getHpPrice()) > 0) {
                // 标准 > 院内 -> 价格上调
                resultItem.put("COMPARE_RESULT", "有差异");
                resultItem.put("DIFF_CATEGORY", "价格上调");
                resultItem.put("DIFF_CONTENT", "标准:" + stdPrice.getStdPrice() + " 院内:" + latestHp.getHpPrice());
            } else if (stdPrice.getStdPrice().compareTo(latestHp.getHpPrice()) < 0) {
                // 标准 < 院内 -> 价格下调
                resultItem.put("COMPARE_RESULT", "有差异");
                resultItem.put("DIFF_CATEGORY", "价格下调");
                resultItem.put("DIFF_CONTENT", "标准:" + stdPrice.getStdPrice() + " 院内:" + latestHp.getHpPrice());
            } else {
                // 标准 = 院内 -> 无差异
                resultItem.put("COMPARE_RESULT", "无差异");
                resultItem.put("DIFF_CATEGORY", "");
                resultItem.put("DIFF_CONTENT", "");
            }
            resultItem.put("DIFF_CONTENT", "标准:" + stdPrice.getStdPrice() + " 院内:" + latestHp.getHpPrice());
        }
        return resultItem;
    }

    private List<Map<String, Object>> getFinalResult(List<Map<String, Object>> resultList, List<String> compResult, List<String> diffCategory) {
        List<Map<String, Object>> finalResult = new ArrayList<>();

        if (CollUtil.isEmpty(compResult) && CollUtil.isEmpty(diffCategory)) {
            finalResult.addAll(resultList);
        } else if (CollUtil.isNotEmpty(compResult) && CollUtil.isEmpty(diffCategory)) {
            // 根据有差异,无差异筛选
            for (Map<String, Object> map : resultList) {
                if (compResult.contains(String.valueOf(map.get("COMPARE_RESULT")))) {
                    finalResult.add(map);
                }
            }
        } else if (CollUtil.isEmpty(compResult) && CollUtil.isNotEmpty(diffCategory)) {
            for (Map<String, Object> map : resultList) {
                if (diffCategory.contains(String.valueOf(map.get("DIFF_CATEGORY")))) {
                    finalResult.add(map);
                }
            }
        } else {
            // 两个筛选条件
            for (Map<String, Object> map : resultList) {
                if (compResult.contains(String.valueOf(map.get("COMPARE_RESULT"))) && diffCategory.contains(String.valueOf(map.get("DIFF_CATEGORY")))) {
                    finalResult.add(map);
                }
            }
        }

        return finalResult;
    }

    private List<Map<String, Object>> getResultList(List<PriceCompStd> stdList, List<PriceCompHp> hpList) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        Map<String, Object> result;
        for (PriceCompStd stdPrice : stdList) {
            result = new HashMap<>();
            result.put("EFFECT_TIME", stdPrice.getEffectTime());
            result.put("CATEGORY", stdPrice.getCategory());
            result.put("STD_CODE", stdPrice.getStdCode());
            result.put("STD_NAME", stdPrice.getStdName());
            result.put("STD_CLASSIFY", stdPrice.getStdClassify());
            result.put("STD_UNIT", stdPrice.getStdUnit());
            result.put("STD_PRICE", stdPrice.getStdPrice());

            // 找到周期内最新的院内数据
            PriceCompHp latestHp = findLatestHp(stdPrice.getStdCode(), hpList);

            if (latestHp == null) {
                // 差异大类 = 新增
                result.put("COMPARE_RESULT", "有差异");
                result.put("DIFF_CATEGORY", "新增");
                result.put("DIFF_CONTENT", "-");
            } else {
                result.put("HP_CODE", latestHp.getHpCode());
                result.put("HP_NAME", latestHp.getHpName());
                result.put("HP_CLASSIFY", latestHp.getHpClassify());
                result.put("HP_UNIT", latestHp.getHpUnit());
                result.put("HP_PRICE", latestHp.getHpPrice());

                if (stdPrice.getStdPrice().compareTo(latestHp.getHpPrice()) > 0) {
                    // 标准 > 院内 -> 价格上调
                    result.put("COMPARE_RESULT", "有差异");
                    result.put("DIFF_CATEGORY", "价格上调");
                    result.put("DIFF_CONTENT", "标准:" + stdPrice.getStdPrice() + " 院内:" + latestHp.getHpPrice());
                } else if (stdPrice.getStdPrice().compareTo(latestHp.getHpPrice()) < 0) {
                    // 标准 < 院内 -> 价格下调
                    result.put("COMPARE_RESULT", "有差异");
                    result.put("DIFF_CATEGORY", "价格下调");
                    result.put("DIFF_CONTENT", "标准:" + stdPrice.getStdPrice() + " 院内:" + latestHp.getHpPrice());
                } else {
                    // 标准 = 院内 -> 无差异
                    result.put("COMPARE_RESULT", "无差异");
                    result.put("DIFF_CATEGORY", "");
                    result.put("DIFF_CONTENT", "");
                }
                result.put("DIFF_CONTENT", "标准:" + stdPrice.getStdPrice() + " 院内:" + latestHp.getHpPrice());
            }

            resultList.add(result);
        }

        return resultList;
    }


    /**
     * 院内物价 - 列表中间部分列
     * @param projectCode 项目编码
     */
    private List<PriceCompHp> getHpPriceList(String projectCode) {
        // 查询出所有门诊缴费数据和住院结算数据
        List<OpPtDetail> opPtDetailList = opPtDetailMapper.selectList(new LambdaQueryWrapper<OpPtDetail>()
                .eq(StrUtil.isNotEmpty(projectCode), OpPtDetail::getItemId, projectCode)
                .select(OpPtDetail::getItemDate, OpPtDetail::getItemId, OpPtDetail::getItemName, OpPtDetail::getUsageUnit, OpPtDetail::getPrice));
        List<HpSettleDetail> settleDetailList = settleService.list(new LambdaQueryWrapper<HpSettleDetail>()
                .eq(StrUtil.isNotEmpty(projectCode), HpSettleDetail::getItemId, projectCode)
                .select(HpSettleDetail::getItemDate, HpSettleDetail::getItemId, HpSettleDetail::getItemName, HpSettleDetail::getUsageUnit, HpSettleDetail::getPrice));

        // 院内物价统一结构
        List<PriceCompHp> hpList = new ArrayList<>();
        for (OpPtDetail opPtDetail : opPtDetailList) {
            hpList.add(PriceCompHp.builder()
                    .itemDate(opPtDetail.getItemDate())
                    .hpCode(String.valueOf(opPtDetail.getItemId()))
                    .hpName(opPtDetail.getItemName())
                    .hpUnit(opPtDetail.getUsageUnit())
                    .hpPrice(opPtDetail.getPrice())
                    .build());
        }
        for (HpSettleDetail settleDetail : settleDetailList) {
            hpList.add(PriceCompHp.builder()
                    .itemDate(settleDetail.getItemDate())
                    .hpCode(String.valueOf(settleDetail.getItemId()))
                    .hpName(settleDetail.getItemName())
                    .hpUnit(settleDetail.getUsageUnit())
                    .hpPrice(settleDetail.getPrice())
                    .build());
        }

        return hpList;
    }


    /**
     * 标准物价 - 列表左边部分列
     *
     * @param categoryList 目录大类
     * @param projectCode  项目编码
     * @param startPeriod  时间周期-开始时间
     * @param endPeriod    时间周期-结束时间
     */
    private List<PriceCompStd> getStdPriceList(List<String> categoryList, String projectCode, String startPeriod, String endPeriod) {
        // 查询出所有服务项目、药品和耗材三种大类的市级标准物价
        List<PmStdServiceItem> serviceItemList = new ArrayList<>();
        List<PmStdDrug> drugList = new ArrayList<>();
        List<PmStdConsume> consumeList = new ArrayList<>();
        if (categoryList.contains("服务项目") || CollUtil.isEmpty(categoryList)) {
            serviceItemList = mapper.queryServiceItem(projectCode, startPeriod, endPeriod);

        }
        if (categoryList.contains("药品") || CollUtil.isEmpty(categoryList)) {
            drugList = mapper.queryDrug(projectCode, startPeriod, endPeriod);
        }
        if (categoryList.contains("耗材") || CollUtil.isEmpty(categoryList)) {
            consumeList = mapper.queryConsume(projectCode, startPeriod, endPeriod);
        }

        // 标准物价统一结构
        List<PriceCompStd> stdLists = new ArrayList<>();
        if (CollUtil.isNotEmpty(serviceItemList)) {
            for (PmStdServiceItem serviceItem : serviceItemList) {
                stdLists.add(PriceCompStd.builder()
                        .effectTime(DateUtil.format(serviceItem.getEffectTime(), "yyyy.MM.dd"))
                        .category("服务项目")
                        .stdCode(serviceItem.getProjectCode())
                        .stdName(serviceItem.getProjectName())
                        .stdUnit(serviceItem.getUnit())
                        .stdPrice(serviceItem.getPrice())
                        .build());
            }
        }
        if (CollUtil.isNotEmpty(drugList)) {
            for (PmStdDrug drug : drugList) {
                stdLists.add(PriceCompStd.builder()
                        .effectTime(DateUtil.format(drug.getEffectTime(), "yyyy.MM.dd"))
                        .category("药品")
                        .stdCode(drug.getDrugCode())
                        .stdName(drug.getDrugName())
                        .stdUnit(drug.getSpecification())
                        .stdPrice(drug.getPrice())
                        .build());
            }
        }
        if (CollUtil.isNotEmpty(consumeList)) {
            for (PmStdConsume consume : consumeList) {
                stdLists.add(PriceCompStd.builder()
                        .effectTime(DateUtil.format(consume.getEffectTime(), "yyyy.MM.dd"))
                        .category("耗材")
                        .stdCode(consume.getProductCode())
                        .stdName(consume.getProductName())
                        .stdUnit(consume.getSpecification())
                        .stdPrice(consume.getPrice())
                        .build());
            }
        }

        return stdLists;
    }

    /**
     * 找到周期内最新的院内数据
     *
     * @param stdCode    标准物价编码
     * @param hpList     所有的院内数据
     */
    private PriceCompHp findLatestHp(String stdCode, List<PriceCompHp> hpList) {


        PriceCompHp latestHp = null;
        List<PriceCompHp> insideList = new ArrayList<>();
        // 找到编码对应的所有的数据
        for (PriceCompHp hpPrice : hpList) {
            if (StrUtil.contains(hpPrice.getHpCode(), stdCode)) {
                insideList.add(hpPrice);
            }
        }
        // 找到所有数据中最新的数据
        if (CollUtil.isNotEmpty(insideList)) {
            latestHp = insideList.get(0);
            for (PriceCompHp one : insideList) {
                if (one.getItemDate().after(latestHp.getItemDate())) {
                    latestHp = one;
                }
            }
        }
        return latestHp;
    }


    /**
     * 导出excel
     *
     * @param response 响应
     * @param queryVo  查询条件
     */
    public void exportExcel(HttpServletResponse response, PmPriceCompQueryVo queryVo) {
        // 表头
        List<List<String>> heads = new ArrayList<>();
        heads.add(CollUtil.list(true, "EFFECT_TIME", "CATEGORY", "STD_CODE", "STD_NAME", "STD_UNIT", "STD_PRICE", "HP_CODE", "HP_NAME", "HP_UNIT", "HP_PRICE", "COMPARE_RESULT", "DIFF_CATEGORY", "DIFF_CONTENT"));
        heads.add(CollUtil.list(true, "生效日期", "目录大类", "标准物价-编码", "标准物价-名称", "标准物价-计价单位", "标准物价-价格(元)", "院内物价-编码", "院内物价-名称", "院内物价-计价单位", "院内物价-价格(元)", "对比结果", "差异大类", "差异内容"));

        // 数据
        List<List<Object>> datas = new ArrayList<>();


        // 查询条件
        List<String> categoryList = queryVo.getCategoryList();
        String projectCode = queryVo.getProjectCode();
        String startPeriod = queryVo.getStartPeriod();
        String endPeriod = queryVo.getEndPeriod();
        List<String> compResult = queryVo.getCompResult();
        List<String> diffCategory = queryVo.getDiffCategory();

        // 标准物价 - 列表左边部分列
        List<PriceCompStd> stdLists = getStdPriceList(categoryList, projectCode, startPeriod, endPeriod);

        // 院内物价
        List<PriceCompHp> hpList = getHpPriceList(projectCode);

        // 根据标准物价的时间周期和物价编码, 找到周期内最新的院内数据
        List<Map<String, Object>> resultList = getResultList(stdLists, hpList);

        // 根据查询条件过滤出符合条件的返回结果
        List<Map<String, Object>> finalResult = getFinalResult(resultList, compResult, diffCategory);

        // 拼接excel数据
        for (Map<String, Object> map : finalResult) {
            datas.add(CollUtil.list(true, map.getOrDefault("EFFECT_TIME", ""),
                    map.getOrDefault("CATEGORY", ""),
                    map.getOrDefault("STD_CODE", ""),
                    map.getOrDefault("STD_NAME", ""),
                    map.getOrDefault("STD_UNIT", ""),
                    map.getOrDefault("STD_PRICE", ""),
                    map.getOrDefault("HP_CODE", ""),
                    map.getOrDefault("HP_NAME", ""),
                    map.getOrDefault("HP_UNIT", ""),
                    map.getOrDefault("HP_PRICE", ""),
                    map.getOrDefault("COMPARE_RESULT", ""),
                    map.getOrDefault("DIFF_CATEGORY", ""),
                    map.getOrDefault("DIFF_CONTENT", "")
            ));
        }

        StringBuilder fileName = new StringBuilder();
        fileName.append("物价管理-院内物价对比");
        MqsUtils.buildExportFileNameSuffix(fileName, queryVo.getStartPeriod(), queryVo.getEndPeriod());

        // 导出excel
        List<ExcelExport> exports = new ArrayList<>();
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);

    }
}
