package com.crhms.cloud.mqs.mqs_mr.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import java.io.Serializable;


/**
 * 人工转违规表
 *
 * <AUTHOR>
 * @since 2023-05-02 10:06:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_mr_audit")
public class MrAudit extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -56021938452238409L;

    @TableId
    private Long id;

    @TableField
    private String detailNo;

    @TableField
    private String no;

    @TableField
    private String admissionNo;

    @TableField
    private String violationFlag;

    @TableField
    private String selfExpense;

    @TableField
    private String mrOpinion;

    @TableField
    private String mrSelfOpinion;

    @TableField
    private String ruleCode;

    @TableField
    private String ruleName;

    @TableField
    private String ruleType;

    @TableField
    private String ruleTypeName;

    @TableField
    private String ruleReason;

    @TableField
    private String itemId;

    @TableField
    private String itemName;

    @TableField
    private String hospitalId;

}
