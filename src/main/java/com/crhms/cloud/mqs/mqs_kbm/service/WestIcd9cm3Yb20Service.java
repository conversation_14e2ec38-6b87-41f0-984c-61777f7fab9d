package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.WestIcd9cm3Yb20;
import com.crhms.cloud.mqs.mqs_kbm.mapper.WestIcd9cm3Yb20Mapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * ICD-9CM3医保版CHS2.0
 *
 * @author: wangxingang
 * @date: 2023.02.06
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class WestIcd9cm3Yb20Service extends ServiceImpl<WestIcd9cm3Yb20Mapper, WestIcd9cm3Yb20> {

    /**
     * ICD-9CM3医保版CHS2.0
     *
     * @param pageInfo 分页信息
     * @param code     手术操作代码
     * @param name     手术操作名称
     */
    public List<WestIcd9cm3Yb20> queryIcd9Yb20(Page<WestIcd9cm3Yb20> pageInfo, String code, String name) {
        return page(pageInfo, new LambdaQueryWrapper<WestIcd9cm3Yb20>()
                .like(StrUtil.isNotEmpty(code), WestIcd9cm3Yb20::getCode, code)
                .like(StrUtil.isNotEmpty(name), WestIcd9cm3Yb20::getName, name)).getRecords();
    }
}
