package com.crhms.cloud.mqs.basic.vo;

import lombok.Data;

import java.util.List;

/**
 * 强制保存反馈原因和自费标识类
 */
@Data
public class ClinicalQueryVo {
    //审核场景
    private String auditScenario;
     //就诊流水号
    private String admissionNo;
    //来源 1.机审 2.人工
    private String violationFlag;
    //自费状态 0.医保 1.自费
    private String selfExpense;
    //项目名称
    private String item;
    //项目类型  多选
    private List<String> itemTypes;
    //院区id
    private String hospitalId;
}
