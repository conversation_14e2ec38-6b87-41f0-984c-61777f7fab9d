package com.crhms.cloud.mqs.basic.mapper;


import com.crhms.cloud.mqs.basic.vo.HisOperationDetailVo;
import com.crhms.cloud.mqs.basic.vo.HisOperationMainVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 住院审核 - 手术记录明细表(HpOperationList)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-02 15:11:58
 */

@Mapper
public interface HpOperationMapper {


    List<HisOperationMainVo> queryOperationList(@Param("admissionNo") String admissionNo, @Param("no") String no, @Param("hospitalId") String hospitalId);

    List<HisOperationDetailVo> queryOperationDetailList(@Param("admissionNo") String admissionNo, @Param("no") String no, @Param("hospitalId") String hospitalId);

    List<HisOperationMainVo> queryOperationListHis(@Param("admissionNo") String admissionNo, @Param("no") String no, @Param("batch") String batch, @Param("hospitalId") String hospitalId);

    List<HisOperationDetailVo> queryOperationDetailListHis(@Param("admissionNo") String admissionNo, @Param("no") String no, @Param("batch") String batch, @Param("hospitalId") String hospitalId);

    void batchSaveOperations(@Param("entities") List<HisOperationMainVo> operation_list);

    void batchSaveList(@Param("entities") List<HisOperationDetailVo> operation_detail_list);

    void deleteOperationByAdNo(@Param("operationIds") List<String> operationIds, @Param("nos") List<String> nos, @Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    void deleteOperationListByAdNo(@Param("operationIds") List<String> operationIds, @Param("nos") List<String> nos, @Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    void batchSaveOperationsHis(@Param("entities")  List<HisOperationMainVo> operation_list);

    void batchSaveListHis(@Param("entities")  List<HisOperationDetailVo> operation_detail_list);
}

