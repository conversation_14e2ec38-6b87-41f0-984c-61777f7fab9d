package com.crhms.cloud.mqs.mqs_kbm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.mqs_kbm.domain.*;
import com.crhms.cloud.mqs.mqs_kbm.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 知识库管理-西医诊断手术controller
 * ICD-10国家临床版2.0
 * ICD-9-CM3国家临床版3.0
 * ICD-10医保版CHS1.0
 * ICD-10医保版CHS2.0
 * ICD-9CM3医保版CHS1.0
 * ICD-9CM3医保版CHS2.0
 *
 * @author: wangxingang
 * @date: 2023.02.06
 */
@RestController
@RequestMapping("/api/mqs/kbm/west")
public class WestController {

    @Autowired
    private WestIcd10Gl20Service gl20Service;

    @Autowired
    WestIcd9cm3Gl30Service gl30Service;

    @Autowired
    WestIcd10Yb10Service icd10Yb10Service;

    @Autowired
    WestIcd10Yb20Service icd10Yb20Service;

    @Autowired
    WestIcd9cm3Yb10Service icd9Yb10Service;

    @Autowired
    WestIcd9cm3Yb20Service icd9Yb20Service;


    /**
     * ICD-10国家临床版2.0
     *
     * @param code 诊断编码
     * @param name 诊断名称
     */
    @GetMapping("/gl20/page")
    public ResponseEntity<List<WestIcd10Gl20>> queryGl20(@RequestParam(value = "code", required = false) String code,
                                                         @RequestParam(value = "name", required = false) String name,
                                                         @RequestParam(value = "page", defaultValue = "1") int page,
                                                         @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<WestIcd10Gl20> pageInfo = PageUtil.getPage(page, pageSize);
        List<WestIcd10Gl20> gl20List = gl20Service.queryGl20(pageInfo, code, name);
        return new ResponseEntity<>(gl20List, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * ICD-9-CM3国家临床版3.0
     *
     * @param code 手术代码
     * @param name 手术名称
     */
    @GetMapping("/gl30/page")
    public ResponseEntity<List<WestIcd9cm3Gl30>> queryGl30(@RequestParam(value = "code", required = false) String code,
                                                           @RequestParam(value = "name", required = false) String name,
                                                           @RequestParam(value = "page", defaultValue = "1") int page,
                                                           @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<WestIcd9cm3Gl30> pageInfo = PageUtil.getPage(page, pageSize);
        List<WestIcd9cm3Gl30> gl30List = gl30Service.queryGl30(pageInfo, code, name);
        return new ResponseEntity<>(gl30List, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * ICD-10医保版CHS1.0
     *
     * @param code 诊断代码
     * @param name 诊断名称
     */
    @GetMapping("/icd10Yb10/page")
    public ResponseEntity<List<WestIcd10Yb10>> queryIcd10Yb10(@RequestParam(value = "code", required = false) String code,
                                                              @RequestParam(value = "name", required = false) String name,
                                                              @RequestParam(value = "page", defaultValue = "1") int page,
                                                              @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<WestIcd10Yb10> pageInfo = PageUtil.getPage(page, pageSize);
        List<WestIcd10Yb10> icd10Yb10List = icd10Yb10Service.queryIcd10Yb10(pageInfo, code, name);
        return new ResponseEntity<>(icd10Yb10List, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * ICD-10医保版CHS2.0
     *
     * @param code 诊断代码
     * @param name 诊断名称
     */
    @GetMapping("/icd10Yb20/page")
    public ResponseEntity<List<WestIcd10Yb20>> queryIcd10Yb20(@RequestParam(value = "code", required = false) String code,
                                                              @RequestParam(value = "name", required = false) String name,
                                                              @RequestParam(value = "page", defaultValue = "1") int page,
                                                              @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<WestIcd10Yb20> pageInfo = PageUtil.getPage(page, pageSize);
        List<WestIcd10Yb20> icd10Yb20List = icd10Yb20Service.queryIcd10Yb20(pageInfo, code, name);
        return new ResponseEntity<>(icd10Yb20List, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * ICD-9CM3医保版CHS1.0
     *
     * @param code 手术操作代码
     * @param name 手术操作名称
     */
    @GetMapping("/icd9Yb10/page")
    public ResponseEntity<List<WestIcd9cm3Yb10>> queryIcd9Yb10(@RequestParam(value = "code", required = false) String code,
                                                               @RequestParam(value = "name", required = false) String name,
                                                               @RequestParam(value = "page", defaultValue = "1") int page,
                                                               @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<WestIcd9cm3Yb10> pageInfo = PageUtil.getPage(page, pageSize);
        List<WestIcd9cm3Yb10> icd9Yb10List = icd9Yb10Service.queryIcd9Yb10(pageInfo, code, name);
        return new ResponseEntity<>(icd9Yb10List, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * ICD-9CM3医保版CHS2.0
     *
     * @param code 手术操作代码
     * @param name 手术操作名称
     */
    @GetMapping("/icd9Yb20/page")
    public ResponseEntity<List<WestIcd9cm3Yb20>> queryIcd9Yb20(@RequestParam(value = "code", required = false) String code,
                                                               @RequestParam(value = "name", required = false) String name,
                                                               @RequestParam(value = "page", defaultValue = "1") int page,
                                                               @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<WestIcd9cm3Yb20> pageInfo = PageUtil.getPage(page, pageSize);
        List<WestIcd9cm3Yb20> icd9Yb20List = icd9Yb20Service.queryIcd9Yb20(pageInfo, code, name);
        return new ResponseEntity<>(icd9Yb20List, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

}
