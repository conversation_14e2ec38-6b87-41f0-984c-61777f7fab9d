package com.crhms.cloud.mqs.sys.dto;



import lombok.Data;

import java.util.List;

/**
 * 特殊人群分页查询条件
 *
 * @author: 王新刚
 * @date: 2023/03/24
 */
@Data
public class SpecialPatientQueryVO {

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者类型 可多选, 传空则查全部
     */
    private List<String> patientTypeList;

    /**
     * 性别: 传空则查全部
     */
    private String gender;

    /**
     * 证件号码
     */
    private String documentNo;

    private int page;
    private int pageSize;

    /**
     * 是否导出模板: ture是false否
     */
    private Boolean isModel;
}
