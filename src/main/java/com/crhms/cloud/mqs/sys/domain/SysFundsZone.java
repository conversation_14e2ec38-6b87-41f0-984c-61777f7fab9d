package com.crhms.cloud.mqs.sys.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 运营管理-基金管理-院区(SysFundsZone)实体类
 *
 * <AUTHOR>
 * @since 2023-03-21 09:28:07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_funds_zone")
public class SysFundsZone extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 907115468007267709L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_FUNDS_ID="funds_id";
    public static final String FIELD_CODE="code";
    public static final String FIELD_NAME="name";
    public static final String FIELD_YEARS_AMOUNT="years_amount";
    public static final String FIELD_YEARS_WARN="years_warn";
    public static final String FIELD_USED_AMOUNT="used_amount";
    public static final String FIELD_USED_RATIO="used_ratio";
    public static final String FIELD_AMOUNT1="amount1";
    public static final String FIELD_WARN1="warn1";
    public static final String FIELD_USED_AMOUNT_MONTH1="used_amount_month1";
    public static final String FIELD_AMOUNT2="amount2";
    public static final String FIELD_WARN2="warn2";
    public static final String FIELD_USED_AMOUNT_MONTH2="used_amount_month2";
    public static final String FIELD_AMOUNT3="amount3";
    public static final String FIELD_WARN3="warn3";
    public static final String FIELD_USED_AMOUNT_MONTH3="used_amount_month3";
    public static final String FIELD_AMOUNT4="amount4";
    public static final String FIELD_WARN4="warn4";
    public static final String FIELD_USED_AMOUNT_MONTH4="used_amount_month4";
    public static final String FIELD_AMOUNT5="amount5";
    public static final String FIELD_WARN5="warn5";
    public static final String FIELD_USED_AMOUNT_MONTH5="used_amount_month5";
    public static final String FIELD_AMOUNT6="amount6";
    public static final String FIELD_WARN6="warn6";
    public static final String FIELD_USED_AMOUNT_MONTH6="used_amount_month6";
    public static final String FIELD_AMOUNT7="amount7";
    public static final String FIELD_WARN7="warn7";
    public static final String FIELD_USED_AMOUNT_MONTH7="used_amount_month7";
    public static final String FIELD_AMOUNT8="amount8";
    public static final String FIELD_WARN8="warn8";
    public static final String FIELD_USED_AMOUNT_MONTH8="used_amount_month8";
    public static final String FIELD_AMOUNT9="amount9";
    public static final String FIELD_WARN9="warn9";
    public static final String FIELD_USED_AMOUNT_MONTH9="used_amount_month9";
    public static final String FIELD_AMOUNT10="amount10";
    public static final String FIELD_WARN10="warn10";
    public static final String FIELD_USED_AMOUNT_MONTH10="used_amount_month10";
    public static final String FIELD_AMOUNT11="amount11";
    public static final String FIELD_WARN11="warn11";
    public static final String FIELD_USED_AMOUNT_MONTH11="used_amount_month11";
    public static final String FIELD_AMOUNT12="amount12";
    public static final String FIELD_WARN12="warn12";
    public static final String FIELD_USED_AMOUNT_MONTH12="used_amount_month12";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 头id
     */    
    @TableField
    private Long fundsId;
    /**
     * 病区编码
     */    
    @TableField
    private String code;
    /**
     * 病区名称
     */    
    @TableField
    private String name;
    /**
     * 年度基金
     */    
    @TableField
    private BigDecimal yearsAmount;
    /**
     * 年度预警值
     */    
    @TableField
    private BigDecimal yearsWarn;
    /**
     * 年度预警占比
     */
    @TableField(exist = false)
    private BigDecimal yearsWarnRatio;
    /**
     * 已使用金额
     */    
    @TableField
    private BigDecimal usedAmount;
    /**
     * 占比
     */    
    @TableField
    private BigDecimal usedRatio;
    /**
     * 月度基金1
     */    
    @TableField
    private BigDecimal amount1;
    /**
     * 月度预警值1
     */    
    @TableField
    private BigDecimal warn1;
    /**
     * 月度已使用金额1
     */    
    @TableField
    private BigDecimal usedAmountMonth1;
    /**
     * 月度基金2
     */    
    @TableField
    private BigDecimal amount2;
    /**
     * 月度预警值2
     */    
    @TableField
    private BigDecimal warn2;
    /**
     * 月度已使用金额2
     */    
    @TableField
    private BigDecimal usedAmountMonth2;
    /**
     * 月度基金3
     */    
    @TableField
    private BigDecimal amount3;
    /**
     * 月度预警值3
     */    
    @TableField
    private BigDecimal warn3;
    /**
     * 月度已使用金额3
     */    
    @TableField
    private BigDecimal usedAmountMonth3;
    /**
     * 月度基金4
     */    
    @TableField
    private BigDecimal amount4;
    /**
     * 月度预警值4
     */    
    @TableField
    private BigDecimal warn4;
    /**
     * 月度已使用金额4
     */    
    @TableField
    private BigDecimal usedAmountMonth4;
    /**
     * 月度基金5
     */    
    @TableField
    private BigDecimal amount5;
    /**
     * 月度预警值5
     */    
    @TableField
    private BigDecimal warn5;
    /**
     * 月度已使用金额5
     */    
    @TableField
    private BigDecimal usedAmountMonth5;
    /**
     * 月度基金6
     */    
    @TableField
    private BigDecimal amount6;
    /**
     * 月度预警值6
     */    
    @TableField
    private BigDecimal warn6;
    /**
     * 月度已使用金额6
     */    
    @TableField
    private BigDecimal usedAmountMonth6;
    /**
     * 月度基金7
     */    
    @TableField
    private BigDecimal amount7;
    /**
     * 月度预警值7
     */    
    @TableField
    private BigDecimal warn7;
    /**
     * 月度已使用金额7
     */    
    @TableField
    private BigDecimal usedAmountMonth7;
    /**
     * 月度基金8
     */    
    @TableField
    private BigDecimal amount8;
    /**
     * 月度预警值8
     */    
    @TableField
    private BigDecimal warn8;
    /**
     * 月度已使用金额8
     */    
    @TableField
    private BigDecimal usedAmountMonth8;
    /**
     * 月度基金9
     */    
    @TableField
    private BigDecimal amount9;
    /**
     * 月度预警值9
     */    
    @TableField
    private BigDecimal warn9;
    /**
     * 月度已使用金额9
     */    
    @TableField
    private BigDecimal usedAmountMonth9;
    /**
     * 月度基金10
     */    
    @TableField
    private BigDecimal amount10;
    /**
     * 月度预警值10
     */    
    @TableField
    private BigDecimal warn10;
    /**
     * 月度已使用金额10
     */    
    @TableField
    private BigDecimal usedAmountMonth10;
    /**
     * 月度基金11
     */    
    @TableField
    private BigDecimal amount11;
    /**
     * 月度预警值11
     */    
    @TableField
    private BigDecimal warn11;
    /**
     * 月度已使用金额11
     */    
    @TableField
    private BigDecimal usedAmountMonth11;
    /**
     * 月度基金12
     */    
    @TableField
    private BigDecimal amount12;
    /**
     * 月度预警值12
     */    
    @TableField
    private BigDecimal warn12;
    /**
     * 月度已使用金额12
     */    
    @TableField
    private BigDecimal usedAmountMonth12;
    /**
     * 更新人
     */    
    @TableField
    private Long lastUpdatedBy;
    /**
     * 更新时间
     */    
    @TableField
    private Date lastUpdatedDate;
    /**
     * 创建人
     */    
    @TableField
    private Long createdBy;
    /**
     * 创建时间
     */    
    @TableField
    private Date createdDate;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;

    public BigDecimal getUsedAmount() {
        return ObjectUtil.isNull(usedAmount) ? BigDecimal.ZERO : usedAmount;
    }

    public BigDecimal getUsedRatio() {
        return ObjectUtil.isNull(usedRatio) ? BigDecimal.ZERO : usedRatio;
    }
}

