package com.crhms.cloud.mqs.basic.mapper;


import com.crhms.cloud.mqs.basic.domain.BaseMedicalDiag;
import com.crhms.cloud.mqs.basic.vo.HisOperationDetailVo;
import com.crhms.cloud.mqs.basic.vo.HisOperationMainVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 住院审核 - 手术记录明细表(HpOperationList)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-02 15:11:58
 */

@Mapper
public interface HpDiagMapper {

    List<BaseMedicalDiag> queryDiagList(@Param("admissionNo") String admissionNo, @Param("no") String no, @Param("hospitalId") String hospitalId);

    List<BaseMedicalDiag> queryDiagListHis(@Param("admissionNo") String admissionNo, @Param("no") String no, @Param("batch") String batch, @Param("hospitalId") String hospitalId);

    void batchSaveDiag(@Param("entities") List<BaseMedicalDiag> diagList);

    void batchSaveDiagHis(@Param("entities") List<BaseMedicalDiag> diagList);

    void deleteDiagByAdNo(@Param("diagNos") List<String> diagNos, @Param("nos") List<String> nos, @Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    //查询单据下诊断明细
    List<BaseMedicalDiag> queryMedicalDiags(BaseMedicalDiag baseMedicalDiag);

}

