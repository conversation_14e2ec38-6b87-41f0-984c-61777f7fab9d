package com.crhms.cloud.mqs.mqs_pm.vo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 物价对比-标准物价
 *
 * @author: wangxingang
 * @date: 2023.02.03
 */
@Data
@Builder
public class PriceCompStd {
    /**
     * 生效时间
     */
    private String effectTime;

    /**
     * 目录大类
     */
    private String category;

    /**
     * 标准物价-编码
     */
    private String stdCode;

    /**
     * 标准物价-名称
     */
    private String stdName;

    /**
     * 标准物价-分类
     */
    private String stdClassify;

    /**
     * 标准物价-计价单位
     */
    private String stdUnit;

    /**
     * 标准物价-价格(元)
     */
    private BigDecimal stdPrice;
}
