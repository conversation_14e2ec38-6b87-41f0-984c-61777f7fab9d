package com.crhms.cloud.mqs.mqs_kbm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 知识库管理-医保项目目录-国家医保药品分类与代码
 *
 * @author: wangxingang
 * @date: 2023/02/03
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("mqs_kbm_item_drug")
public class ItemDrug {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 药品代码
     */
    @TableField
    private String drugCode;

    /**
     * 数据来源
     */
    @TableField
    private String dataSource;

    /**
     * 注册名称
     */
    @TableField
    private String registeredName;

    /**
     * 药品名称
     */
    @TableField
    private String drugName;

    /**
     * 注册剂型
     */
    @TableField
    private String registeredDosageForm;

    /**
     * 实际剂型
     */
    @TableField
    private String actualDosageForm;

    /**
     * 注册规格
     */
    @TableField
    private String registrationSpecification;

    /**
     * 实际规格
     */
    @TableField
    private String actualSpecification;

    /**
     * 包装材质
     */
    @TableField
    private String packageMaterial;

    /**
     * 最小包装数量
     */
    @TableField
    private String minPackageQuantity;

    /**
     * 最小制剂单位
     */
    @TableField
    private String minPreparationUnit;

    /**
     * 最小包装单位
     */
    @TableField
    private String minPackageUnit;

    /**
     * 药品企业
     */
    @TableField
    private String drugEnterprise;

    /**
     * 上市药品持有人
     */
    @TableField
    private String drugHolder;

    /**
     * 批准文号
     */
    @TableField
    private String approvalNo;

    /**
     * 原批准文号
     */
    @TableField
    private String originalApprovalNo;

    /**
     * 药品本位码
     */
    @TableField
    private String drugStandardCode;

    /**
     * 分装企业名称
     */
    @TableField
    private String subPackageEnterprise;


    /**
     * 市场状态
     */
    @TableField
    private String marketStatus;

    /**
     * 医保药品名称
     */
    @TableField
    private String ybDrugName;

    /**
     * 2021版甲乙类
     */
    @TableField
    private String version2021;

    /**
     * 医保剂型
     */
    @TableField
    private String ybDosageForm;

    /**
     * 编号
     */
    @TableField
    private String number;

    /**
     * 备注
     */
    @TableField
    private String remark;
}
