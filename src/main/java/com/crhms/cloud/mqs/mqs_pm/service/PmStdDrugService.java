package com.crhms.cloud.mqs.mqs_pm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.excel.dto.ExcelExport;
import com.crhms.cloud.core.excel.dto.SheetData;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdDrug;
import com.crhms.cloud.mqs.mqs_pm.mapper.PmStdDrugMapper;
import com.crhms.cloud.mqs.mqs_pm.vo.DrugImportVo;
import com.crhms.cloud.mqs.sys.utils.DictUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 物价管理-市级标准物价-药品
 *
 * @author: wangxingang
 * @date: 2023/01/31
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class PmStdDrugService extends ServiceImpl<PmStdDrugMapper, PmStdDrug> {


    /**
     * 新增或修改
     *
     * @param drug 药品信息
     * <AUTHOR>
     * @date 2023.01.31
     */
    public void submitDrug(PmStdDrug drug) {
        saveOrUpdate(drug);
    }


    /**
     * 分页查询
     *
     * @param drugCode       药品代码
     * @param drugName       药品名称
     * @param startPeriod    时间周期-开始时间
     * @param endPeriod      时间周期-结束时间
     * @param pageInfo       分页信息
     * <AUTHOR>
     * @date 2023.01.31
     */
    public List<PmStdDrug> queryPage(String drugCode, String drugName, String startPeriod, String endPeriod, Page<PmStdDrug> pageInfo) {
        Page<PmStdDrug> result = page(pageInfo, new LambdaQueryWrapper<PmStdDrug>()
                .like(StrUtil.isNotEmpty(drugCode), PmStdDrug::getDrugCode, drugCode)
                .like(StrUtil.isNotEmpty(drugName), PmStdDrug::getDrugName, drugName)
                .between(StrUtil.isNotEmpty(startPeriod) && StrUtil.isNotEmpty(endPeriod), PmStdDrug::getEffectTime, startPeriod, endPeriod)
        );

        return result.getRecords();
    }


    /**
     * 根据id批量删除
     *
     * @param ids id集合
     * <AUTHOR>
     * @date 2023.01.31
     */
    public void deleteByIds(List<Long> ids) {
        removeByIds(ids);
    }

    /**
     * 导出Excel
     *
     * @param response    响应
     * @param drugCode    药品代码
     * @param drugName    药品名称
     * @param startPeriod 时间周期-开始时间
     * @param endPeriod   时间周期-结束时间
     * @param isModel     是否导出模板?true是false否(默认是否)
     * <AUTHOR>
     * @date 2023.01.30
     */
    public void exportExcel(HttpServletResponse response, String drugCode, String drugName, String startPeriod, String endPeriod, boolean isModel) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        StringBuilder fileName = new StringBuilder();
        fileName.append("物价管理-市级标准物价-药品");



        // 导出模板时isModel是ture
        if (isModel) {
            // 表头
            heads.add(CollUtil.list(true, "DRUG_CODE", "DRUG_NAME", "DOSAGE_FORM", "SPECIFICATION", "CLASSIFY", "PRICE", "ORIGIN"));
            heads.add(CollUtil.list(true, "药品编码", "药品名称", "剂型", "规格", "分类", "价格(元)", "产地/制药厂家"));
        }
        else {
            // 表头
            heads.add(CollUtil.list(true, "EFFECT_TIME", "DRUG_CODE", "DRUG_NAME", "DOSAGE_FORM", "SPECIFICATION", "CLASSIFY", "PRICE", "ORIGIN"));
            heads.add(CollUtil.list(true, "生效时间", "药品编码", "药品名称", "剂型", "规格", "分类", "价格(元)", "产地/制药厂家"));

            // 查询勾选的数据
            List<PmStdDrug> drugs = baseMapper.selectList(new LambdaQueryWrapper<PmStdDrug>()
                    .like(StrUtil.isNotEmpty(drugCode), PmStdDrug::getDrugCode, drugCode)
                    .like(StrUtil.isNotEmpty(drugName), PmStdDrug::getDrugName, drugName)
                    .between(StrUtil.isNotEmpty(startPeriod) && StrUtil.isNotEmpty(endPeriod), PmStdDrug::getEffectTime, startPeriod, endPeriod));
            // 字典值转换
            DictUtils.translateDict(drugs);
            // 构建数据
            for (PmStdDrug drug : drugs) {
                datas.add(CollUtil.list(true, DateUtil.format(drug.getEffectTime(), "yyyy.MM.dd"), drug.getDrugCode(), drug.getDrugName(), drug.getDosageForm(), drug.getSpecification(), drug.getClassify(), drug.getPrice(), drug.getOrigin()));
            }
        }

        MqsUtils.buildExportFileNameSuffix(fileName, startPeriod, endPeriod);

        // 导出
        List<ExcelExport> exports = new ArrayList<>();
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }

    /**
     * 导入Excel, 返回重复的数据
     *
     * @param importVo 导入VO
     */
    public List<PmStdDrug> importExcel(DrugImportVo importVo) {
        List<String> drugCodeList = importVo.getDrugCodeList();
        MultipartFile file = importVo.getFile();
        Date effectTime = importVo.getEffectTime();
        // 获取sheet页
        SheetData sheetData = BigExcelUtil.parseExcel(file).getSheets().get(0);

        // 校验Excel
        checkExcel(sheetData);

        // 解析Excel数据
        List<PmStdDrug> importData = parseExcel(sheetData, effectTime);

        List<PmStdDrug> duplicateData = new ArrayList<>();
        if (drugCodeList == null) {
            // 第一次调用, 返回重复数据

            // 获取已存在的数据
            List<PmStdDrug> existData = baseMapper.selectList(null);

            // 根据项目编码和时间周期判断已存在
            for (PmStdDrug importDatum : importData) {
                for (PmStdDrug existDatum : existData) {
                    if (DateUtil.isSameDay(importDatum.getEffectTime(), existDatum.getEffectTime())
                            && StrUtil.equals(importDatum.getDrugCode(), existDatum.getDrugCode())) {
                        duplicateData.add(importDatum);
                    }
                }
            }

        } else {
            if (CollUtil.isNotEmpty(drugCodeList)) {
                baseMapper.delete(new LambdaQueryWrapper<PmStdDrug>().eq(PmStdDrug::getEffectTime, effectTime).in(PmStdDrug::getDrugCode, drugCodeList));
            }
            for (PmStdDrug importDatum : importData) {
                importDatum.setId(IdWorker.getId());
            }
            saveBatch(importData);
        }


        // 返回重复数据
        return duplicateData;
    }


    /**
     * 解析Excel数据
     *
     * @param sheetData  sheet页
     * @param effectTime 生效时间
     * @return 去除表头后的Excel数据(List < 实体类 > 形式)
     */
    private List<PmStdDrug> parseExcel(SheetData sheetData, Date effectTime) {

        // 获取数据
        List<PmStdDrug> datas = new ArrayList<>();
        List<Object> line;
        for (int i = 0; i < sheetData.getLines().size(); i++) {
            if (i <= 1) {
                continue;
            }
            line = sheetData.getLines().get(i);

            String price = BigExcelUtil.getItemStr(line, 5) == null ? "0" : BigExcelUtil.getItemStr(line, 5);

            datas.add(PmStdDrug.builder()
                    .effectTime(effectTime)
                    .drugCode(BigExcelUtil.getItemStr(line, 0))
                    .drugName(BigExcelUtil.getItemStr(line, 1))
                    .dosageForm(BigExcelUtil.getItemStr(line, 2))
                    .specification(BigExcelUtil.getItemStr(line, 3))
                    .classify(BigExcelUtil.getItemStr(line, 4))
                    .price(NumberUtil.toBigDecimal(price))
                    .origin(BigExcelUtil.getItemStr(line, 6))
                    .build());
        }

        return datas;
    }


    /**
     * 校验Excel表头及数据是否重复
     *
     * @param sheetData sheet页
     */
    private void checkExcel(SheetData sheetData) {

        // 校验表头
        String checkHead = BigExcelUtil.checkHead(sheetData, CollUtil.list(true, "DRUG_CODE", "DRUG_NAME", "DOSAGE_FORM", "SPECIFICATION", "CLASSIFY", "PRICE", "ORIGIN"));
        if (StrUtil.isNotEmpty(checkHead)) {
            throw new BaseException(checkHead);
        }

        // 校验项目编码是否重复
        /*Set<String> projectCodeSet = new HashSet<>();
        List<Object> line;
        String projectCode;
        List<String> errorList = new ArrayList<>();
        for (int i = 0; i < sheetData.getLines().size(); i++) {
            if (i <= 1) {
                continue;
            }
            line = sheetData.getLines().get(i);

            projectCode = BigExcelUtil.getItemStr(line, 0);
            if (CollUtil.contains(projectCodeSet, projectCode)) {
                // 提示重复
                errorList.add(StrUtil.format("第{}行数据中, 项目编码'{}'已存在", i, projectCode));
            } else {
                projectCodeSet.add(projectCode);
            }
        }

        if (CollUtil.isNotEmpty(errorList)) {
            throw new BaseException(errorList.toString());
        }*/
    }

}
