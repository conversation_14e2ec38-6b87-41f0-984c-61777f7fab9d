package com.crhms.cloud.mqs.sys.scheduling;

import com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper;
import com.crhms.cloud.mqs.sys.domain.SysConfig;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.service.SysConfigService;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * @Description    清除审核历史记录定时任务
 * @ClassName ClearHisDataTask
 * <AUTHOR>
 * @date 2023.02.10 15:23
 */
@Log4j2
@Component
@EnableScheduling
public class ClearHisDataTask implements SchedulingConfigurer {

    @Autowired
    protected SysConfigService sysConfigService;

    @Autowired
    private BaseMedicalMapper baseMedicalMapper;

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        scheduledTaskRegistrar.addTriggerTask(() -> process(),
                triggerContext -> {
                    SysConfig sysConfig = sysConfigService.queryByKey("CLEAR_HISDATA_TIME", "1");
                    if (sysConfig == null || Strings.isEmpty(sysConfig.getSysValue())) {
                        log.warn("CLEAR_HISDATA_TIME cron is null");
                    }
                    return new CronTrigger(sysConfig.getSysValue()).nextExecutionTime(triggerContext);
                });
    }

    private void process() {
        log.info("执行 清除审核历史数据 定时任务(清除一个月前历史数据) ===== start");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");//格式化
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);//获取1个月前的日期对象
        String day1 = sdf.format(calendar.getTime());//获取1个月前的日期

        for (AuditScenarioEnum value : AuditScenarioEnum.values()) {
            baseMedicalMapper.clearHisData(value.getTableName(),day1);
        }

        log.info("执行 清除审核历史数据 定时任务(清除一个月前历史数据)  ===== end");
    }
}
