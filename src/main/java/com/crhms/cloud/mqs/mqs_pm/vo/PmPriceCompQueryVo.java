package com.crhms.cloud.mqs.mqs_pm.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 物价管理-院内物价对比 查询接口参数
 *
 * @author: wangxingang
 * @date: 2023/02/02
 */

@Data
public class PmPriceCompQueryVo {

    /**
     * 目录大类多选下拉列表
     */
    private List<String> categoryList;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 时间周期-开始时间
     */
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private String startPeriod;

    /**
     * 时间周期-结束时间
     */
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private String endPeriod;

    /**
     * 对比结果多选下拉列表
     */
    private List<String> compResult;

    /**
     * 差异大类多选下拉列表
     */
    private List<String> diffCategory;
}

