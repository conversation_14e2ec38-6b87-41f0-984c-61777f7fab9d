package com.crhms.cloud.mqs.mqs_op.mapper;

import com.crhms.cloud.mqs.mqs_op.domain.OpPtDetailHis;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 门诊审核-处方费用明细(OpPtDetailHis)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-12-19 14:25:46
 */

@Mapper
public interface OpPtDetailHisMapper extends BaseMapper<OpPtDetailHis> {


}

