package com.crhms.cloud.mqs.sys.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.mqs.sys.domain.SysRules;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.sys.domain.SysSceneRule;
import com.crhms.cloud.mqs.sys.dto.RuleConfigQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 运营管理-规则管理(SysRules)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-12-19 09:40:48
 */

@Mapper
public interface SysRulesMapper extends BaseMapper<SysRules> {

    List<SysRules> queryByPage(@Param("rule") String rule,
                               @Param("broadHeading") String broadHeading,
                               @Param("ruleLevel") String ruleLevel,
                               @Param("isHis") String isHis,
                               @Param("isMain") String isMain,
                               @Param("ruleType") String ruleType,
                               @Param("hospitalId") String hospitalId,
                               @Param("page") IPage pageRequest);

    List<SysRules> seletRuleList(@Param("rule") String rule,@Param("hospitalId") String hospitalId);

    /**
     * 查询分页
     *
     * @param pageInfo   分页信息
     * @param queryVO    查询参数
     * @param hospitalId 院区ID
     * @return the list
     */
    List<SysRules> queryPageByVO(@Param("pageInfo") Page<SysRules> pageInfo,
                                 @Param("queryVO") RuleConfigQueryVO queryVO,
                                 @Param("hospitalId") String hospitalId);

    /**
     * 查询阻断规则
     * @param levelConfig
     * @param rules
     * @param hospitalId
     * @return
     */
    List<String> selectBlockRules(@Param("levelConfig") String levelConfig, @Param("rules") List<String> rules, @Param("hospitalId") String hospitalId);


    List<SysRules> selectRulesCache(@Param("hospitalId") String hospitalId);

    /**
     * 查询场景下规则
     * @param hospitalId
     * @return
     */
    List<SysSceneRule> selectSceneRules(@Param("hospitalId") String hospitalId);

    /**
     * 查询人工审核预制的规则
     * @param ruleType
     * @param hospitalId
     * @return
     */
    SysRules queryMrRuleByLevel(@Param("ruleType") String ruleType, @Param("hospitalId") String hospitalId);
}

