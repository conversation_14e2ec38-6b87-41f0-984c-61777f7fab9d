package com.crhms.cloud.mqs.sys.mqsInterface;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/** * 数据字典注解 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DictTranslate {
    /**
     * 字典编码
     *
     * @return
     */
    String dictCode();

    /**
     * 返回属性名 为空默认覆盖到原字段
     *
     * @return
     */
    String dictText() default "";

    /**
     * 分隔符 支持分割的多个值翻译
     *
     * @return
     */
    String dictSplit() default "|";

}
