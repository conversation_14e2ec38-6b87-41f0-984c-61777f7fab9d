package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpSettleHis;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpSettleHisMapper;
import com.crhms.cloud.mqs.mqs_hp.service.HpSettleHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-结算审核-历史表(HpSettleHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 10:22:14
 */
@Service("hpSettleHisService")
public class HpSettleHisService extends ServiceImpl< HpSettleHisMapper, HpSettleHis> {

}
