<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_ais.mapper.MqsAnalyzeMapper">


    <select id="totalTimes" resultType="java.util.Map">

        select
        COUNT(DISTINCT no,batch_no) AS totalTimes,
        ifNull(COUNT(DISTINCT IF(violation_flag = '1',concat(no,batch_no),null)),0) AS vTimes
        from mqs_fee_list_his
        where hospital_id = #{hospitalId}
        and audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>

    </select>
    <select id="busDetailTotal" resultType="java.util.Map">

        select
        COUNT(1) AS totalDetails,
        sumIf( 1, violation_flag = '1' ) AS vDetails,
        SUM(detail.costs) AS totalCosts,
        sumIf(detail.costs, violation_flag = '1' ) AS vCosts,
        sumIf( detail.costs, item_type_code = 'I') AS ICosts,
        sumIf( detail.costs, item_type_code in ('A', 'B', 'M')) AS ABMCosts
        from mqs_fee_list detail
        where detail.hospital_id = #{hospitalId}
        and audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>

    </select>
    <select id="getAnalysisDetailsByDept" resultType="java.util.Map">
        SELECT
        apply_dept_code as code,
        apply_dept_name as name,
        COUNT(1) AS totalDetails,
        sumIf( 1, violation_flag = '1' ) AS vDetails,
        SUM(detail.costs) AS totalCosts,
        sumIf(detail.costs, violation_flag = '1' ) AS vCosts,
        SUM(detail.bmi_overall_amount) AS bmiAmount,
        sumIf(detail.bmi_overall_amount, violation_flag = '1' ) AS vBmiAmount
        FROM
        mqs_fee_list detail
        where detail.hospital_id = #{hospitalId}
        and audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        GROUP BY
        detail.apply_dept_code,
        detail.apply_dept_name
    </select>

    <select id="getRuleNumsByDept" resultType="java.util.Map">
        SELECT
        apply_dept_code as code,
        apply_dept_name as name,
        COUNT(DISTINCT audit.rule_code) as ruleNums
        FROM
        mqs_audit_fee_list audit
        where audit.hospital_id = #{hospitalId}
        and audit.audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        GROUP BY
        apply_dept_code,
        apply_dept_name
    </select>

    <select id="getAnalysisDetailsByDoc" resultType="java.util.Map">
        SELECT
        apply_doctor_code as code,
        apply_doctor_name as name,
        COUNT(1) AS totalDetails,
        sumIf( 1, violation_flag = '1' ) AS vDetails,
        SUM(detail.costs) AS totalCosts,
        sumIf(detail.costs, violation_flag = '1' ) AS vCosts,
        SUM(detail.bmi_overall_amount) AS bmiAmount,
        sumIf(detail.bmi_overall_amount, violation_flag = '1' ) AS vBmiAmount
        FROM
        mqs_fee_list detail
        where detail.hospital_id = #{hospitalId}
        and audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        GROUP BY
        detail.apply_doctor_code,
        detail.apply_doctor_name

    </select>

    <select id="getRuleNumsByDoc" resultType="java.util.Map">
        SELECT
        apply_doctor_code as code,
        apply_doctor_name as name,
        COUNT(DISTINCT audit.rule_code) as ruleNums
        FROM
        mqs_audit_fee_list audit
        where audit.hospital_id = #{hospitalId}
        and audit.audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        GROUP BY
        audit.apply_doctor_code,
        audit.apply_doctor_name
    </select>

    <select id="getAnalysisHisByDept" resultType="java.util.Map">
        SELECT
        detail.apply_dept_code as code,
        detail.apply_dept_name as name,
        COUNT( DISTINCT detail.no,detail.batch_no ) total
        FROM
        mqs_fee_list_his detail
        where detail.hospital_id = #{hospitalId}
        and detail.audit_scenario = #{scenario}

        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        GROUP BY
        detail.apply_dept_code,
        detail.apply_dept_name
    </select>
    <select id="getAnalysisHisByDoc" resultType="java.util.Map">
        SELECT
        detail.apply_doctor_code as code,
        detail.apply_doctor_name as name,
        COUNT( DISTINCT detail.no,detail.batch_no ) total
        FROM
        mqs_fee_list_his detail
        where detail.hospital_id = #{hospitalId}
        and detail.audit_scenario = #{scenario}

        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        GROUP BY
        detail.apply_doctor_code,
        detail.apply_doctor_name
    </select>
    <select id="genptTrend" resultType="java.util.Map">

        select
        toDate(item_date) as monthDay,
        COUNT(DISTINCT no) AS totalTimes,
        ifNull(COUNT(DISTINCT IF(violation_flag = '1',no,null)),0) AS vTimes
        from mqs_fee_list detail
        where hospital_id = #{hospitalId}
        and detail.audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        group by
        monthDay
        ORDER BY
        monthDay

    </select>
    <select id="gendpTrendDept" resultType="java.util.Map">
        select
        toDate(item_date) AS monthDay,
        COUNT(DISTINCT detail.apply_dept_code) AS totalPro,
        ifNull(COUNT(DISTINCT IF(detail.violation_flag = '1', detail.apply_dept_code, null)),0) AS vPro
        from mqs_fee_list detail
        where detail.hospital_id = #{hospitalId}
        and detail.audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        GROUP BY
        monthDay
        ORDER BY
        monthDay

    </select>

    <select id="gendpTrendDoc" resultType="java.util.Map">
        select
        toDate(item_date) AS monthDay,
        COUNT(DISTINCT detail.apply_doctor_code) AS totalPro,
        ifNull(COUNT(DISTINCT IF(detail.violation_flag = '1',detail.apply_doctor_code,null)),0) AS vPro
        from mqs_fee_list detail
        where detail.hospital_id = #{hospitalId}
        and detail.audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        GROUP BY
        monthDay
        ORDER BY
        monthDay

    </select>
    <select id="feeTrend" resultType="java.util.Map">

        select
        toDate(item_date) AS monthDay,
        SUM(detail.costs) AS totalCosts,
        sumIf(detail.costs, detail.violation_flag = '1') AS vCosts
        from mqs_fee_list detail
        where detail.hospital_id = #{hospitalId}
        and detail.audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        group by
        monthDay
        ORDER BY
        monthDay

    </select>
    <select id="feeTypeTrend" resultType="java.util.Map">

        select
        item_type_code AS typeCode,
        SUM(detail.costs) AS totalCosts,
        sumIf(detail.costs, detail.violation_flag = '1') AS vCosts
        from mqs_fee_list detail
        where detail.hospital_id = #{hospitalId}
        and detail.audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        group by
        item_type_code

    </select>

    <select id="getAnalysisType" resultType="java.util.Map">
        SELECT
        detail.item_type_code,
        detail.item_type_name,
        COUNT( 1 ) AS totalDetails,
        sumIf(1 , detail.violation_flag = '1') AS vDetails,
        SUM( detail.costs ) AS totalCosts,
        sumIf(detail.costs, detail.violation_flag = '1') AS vCosts,
        SUM( detail.bmi_overall_amount ) AS bmiAmount,
        sumIf(detail.bmi_overall_amount, detail.violation_flag = '1') AS vBmiAmount
        FROM
        mqs_fee_list detail
        where detail.hospital_id = #{hospitalId}
        and detail.audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        <if test="proDept != null and proDept != ''">
            and apply_dept_code = #{proDept}
        </if>
        <if test="proDoc != null and proDoc != ''">
            and apply_doctor_code = #{proDoc}
        </if>
        GROUP BY
        detail.item_type_code,
        detail.item_type_name
    </select>


    <select id="getAnalysisTypeRuleNums" resultType="java.util.Map">
        SELECT
        detail.item_type_code,
        detail.item_type_name,
        COUNT(DISTINCT audit.rule_code) as ruleNums
        FROM
        mqs_audit_result audit
        LEFT JOIN mqs_fee_list detail ON audit.detail_no = detail.detail_no and audit.`no` = detail.`no`
        and audit.audit_scenario = detail.audit_scenario
        where audit.hospital_id = #{hospitalId}
        and detail.violation_flag = '1'
        and detail.audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        <if test="proDept != null and proDept != ''">
            and apply_dept_code = #{proDept}
        </if>
        <if test="proDoc != null and proDoc != ''">
            and apply_doctor_code = #{proDoc}
        </if>
        GROUP BY
        detail.item_type_code,
        detail.item_type_name

    </select>

    <select id="getAnalysisTypeHis" resultType="java.util.Map">
        SELECT
        detail.item_type_code,
        detail.item_type_name,
        COUNT( DISTINCT detail.no ) total
        FROM
        mqs_fee_list_his detail
        where detail.hospital_id = #{hospitalId}
        and detail.audit_scenario = #{scenario}
        <if test="itemDateFrom != null and itemDateFrom != ''">
            and item_date &gt;= concat(#{itemDateFrom},' 00:00:00')
        </if>
        <if test="itemDateTo != null and itemDateTo != ''">
            and item_date &lt;= concat(#{itemDateTo},' 23:59:59')
        </if>
        <if test="dept != null and dept != ''">
            and apply_dept_code = #{dept}
        </if>
        <if test="proDept != null and proDept != ''">
            and apply_dept_code = #{proDept}
        </if>
        <if test="proDoc != null and proDoc != ''">
            and apply_doctor_code = #{proDoc}
        </if>
        GROUP BY
        detail.item_type_code,
        detail.item_type_name
    </select>

    <select id="getFeeAnalysisTotal" resultType="java.util.Map">
        SELECT
        COUNT(DISTINCT detail.detail_no ) totalDetails,
        SUM(detail.costs) AS totalCosts,
        SUM(detail.bmi_convered_amount) AS totalBmiAmount
        FROM
        mqs_fee_list detail
        WHERE
        detail.hospital_id = #{queryVo.hospitalId}
        AND detail.audit_scenario = #{scenario}
        AND detail.violation_flag = '1'
        <if test="queryVo.item != null and queryVo.item != '' ">
            and ( detail.item_id like concat('%' , #{queryVo.item} ,'%')
            or detail.item_name like concat('%' , #{queryVo.item} ,'%'))
        </if>
        <if test="queryVo.itemTypeCodes != null and queryVo.itemTypeCodes.size() > 0">
            and detail.item_type_code in
            <foreach collection="queryVo.itemTypeCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.itemDateFrom != null and queryVo.itemDateFrom != ''">
            and detail.item_date &gt;= concat(#{queryVo.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.itemDateTo != null and queryVo.itemDateTo != ''">
            and detail.item_date &lt;= concat(#{queryVo.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() > 0">
            and detail.apply_dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() > 0">
            and detail.apply_doctor_code in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getFeeAnalysis" resultType="java.util.Map">

        SELECT
        item_id,
        item_name,
        item_type_code as typeCode,
        COUNT(DISTINCT `no` ) totalNo,
        COUNT(DISTINCT detail_no ) totalDetails,
        arrayStringConcat(groupUniqArray(rule_name),';') rules,
        arrayStringConcat(groupUniqArray(apply_dept_name),';') deptNames,
        arrayStringConcat(groupUniqArray(apply_doctor_name),';') docNames,
        SUM(costs) AS totalCosts,
        SUM(bmi_convered_amount) AS totalBmiAmount
        FROM
        mqs_audit_fee_list detail
        WHERE
        hospital_id = #{queryVo.hospitalId}
        AND audit_scenario = #{scenario}
        <if test="queryVo.item != null and queryVo.item != '' ">
            and ( item_id like concat('%' , #{queryVo.item} ,'%')
            or item_name like concat('%' , #{queryVo.item} ,'%'))
        </if>
        <if test="queryVo.itemTypeCodes != null and queryVo.itemTypeCodes.size() > 0">
            and item_type_code in
            <foreach collection="queryVo.itemTypeCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.itemDateFrom != null and queryVo.itemDateFrom != ''">
            and item_date &gt;= concat(#{queryVo.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.itemDateTo != null and queryVo.itemDateTo != ''">
            and item_date &lt;= concat(#{queryVo.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() > 0">
            and apply_dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() > 0">
            and apply_doctor_code in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY
        item_id,
        item_name,
        item_type_code
    </select>

    <select id="getVRuleTital" resultType="java.util.Map">
        SELECT
        COUNT(DISTINCT IF (detail.violation_flag = '1', detail.`no`, null )) totalNo, -- 违反总次数
        COUNT(DISTINCT detail.`no`) patientTotal, -- 参保人数
        COUNT(DISTINCT IF (detail.violation_flag = '1', detail.detail_no , null )) totalDetail, -- 违规明细数
        ifNull(COUNT( DISTINCT IF ( detail.violation_flag = '1', detail.apply_dept_code, null )),0) totalDepts, -- 涉及科室数
        sumIf(detail.costs,detail.violation_flag = '1') AS vCosts, -- 违规总金额
        SUM(detail.costs) AS totalCosts -- 明细总金额
        FROM
        mqs_fee_list detail
        WHERE detail.hospital_id = #{queryVo.hospitalId}
        AND detail.audit_scenario = #{scenario}
        <if test="queryVo.item != null and queryVo.item != '' ">
            and ( detail.item_id like concat('%' , #{queryVo.item} ,'%')
            or detail.item_name like concat('%' , #{queryVo.item} ,'%'))
        </if>
        <if test="queryVo.itemTypeCodes != null and queryVo.itemTypeCodes.size() > 0">
            and detail.item_type_code in
            <foreach collection="queryVo.itemTypeCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.itemDateFrom != null and queryVo.itemDateFrom != ''">
            and detail.item_date &gt;= concat(#{queryVo.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.itemDateTo != null and queryVo.itemDateTo != ''">
            and detail.item_date &lt;= concat(#{queryVo.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() > 0">
            and detail.apply_dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() > 0">
            and detail.apply_doctor_code in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

    </select>


    <select id="getVRuleTital2" resultType="java.util.Map">
        SELECT
        COUNT(DISTINCT concat(detail.`no` ,detail.rule_code)) totalRule -- 违反规则数量
        FROM
        mqs_audit_fee_list detail
        WHERE detail.hospital_id = #{queryVo.hospitalId}
        AND detail.audit_scenario = #{scenario}
        <if test="queryVo.item != null and queryVo.item != '' ">
            and ( detail.item_id like concat('%' , #{queryVo.item} ,'%')
            or detail.item_name like concat('%' , #{queryVo.item} ,'%'))
        </if>
        <if test="queryVo.itemTypeCodes != null and queryVo.itemTypeCodes.size() > 0">
            and detail.item_type_code in
            <foreach collection="queryVo.itemTypeCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.itemDateFrom != null and queryVo.itemDateFrom != ''">
            and detail.item_date &gt;= concat(#{queryVo.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.itemDateTo != null and queryVo.itemDateTo != ''">
            and detail.item_date &lt;= concat(#{queryVo.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() > 0">
            and detail.apply_dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() > 0">
            and detail.apply_doctor_code in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="getVRuleListByPage" resultType="java.util.Map">

        SELECT
        detail.rule_code,
        detail.rule_name,
        COUNT(DISTINCT detail.`no`) totalNo, -- 违反次数
        SUM(detail.costs) AS totalCosts, -- 违规总金额
        COUNT(DISTINCT detail.detail_no) totalDetail -- 违规明细数
        FROM
        mqs_audit_fee_list detail
        where detail.hospital_id = #{queryVo.hospitalId}
        and detail.audit_scenario = #{scenario}
        <if test="queryVo.item != null and queryVo.item != '' ">
            and ( detail.item_id like concat('%' , #{queryVo.item} ,'%')
            or detail.item_name like concat('%' , #{queryVo.item} ,'%'))
        </if>
        <if test="queryVo.itemTypeCodes != null and queryVo.itemTypeCodes.size() > 0">
            and detail.item_type_code in
            <foreach collection="queryVo.itemTypeCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.itemDateFrom != null and queryVo.itemDateFrom != ''">
            and detail.item_date &gt;= concat(#{queryVo.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.itemDateTo != null and queryVo.itemDateTo != ''">
            and detail.item_date &lt;= concat(#{queryVo.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() > 0">
            and detail.apply_dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() > 0">
            and detail.apply_doctor_code in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY
        detail.rule_code,
        detail.rule_name
    </select>
    <select id="getVRuleListTotal" resultType="java.util.Map">
        SELECT
        COUNT(DISTINCT detail.`no`) totalNo, -- 违反次数
        SUM(detail.costs) AS totalCosts, -- 违规总金额
        COUNT( DISTINCT detail.detail_no,detail.`no`) totalDetail -- 违规明细数
        FROM
        mqs_audit_fee_list detail
        where detail.hospital_id = #{queryVo.hospitalId}
        and detail.audit_scenario = #{scenario}
        and detail.detail_no != '0'

        <if test="queryVo.item != null and queryVo.item != '' ">
            and ( detail.item_id like concat('%' , #{queryVo.item} ,'%')
            or detail.item_name like concat('%' , #{queryVo.item} ,'%'))
        </if>
        <if test="queryVo.itemTypeCodes != null and queryVo.itemTypeCodes.size() > 0">
            and detail.item_type_code in
            <foreach collection="queryVo.itemTypeCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.itemDateFrom != null and queryVo.itemDateFrom != ''">
            and detail.item_date &gt;= concat(#{queryVo.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.itemDateTo != null and queryVo.itemDateTo != ''">
            and detail.item_date &lt;= concat(#{queryVo.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() > 0">
            and detail.apply_dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() > 0">
            and detail.apply_doctor_code in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getVRuleList" resultType="java.util.Map">
        SELECT
        detail.apply_dept_code,
        detail.apply_dept_name,
        COUNT(DISTINCT detail.`no`) totalNo, -- 违反次数
        SUM(detail.costs) AS totalCosts, -- 违规总金额
        COUNT( DISTINCT detail.detail_no) totalDetail -- 违规明细数
        FROM
        mqs_audit_fee_list detail
        where detail.hospital_id = #{queryVo.hospitalId}
        and detail.rule_code = #{queryVo.ruleCode}
        and detail.audit_scenario = #{scenario}
        <if test="queryVo.item != null and queryVo.item != '' ">
            and ( detail.item_id like concat('%' , #{queryVo.item} ,'%')
            or detail.item_name like concat('%' , #{queryVo.item} ,'%'))
        </if>
        <if test="queryVo.itemTypeCodes != null and queryVo.itemTypeCodes.size() > 0">
            and detail.item_type_code in
            <foreach collection="queryVo.itemTypeCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.itemDateFrom != null and queryVo.itemDateFrom != ''">
            and detail.item_date &gt;= concat(#{queryVo.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.itemDateTo != null and queryVo.itemDateTo != ''">
            and detail.item_date &lt;= concat(#{queryVo.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() > 0">
            and detail.apply_dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() > 0">
            and detail.apply_doctor_code in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY
        detail.apply_dept_code,
        detail.apply_dept_name

    </select>
    <select id="getVItemTop10" resultType="java.util.Map">
        SELECT
        detail.item_id,
        detail.item_name,
        COUNT( DISTINCT detail.detail_no) total
        FROM
        mqs_audit_fee_list detail
        WHERE detail.hospital_id = #{queryVo.hospitalId}
        AND detail.audit_scenario = #{scenario}
        <if test="queryVo.item != null and queryVo.item != '' ">
            and ( detail.item_id like concat('%' , #{queryVo.item} ,'%')
            or detail.item_name like concat('%' , #{queryVo.item} ,'%'))
        </if>
        <if test="queryVo.itemTypeCodes != null and queryVo.itemTypeCodes.size() > 0">
            and detail.item_type_code in
            <foreach collection="queryVo.itemTypeCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.itemDateFrom != null and queryVo.itemDateFrom != ''">
            and detail.item_date &gt;= concat(#{queryVo.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.itemDateTo != null and queryVo.itemDateTo != ''">
            and detail.item_date &lt;= concat(#{queryVo.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() > 0">
            and detail.apply_dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() > 0">
            and detail.apply_doctor_code in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY
        detail.item_id,
        detail.item_name
        ORDER BY total desc
        LIMIT 10
    </select>
    <select id="getVRuleTop10" resultType="java.util.Map">
        SELECT
        detail.rule_code,
        detail.rule_name,
        COUNT( DISTINCT detail.detail_no) total
        FROM
        mqs_audit_fee_list detail
        where detail.hospital_id = #{queryVo.hospitalId}
        AND detail.audit_scenario = #{scenario}
        <if test="queryVo.item != null and queryVo.item != '' ">
            and ( detail.item_id like concat('%' , #{queryVo.item} ,'%')
            or detail.item_name like concat('%' , #{queryVo.item} ,'%'))
        </if>
        <if test="queryVo.itemTypeCodes != null and queryVo.itemTypeCodes.size() > 0">
            and detail.item_type_code in
            <foreach collection="queryVo.itemTypeCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.itemDateFrom != null and queryVo.itemDateFrom != ''">
            and detail.item_date &gt;= concat(#{queryVo.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.itemDateTo != null and queryVo.itemDateTo != ''">
            and detail.item_date &lt;= concat(#{queryVo.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() > 0">
            and detail.apply_dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() > 0">
            and detail.apply_doctor_code in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY
        detail.rule_code,
        detail.rule_name
        ORDER BY total desc
        LIMIT 10
    </select>
    <select id="getcsDiseaseTop5" resultType="java.util.Map">

        SELECT
        cs_disease_code,
        cs_disease_name,
        SUM( total_amount ) AS totalAmount,
        SUM( bmi_overall_amount ) as totalBmiAmount,
        COUNT(DISTINCT patient_id) as patientNums
        FROM
        mqs_medical_case head
        WHERE hospital_id = #{hospitalId}
        AND audit_scenario in ('opPt','hpSettle')
        AND (head.cs_disease_code IS NOT NULL and head.cs_disease_code != '')
        AND head.bill_date &gt;= concat(#{year},'-01-01 00:00:00')
        AND head.bill_date &lt;= concat(#{year},'-12-31 23:59:59')
        GROUP BY
        cs_disease_code,
        cs_disease_name
        ORDER BY
        totalAmount DESC
        LIMIT 5

    </select>

    <select id="getcsTotalBmiYear" resultType="java.math.BigDecimal">
        SELECT
            SUM( bmi_overall_amount )	as totalBmiAmount
        FROM
        mqs_medical_case head
        WHERE hospital_id = #{hospitalId}
        AND audit_scenario in ('opPt','hpSettle')
        AND (head.cs_disease_code IS NOT NULL and head.cs_disease_code != '')
        AND head.bill_date &gt;= concat(#{year},'-01-01 00:00:00')
        AND head.bill_date &lt;= concat(#{year},'-12-31 23:59:59')

    </select>
    <select id="getcsDiseaseDetailTop5" resultType="java.util.Map">
        SELECT
        head.cs_disease_code,
        head.cs_disease_name,
        sumIf(detail.costs, detail.violation_flag = '1') AS totalCosts,
        arrayStringConcat(groupUniqArray(detail.apply_dept_name),';') AS deptNames
        FROM
        mqs_medical_case head
        LEFT JOIN mqs_fee_list detail ON detail.`no` = head.`no` and detail.audit_scenario = head.audit_scenario
        WHERE
        head.hospital_id = #{hospitalId}
        AND audit_scenario IN ('opPt','hpSettle')
        AND head.bill_date &gt;= concat(#{year},'-01-01 00:00:00')
        AND head.bill_date &lt;= concat(#{year},'-12-31 23:59:59')
        AND head.cs_disease_code IN
        <foreach collection="csDiseaseCodes" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        GROUP BY
        head.cs_disease_code,
        head.cs_disease_name

    </select>
    <select id="getcsgetListByPage" resultType="com.crhms.cloud.mqs.mqs_ais.vo.CsDiseaseDto">

        SELECT
        detail.detail_no,
        head.no as `no`,
        head.patient_id,
        head.patient_name,
        head.patient_gender,
        detail.ptype,
        detail.item_type_code,
        detail.item_type_name,
        detail.item_date as itemDate,
        detail.item_name,
        detail.violation_flag as violationFlag,
        head.cs_disease_code,
        head.cs_disease_name,
        head.bill_date as billDate,
        head.admission_no as admissionNo,
        head.benefit_type_id,
        detail.apply_dept_name,
        detail.apply_doctor_name,
        detail.price,
        detail.numbers,
        detail.costs,
        detail.bmi_overall_amount as bmiOverallAmount
        FROM
        mqs_fee_list detail
        LEFT JOIN mqs_medical_case head ON detail.`no` = head.`no` and detail.audit_scenario = head.audit_scenario
        WHERE
        head.hospital_id = #{queryVo.hospitalId}
        AND (head.cs_disease_code IS NOT NULL and head.cs_disease_code != '')
        AND audit_scenario IN ('opPt','hpSettle')
        <if test="queryVo.item != null and queryVo.item != '' ">
            and ( detail.item_id like concat('%' , #{queryVo.item} ,'%')
            or detail.item_name like concat('%' , #{queryVo.item} ,'%'))
        </if>
        <if test="queryVo.itemDateFrom != null and queryVo.itemDateFrom != ''">
            and head.bill_date &gt;= concat(#{queryVo.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.itemDateTo != null and queryVo.itemDateTo != ''">
            and head.bill_date &lt;= concat(#{queryVo.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() > 0">
            and detail.apply_dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() > 0">
            and detail.apply_doctor_code in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.csDisease != null and queryVo.csDisease != '' ">
            and ( head.cs_disease_code like concat('%' , #{queryVo.csDisease} ,'%')
            or head.cs_disease_name like concat('%' , #{queryVo.csDisease} ,'%'))
        </if>


    </select>
</mapper>

