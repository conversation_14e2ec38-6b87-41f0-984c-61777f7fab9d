package com.crhms.cloud.mqs.mqs_pm.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义日志注解
 *
 * @author: wangxingang
 * @date: 2023/02/01
 */

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PmStdLogRecord {

    /**
     * 更新大类
     */
    String category() default "未知大类";

    /**
     * 日志详情
     */
    String log() default "";
}
