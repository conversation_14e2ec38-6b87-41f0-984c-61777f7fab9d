<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.basic.mapper.HpOperationMapper">

    <resultMap type="com.crhms.cloud.mqs.basic.vo.HisOperationMainVo" id="HpOperationRecordMap">
        <result property="no" column="no" jdbcType="VARCHAR"/>
        <result property="admission_no" column="admission_no" jdbcType="VARCHAR"/>
        <result property="hospital_id" column="hospital_id" jdbcType="VARCHAR"/>
        <result property="operation_id" column="operation_id" jdbcType="VARCHAR"/>
        <result property="opera_doctor_code" column="opera_doctor_code" jdbcType="VARCHAR"/>
        <result property="opera_doctor_name" column="opera_doctor_name" jdbcType="VARCHAR"/>
        <result property="assistant1_code" column="assistant1_code" jdbcType="VARCHAR"/>
        <result property="assistant1_name" column="assistant1_name" jdbcType="VARCHAR"/>
        <result property="assistant2_code" column="assistant2_code" jdbcType="VARCHAR"/>
        <result property="assistant2_name" column="assistant2_name" jdbcType="VARCHAR"/>
        <result property="anst_docotr_code" column="anst_docotr_code" jdbcType="VARCHAR"/>
        <result property="anst_docotr_name" column="anst_docotr_name" jdbcType="VARCHAR"/>
        <result property="opera_start_date" column="opera_start_date" jdbcType="VARCHAR"/>
        <result property="opera_end_date" column="opera_end_date" jdbcType="VARCHAR"/>
        <result property="anst_type_code" column="anst_type_code" jdbcType="VARCHAR"/>
        <result property="is_complication" column="is_complication" jdbcType="VARCHAR"/>
        <result property="complication_code" column="complication_code" jdbcType="VARCHAR"/>
        <result property="complication_name" column="complication_name" jdbcType="VARCHAR"/>
        <result property="opera_process_record" column="opera_process_record" jdbcType="VARCHAR"/>
        <result property="record_doctor_code" column="record_doctor_code" jdbcType="VARCHAR"/>
        <result property="record_doctor_name" column="record_doctor_name" jdbcType="VARCHAR"/>
        <result property="operation_type_code" column="operation_type_code" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap type="com.crhms.cloud.mqs.basic.vo.HisOperationDetailVo" id="HpOperationListMap">
        <result property="detail_id" column="detail_id" jdbcType="VARCHAR"/>
        <result property="no" column="no" jdbcType="VARCHAR"/>
        <result property="admission_no" column="admission_no" jdbcType="VARCHAR"/>
        <result property="hospital_id" column="hospital_id" jdbcType="VARCHAR"/>
        <result property="operation_id" column="operation_id" jdbcType="VARCHAR"/>
        <result property="operation_no" column="operation_no" jdbcType="VARCHAR"/>
        <result property="operation_code" column="operation_code" jdbcType="VARCHAR"/>
        <result property="operation_name" column="operation_name" jdbcType="VARCHAR"/>
        <result property="opera_level_code" column="opera_level_code" jdbcType="VARCHAR"/>
        <result property="inc_type_code" column="inc_type_code" jdbcType="VARCHAR"/>
        <result property="inc_heal_level_code" column="inc_heal_level_code" jdbcType="VARCHAR"/>
        <result property="main_operation" column="main_operation" jdbcType="VARCHAR"/>
        <result property="is_iatrogenic" column="is_iatrogenic" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="baseSql">
        no, admission_no, patient_id, patient_name, operation_id, opera_doctor_code, opera_doctor_name, assistant1_code, assistant1_name, assistant2_code, assistant2_name, anst_docotr_code, anst_docotr_name, opera_start_date, opera_end_date, anst_type_code, is_complication, complication_code, complication_name, opera_process_record, record_doctor_code, record_doctor_name, operation_type_code, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id
    </sql>
    <sql id="baseListSql">
        no, admission_no, detail_id,operation_id, operation_no, operation_code, operation_name, opera_level_code, inc_type_code, inc_heal_level_code, main_operation, is_iatrogenic, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id
    </sql>

    <insert id="batchSaveOperations">
        insert into mqs_hp_operation_record(no, admission_no, patient_id, patient_name, operation_id, opera_doctor_code, opera_doctor_name, assistant1_code, assistant1_name, assistant2_code, assistant2_name, anst_docotr_code, anst_docotr_name, opera_start_date, opera_end_date, anst_type_code, is_complication, complication_code, complication_name, opera_process_record, record_doctor_code, record_doctor_name, operation_type_code, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.no}, #{entity.admission_no}, #{entity.patient_id}, #{entity.patient_name}, #{entity.operation_id}, #{entity.opera_doctor_code}, #{entity.opera_doctor_name}, #{entity.assistant1_code}, #{entity.assistant1_name}, #{entity.assistant2_code}, #{entity.assistant2_name}, #{entity.anst_docotr_code}, #{entity.anst_docotr_name}, #{entity.opera_start_date}, #{entity.opera_end_date}, #{entity.anst_type_code}, #{entity.is_complication}, #{entity.complication_code}, #{entity.complication_name}, #{entity.opera_process_record}, #{entity.record_doctor_code}, #{entity.record_doctor_name}, #{entity.operation_type_code}, #{entity.batch_no}, #{entity.lastUpdatedBy}, now(), #{entity.createdBy}, now(), #{entity.hospital_id})
        </foreach>
    </insert>


    <insert id="batchSaveList">
        insert into mqs_hp_operation_list(no, admission_no, detail_id,operation_id, operation_no, operation_code, operation_name, opera_level_code, inc_type_code, inc_heal_level_code, main_operation, is_iatrogenic, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.no}, #{entity.admission_no}, #{entity.detail_id}, #{entity.operation_id}, #{entity.operation_no}, #{entity.operation_code}, #{entity.operation_name}, #{entity.opera_level_code}, #{entity.inc_type_code}, #{entity.inc_heal_level_code}, #{entity.main_operation}, #{entity.is_iatrogenic}, #{entity.batch_no}, #{entity.lastUpdatedBy}, now(), #{entity.createdBy}, now(), #{entity.hospital_id})
        </foreach>
    </insert>
    <insert id="batchSaveOperationsHis">
        insert into mqs_hp_operation_record_his(no, admission_no, patient_id, patient_name, operation_id, opera_doctor_code, opera_doctor_name, assistant1_code, assistant1_name, assistant2_code, assistant2_name, anst_docotr_code, anst_docotr_name, opera_start_date, opera_end_date, anst_type_code, is_complication, complication_code, complication_name, opera_process_record, record_doctor_code, record_doctor_name, operation_type_code, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.no}, #{entity.admission_no}, #{entity.patient_id}, #{entity.patient_name}, #{entity.operation_id}, #{entity.opera_doctor_code}, #{entity.opera_doctor_name}, #{entity.assistant1_code}, #{entity.assistant1_name}, #{entity.assistant2_code}, #{entity.assistant2_name}, #{entity.anst_docotr_code}, #{entity.anst_docotr_name}, #{entity.opera_start_date}, #{entity.opera_end_date}, #{entity.anst_type_code}, #{entity.is_complication}, #{entity.complication_code}, #{entity.complication_name}, #{entity.opera_process_record}, #{entity.record_doctor_code}, #{entity.record_doctor_name}, #{entity.operation_type_code}, #{entity.batch_no}, #{entity.lastUpdatedBy}, now(), #{entity.createdBy}, now(), #{entity.hospital_id})
        </foreach>
    </insert>
    <insert id="batchSaveListHis">
        insert into mqs_hp_operation_list_his(no, admission_no, detail_id,operation_id, operation_no, operation_code, operation_name, opera_level_code, inc_type_code, inc_heal_level_code, main_operation, is_iatrogenic, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.no}, #{entity.admission_no}, #{entity.detail_id}, #{entity.operation_id}, #{entity.operation_no}, #{entity.operation_code}, #{entity.operation_name}, #{entity.opera_level_code}, #{entity.inc_type_code}, #{entity.inc_heal_level_code}, #{entity.main_operation}, #{entity.is_iatrogenic}, #{entity.batch_no}, #{entity.lastUpdatedBy}, now(), #{entity.createdBy}, now(), #{entity.hospital_id})
        </foreach>

    </insert>
    <delete id="deleteOperationByAdNo">
        DELETE FROM mqs_hp_operation_record
        WHERE
        hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and admission_no = #{admissionNo}
        </if>
        <if test="nos != null and nos.size() >0 ">
            and `no` in
            <foreach collection="nos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="operationIds != null and operationIds.size() >0 ">
            and operation_id in
            <foreach collection="operationIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

    </delete>
    <delete id="deleteOperationListByAdNo">
        DELETE FROM mqs_hp_operation_list
        WHERE
        hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and admission_no = #{admissionNo}
        </if>
        <if test="nos != null and nos.size() >0 ">
            and `no` in
            <foreach collection="nos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="operationIds != null and operationIds.size() >0 ">
            and operation_id in
            <foreach collection="operationIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

    </delete>
    <select id="queryOperationList" resultMap="HpOperationRecordMap">
        select
        <include refid="baseSql"/>
        from
        mqs_hp_operation_record
        where hospital_id = #{hospitalId}
        and admission_no = #{admissionNo}
        <if test="no != null and no != ''">
            and `no` = #{no}
        </if>
    </select>
    <select id="queryOperationDetailList" resultMap="HpOperationListMap">
        select
        <include refid="baseListSql"/>
        from
        mqs_hp_operation_list
        where hospital_id = #{hospitalId}
        and admission_no = #{admissionNo}
        <if test="no != null and no != ''">
            and `no` = #{no}
        </if>
    </select>
    <select id="queryOperationListHis" resultMap="HpOperationRecordMap">
        select
        <include refid="baseSql"/>
        from
        mqs_hp_operation_record_his
        where hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and admission_no = #{admissionNo}
        </if>
        and batch_no = #{batch}
        <if test="no != null and no != ''">
            and `no` = #{no}
        </if>
    </select>
    <select id="queryOperationDetailListHis" resultMap="HpOperationListMap">
        select
        <include refid="baseListSql"/>
        from
        mqs_hp_operation_list_his
        where hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and admission_no = #{admissionNo}
        </if>
        and batch_no = #{batch}
        <if test="no != null and no != ''">
            and `no` = #{no}
        </if>
    </select>

</mapper>

