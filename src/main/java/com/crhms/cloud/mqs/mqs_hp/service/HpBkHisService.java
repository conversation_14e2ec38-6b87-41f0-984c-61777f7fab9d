package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpBkHis;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpBkHisMapper;
import com.crhms.cloud.mqs.mqs_hp.service.HpBkHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-每日记账审核(HpBkHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 10:22:14
 */
@Service("hpBkHisService")
public class HpBkHisService extends ServiceImpl< HpBkHisMapper, HpBkHis> {

}
