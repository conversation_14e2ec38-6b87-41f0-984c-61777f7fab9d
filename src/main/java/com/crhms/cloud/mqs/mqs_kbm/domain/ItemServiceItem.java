package com.crhms.cloud.mqs.mqs_kbm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 知识库管理-医保项目目录-国家医保服务项目信息
 *
 * @author: wangxingang
 * @date: 2023/02/03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("mqs_kbm_item_service_item")
public class ItemServiceItem {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 医保区划
     */
    @TableField
    private String medZone;

    /**
     * 项目代码
     */
    @TableField
    private String projectCode;

    /**
     * 项目名称
     */
    @TableField
    private String projectName;

    /**
     * 项目内涵
     */
    @TableField
    private String projectConnotation;

    /**
     * 除外内容
     */
    @TableField
    private String otherContent;

    /**
     * 计价单位
     */
    @TableField
    private String unit;

    /**
     * 归集口径
     */
    @TableField
    private String collectCaliber;

    /**
     * 说明
     */
    @TableField
    private String description;
}
