package com.crhms.cloud.mqs.core.sso;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * @ClassName SsoConfController
 * @Description 查询诊间审核2.0单点登录配置
 * <AUTHOR>
 * @Date 2022/3/10
 **/
@RestController
@RequestMapping("/api/mqs/sso")
public class SsoConfController {

    // 是否启用单点登录
    @Value("${crhms.sso-url:}")
    private String ssoUrl;

    // 是否启用单点登录
    @Value("${crhms.sso-redirect_uri:}")
    private String ssoRedirectUri;


    /**
     * 查询诊间审核2.0单点登录配置
     * <AUTHOR>
     * @date 2022/8/22
     **/
    @GetMapping("/conf")
    public ResponseEntity<HashMap> querySsoConf(){
        HashMap<String, Object> result = new HashMap<>();
        result.put("sso-url", ssoUrl);
        result.put("sso-redirect_uri", ssoRedirectUri);

        return new ResponseEntity(result, HttpStatus.OK);
    }
}
