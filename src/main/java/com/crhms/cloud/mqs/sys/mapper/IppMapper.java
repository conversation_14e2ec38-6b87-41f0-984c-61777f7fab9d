package com.crhms.cloud.mqs.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.sys.domain.IppDept;
import com.crhms.cloud.mqs.sys.domain.IppDoctor;
import com.crhms.cloud.mqs.sys.domain.IppZone;
import com.crhms.cloud.mqs.sys.domain.SysFundsDept;
import com.crhms.cloud.mqs.sys.dto.IppUser;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 集成平台ipp 数据库访问层
 *
 * <AUTHOR>
 * @since 2022-12-19 09:40:47
 */

@Mapper
public interface IppMapper {

    List<IppDept> selectDeptList(@Param("dept") String dept, @Param("hospitalId") String hospitalId);

    List<IppDoctor> selectDoctorList(@Param("doctor") String doctor, @Param("hospitalId") String hospitalId);

    List<IppZone> selectZoneList(@Param("zone") String zone, @Param("hospitalId") String hospitalId);

    List<Map> selectUserList(@Param("user") String user, @Param("ids") List<String> ids, @Param("hospitalId") String hospitalId);

    //查询用户所拥有的科室
    List<String> selectUserAuthDetp(@Param("userId") Long userId, @Param("hospitalId") String hospitalId);

    //查询院区名称
    String selectHospitalName(@Param("hospitalId") String hospitalId);

    @MapKey("code")
    Map<String, IppZone> selectZoneMap(@Param("zone") String zone, @Param("hospitalId") String hospitalId);

    //根据开启的菜单编码查询角色
    List<String> queryRoleByMenuCode (@Param("menuCode") String menuCode);

    //根据角色查询用户信息
    List<IppUser> queryUserNameAndId (@Param("roleIds") List<String> roleIds);
}

