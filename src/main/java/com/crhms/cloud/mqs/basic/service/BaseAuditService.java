package com.crhms.cloud.mqs.basic.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDiag;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.basic.vo.*;
import com.crhms.cloud.mqs.core.service.EngineService;
import com.crhms.cloud.mqs.sys.domain.SysRules;
import com.crhms.cloud.mqs.sys.domain.SysSceneFunction;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.service.SysConfigService;
import com.crhms.cloud.mqs.sys.service.SysSceneService;
import liquibase.pro.packaged.O;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.websocket.TransformationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 审核基础类
 *
 * <AUTHOR>
 * @since 2022-12-16 10:19:56
 */

public interface BaseAuditService {

    Logger log = LoggerFactory.getLogger(BaseAuditService.class);

    //诊间审核弹窗地址
    String MQS_AUDIT_UI_URL = "MQS_AUDIT_UI_URL";
    //诊间审核综合弹窗地址
    String MQS_AUDIT_UI_URL_ZH = "MQS_AUDIT_UI_URL_ZH";

    /**
     * 审核前数据加强
     *
     * @param hisBaseAuditDto
     */
    void beforeAudit(String batch, HisBaseAuditDto hisBaseAuditDto);

    /**
     * 审核后结果数据增强
     *
     * @param result
     */
    void afterAudit(String batch, EngAuditResultVo result, HisBaseAuditDto hisBaseAuditDto, List<BaseMedicalParam> data);

    /**
     * 组装引擎返回结果（第一个违规就诊流水号）
     *
     * @param engAuditResultVo
     * @return
     */
    default Map<String, Object> genEngViolationResults(EngAuditResultVo engAuditResultVo, EngAuditVo engAuditVo) {
        //若有违规明细则单据有违规
        List<EngPlReVo> engPlReVos = new ArrayList<>();
        if (ObjectUtil.isNotNull(engAuditResultVo.getLocal())) {
            engPlReVos.add(engAuditResultVo.getLocal());
        }
        if (ObjectUtil.isNotNull(engAuditResultVo.getEngine())) {
            engPlReVos.add(engAuditResultVo.getEngine());
        }
        if (ObjectUtil.isNotNull(engAuditResultVo.getPlatform())) {
            engPlReVos.add(engAuditResultVo.getPlatform());
        }
        for (EngPlReVo engPlReVo : engPlReVos) {
            if (ObjectUtil.isNotNull(engPlReVo.getResult())) {
                for (EngResultVo engResultVo : engPlReVo.getResult()) {
                    if (ObjectUtil.isNotNull(engResultVo.getViolation_result())) {
                        //本次审核结果有违规项
                        for (EngViolationResultVo engViolationResultVo : engResultVo.getViolation_result()) {
                            //违规结果信息
                            String detailNo = ObjectUtil.isEmpty(engViolationResultVo.getDetail_id())? "0":engViolationResultVo.getDetail_id();
                            String no = engResultVo.getClaim_id();

                            for (EngBillVo engBillVo : engAuditVo.getBill_list()) {
                                Boolean is_increment = engBillVo.getIs_increment();
                                String admission_no = engBillVo.getMedical_case().getHospitalized_no();
                                Optional<EngFeeVo> first = engBillVo.getFee_list().stream()
                                        .filter(x -> "1".equals(x.getIs_current()) && x.getId().equals(detailNo)).findFirst();
                                //本次主单违规 || 当次明细存在违规
                                if (("0".equals(detailNo) && no.equals(engBillVo.getMedical_case().getAdmission_no())) || first.isPresent()) {
                                    Map<String, Object> result = new HashMap<>();
                                    result.put("admission_no", admission_no);
                                    result.put("is_increment", is_increment);
                                    return result;
                                }
                            }
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * 审核引擎接口调用
     *
     * @param batch         批次号
     * @param auditScenario 审核场景
     * @return
     */
    default BaseAuditResultDto sentToAudit(String batch, String auditScenario, HisBaseAuditDto hisBaseAuditDto, SysSceneFunction sysSceneFunction) {
        EngineService engineService = SpringUtil.getBean(EngineService.class);
        BaseMedicalService medicalService = SpringUtil.getBean(BaseMedicalService.class);
        SysConfigService sysConfigService = SpringUtil.getBean(SysConfigService.class);
        String hospitalId = hisBaseAuditDto.getHospital_id();
        log.info("======原始数据组装 Start ======" + new java.sql.Timestamp(System.currentTimeMillis()).toString());
        beforeAudit(batch, hisBaseAuditDto);
        log.info("======数据二级处理 End ======" + new java.sql.Timestamp(System.currentTimeMillis()).toString());

        //排除不符合审核条件的就诊单类型
        List<HisMedicalParamsVo> filterList = filterList(hisBaseAuditDto,sysConfigService,hospitalId,batch);
        if(CollectionUtil.isEmpty(filterList)){
            return BaseAuditResultDto.builder().result_code("200100").batch(batch).msg("单据无违规").build();
        } else {
            hisBaseAuditDto.setData(filterList);
        }
        //组装处方数据
        List<BaseMedicalParam> baseMedicalParams;
        try {
            baseMedicalParams = MqsUtils.transformationToCase(hisBaseAuditDto);
        }catch (BaseException e) {
            e.printStackTrace();
            return BaseAuditResultDto.builder().result_code("200300").batch(batch).msg("数据加工异常！请检查：" + e.getErrMsg()).build();
        }

        //组装引擎报文
        EngAuditVo engAuditVo = MqsUtils.transformationToEng(hisBaseAuditDto.getIs_pretrial(), baseMedicalParams, auditScenario, sysSceneFunction);
        //调用引擎审核
        EngAuditResultVo engAuditResultVo = engineService.execRulesByCodes(batch ,engAuditVo, hisBaseAuditDto.getAuditRules(), hospitalId);
        //引擎规则名称映射
        transformationResult(engAuditResultVo, hospitalId);
        //组装处方数据
        afterAudit(batch, engAuditResultVo, hisBaseAuditDto, baseMedicalParams);
        //如果是无弹窗则直接保存
        boolean isSave = false;
        if("2".equals(sysSceneFunction.getFunctionCode())){
            isSave = true;
        }
        //保存审核数据
        medicalService.auditSave(batch, auditScenario, engAuditVo, engAuditResultVo, baseMedicalParams, hisBaseAuditDto.getIs_pretrial(), isSave, hospitalId);

        return genBaseAuditResultDto( engAuditResultVo, engAuditVo, hospitalId, batch, auditScenario, sysSceneFunction);
    }

    default void transformationResult(EngAuditResultVo engAuditResultVo,String hospitalId){

        RedisTemplate redisTemplate = SpringUtil.getBean("redisTemplate",RedisTemplate.class);
        Map<Object, Object> ruleMap = redisTemplate.opsForHash().entries(StrUtil.format(GloablData.GLOBAL_SYS_RULES_KEY, hospitalId));

        List<EngPlReVo> engPlReVos = new ArrayList<>();
        if (ObjectUtil.isNotNull(engAuditResultVo.getLocal())) {
            engPlReVos.add(engAuditResultVo.getLocal());
        }
        if (ObjectUtil.isNotNull(engAuditResultVo.getEngine())) {
            engPlReVos.add(engAuditResultVo.getEngine());
        }
        if (ObjectUtil.isNotNull(engAuditResultVo.getPlatform())) {
            engPlReVos.add(engAuditResultVo.getPlatform());
        }
        for (EngPlReVo engPlReVo : engPlReVos) {
            if (ObjectUtil.isNotNull(engPlReVo.getResult())) {
                for (EngResultVo engResultVo : engPlReVo.getResult()) {
                    if (ObjectUtil.isNotNull(engResultVo.getViolation_result())) {
                        //有违规项
                        for (EngViolationResultVo engViolationResultVo : engResultVo.getViolation_result()) {
                            String ruleNo = engViolationResultVo.getRule_no();
                            if(StrUtil.isNotEmpty(ruleNo)){
                                Map<String, String> rule = (Map<String, String>) ruleMap.get(ruleNo);
                                if(ObjectUtil.isNotNull(rule)){
                                    engViolationResultVo.setRule_name(rule.get(SysRules.FIELD_RULE_NAME));
                                }
                            }
                        }
                    }
                }
            }
        }
    };

    /**
     * 单据保存接口
     *
     * @param batch         批次号
     * @param auditScenario 审核场景
     * @return
     */
    default BaseAuditResultDto saveToRecord(String batch, String auditScenario, HisBaseAuditDto hisBaseAuditDto, SysSceneFunction sysSceneFunction) {
        hisBaseAuditDto.setIs_pretrial("0");
        EngineService engineService = SpringUtil.getBean(EngineService.class);
        BaseMedicalService medicalService = SpringUtil.getBean(BaseMedicalService.class);
        SysConfigService sysConfigService = SpringUtil.getBean(SysConfigService.class);
        String hospitalId = hisBaseAuditDto.getHospital_id();
        log.info("======原始数据组装 Start ======" + new java.sql.Timestamp(System.currentTimeMillis()).toString());
        beforeAudit(batch, hisBaseAuditDto);
        log.info("======数据二级处理 End ======" + new java.sql.Timestamp(System.currentTimeMillis()).toString());

        List<HisMedicalParamsVo> filterList = filterList(hisBaseAuditDto,sysConfigService,hospitalId,batch);
        if(CollectionUtil.isEmpty(filterList)){
            return BaseAuditResultDto.builder().url("").result_code("200100").batch(batch).msg("保存成功，无违规").build();
        } else {
            hisBaseAuditDto.setData(filterList);
        }

        //组装处方数据
        List<BaseMedicalParam> baseMedicalParams;
        try {
            baseMedicalParams = MqsUtils.transformationToCase(hisBaseAuditDto);
        }catch (BaseException e) {
            e.printStackTrace();
            return BaseAuditResultDto.builder().result_code("200300").batch(batch).msg("数据加工异常！请检查：" + e.getErrMsg()).build();
        }
        //组装引擎报文
        EngAuditVo engAuditVo = MqsUtils.transformationToEng(hisBaseAuditDto.getIs_pretrial(), baseMedicalParams, auditScenario, sysSceneFunction);
        //调用引擎审核
        EngAuditResultVo engAuditResultVo = engineService.execRulesByCodes(batch, engAuditVo, hisBaseAuditDto.getAuditRules(), hospitalId);
        //引擎规则名称映射
        transformationResult(engAuditResultVo, hospitalId);
        //保存审核数据
        medicalService.auditSave(batch, auditScenario,engAuditVo, engAuditResultVo, baseMedicalParams, hisBaseAuditDto.getIs_pretrial(), true, hospitalId);
        //返回报文
        return genBaseAuditResultDto( engAuditResultVo, engAuditVo, hospitalId, batch, auditScenario, sysSceneFunction);
    }

    /**
     * 单据重审接口
     *
     * @param batch           批次号(新的批次)
     * @param auditScenario   审核场景
     * @param hisBaseAuditDto 基础信息
     * @param no              单据号，可以指定处方重审(为空默认取所有处方)
     * @param saved           是否直接保存
     * @param sysSceneFunction 场景功能配置
     * @param oldBatch        原批次(支持历史批次重审)
     * @return
     */
    default BaseAuditResultDto reSentToAudit(String batch, String auditScenario, HisBaseAuditDto hisBaseAuditDto, String no,String saved,SysSceneFunction sysSceneFunction,String oldBatch) {
        EngineService engineService = SpringUtil.getBean(EngineService.class);
        BaseMedicalService medicalService = SpringUtil.getBean(BaseMedicalService.class);

        String hospitalId = hisBaseAuditDto.getHospital_id();
        //拉取已保存的处方数据
        List<BaseMedicalParam> baseMedicalParams = medicalService.genExitAuditDto(batch, hisBaseAuditDto.getAdmission_no(), no, auditScenario, hospitalId, oldBatch);

        //组装引擎报文
        EngAuditVo engAuditVo = MqsUtils.transformationToEng(hisBaseAuditDto.getIs_pretrial(), baseMedicalParams, auditScenario, sysSceneFunction);
        //调用引擎审核
        EngAuditResultVo engAuditResultVo = engineService.execRulesByCodes(batch, engAuditVo, hisBaseAuditDto.getAuditRules(), hospitalId);
        //引擎规则名称映射
        transformationResult(engAuditResultVo, hospitalId);
        //组装处方数据
        afterAudit(batch, engAuditResultVo, hisBaseAuditDto, baseMedicalParams);
        //保存审核数据
        medicalService.auditSave(batch, auditScenario, engAuditVo, engAuditResultVo, baseMedicalParams, hisBaseAuditDto.getIs_pretrial(), "1".equals(saved), hospitalId);

        return genBaseAuditResultDto( engAuditResultVo, engAuditVo, hospitalId, batch, auditScenario, sysSceneFunction);
    }

    /**
     * 统一返回解析内容
     * @param engAuditResultVo
     * @param engAuditVo
     * @param hospitalId
     * @param batch
     * @param auditScenario
     * @param sysSceneFunction
     * @return
     */
    default BaseAuditResultDto genBaseAuditResultDto(EngAuditResultVo engAuditResultVo,EngAuditVo engAuditVo,String hospitalId,String batch,String auditScenario,SysSceneFunction sysSceneFunction){

        SysConfigService sysConfigService = SpringUtil.getBean(SysConfigService.class);
        BaseAuditResultDto baseAuditResultDto;

        if ("true".equals(engAuditResultVo.getIs_success())) {
            //引擎执行成功 处理执行结果
            Map<String, Object> results = genEngViolationResults(engAuditResultVo, engAuditVo);
            //本次明细有违规记录 = 违规
            if (ObjectUtil.isNotNull(results)) {
                //根据配置,获取当前弹窗类型
                if("2".equals(sysSceneFunction.getFunctionCode())){
                    baseAuditResultDto = BaseAuditResultDto.builder().result_code("200200").batch(batch).msg("单据违规").violation_result(engAuditResultVo).build();
                } else {
                    String URL = sysConfigService.queryValueByKey("0".equals(sysSceneFunction.getFunctionCode())? MQS_AUDIT_UI_URL: MQS_AUDIT_UI_URL_ZH, hospitalId);
                    StringBuffer url = new StringBuffer();
                    url.append(URL);
                    url.append("?batch=").append(batch)
                            .append("&admission_no=")
                            .append(results.get("admission_no"))
                            .append("&audit_scenario=")
                            .append(auditScenario)
                            .append("&is_increment=")
                            .append(results.get("is_increment"))
                            .append("&hospital_id=")
                            .append(hospitalId);
                    String resultUrl = url.toString();
                    //审核违规
                    baseAuditResultDto = BaseAuditResultDto.builder().result_code("200200").batch(batch).msg("单据违规")
                            .url(resultUrl)
                            .violation_result(engAuditResultVo).build();
                }
            } else {
                //审核通过返回结果
                baseAuditResultDto = BaseAuditResultDto.builder().result_code("200100").batch(batch).msg("单据无违规").build();
            }

        } else {
            //引擎执行异常
            baseAuditResultDto = BaseAuditResultDto.builder().result_code("200300").batch(batch).msg("引擎执行异常！请检查").build();

        }
        return baseAuditResultDto;
    }


    /**
     * 审核后保存(检查反馈原因后数据保存)
     *
     * @param auditScenario
     * @param admissionNo
     * @param hospitalId
     * @param baseMedicalCases
     * @param medicalDetails
     * @param hisOperationMainVos
     * @param hisOperationDetailVos
     */
    default BaseAuditResultDto auditSave(String auditScenario, String admissionNo, String hospitalId, List<BaseMedicalCase> baseMedicalCases, List<BaseMedicalDetail> medicalDetails, List<HisOperationMainVo> hisOperationMainVos, List<HisOperationDetailVo> hisOperationDetailVos,List<BaseMedicalDiag> medicalDiagList) {
        EngineService engineService = SpringUtil.getBean(EngineService.class);

        List<BaseMedicalParam> baseMedicalParamList = new ArrayList<>();

        Map<String, List<BaseMedicalDetail>> feeMap = medicalDetails.stream().collect(Collectors.groupingBy(x -> x.getNo()));
        Map<String, List<HisOperationMainVo>> operationMap = hisOperationMainVos.stream().collect(Collectors.groupingBy(x -> x.getNo()));
        Map<String, List<HisOperationDetailVo>> operationDetailMap = hisOperationDetailVos.stream().collect(Collectors.groupingBy(x -> x.getOperation_id()));
        for (HisOperationMainVo hisOperationMainVo : hisOperationMainVos) {
            hisOperationMainVo.setOperation_detail_list(operationDetailMap.get(hisOperationMainVo.getOperation_id()));
        }

        for (BaseMedicalCase medicalCase : baseMedicalCases) {
            BaseMedicalParam baseMedicalParam = new BaseMedicalParam();
            baseMedicalParamList.add(baseMedicalParam);
            String NO = medicalCase.getNo();
            baseMedicalParam.setMedical_case(medicalCase);
            baseMedicalParam.setIs_increment("true".equals(medicalCase.getIsIncrement()));
            baseMedicalParam.setFee_list(feeMap.get(NO));
            baseMedicalParam.setOperation_list(operationMap.get(NO));
        }

        //组装引擎报文
        EngAuditVo engAuditVo = MqsUtils.transformationToEng("0", baseMedicalParamList, auditScenario, new SysSceneFunction());


        log.info("======调用引擎保存单据 Start======" + new java.sql.Timestamp(System.currentTimeMillis()).toString());
        EngAuditResultVo result = engineService.saveRecord(engAuditVo, hospitalId);
        log.info("引擎返回：" + JSONUtil.toJsonStr(result));
        log.info("======调用引擎保存单据 End======" + new java.sql.Timestamp(System.currentTimeMillis()).toString());
        BaseAuditResultDto baseAuditResultDto;

        if ("true".equals(result.getIs_success())) {
            //审核通过返回结果
            baseAuditResultDto = BaseAuditResultDto.builder().result_code("200100").msg("保存成功").build();
        } else {
            //引擎执行异常
            baseAuditResultDto = BaseAuditResultDto.builder().result_code("200300").msg("保存失败,引擎执行异常！请检查").build();
        }
        return baseAuditResultDto;
    }

    /*
    * 根据险种类型编码和人员类别编码筛选主单
    * */
    default List<HisMedicalParamsVo> filterList (HisBaseAuditDto hisBaseAuditDto,SysConfigService sysConfigService,String hospitalId,String batch) {
        String value = sysConfigService.queryValueByKey("MQS_SYS_PERSONNEL_BENEFIT_TYPE",hospitalId);
        if(StringUtils.isBlank(value)){
            return hisBaseAuditDto.getData();
        }
        JSONObject jSONObject = JSONUtil.parseObj(value);
        List<String> personnels = jSONObject.getJSONArray("personnel").toList(String.class);
        List<String> benefits = jSONObject.getJSONArray("benefit").toList(String.class);

        List<HisMedicalParamsVo> list = hisBaseAuditDto.getData();
        List<HisMedicalParamsVo> filterList = list.stream().filter(e->!personnels.contains(e.getMedical_case().getPersonnel_type()) && !benefits.contains(e.getMedical_case().getBenefit_type_id())).collect(Collectors.toList());
        log.info("======单据不在审核范围({})======({})===",list.size() - filterList.size(),batch);
        return filterList;
    }
}
