<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.sys.mapper.SysFundsAloneMapper">

    <resultMap type="com.crhms.cloud.mqs.sys.domain.SysFundsAlone" id="SysFundsAloneMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="years" column="years" jdbcType="VARCHAR"/>
        <result property="fundsId" column="funds_id" jdbcType="INTEGER"/>
        <result property="aloneCode" column="alone_code" jdbcType="VARCHAR"/>
        <result property="aloneName" column="alone_name" jdbcType="VARCHAR"/>
        <result property="amount" column="amount" jdbcType="NUMERIC"/>
        <result property="warn" column="warn" jdbcType="NUMERIC"/>
        <result property="usedAmount" column="used_amount" jdbcType="NUMERIC"/>
        <result property="usedRatio" column="used_ratio" jdbcType="NUMERIC"/>
        <result property="lastUpdatedBy" column="last_updated_by" jdbcType="INTEGER"/>
        <result property="lastUpdatedDate" column="last_updated_date" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="created_by" jdbcType="INTEGER"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="hospitalId" column="hospital_id" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

