package com.crhms.cloud.mqs.sys.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.sys.disruptor.DisruptorQueue;
import com.crhms.cloud.mqs.sys.domain.SysLogs;
import com.crhms.cloud.mqs.sys.dto.SysLogsVO;
import com.crhms.cloud.mqs.sys.mapper.SysLogsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
;
import java.util.List;

@Service
@Slf4j
public class SysLogsService extends ServiceImpl<SysLogsMapper, SysLogs> {

    @Autowired
    private SysLogsMapper sysLogsMapper;

    public List<SysLogs> queryByPage(Page<Object> pageInfo,SysLogsVO queryVO){
        String hospitalId = LoginContext.getHospitalId();
        return sysLogsMapper.queryPage(pageInfo,queryVO,hospitalId);
    }


    public void recordLogs (List<SysLogs> logs) {
        DisruptorQueue.SYS_LOGS_QUEUE.getRingBuffer()
                .publishEvent((event, sequence, buffer) -> {
                    event.setLogs(logs);
                });
    }

}
