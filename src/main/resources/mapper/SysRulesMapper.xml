<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.sys.mapper.SysRulesMapper">

    <resultMap type="com.crhms.cloud.mqs.sys.domain.SysRules" id="SysRulesMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="ruleCode" column="rule_code" jdbcType="VARCHAR"/>
        <result property="broadHeading" column="broad_heading" jdbcType="VARCHAR"/>
        <result property="ruleLevel" column="rule_level" jdbcType="VARCHAR"/>
        <result property="isHis" column="is_his" jdbcType="VARCHAR"/>
        <result property="isMain" column="is_main" jdbcType="VARCHAR"/>

        <result property="remake" column="remake" jdbcType="VARCHAR"/>
        <result property="enable" column="enable" jdbcType="VARCHAR"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
        <result property="ruleType" column="rule_type" jdbcType="VARCHAR"/>
        <result property="hospitalId" column="hospital_id" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="baseSql">
        id, rule_code, broad_heading, rule_level, is_his, is_main,rule_logic_desc,rule_data_desc, remake, rule_source,enable, last_updated_by, last_updated_date, created_by, created_date, rule_name, rule_type, hospital_id
    </sql>
    <!--查询指定行数据-->
    <select id="queryByPage" resultType="com.crhms.cloud.mqs.sys.domain.SysRules">
        select
          <include refid="baseSql"/>
        from mqs_sys_rules
        <where>
            deleted = '0'
            <if test="rule != null and rule != ''">
                and ( rule_code like CONCAT('%',#{rule,jdbcType = VARCHAR},'%')
                  or rule_name like CONCAT('%',#{rule,jdbcType = VARCHAR},'%'))
            </if>
            <if test="broadHeading != null and broadHeading != ''">
                and broad_heading = #{broadHeading}
            </if>
            <if test="ruleLevel != null and ruleLevel != ''">
                and rule_level = #{ruleLevel}
            </if>
            <if test="isHis != null and isHis != ''">
                and is_his = #{isHis}
            </if>
            <if test="isMain != null and isMain != ''">
                and is_main = #{isMain}
            </if>

            <if test="ruleType != null and ruleType != ''">
                and rule_type = #{ruleType}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and hospital_id = #{hospitalId}
            </if>
        </where>
    </select>
    <select id="seletRuleList" resultType="com.crhms.cloud.mqs.sys.domain.SysRules">

        select
        rule_code,
        rule_name
        from mqs_sys_rules
        <where>
        deleted = '0'
        and rule_type != '0'
        <if test="rule != null and rule != ''">
            and ( rule_code like CONCAT('%',#{rule,jdbcType = VARCHAR},'%')
            or rule_name like CONCAT('%',#{rule,jdbcType = VARCHAR},'%'))
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            and hospital_id = #{hospitalId}
        </if>
        </where>

    </select>
    <select id="queryPageByVO" resultType="com.crhms.cloud.mqs.sys.domain.SysRules">
        SELECT
            t1.id,
            t1.rule_code,
            t1.rule_name,
            t1.rule_logic_desc,
            t1.rule_data_desc,
            t1.primary_category_code,
            t1.primary_category_name,
            t1.secondary_category_code,
            t1.secondary_category_name,
            t2.code as rule_level,
            t2.`name` AS rule_level_name,
            GROUP_CONCAT( t3.broad_heading ) AS broad_heading,
            t1.is_his,
            t1.is_main,
            t1.remake,
            t1.rule_type,
            t1.`enable`,
            t1.data_config_code
        FROM
            mqs_sys_rules t1
        LEFT JOIN mqs_sys_rule_level_third b ON t1.rule_type = b.platform
        AND t1.rule_level = b.`code` AND t1.hospital_id = b.hospital_id
        LEFT JOIN mqs_sys_rule_level t2 ON b.sys_code = t2.`code` AND b.hospital_id = t2.hospital_id
        LEFT JOIN mqs_sys_rule_broad_heading t3 ON t1.rule_code = t3.rule_code AND t3.hospital_id = t1.hospital_id
        <where>
            t1.hospital_id = #{hospitalId} and t1.deleted = '0'
            and t1.rule_type != '0'
            <!-- 规则编码/名称模糊查询 -->
            <if test="queryVO.rule != null and queryVO.rule != '' ">
                and (
                t1.rule_code like CONCAT('%', #{queryVO.rule}, '%')
                or t1.rule_name like CONCAT('%', #{queryVO.rule}, '%')
                )
            </if>
            <!-- 规则级别 -->
            <if test="queryVO.ruleLevelList != null and queryVO.ruleLevelList.size > 0 ">
                and t2.code in
                <foreach collection="queryVO.ruleLevelList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <!-- 规则大类 -->
            <if test="queryVO.broadHeadingList != null and queryVO.broadHeadingList.size > 0 ">
                and t3.broad_heading in
                <foreach collection="queryVO.broadHeadingList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <!-- 是否查询历史数据 -->
            <if test="queryVO.isHisList != null and queryVO.isHisList.size > 0 ">
                and t1.is_his in
                <foreach collection="queryVO.isHisList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <!-- 是否作用在主单 -->
            <if test="queryVO.isMainList != null and queryVO.isMainList.size > 0 ">
                and t1.is_main in
                <foreach collection="queryVO.isMainList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <!-- 规则来源 -->
            <if test="queryVO.ruleType != null and queryVO.ruleType != '' ">
                and t1.rule_type = #{queryVO.ruleType}
            </if>
            <if test="queryVO.primaryCategory != null and queryVO.primaryCategory != ''">
                and primary_category_code = #{queryVO.primaryCategory}
            </if>
            <if test="queryVO.secondaryCategory != null and queryVO.secondaryCategory != ''">
                and secondary_category_code = #{queryVO.secondaryCategory}
            </if>
        </where>
        GROUP BY
        t1.id,
        t1.rule_code,
        t1.rule_name,
        t1.rule_logic_desc,
        t1.rule_data_desc,
        t1.primary_category_code,
        t1.primary_category_name,
        t1.secondary_category_code,
        t1.secondary_category_name,
        t2.`code`,
        t2.`name`,
        t1.is_his,
        t1.is_main,
        t1.remake,
        t1.rule_type,
        t1.`enable`,
        t1.data_config_code
        ORDER BY rule_code DESC
    </select>
    <select id="selectBlockRules" resultType="java.lang.String">
        SELECT
        a.rule_code
        FROM
        mqs_sys_rules a
        LEFT JOIN mqs_sys_rule_level_third b ON a.rule_type = b.platform
        AND a.rule_level = b.`code`AND a.hospital_id = b.hospital_id
        LEFT JOIN mqs_sys_rule_level c ON b.sys_code = c.`code` AND b.hospital_id = c.hospital_id
        WHERE
        a.hospital_id = #{hospitalId}
        AND a.deleted = '0'
        AND c.level_config &lt;= #{levelConfig}
        <if test="rules != null and rules.size > 0 ">
            AND a.rule_code in
            <foreach collection="rules" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectRulesCache" resultType="com.crhms.cloud.mqs.sys.domain.SysRules">

        SELECT
            a.rule_code,
            a.rule_name,
            a.rule_type,
            a.rule_level,
            a.hospital_id,
            c.`name` AS rule_level_name,
			c.level_config
            FROM mqs_sys_rules a
            LEFT JOIN mqs_sys_rule_level_third b ON a.rule_type = b.platform AND a.rule_level = b.`code` AND a.hospital_id = b.hospital_id
            LEFT JOIN mqs_sys_rule_level c ON b.sys_code = c.`code` AND b.hospital_id = c.hospital_id
            WHERE
            a.enable = '1'
            AND a.deleted = '0'
            <if test="hospitalId != null and hospitalId != ''">
                AND a.hospital_id = #{hospitalId}
            </if>

    </select>
    <select id="selectSceneRules" resultType="com.crhms.cloud.mqs.sys.domain.SysSceneRule">
        select
        mssr.scene_code,
        mssr.rule_code,
        mssr.hospital_id
        from
        mqs_sys_scene_rule mssr
        LEFT JOIN mqs_sys_rules msr ON msr.rule_code = mssr.rule_code and mssr.hospital_id = msr.hospital_id
        WHERE
        msr.deleted = '0'
        and msr.enable = '1'
        <if test="hospitalId != null and hospitalId != ''">
            and mssr.hospital_id = #{hospitalId}
        </if>

    </select>
    <select id="queryMrRuleByLevel" resultType="com.crhms.cloud.mqs.sys.domain.SysRules">
        SELECT
        a.rule_code,
        a.rule_name
        FROM mqs_sys_rules a
        WHERE
        a.rule_type = '0'
        and a.rule_level = #{ruleType}
        and a.hospital_id = #{hospitalId}
    </select>


</mapper>

