<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_kbm.mapper.InstructionWestMapper">

    <select id="queryDosageList" resultType="java.lang.String">
        SELECT
            drug_dosage
        FROM
            mqs_kbm_instruction_drug
        WHERE
            drug_type = #{type}
          and drug_dosage is not null
        GROUP BY
            drug_dosage
    </select>
    <select id="queryWestDrugList" resultType="com.crhms.cloud.mqs.mqs_kbm.vo.InstructionDrug">
        SELECT
            drug_code,
            drug_name,
            drug_name_en,
            drug_alias,
            drug_dosage
        FROM
            mqs_kbm_instruction_drug
        <include refid="where"></include>
    </select>
    <select id="queryChineseDrugList" resultType="com.crhms.cloud.mqs.mqs_kbm.vo.InstructionDrug">
        SELECT
            drug_code,
            drug_name,
            drug_name_en,
            drug_alias,
            drug_dosage,
            first_class_name,
            second_class_name,
            third_class_name,
            forth_class_name,
            fifth_class_name
        FROM
            mqs_kbm_instruction_drug
        <include refid="where"></include>
    </select>
    <select id="querySpecialGroup" resultType="java.util.Map">
        select
            drug_code,
            drug_name,
            drug_dosage,
            special_groups_type_name,
            special_groups_level_name,
            special_groups_sources_name,
            special_groups_limit,
            prompt_info,
            special_groups_reason,
            remark
        from mqs_kbm_instruction_special
        where drug_type = #{type} and drug_code = #{drugCode}
    </select>
    <select id="queryIncompatibility" resultType="java.util.Map">
        select
            drug_code_A,
            drug_name_A,
            drug_dosage_A,
            drug_code_B,
            drug_name_B,
            drug_dosage_B,
            alert_level_name,
            caution,
            data_source_name,
            mechanism
        from mqs_kbm_instruction_incompatibility
        where drug_type = #{type} and drug_code_A = #{drugCode}
    </select>
    <select id="queryIndication" resultType="java.util.Map">
        select
            drug_code,
            drug_name,
            idication_idication_cn,
            idication_B_idication_cn,
            adult_daily_max,
            old_daily_max,
            child_daily_max,
            kidney_failure_daily_max,
            hypohepatia_daily_max,
            prompt_info,
            data_source_name
        from mqs_kbm_instruction_indication
        where drug_type = #{type} and drug_code = #{drugCode}
    </select>
    <select id="queryTaboos" resultType="java.util.Map">
        select
            drug_code,
            drug_name,
            idication_code,
            idication_idication_cn,
            prompt_info,
            alert_level_name,
            data_source_name,
            remark
        from mqs_kbm_instruction_taboos
        where drug_type = #{type} and drug_code = #{drugCode}
    </select>
    <select id="queryInteraction" resultType="java.util.Map">
        select
            drug_code_A,
            drug_name_A,
            drug_dosage_A,
            drug_code_B,
            drug_name_B,
            drug_dosage_B,
            alert_level_name,
            data_source_name,
            mechanism,
            remark
        from mqs_kbm_instruction_interaction
        where drug_type = "0" and drug_code_A = #{drugCode}
    </select>
    <select id="queryAllergy" resultType="java.util.Map">
        select
            drug_code,
            drug_name,
            safety_instruction,
            allergy_warning,
            data_source_name,
            treatment_measure,
            skin_test_concentration,
            medication,
            remark
        from mqs_kbm_instruction_allergy
        where drug_type = "0" and drug_code = #{drugCode}
    </select>

    <sql id="where">
        WHERE
            drug_type = #{type}
            <if test="drugCode != null and drugCode != '' ">
                and drug_code like CONCAT('%', #{drugCode}, '%')
            </if>
            <if test="drugName != null and drugName != '' ">
                and drug_name like CONCAT('%', #{drugName}, '%')
            </if>
            <if test="dosageList != null and dosageList.size > 0">
                and drug_dosage in
                <foreach collection="dosageList" open="(" close=")" separator="," item="dosage">
                    #{dosage}
                </foreach>
            </if>
    </sql>
</mapper>