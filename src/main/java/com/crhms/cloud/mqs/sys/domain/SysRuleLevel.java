package com.crhms.cloud.mqs.sys.domain;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 规则级别配置表(SysRuleLevel)实体类
 *
 * <AUTHOR>
 * @since 2023-03-21 17:54:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_rule_level")
public class SysRuleLevel extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 781097389777480711L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_CODE="code";
    public static final String FIELD_ENABLE="enable";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";
    public static final String FIELD_DEFAULT_NAME="default_name";
    public static final String FIELD_NAME="name";
    public static final String FIELD_LEVEL_CONFIG="level_config";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 平台 (zdy/tcq)
     */
    @TableField
    private String platform;
    /**
     * 规则级别编码
     */    
    @TableField
    private String code;
    /**
     * 默认规则级别名称
     */    
    @TableField
    private String defaultName;
    /**
     * 自定义规则级别名称
     */    
    @TableField
    private String name;

    /**
     * 级别配置 1.阻断 2.需说明 3.无需说明 4.仅提醒
     */
    @TableField
    private String levelConfig;


    /**
     * 是否启用1.启用 0.禁用
     */
    @TableField
    private String enable;
    /**
     * 院区id
     */
    @TableField
    private String hospitalId;


    @TableField(exist = false)
    private String config1;
    @TableField(exist = false)
    private String config2;
    @TableField(exist = false)
    private String config3;
    @TableField(exist = false)
    private String config4;


    public void setLevelConfig(String levelConfig) {
        this.levelConfig = levelConfig;
        switch (levelConfig){
            case "1":
                this.config1 = "1";
                break;
            case "2":
                this.config2 = "1";
                break;
            case "3":
                this.config3 = "1";
                break;
            case "4":
                this.config4 = "1";
                break;
        }
    }
}

