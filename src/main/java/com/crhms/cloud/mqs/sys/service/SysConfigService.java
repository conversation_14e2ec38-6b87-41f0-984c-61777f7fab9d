package com.crhms.cloud.mqs.sys.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.service.GlobalCache;
import com.crhms.cloud.mqs.mqs_ais.domain.SysMonitorPersonal;
import com.crhms.cloud.mqs.mqs_ais.service.SysMonitorPersonalService;
import com.crhms.cloud.mqs.sys.domain.*;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.mapper.SysConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 系统配置表(SysConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-12 14:38:53
 */
@Service("sysConfigService")
public class SysConfigService extends ServiceImpl< SysConfigMapper, SysConfig> {

    @Autowired
    SysRuleLevelService sysRuleLevelService;

    @Autowired
    SysRuleLevelThirdService sysRuleLevelThirdService;
    @Autowired
    GlobalCache globalCache;
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    SysSceneService sysSceneService;
    @Autowired
    SysSceneFunctionService sysSceneFunctionService;
    @Autowired
    SysReasonTypeService sysReasonTypeService;
    @Autowired
    SysMonitorPersonalService sysMonitorPersonalService;
    @Autowired
    SysRulesService sysRulesService;


    /**
     * 查询单条配置数据
     *
     * @param key
     * @return 实例对象
     */
    public SysConfig queryByKey(String key,String hospitalId) {
        SysConfig sysConfig = this.getOne(new QueryWrapper<SysConfig>().eq(SysConfig.FIELD_SYS_KEY, key).eq(SysConfig.FIELD_HOSPITAL_ID, hospitalId));
        return sysConfig;
    }

    /**
     * 查询单条配置数据（优先查询缓存）
     *
     * @param key
     * @return 实例对象
     */
    public String queryValueByKey(String key, String hospitalId) {
        String value = (String)redisTemplate.opsForHash().entries(StrUtil.format(GloablData.GLOBAL_SYS_CONFIG_KEY, hospitalId)).get(key);
        if(StrUtil.isNotEmpty(value)){
            return value;
        }
        SysConfig sysConfig = this.getOne(new QueryWrapper<SysConfig>().eq(SysConfig.FIELD_SYS_KEY, key).eq(SysConfig.FIELD_HOSPITAL_ID, hospitalId).eq(SysConfig.FIELD_ENABLE,"1"));
        return Objects.isNull(sysConfig) ? "" : sysConfig.getSysValue();
    }

    /**
     * 查询配置列表
     * @param sysConfig
     * @return
     */
    public List<SysConfig> queryList(SysConfig sysConfig){
        sysConfig.setHospitalId(LoginContext.getHospitalId());
        return this.baseMapper.queryList(sysConfig);
    }


    /**
     * 查询规则级别配置
     * @param sysRuleLevel
     * @return
     */
    public List<SysRuleLevel> queryRuleLevelList(SysRuleLevel sysRuleLevel){
        sysRuleLevel.setHospitalId(LoginContext.getHospitalId());
        return baseMapper.queryRuleLevelList(sysRuleLevel);
    }

    /**
     * 更新规则级别配置
     */
    public void updateRuleLevel(List<SysRuleLevel> sysRuleLevels){
        sysRuleLevelService.updateBatchById(sysRuleLevels);
    }

    /**
     * 更新系统配置
     */
    public void updateConfig(SysConfig sysConfig){
        sysConfig.setHospitalId(LoginContext.getHospitalId());
        Long count = 0L;
        if(Objects.isNull(sysConfig.getId())){
            count = this.baseMapper.selectCount(new QueryWrapper<SysConfig>().eq(SysConfig.FIELD_SYS_KEY, sysConfig.getSysKey()).eq(SysConfig.FIELD_HOSPITAL_ID,sysConfig.getHospitalId()));
        }else {
            count = this.baseMapper.selectCount(new QueryWrapper<SysConfig>().eq(SysConfig.FIELD_SYS_KEY, sysConfig.getSysKey()).ne(SysConfig.FIELD_ID, sysConfig.getId()).eq(SysConfig.FIELD_HOSPITAL_ID,sysConfig.getHospitalId()));
        }
        if(count > 0){
            throw new BaseException("配置编码重复！");
        }
        this.saveOrUpdate(sysConfig);
        globalCache.reflashSystemConfigCache(LoginContext.getHospitalId());
    }

    /**
     * 根据不同的platform查询规则级别下拉列表
     * @param platform 平台 1自定义2内置3医保平台
     * @return 规则级别集合
     */
    public List<SysRuleLevelThird> ruleLevelList(String platform) {
        List<SysRuleLevelThird> ruleLevelList = sysRuleLevelThirdService.getBaseMapper().ruleLevelList(platform, LoginContext.getHospitalId());
        return ruleLevelList;
    }

    //检查用户所在院区的初始化状态 未初始化将以 院区为1的数据为模板初始化
    @Async
    @Transactional
    public void compatibilityHospital(String hospitalId) {
        /**
         * mqs_sys_config               全量
         * mqs_sys_rule_level_third     全量
         * mqs_sys_rule_level           全量
         * mqs_sys_scene                全量
         * mqs_sys_scene_function       全量
         * mqs_sys_reason_type          全量
         * mqs_sys_monitor_personal     全量
         * mqs_sys_rules                部分数据 rule_type = 0
         */
        if("1".equals(hospitalId) || StrUtil.isEmpty(hospitalId)){
            return;
        }
        if(this.count(new QueryWrapper<SysConfig>().eq(SysConfig.FIELD_HOSPITAL_ID, hospitalId)) == 0){
            List<SysConfig> initDatas = this.list(new QueryWrapper<SysConfig>().eq(SysConfig.FIELD_HOSPITAL_ID, "1"));
            initDatas.stream().forEach(x -> {x.setId(null); x.setHospitalId(hospitalId);});
            this.saveBatch(initDatas);
        }
        if(sysRuleLevelThirdService.count(new QueryWrapper<SysRuleLevelThird>().eq(SysRuleLevelThird.FIELD_HOSPITAL_ID, hospitalId)) == 0){
            List<SysRuleLevelThird> initDatas = sysRuleLevelThirdService.list(new QueryWrapper<SysRuleLevelThird>().eq(SysRuleLevelThird.FIELD_HOSPITAL_ID, "1"));
            initDatas.stream().forEach(x -> {x.setId(null); x.setHospitalId(hospitalId);});
            sysRuleLevelThirdService.saveBatch(initDatas);
        }
        if(sysRuleLevelService.count(new QueryWrapper<SysRuleLevel>().eq(SysRuleLevel.FIELD_HOSPITAL_ID, hospitalId)) == 0){
            List<SysRuleLevel> initDatas = sysRuleLevelService.list(new QueryWrapper<SysRuleLevel>().eq(SysRuleLevel.FIELD_HOSPITAL_ID, "1"));
            initDatas.stream().forEach(x -> {x.setId(null); x.setHospitalId(hospitalId);});
            sysRuleLevelService.saveBatch(initDatas);
        }
        if(sysSceneService.count(new QueryWrapper<SysScene>().eq(SysScene.FIELD_HOSPITAL_ID, hospitalId)) == 0){
            List<SysScene> initDatas = sysSceneService.list(new QueryWrapper<SysScene>().eq(SysScene.FIELD_HOSPITAL_ID, "1"));
            initDatas.stream().forEach(x -> {x.setId(null); x.setHospitalId(hospitalId);});
            sysSceneService.saveBatch(initDatas);
        }
        if(sysSceneFunctionService.count(new QueryWrapper<SysSceneFunction>().eq(SysSceneFunction.FIELD_HOSPITAL_ID, hospitalId)) == 0){
            List<SysSceneFunction> initDatas = sysSceneFunctionService.list(new QueryWrapper<SysSceneFunction>().eq(SysSceneFunction.FIELD_HOSPITAL_ID, "1"));
            initDatas.stream().forEach(x -> {x.setId(null); x.setHospitalId(hospitalId);});
            sysSceneFunctionService.saveBatch(initDatas);
        }
        if(sysReasonTypeService.count(new QueryWrapper<SysReasonType>().eq(SysReasonType.FIELD_HOSPITAL_ID, hospitalId)) == 0){
            List<SysReasonType> initDatas = sysReasonTypeService.list(new QueryWrapper<SysReasonType>().eq(SysReasonType.FIELD_HOSPITAL_ID, "1"));
            initDatas.stream().forEach(x -> {x.setId(null); x.setHospitalId(hospitalId);});
            sysReasonTypeService.saveBatch(initDatas);
        }
        if(sysMonitorPersonalService.count(new QueryWrapper<SysMonitorPersonal>().eq(SysMonitorPersonal.FIELD_HOSPITAL_ID, hospitalId)) == 0){
            List<SysMonitorPersonal> initDatas = sysMonitorPersonalService.list(new QueryWrapper<SysMonitorPersonal>().eq(SysMonitorPersonal.FIELD_HOSPITAL_ID, "1"));
            initDatas.stream().forEach(x -> {x.setId(null); x.setHospitalId(hospitalId);});
            sysMonitorPersonalService.saveBatch(initDatas);
        }
        if(sysRulesService.count(new QueryWrapper<SysRules>().eq(SysRules.FIELD_HOSPITAL_ID, hospitalId).eq(SysRules.FIELD_RULE_TYPE,"0")) == 0){
            List<SysRules> initDatas = sysRulesService.list(new QueryWrapper<SysRules>().eq(SysRules.FIELD_HOSPITAL_ID, "1").eq(SysRules.FIELD_RULE_TYPE,"0"));
            initDatas.stream().forEach(x -> {x.setId(null); x.setHospitalId(hospitalId);});
            sysRulesService.saveBatch(initDatas);
        }
    }
}
