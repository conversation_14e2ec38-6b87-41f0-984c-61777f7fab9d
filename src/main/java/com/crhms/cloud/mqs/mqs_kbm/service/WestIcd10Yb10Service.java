package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.WestIcd10Yb10;
import com.crhms.cloud.mqs.mqs_kbm.mapper.WestIcd10Yb10Mapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * ICD-10医保版CHS1.0
 *
 * @author: wangxingang
 * @date: 2023.02.06
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class WestIcd10Yb10Service extends ServiceImpl<WestIcd10Yb10Mapper, WestIcd10Yb10> {

    /**
     * ICD-10医保版CHS1.0
     *
     * @param pageInfo 分页信息
     * @param code     诊断代码
     * @param name     诊断名称
     */
    public List<WestIcd10Yb10> queryIcd10Yb10(Page<WestIcd10Yb10> pageInfo, String code, String name) {
        return page(pageInfo, new LambdaQueryWrapper<WestIcd10Yb10>()
                .like(StrUtil.isNotEmpty(code), WestIcd10Yb10::getCode, code)
                .like(StrUtil.isNotEmpty(name), WestIcd10Yb10::getName, name)).getRecords();
    }
}
