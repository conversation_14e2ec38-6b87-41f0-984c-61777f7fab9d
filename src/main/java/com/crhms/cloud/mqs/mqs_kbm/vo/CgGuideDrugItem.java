package com.crhms.cloud.mqs.mqs_kbm.vo;

import lombok.Data;

/**
 * 临床指南-"药品"页面列表信息
 *
 * @author: wangxingang
 * @date: 2023.02.08
 */
@Data
public class CgGuideDrugItem {

    /**
     * 编码
     */
    private String diseaseCode;

    /**
     * 药品名称
     */
    private String serviceName;

    /**
     * 药品治疗类型
     */
    private String serviceTypeName;

    /**
     * 联合用药
     */
    private String serviceUnion;

    /**
     * 使用阶梯
     */
    private String useladder;

    /**
     * 给药途径
     */
    private String medication;

    /**
     * 单次剂量
     */
    private String frequencySingle;

    /**
     * 剂量单位
     */
    private String frequencyClass;

    /**
     * 单日频次
     */
    private String serviceNumber;

    /**
     * 药品疗程
     */
    private String serviceProcess;

    /**
     * 数据内涵
     */
    private String serviceDetail;

    /**
     * 备注
     */
    private String remarks;
}
