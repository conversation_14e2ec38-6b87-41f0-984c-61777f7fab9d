package com.crhms.cloud.mqs.mqs_ais.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.vo.BaseDetailQueryVO;
import com.crhms.cloud.mqs.mqs_ais.service.MqsAnalyzeService;
import com.crhms.cloud.mqs.mqs_ais.vo.*;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * 决策支持 统计页面
 *
 * <AUTHOR>
 * @since 2023-02-15 14:43:09
 */
@RestController
@RequestMapping("api/mqs/analyze")
public class MqsAnalyzeController {
    /**
     * 服务对象
     */
    @Resource
    private MqsAnalyzeService mqsAnalyzeService;

    /**
     * 业务分析 - title
     *
     * @param itemDateFrom
     * @param itemDateTo
     * @param dept
     * @return
     */
    @GetMapping("/bus/title")
    public ResponseEntity<Map> busTitleTotal(@RequestParam(value = "itemDateFrom") String itemDateFrom,
                                             @RequestParam(value = "itemDateTo") String itemDateTo,
                                             @RequestParam(value = "dept", required = false) String dept,
                                             @RequestParam(value = "type") String type) {
        return ResponseEntity.ok(mqsAnalyzeService.busTitleTotal(itemDateFrom, itemDateTo, dept, type, LoginContext.getHospitalId()));
    }

    /**
     * 业务分析 - 统计列表
     *
     * @return
     */
    @GetMapping("/bus/getAnalysisDetails")
    public ResponseEntity<List<Map>> getAnalysisDetails(@RequestParam(value = "itemDateFrom") String itemDateFrom,
                                                        @RequestParam(value = "itemDateTo") String itemDateTo,
                                                        @RequestParam(value = "dept", required = false) String dept,
                                                        @RequestParam(value = "type") String type,
                                                        @RequestParam(value = "order") String order) {
        return ResponseEntity.ok(mqsAnalyzeService.getAnalysisDetails(itemDateFrom, itemDateTo, dept, type, order, LoginContext.getHospitalId()));
    }

    /**
     * 业务分析 - 统计列表 - 费用大类统计
     *
     * @return
     */
    @GetMapping("/bus/getAnalysisType")
    public ResponseEntity<List<Map>> getAnalysisType(@RequestParam(value = "itemDateFrom") String itemDateFrom,
                                                     @RequestParam(value = "itemDateTo") String itemDateTo,
                                                     @RequestParam(value = "dept", required = false) String dept,
                                                     @RequestParam(value = "type") String type,
                                                     @RequestParam(value = "pro") String pro,
                                                     @RequestParam(value = "order") String order) {
        return ResponseEntity.ok(mqsAnalyzeService.getAnalysisType(itemDateFrom, itemDateTo, dept, type, pro, order, LoginContext.getHospitalId()));
    }

    /**
     * 业务分析 - 人次趋势
     *
     * @return
     */
    @GetMapping("/bus/ptTrend")
    public ResponseEntity<List<Map>> genptTrend(@RequestParam(value = "itemDateFrom") String itemDateFrom,
                                                @RequestParam(value = "itemDateTo") String itemDateTo,
                                                @RequestParam(value = "dept", required = false) String dept,
                                                @RequestParam(value = "type") String type) {
        return ResponseEntity.ok(mqsAnalyzeService.genptTrend(itemDateFrom, itemDateTo, dept, type, LoginContext.getHospitalId()));
    }

    /**
     * 业务分析 - 科室/医生趋势
     *
     * @return
     */
    @GetMapping("/bus/dpTrend")
    public ResponseEntity<List<Map>> gendpTrend(@RequestParam(value = "itemDateFrom") String itemDateFrom,
                                                @RequestParam(value = "itemDateTo") String itemDateTo,
                                                @RequestParam(value = "dept", required = false) String dept,
                                                @RequestParam(value = "type") String type,
                                                @RequestParam(value = "order") String order) {
        return ResponseEntity.ok(mqsAnalyzeService.gendpTrend(itemDateFrom, itemDateTo, dept, type, order, LoginContext.getHospitalId()));
    }

    /**
     * 业务分析 - 费用趋势
     *
     * @return
     */
    @GetMapping("/bus/feeTrend")
    public ResponseEntity<List<Map>> feeTrend(@RequestParam(value = "itemDateFrom") String itemDateFrom,
                                              @RequestParam(value = "itemDateTo") String itemDateTo,
                                              @RequestParam(value = "dept", required = false) String dept,
                                              @RequestParam(value = "type") String type) {
        return ResponseEntity.ok(mqsAnalyzeService.feeTrend(itemDateFrom, itemDateTo, dept, type, LoginContext.getHospitalId()));
    }

    /**
     * 业务分析 - 费用结构
     *
     * @return
     */
    @GetMapping("/bus/feeTypeTrend")
    public ResponseEntity<List<Map>> feeTypeTrend(@RequestParam(value = "itemDateFrom") String itemDateFrom,
                                                  @RequestParam(value = "itemDateTo") String itemDateTo,
                                                  @RequestParam(value = "dept", required = false) String dept,
                                                  @RequestParam(value = "type") String type) {
        return ResponseEntity.ok(mqsAnalyzeService.feeTypeTrend(itemDateFrom, itemDateTo, dept, type, LoginContext.getHospitalId()));
    }

    /**
     * 违规分析 - 违规费用统计
     *
     * @return
     */
    @PostMapping("/violation/getFeeAnalysis")
    public ResponseEntity<List<Map>> getFeeAnalysis(@RequestBody ViolationQueryVo queryVo) {
        Page page = PageUtil.getPage(queryVo.getPage(), queryVo.getPageSize());
        return new ResponseEntity(this.mqsAnalyzeService.getFeeAnalysis(queryVo, page, LoginContext.getHospitalId()), PageUtil.getTotalHeader(page), HttpStatus.OK);
    }

    /**
     * 违规分析 - 违反规则统计 - title
     *
     * @return
     */
    @PostMapping("/violation/getVRuleTital")
    public ResponseEntity<Map> getVRuleTital(@RequestBody ViolationQueryVo queryVo) {
        return ResponseEntity.ok(this.mqsAnalyzeService.getVRuleTital(queryVo, LoginContext.getHospitalId()));
    }

    /**
     * 违规分析 - 违反规则统计 - 分页查询
     *
     * @return
     */
    @PostMapping("/violation/getVRuleListByPage")
    public ResponseEntity<List<Map>> getVRuleListByPage(@RequestBody ViolationQueryVo queryVo) {
        Page page = PageUtil.getPage(queryVo.getPage(), queryVo.getPageSize());
        return new ResponseEntity(this.mqsAnalyzeService.getVRuleListByPage(queryVo, page, LoginContext.getHospitalId()), PageUtil.getTotalHeader(page), HttpStatus.OK);
    }

    /**
     * 违规分析 - 违反规则统计 - 列表查询
     *
     * @return
     */
    @PostMapping("/violation/getVRuleList")
    public ResponseEntity<List<Map>> getVRuleList(@RequestBody ViolationQueryVo queryVo) {
        return ResponseEntity.ok(this.mqsAnalyzeService.getVRuleList(queryVo, LoginContext.getHospitalId()));
    }

    /**
     * 违规分析 - 违规项目排行TOP10
     *
     * @return
     */
    @PostMapping("/violation/getVItemTop")
    public ResponseEntity<List<Map>> getVItemTop10(@RequestBody ViolationQueryVo queryVo) {
        return ResponseEntity.ok(this.mqsAnalyzeService.getVItemTop10(queryVo, LoginContext.getHospitalId()));
    }

    /**
     * 违规分析 - 违反规则排行TOP10
     *
     * @return
     */
    @PostMapping("/violation/getVRuleTop")
    public ResponseEntity<List<Map>> getVRuleTop10(@RequestBody ViolationQueryVo queryVo) {
        return ResponseEntity.ok(this.mqsAnalyzeService.getVRuleTop10(queryVo, LoginContext.getHospitalId()));
    }


    /**
     * 慢特病分析 - 慢特病病种排行TOP5 - 年内
     *
     * @return
     */
    @GetMapping("/csDisease/getTop5")
    public ResponseEntity<List<Map>> getcsDiseaseTop5(@RequestParam(value = "year", required = false) String year) {

        Calendar calendar = Calendar.getInstance();
        if(Strings.isEmpty(year)){
            year = Integer.toString(calendar.get(Calendar.YEAR));
        }
        return ResponseEntity.ok(this.mqsAnalyzeService.getcsDiseaseTop5(year,LoginContext.getHospitalId()));
    }

    /**
     * 慢特病分析 - 慢特病明细list
     *
     * @return
     */
    @PostMapping("/csDisease/getListByPage")
    public ResponseEntity<List<CsDiseaseDto>> getcsgetListByPage(@RequestBody CsDiseaseQueryVo queryVo) {
        Page mypage = PageUtil.getPage(queryVo.getPage(), queryVo.getPageSize());
        queryVo.setHospitalId(LoginContext.getHospitalId());
        List<CsDiseaseDto> maps = this.mqsAnalyzeService.getcsgetListByPage(queryVo, mypage);
        return new ResponseEntity(maps, PageUtil.getTotalHeader(mypage), HttpStatus.OK);
    }

    /**
     * 查询费用明细 - 全量查询费用明细及其单据信息
     *
     * @return
     */
    @PostMapping("/analyze/queryDetail")
    public ResponseEntity<List<DetailDto>> analyzeQueryDetail(@RequestBody DetailQueryVo queryVo) {
        Page mypage = PageUtil.getPage(queryVo.getPage(), queryVo.getPageSize());
        return new ResponseEntity(this.mqsAnalyzeService.analyzeQueryDetail(queryVo, mypage), PageUtil.getTotalHeader(mypage), HttpStatus.OK);
    }


    /**
     * 业务分析 - 导出excel
     *
     * @param itemDateFrom 开始日期
     * @param itemDateTo   结束日期
     * @param dept         科室名称
     * @param type         业务 - 门诊:opPt 住院:hpSettle
     * @param order        维度筛选 - 科室:dept 医生:doc
     */
    @GetMapping("/bus/export")
    public void busExport(HttpServletResponse response,
                          @RequestParam(value = "itemDateFrom") String itemDateFrom,
                          @RequestParam(value = "itemDateTo") String itemDateTo,
                          @RequestParam(value = "dept", required = false) String dept,
                          @RequestParam(value = "type") String type,
                          @RequestParam(value = "order") String order) {
        mqsAnalyzeService.busExport(response, itemDateFrom, itemDateTo, dept, type, order, LoginContext.getHospitalId());
    }


    /**
     * 违规分析 - 导出excel
     *
     * @param queryVo  查询条件
     * @param response 响应
     */
    @PostMapping("/violation/export")
    public void violationExcel(HttpServletResponse response, @RequestBody ViolationQueryVo queryVo) {
        mqsAnalyzeService.violationExcel(response, queryVo);
    }

    /**
     * 慢特病分析 - 导出excel
     *
     * @param response 响应
     * @param queryVo  查询条件
     */
    @PostMapping("/csDisease/export")
    public void csDiseaseExport(HttpServletResponse response, @RequestBody CsDiseaseQueryVo queryVo) {
        queryVo.setHospitalId(LoginContext.getHospitalId());
        mqsAnalyzeService.csDiseaseExport(response, queryVo);
    }

    /**
     * 明细查询 - 导出excel
     *
     * @param response 响应
     * @param queryVo  查询条件
     */
    @PostMapping("/analyze/export")
    public void analyzeExport(HttpServletResponse response, @RequestBody DetailQueryVo queryVo) {
        mqsAnalyzeService.analyzeExport(response, queryVo);
    }

}

