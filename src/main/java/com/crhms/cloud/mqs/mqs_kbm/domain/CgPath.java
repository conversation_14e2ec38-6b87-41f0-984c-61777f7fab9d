package com.crhms.cloud.mqs.mqs_kbm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 临床指南-临床路径实体类
 *
 * @author: wangxingang
 * @date: 2023.02.08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("mqs_kbm_cg_path")
public class CgPath {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 适用科室
     */
    @TableField
    private String deptName;

    /**
     * 文件名称
     */
    @TableField
    private String fileName;

    /**
     * 文件id
     */
    @TableField
    private String oid;

    /**
     * 发布时间:选择年份
     */
    @TableField
    private Integer releaseTime;

    /**
     * 来源
     */
    @TableField
    private String dataSource;
}
