<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet author="zhaoyac (generated)" id="20240220-1">
        <validCheckSum>1:any</validCheckSum>
        <createTable tableName="mqs_hp_diag" remarks="审核-诊断明细表(包含门诊)">
            <column autoIncrement="true" name="id" type="BIGINT" remarks="id">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="diagnosis_no" type="VARCHAR(255)" remarks="诊断号：诊断唯一id">
                <constraints nullable="false" unique="true" />
            </column>
            <column name="no" type="VARCHAR(100)" remarks="单据号">
                <constraints nullable="false" />
            </column>
            <column name="admission_no" type="VARCHAR(100)" remarks="就诊流水号">
                <constraints nullable="false" />
            </column>
            <column name="patient_id" type="VARCHAR(255)" remarks="患者编码" />
            <column name="patient_name" type="VARCHAR(255)" remarks="患者姓名" />
            <column name="diagnosis_code" type="VARCHAR(255)" remarks="诊断编码">
                <constraints nullable="false" />
            </column>
            <column name="diagnosis_name" type="VARCHAR(255)" remarks="诊断名称">
                <constraints nullable="false" />
            </column>
            <column name="diag_dept_code" type="VARCHAR(255)" remarks="诊断科室编码" />
            <column name="diag_dept_name" type="VARCHAR(255)" remarks="诊断科室名称" />
            <column name="main_flag" type="VARCHAR(3)" remarks="主诊断标志 1.是 0.否">
                <constraints nullable="false" />
            </column>
            <column name="diag_inout_type" type="VARCHAR(10)" remarks="出入院诊断类型 1.入院 2.出院">
                <constraints nullable="false" />
            </column>
            <column name="diag_type" type="VARCHAR(10)" remarks="诊断类别" />
            <column name="diagnosis_order" type="VARCHAR(10)" remarks="诊断排序号" />
            <column name="diagnosis_time" type="datetime" remarks="诊断时间" />
            <column name="diagnosis_situation" type="VARCHAR(255)" remarks="入院病情" />
            <column name="last_updated_by" type="BIGINT" remarks="更新人" />
            <column name="last_updated_date" type="datetime" remarks="更新时间" />
            <column name="created_by" type="BIGINT" remarks="创建人" />
            <column name="created_date" type="datetime" remarks="创建时间" />
            <column name="hospital_id" type="VARCHAR(10)" remarks="医院id">
                <constraints nullable="false" />
            </column>
            <column name="batch_no" type="VARCHAR(100)" remarks="批次号" />
        </createTable>

        <!-- 添加索引 -->
        <createIndex indexName="mqs_hp_diagU1" tableName="mqs_hp_diag">
            <column name="admission_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_diagU2" tableName="mqs_hp_diag" >
            <column name="no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_diagU3" tableName="mqs_hp_diag" >
            <column name="diagnosis_code"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhaoyac (generated)" id="20240220-2">
        <validCheckSum>1:any</validCheckSum>
        <createTable tableName="mqs_hp_diag_his" remarks="审核-诊断明细表(包含门诊)">
            <column autoIncrement="true" name="id" type="BIGINT" remarks="id">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="diagnosis_no" type="VARCHAR(255)" remarks="诊断号：诊断唯一id">
                <constraints nullable="false" />
            </column>
            <column name="no" type="VARCHAR(100)" remarks="单据号">
                <constraints nullable="false"/>
            </column>
            <column name="admission_no" type="VARCHAR(100)" remarks="就诊流水号">
                <constraints nullable="false"/>
            </column>
            <column name="patient_id" type="VARCHAR(255)" remarks="患者编码"/>
            <column name="patient_name" type="VARCHAR(255)" remarks="患者姓名"/>
            <column name="diagnosis_code" type="VARCHAR(255)" remarks="诊断编码">
                <constraints nullable="false"/>
            </column>
            <column name="diagnosis_name" type="VARCHAR(255)" remarks="诊断名称">
                <constraints nullable="false"/>
            </column>
            <column name="diag_dept_code" type="VARCHAR(255)" remarks="诊断科室编码"/>
            <column name="diag_dept_name" type="VARCHAR(255)" remarks="诊断科室名称"/>
            <column name="main_flag" type="VARCHAR(3)" remarks="主诊断标志 1.是 0.否">
                <constraints nullable="false"/>
            </column>
            <column name="diag_inout_type" type="VARCHAR(10)" remarks="出入院诊断类型 1.入院 2.出院">
                <constraints nullable="false"/>
            </column>
            <column name="diag_type" type="VARCHAR(10)" remarks="诊断类别"/>
            <column name="diagnosis_order" type="VARCHAR(10)" remarks="诊断排序号"/>
            <column name="diagnosis_time" type="DATETIME" remarks="诊断时间"/>
            <column name="diagnosis_situation" type="VARCHAR(255)" remarks="入院病情"/>
            <column name="last_updated_by" type="BIGINT" remarks="更新人"/>
            <column name="last_updated_date" type="DATETIME" remarks="更新时间"/>
            <column name="created_by" type="BIGINT" remarks="创建人"/>
            <column name="created_date" type="DATETIME" remarks="创建时间"/>
            <column name="hospital_id" type="VARCHAR(10)" remarks="医院id">
                <constraints nullable="false"/>
            </column>
            <column name="batch_no" type="VARCHAR(100)" remarks="批次号"/>
        </createTable>

        <!-- 添加索引 -->
        <createIndex indexName="mqs_hp_diag_hisU1" tableName="mqs_hp_diag_his">
            <column name="admission_no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_diag_hisU2" tableName="mqs_hp_diag_his">
            <column name="no"/>
        </createIndex>
        <createIndex indexName="mqs_hp_diag_hisU3" tableName="mqs_hp_diag_his">
            <column name="diagnosis_code"/>
        </createIndex>
    </changeSet>

    <!-- 违规基础上增加违规计费标记 -->
    <changeSet author="zhaoyac (generated)" id="20240227-1">
        <validCheckSum>1:any</validCheckSum>
        <sql>
            ALTER TABLE `mqs_hp_rg_audit`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_bk_audit`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_do_audit`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_tf_audit`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_out_audit`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_pred_audit`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_settle_audit`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_rg_audit`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_pt_audit`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_pct_audit`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_rg_audit_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_bk_audit_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_do_audit_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_tf_audit_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_out_audit_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_pred_audit_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_settle_audit_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_rg_audit_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_pt_audit_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_pct_audit_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_rg_detail`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_bk_detail`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_do_detail`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_tf_detail`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_out_detail`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_pred_detail`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_settle_detail`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_rg_detail`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_pt_detail`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_pct_detail`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_rg_detail_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_bk_detail_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_do_detail_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_tf_detail_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_out_detail_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_pred_detail_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_hp_settle_detail_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_rg_detail_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_pt_detail_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;

            ALTER TABLE `mqs_op_pct_detail_his`
            ADD COLUMN `violation_type` varchar(10) NULL COMMENT '违规计费标记，0未违规 1违规' ;
        </sql>
    </changeSet>


    <!-- 去掉诊断编码必输配置 -->
    <changeSet author="zhaoyc (generated)" id="20240321-1">
        <validCheckSum>1:any</validCheckSum>
        <sql>
            ALTER TABLE `mqs_hp_diag`
            MODIFY COLUMN `diagnosis_code` varchar(255) NULL COMMENT '诊断编码' ,
            MODIFY COLUMN `diagnosis_name` varchar(255) NULL COMMENT '诊断名称' ;
            ALTER TABLE `mqs_hp_diag_his`
            MODIFY COLUMN `diagnosis_code` varchar(255) NULL COMMENT '诊断编码' ,
            MODIFY COLUMN `diagnosis_name` varchar(255) NULL COMMENT '诊断名称' ;
        </sql>
    </changeSet>


    <!-- 增加院内项目编码和名称 -->
    <changeSet id="20241010-1" author="zhaoyc (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_hp_rg_detail">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_rg_detail_his">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail_his">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail_his">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail_his">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail_his">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail_his">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail_his">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail_his">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail_his">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail_his">
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码">
                <constraints nullable="true"/>
            </column>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241011-1">
        <validCheckSum>1:any</validCheckSum>
        <sql>
            INSERT INTO `mqs_sys_config`(`id`, `sys_key`, `sys_value`, `remake`, `enable`, `last_updated_by`, `last_updated_date`, `created_by`, `created_date`, `hospital_id`) VALUES (19, 'MQS_SYS_PERSONNEL_BENEFIT_TYPE', '{"personnel":[],"benefit":[]}', '不审核单据过滤：人员类别+险种类别', '1', 1, '2024-10-11 17:36:27', 1, '2024-10-11 17:36:31', '1');
        </sql>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-1">
        <addColumn tableName="mqs_op_pct">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-2">
        <addColumn tableName="mqs_op_pct_his">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-3">
        <addColumn tableName="mqs_op_pt">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-4">
        <addColumn tableName="mqs_op_pt_his">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-5">
        <addColumn tableName="mqs_op_rg">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-6">
        <addColumn tableName="mqs_op_rg_his">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-7">
        <addColumn tableName="mqs_hp_bk">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-8">
        <addColumn tableName="mqs_hp_bk_his">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-9">
        <addColumn tableName="mqs_hp_do">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-10">
        <addColumn tableName="mqs_hp_do_his">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-11">
        <addColumn tableName="mqs_hp_out">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-12">
        <addColumn tableName="mqs_hp_out_his">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-13">
        <addColumn tableName="mqs_hp_pred">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-14">
        <addColumn tableName="mqs_hp_pred_his">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-15">
        <addColumn tableName="mqs_hp_rg">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-16">
        <addColumn tableName="mqs_hp_rg_his">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-17">
        <addColumn tableName="mqs_hp_settle">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-18">
        <addColumn tableName="mqs_hp_settle_his">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-19">
        <addColumn tableName="mqs_hp_tf">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241024-20">
        <addColumn tableName="mqs_hp_tf_his">
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241030-1" author="zhaoyc (generated)">
        <validCheckSum>1:any</validCheckSum>
        <sql>
            ALTER TABLE `mqs_hp_rg`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_bk`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_do`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_tf`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_out`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_pred`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_settle`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_op_rg`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_op_pt`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_op_pct`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;

            ALTER TABLE `mqs_hp_rg_his`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_bk_his`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_do_his`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_tf_his`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_out_his`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_pred_his`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_hp_settle_his`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_op_rg_his`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_op_pt_his`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
            ALTER TABLE `mqs_op_pct_his`
                ADD COLUMN `doc_group_code` varchar(100) NULL COMMENT '医师小组编码',
                ADD COLUMN `doc_group_name` varchar(100) NULL COMMENT '医师小组名称' ,
                ADD COLUMN `hosp_area_code` varchar(100) NULL COMMENT '院区编码' ,
                ADD COLUMN `hosp_area_name` varchar(100) NULL COMMENT '院区名称' ,
                ADD COLUMN `units_dept_code` varchar(100) NULL COMMENT '专科科室编码',
                ADD COLUMN `units_dept_name` varchar(100) NULL COMMENT '专科科室名称' ;
        </sql>
    </changeSet>
</databaseChangeLog>