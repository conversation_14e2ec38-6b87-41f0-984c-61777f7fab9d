package com.crhms.cloud.mqs.mqs_mr.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.crhms.cloud.mqs.basic.domain.BaseAudit;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalStats;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlan;
import java.util.List;
import java.util.Map;

/*
*  规则级别算法实现 (最后一个算法节点)
* */
public class RuleLevel extends MrPlanFilter{

    @Override
    public boolean filter(BaseMedicalCase medicalCase, MrPlan mrPlan, List<BaseAudit> baseAudits, String filterFlag, List<String> sceneCodes, String auditScenario, Map<String,List<BaseMedicalStats>> baseMedicalStatsMap) {
        //如果规则级别为空则不进行过滤并且结束流程
        String ruleLevel = mrPlan.getRuleLevel1();
        if(ruleLevel == null || "".equals(ruleLevel)){
            return true;
        }
        //转list
        List<String> mrRuleLevels = StrUtil.split(ruleLevel, ",");
        //处理未违规情况
        if(mrRuleLevels.contains("0") && CollectionUtil.isEmpty(baseAudits) || baseAudits.stream().filter(x -> mrRuleLevels.contains(x.getRuleType())).findFirst().isPresent()){
            return true;
        } else {
            return false;
        }
    }
}
