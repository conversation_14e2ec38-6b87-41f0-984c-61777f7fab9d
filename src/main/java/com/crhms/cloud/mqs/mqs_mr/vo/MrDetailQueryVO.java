package com.crhms.cloud.mqs.mqs_mr.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;


/**
 * 门诊审核-门诊处方审核(OpPct)实体类
 *
 * <AUTHOR>
 * @since 2022-12-19 09:40:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class MrDetailQueryVO {
    // no单据号 必输
    private String no;

    //查询条件 是否违规
    private String violationFlag;

    //违规来源（0-医保，1-院内，2-人工） 审核渠道
    private String auditSource;

    //查询条件 规则级别
    private List<String> ruleTypes;

    //查询条件 是否自费
    private String selfExpense;

    //查询条件 项目名称 模糊
    private String itemName;

    //查询条件 项目类型
    private String itemTypeCode;

    private int page;
    private int pageSize;

    //甲乙类
    private String categoryName;

    //是否出院带药（1-是，0-否）
    private String outpatientMedication;

    //项目id集合
    private List<String> itemIds;

    private List<String> detailNos;

    private String relatedRecords;
}

