package com.crhms.cloud.mqs.mqs_ais.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.crhms.cloud.mqs.sys.dto.FundAloneCodesVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 运营监控-审核监控 mapper接口
 *
 * @author: 王新刚
 * @date: 2023/03/14
 */
@Mapper
public interface MqsBmiMonitorMapper {

    /**
     * 查询今日医保结算总金额
     * @param now
     * @param scenarioList
     * @param hospitalId
     * @return
     */
    BigDecimal selectBmiAmount(@Param("now") String now, @Param("scenarioList") List<String> scenarioList, @Param("hospitalId") String hospitalId);

    /**
     * 查询今日审核次数
     * @param now
     * @param scenarioList
     * @param hospitalId
     * @return
     */
    BigDecimal selectBmiAuditTimes(@Param("now") String now, @Param("scenarioList") List<String> scenarioList, @Param("hospitalId") String hospitalId);

    /**
     * 查询费用结构情况
     * @param startTime
     * @param endTime
     * @param scenarioList
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    List<Map<String,Object>> selectBmiAmountByItemType(String startTime, String endTime, List<String> scenarioList, String hospitalId);

    /**
     * 查询时间范围内各部门已使用金额
     * @param startTime
     * @param endTime
     * @param scenarioList
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    List<Map<String, Object>> deptFundsBmiAmount(@Param("startTime") String startTime,
                                                 @Param("endTime") String endTime,
                                                 @Param("scenarioList") List<String> scenarioList,
                                                 @Param("fundAloneCodesVO") FundAloneCodesVO fundAloneCodesVO,
                                                 @Param("hospitalId") String hospitalId);

    /**
     * 查询基金金额 （万元)
     * @param filterYear
     * @param auditScenario
     * @param hospitalId
     * @return
     */
    List<Map<String, Object>> selectDeptFuns(@Param("filterYear") String filterYear, @Param("auditScenario") String auditScenario, @Param("hospitalId") String hospitalId);

    /**
     * 查询消耗基金最多的科室
     * @param startTime
     * @param endTime
     * @param scenarioList
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    Map<String,Object> getTopDeptBmiAmount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("scenarioList") List<String> scenarioList, @Param("hospitalId") String hospitalId);

    /**
     * 查询消耗基金最多的项目
     * @param startTime
     * @param endTime
     * @param scenarioList
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    Map<String,Object> getTopItemBmiAmount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("scenarioList") List<String> scenarioList, @Param("hospitalId") String hospitalId);

    /**
     * 查询慢特病患者 均消耗基金
     * @param startTime
     * @param endTime
     * @param cdsList
     * @param scenarioList
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    Map<String,Object> getCdsBmiAmount(String startTime, String endTime, List<String> cdsList, List<String> scenarioList, String hospitalId);

    /**
     *查询特殊病患者 均消耗基金
     * @param startTime
     * @param endTime
     * @param ciList
     * @param scenarioList
     * @param hospitalId
     */
    @DS("clickhouse")
    Map<String,Object> getCiBmiAmount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("ciList") List<String> ciList, @Param("scenarioList") List<String> scenarioList, @Param("hospitalId") String hospitalId);

    /**
     * 查询月度金额
     * @param startTime
     * @param endTime
     * @param scenarioList  统计的场景(目前支持opPt 和 hpSettle)
     * @param fundAloneCodesVO 排除单独管理的支出基金
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    List<Map<String, Object>> getUsedBmiAmountByMonth(@Param("startTime") String startTime,
                                                      @Param("endTime") String endTime,
                                                      @Param("scenarioList") List<String> scenarioList,
                                                      @Param("fundAloneCodesVO") FundAloneCodesVO fundAloneCodesVO,
                                                      @Param("hospitalId") String hospitalId);
}
