package com.crhms.cloud.mqs.mqs_ais.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.stream.CollectorUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.mqs_ais.mapper.MqsAuditMonitorMapper;
import com.crhms.cloud.mqs.mqs_ais.mapper.MqsBmiMonitorMapper;
import com.crhms.cloud.mqs.sys.domain.SysFunds;
import com.crhms.cloud.mqs.sys.domain.SysFundsAlone;
import com.crhms.cloud.mqs.sys.domain.SysFundsDept;
import com.crhms.cloud.mqs.sys.dto.FundAloneCodesVO;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.mapper.SysFundsMapper;
import com.crhms.cloud.mqs.sys.service.SysFundsAloneService;
import com.crhms.cloud.mqs.sys.service.SysFundsDeptService;
import com.crhms.cloud.mqs.sys.service.SysFundsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: zhaoyachao
 * @date: 2023/03/17
 */
@Service
public class MqsBmiMonitorService {

    @Autowired
    private MqsBmiMonitorMapper monitorMapper;
    @Autowired
    private SysFundsMapper sysFundsMapper;
    @Autowired
    private SysFundsService sysFundsService;

    // 统计单位 "万"
    private static BigDecimal tenThousand = new BigDecimal(10000l);

    /**
     * 今日实时
     *
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * <AUTHOR>
     * @date 2023/03/17
     */
    public Map<String, Object> realTime(String auditScenario, String hospitalId) {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String now = formatter.format(currentTime);
        Map<String, Object> result = new HashMap<>();
        //统计场景范围
        List<String> scenarioList = new ArrayList<>();
        switch (auditScenario) {
            case "all":
                scenarioList.add(AuditScenarioEnum.hpSettle.getTableName());
                scenarioList.add(AuditScenarioEnum.opPt.getTableName());
                break;
            case "op":
                scenarioList.add(AuditScenarioEnum.opPt.getTableName());
                break;
            case "hp":
                scenarioList.add(AuditScenarioEnum.hpSettle.getTableName());
                break;
        }

        BigDecimal bmiTotalAmount = monitorMapper.selectBmiAmount(now, scenarioList, hospitalId);
        BigDecimal auditTimes = monitorMapper.selectBmiAuditTimes(now, scenarioList, hospitalId);


        result.put("auditTimes", auditTimes);
        result.put("bmiTotalAmount", bmiTotalAmount);

        return result;
    }

    /**
     * 年度使用情况
     *
     * @param filterYear    年份
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * <AUTHOR>
     * @date 2023/03/15
     */
    public Map<String, Object> annualUsage(String filterYear, String auditScenario, String hospitalId) {
        //统计场景范围
        List<String> scenarioList = new ArrayList<>();
        switch (auditScenario) {
            case "all":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                break;
            case "op":
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                break;
            case "hp":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                break;
        }
        Map<String, String> scenarioMap = new HashMap<>();
        scenarioMap.put(AuditScenarioEnum.opPt.getAuditScenario(), "1");
        scenarioMap.put(AuditScenarioEnum.hpSettle.getAuditScenario(), "2");

        //查询慢特病数据
        List<Map> csDiseases = sysFundsMapper.queryCsDisease(hospitalId);
        //查询大病数据
        List<Map> siDieasesMap = sysFundsMapper.querySiDisease(hospitalId);
        //查询靶向药数据
        List<Map> tdDieasesMap = sysFundsMapper.queryTdDisease(hospitalId);

        List<String> cdsCiDieasess = csDiseases.stream().map(x -> x.get("cs_disease_code").toString()).collect(Collectors.toList());
        List<String> cdsDieasess = csDiseases.stream().filter(x -> "1".equals(x.get("type"))).map(x -> x.get("cs_disease_code").toString()).collect(Collectors.toList());
        List<String> ciDieasess = csDiseases.stream().filter(x -> "2".equals(x.get("type"))).map(x -> x.get("cs_disease_code").toString()).collect(Collectors.toList());
        List<String> siDieasess = siDieasesMap.stream().map(x -> x.get("code").toString()).collect(Collectors.toList());
        List<String> tdDieasess = tdDieasesMap.stream().map(x -> x.get("item_id").toString()).collect(Collectors.toList());


        Map<String, Object> resultMap = new HashMap<>();

        QueryWrapper<SysFunds> wrapper = new QueryWrapper<SysFunds>().eq(SysFunds.FIELD_HOSPITAL_ID, hospitalId).eq(SysFunds.FIELD_YEARS, filterYear);
        switch (auditScenario) {
            case "op":
                wrapper.eq(SysFunds.FIELD_VISIT_TYPE, "1");
                break;
            case "hp":
                wrapper.eq(SysFunds.FIELD_VISIT_TYPE, "2");
                break;
        }
        //基金配置
        List<SysFunds> fundsList = sysFundsMapper.selectList(wrapper);
        List<SysFundsAlone> sysFundsAlones = new ArrayList<>();
        if(CollUtil.isNotEmpty(fundsList)){
            //查询单独管理额度
            List<Long> fundIds = fundsList.stream().map(SysFunds::getId).collect(Collectors.toList());
            sysFundsAlones = sysFundsMapper.queryAloneAmount(fundIds, hospitalId);
        }


        //按统计需求分组后 统计金额
        List<Map> yearUsedMap = sysFundsMapper.queryUsedAmountAll(scenarioList, filterYear, hospitalId);

        BigDecimal usedAmount = BigDecimal.ZERO;
        BigDecimal opUsedAmount = BigDecimal.ZERO;
        BigDecimal hpUsedAmount = BigDecimal.ZERO;
        BigDecimal CdsCiUsedAmount = BigDecimal.ZERO;
        BigDecimal CdsUsedAmount = BigDecimal.ZERO;
        BigDecimal CiUsedAmount = BigDecimal.ZERO;
        BigDecimal SdUsedAmount = BigDecimal.ZERO;
        BigDecimal TdUsedAmount = BigDecimal.ZERO;
        BigDecimal SiUsedAmount = BigDecimal.ZERO;

        //遍历结果 累计金额汇总
        for (Map map : yearUsedMap) {
            BigDecimal amount = MqsUtils.getBigDecimal(map.get("total"));
            boolean flag = true;
            String VISIT_TYPE = scenarioMap.get(map.get("audit_scenario"));
            List<String> aloneCodes = sysFundsAlones.stream().filter(x -> VISIT_TYPE.equals(x.getVisitType())).map(SysFundsAlone::getAloneCode).collect(Collectors.toList());
            if(cdsCiDieasess.contains(map.get("cs_disease_code"))){
                CdsCiUsedAmount = CdsCiUsedAmount.add(amount);
                if (aloneCodes.contains("CdsCi")) {
                    flag = false;
                }
            }

            if (cdsDieasess.contains(map.get("cs_disease_code"))) {
                CdsUsedAmount = CdsUsedAmount.add(amount);
                if (aloneCodes.contains("Cds")) {
                    flag = false;
                }
            }
            if (ciDieasess.contains(map.get("cs_disease_code"))) {
                CiUsedAmount = CiUsedAmount.add(amount);
                if (aloneCodes.contains("Ci")) {
                    flag = false;
                }
            }
            if (siDieasess.contains(map.get("out_diagnosis_code"))) {
                SiUsedAmount = SiUsedAmount.add(amount);
                if (aloneCodes.contains("Si")) {
                    flag = false;
                }
            }
            if (StrUtil.isNotEmpty((String)map.get("single_disease_code")) && !"0".equals(map.get("single_disease_code"))) {
                SdUsedAmount = SdUsedAmount.add(amount);
                if (aloneCodes.contains("Sd")) {
                    flag = false;
                }
            }
            //需要单独管理的基金不计入总支出
            if (flag) {
                usedAmount = usedAmount.add(amount);
                if (AuditScenarioEnum.hpSettle.getAuditScenario().equals(map.get("audit_scenario"))) {
                    hpUsedAmount = hpUsedAmount.add(amount);
                }
                if (AuditScenarioEnum.opPt.getAuditScenario().equals(map.get("audit_scenario"))) {
                    opUsedAmount = opUsedAmount.add(amount);
                }
            }
        }


        if(CollectionUtil.isNotEmpty(tdDieasess)){
            List<Map> tdDieasessMap = sysFundsMapper.queryUsedAmountAlltdDieasess(scenarioList, filterYear, hospitalId,tdDieasess);

            for (Map map : tdDieasessMap) {
                BigDecimal amount = MqsUtils.getBigDecimal(map.get("total"));
                TdUsedAmount = TdUsedAmount.add(amount);
            }
        }


        CdsCiUsedAmount = CdsCiUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal CdsCiTotalAmount = sysFundsAlones.stream().filter(x -> "CdsCi".equals(x.getAloneCode())).map(SysFundsAlone::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal CdsCiUsedRatio = BigDecimal.ZERO.compareTo(CdsCiTotalAmount) == 0 ? BigDecimal.ZERO : CdsCiUsedAmount.divide(CdsCiTotalAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l));

        //慢病累计
        CdsUsedAmount = CdsUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal CdsTotalAmount = sysFundsAlones.stream().filter(x -> "Cds".equals(x.getAloneCode())).map(SysFundsAlone::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal CdsUsedRatio = BigDecimal.ZERO.compareTo(CdsTotalAmount) == 0 ? BigDecimal.ZERO : CdsUsedAmount.divide(CdsTotalAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l));

        //特病累计
        CiUsedAmount = CiUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal CiTotalAmount = sysFundsAlones.stream().filter(x -> "Ci".equals(x.getAloneCode())).map(SysFundsAlone::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal CiUsedRatio = BigDecimal.ZERO.compareTo(CiTotalAmount) == 0 ? BigDecimal.ZERO : CiUsedAmount.divide(CiTotalAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l));

        //单病种
        SdUsedAmount = SdUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal SdTotalAmount = sysFundsAlones.stream().filter(x -> "Sd".equals(x.getAloneCode())).map(SysFundsAlone::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal SdUsedRatio = BigDecimal.ZERO.compareTo(SdTotalAmount) == 0 ? BigDecimal.ZERO : SdUsedAmount.divide(SdTotalAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l));

        //靶向药
        TdUsedAmount = TdUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal TdTotalAmount = sysFundsAlones.stream().filter(x -> "Td".equals(x.getAloneCode())).map(SysFundsAlone::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal TdUsedRatio = BigDecimal.ZERO.compareTo(TdTotalAmount) == 0 ? BigDecimal.ZERO : TdUsedAmount.divide(TdTotalAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l));

        //大病
        SiUsedAmount = SiUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal SiTotalAmount = sysFundsAlones.stream().filter(x -> "Si".equals(x.getAloneCode())).map(SysFundsAlone::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal SiUsedRatio = BigDecimal.ZERO.compareTo(SiTotalAmount) == 0 ? BigDecimal.ZERO : SiUsedAmount.divide(SiTotalAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l));

        //查询门诊/住院基金/全额基金
        List<Map> totalFundMap = sysFundsMapper.queryFundsByYear(filterYear, hospitalId);
        //年度基金和
        BigDecimal totalAmount = BigDecimal.ONE;
        //年度金额

        // 门诊进度
        BigDecimal opTotalAmount = BigDecimal.ZERO;
        BigDecimal opRatio = BigDecimal.ZERO;
        Optional<Map> opFirst = totalFundMap.stream().filter(x -> "1".equals(x.get("visit_type"))).findFirst();
        opUsedAmount = opUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        if (opFirst.isPresent()) {
            opTotalAmount = MqsUtils.getBigDecimal(opFirst.get().get("years_amount")).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
            opRatio = BigDecimal.ZERO.compareTo(opTotalAmount) == 0 ? BigDecimal.ZERO : opUsedAmount.divide(opTotalAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l));
        }
        // 住院进度
        BigDecimal hpTotalAmount = BigDecimal.ZERO;
        BigDecimal hpRatio = BigDecimal.ZERO;
        Optional<Map> hpFirst = totalFundMap.stream().filter(x -> "2".equals(x.get("visit_type"))).findFirst();
        hpUsedAmount = hpUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        if (hpFirst.isPresent()) {
            hpTotalAmount = MqsUtils.getBigDecimal(hpFirst.get().get("years_amount")).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
            hpRatio = BigDecimal.ZERO.compareTo(hpTotalAmount) == 0 ? BigDecimal.ZERO : hpUsedAmount.divide(hpTotalAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l));
        }
        totalAmount = totalFundMap.stream().map(x -> MqsUtils.getBigDecimal(x.get("years_amount"))).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        // 累计支出
        usedAmount = usedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        // 总进度
        BigDecimal usedRatio = BigDecimal.ZERO.compareTo(totalAmount) == 0 ? BigDecimal.ZERO : usedAmount.divide(totalAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l));

        resultMap.put("usedAmount", usedAmount);
        resultMap.put("totalAmount", totalAmount);
        resultMap.put("usedRatio", usedRatio.toPlainString());
        resultMap.put("opUsedAmount", opUsedAmount);
        resultMap.put("opTotalAmount", opTotalAmount);
        resultMap.put("opRatio", opRatio.toPlainString());
        resultMap.put("hpUsedAmount", hpUsedAmount);
        resultMap.put("hpTotalAmount", hpTotalAmount);
        resultMap.put("hpRatio", hpRatio.toPlainString());
        resultMap.put("CdsCiUsedAmount", CdsCiUsedAmount);
        resultMap.put("CdsCiTotalAmount", CdsCiTotalAmount);
        resultMap.put("CdsCiUsedRatio", CdsCiUsedRatio.toPlainString());
        resultMap.put("CdsUsedAmount", CdsUsedAmount);
        resultMap.put("CdsTotalAmount", CdsTotalAmount);
        resultMap.put("CdsUsedRatio", CdsUsedRatio.toPlainString());
        resultMap.put("CiUsedAmount", CiUsedAmount);
        resultMap.put("CiTotalAmount", CiTotalAmount);
        resultMap.put("CiUsedRatio", CiUsedRatio.toPlainString());
        resultMap.put("SdUsedAmount", SdUsedAmount);
        resultMap.put("SdTotalAmount", SdTotalAmount);
        resultMap.put("SdUsedRatio", SdUsedRatio.toPlainString());
        resultMap.put("TdUsedAmount", TdUsedAmount);
        resultMap.put("TdTotalAmount", TdTotalAmount);
        resultMap.put("TdUsedRatio", TdUsedRatio.toPlainString());
        resultMap.put("SiUsedAmount", SiUsedAmount);
        resultMap.put("SiTotalAmount", SiTotalAmount);
        resultMap.put("SiUsedRatio", SiUsedRatio.toPlainString());
        return resultMap;
    }

    /**
     * 查询医保结算金额费用情况
     *
     * @param filterYear
     * @param filterMonth
     * @param filterDay
     * @param auditScenario
     * @return
     */
    public List<Map<String, Object>> costStructure(String filterYear, String filterMonth, String filterDay, String auditScenario, String hospitalId) {
        //统计场景范围
        List<String> scenarioList = new ArrayList<>();
        switch (auditScenario) {
            case "all":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                break;
            case "op":
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                break;
            case "hp":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                break;
        }
        Map<String, String> timeMap = MqsUtils.buildDate(filterYear, filterMonth, filterDay);
        String startTime = timeMap.getOrDefault("startTime", null);
        String endTime = timeMap.getOrDefault("endTime", null);

        List<Map<String, Object>> dataList = monitorMapper.selectBmiAmountByItemType(startTime, endTime, scenarioList, hospitalId);
        BigDecimal total = dataList.stream().map(m -> MqsUtils.getBigDecimal(m.get("bmiAmount"))).reduce(BigDecimal.ZERO, BigDecimal::add);
        for (Map map : dataList) {
            map.put("bmiRatio", BigDecimal.ZERO.compareTo(total) == 0  ? BigDecimal.ZERO : MqsUtils.getBigDecimal(map.get("bmiAmount")).divide(total, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        }
        return dataList;
    }

    public List<Map<String, Object>> deptFundsBmiAmount(String filterYear, String filterMonth, String filterDay, String auditScenario, String hospitalId) {
        //统计场景范围
        List<String> scenarioList = new ArrayList<>();
        switch (auditScenario) {
            case "all":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                break;
            case "op":
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                break;
            case "hp":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                break;
        }
        Map<String, String> timeMap = MqsUtils.buildDate(filterYear, filterMonth, filterDay);
        String startTime = timeMap.getOrDefault("startTime", null);
        String endTime = timeMap.getOrDefault("endTime", null);

        //查询范围内部门已使用金额
        //查询单独管理
        FundAloneCodesVO fundAloneCodesVO = sysFundsService.genIgnore(filterYear, hospitalId);
        List<Map<String, Object>> datas = monitorMapper.deptFundsBmiAmount(startTime, endTime, scenarioList, fundAloneCodesVO, hospitalId);
        //查询部门时间范围内登记的基金
        List<Map<String, Object>> depDatas = monitorMapper.selectDeptFuns(filterYear, auditScenario, hospitalId);

        for (Map<String, Object> depData : datas) {
            List<Map<String, Object>> collect = depDatas.stream().filter(s -> s.get("deptCode").equals(depData.get("deptCode"))).collect(Collectors.toList());
            BigDecimal fundsAmount = BigDecimal.ZERO;
            BigDecimal fundsWarnAmount = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(collect)) {

                if (StringUtils.isNoneEmpty(filterMonth)) {
                    //月份基金统计
                    fundsAmount = collect.stream().map(x -> MqsUtils.getBigDecimal(x.get(filterMonth))).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
                    fundsWarnAmount = collect.stream().map(x -> MqsUtils.getBigDecimal(x.get("warn" + filterMonth))).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
                } else {
                    //年份基金统计
                    fundsAmount = collect.stream().map(x -> MqsUtils.getBigDecimal(x.get("years_amount"))).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
                    fundsWarnAmount = collect.stream().map(x -> MqsUtils.getBigDecimal(x.get("years_warn"))).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);

                }
            }
            BigDecimal bmiRatio = BigDecimal.ZERO.compareTo(fundsAmount) == 0 ? BigDecimal.ZERO : MqsUtils.getBigDecimal(depData.get("usedAmount")).divide(fundsAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l));
            depData.put("fundsAmount", fundsAmount);
            depData.put("fundsWarnAmount", fundsWarnAmount);
            BigDecimal warnRatio = BigDecimal.ZERO.compareTo(fundsAmount) == 0 ? BigDecimal.ZERO : fundsWarnAmount.divide(fundsAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l));
            depData.put("warnRatio", warnRatio);
            depData.put("bmiRatio", bmiRatio);
        }
        return datas;
    }

    public Map<String, Object> getMessageWarn(String filterYear, String filterMonth, String filterDay, String auditScenario, String hospitalId) {
        //统计场景范围
        List<String> scenarioList = new ArrayList<>();
        switch (auditScenario) {
            case "all":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                break;
            case "op":
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                break;
            case "hp":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                break;
        }
        Map<String, String> timeMap = MqsUtils.buildDate(filterYear, filterMonth, filterDay);
        String startTime = timeMap.getOrDefault("startTime", null);
        String endTime = timeMap.getOrDefault("endTime", null);
        //病种类型 1慢病 2特病
        List<Map> csDiseases = sysFundsMapper.queryCsDisease(hospitalId);
        List<String> cdsList = csDiseases.stream().filter(x -> "1".equals(x.get("type"))).map(x -> x.get("cs_disease_code").toString()).collect(Collectors.toList());
        List<String> ciList = csDiseases.stream().filter(x -> "2".equals(x.get("type"))).map(x -> x.get("cs_disease_code").toString()).collect(Collectors.toList());

        Map<String, Object> topDeptBmiAmount = monitorMapper.getTopDeptBmiAmount(startTime, endTime, scenarioList, hospitalId);
        Map<String, Object> topItemBmiAmount = monitorMapper.getTopItemBmiAmount(startTime, endTime, scenarioList, hospitalId);
        Map<String, Object> cdsBmiAmount = monitorMapper.getCdsBmiAmount(startTime, endTime, cdsList, scenarioList, hospitalId);
        Map<String, Object> ciBmiAmount = monitorMapper.getCiBmiAmount(startTime, endTime, ciList, scenarioList, hospitalId);
        Map<String, Object> resultMap = new HashMap<>();
        if (ObjectUtil.isNotNull(topDeptBmiAmount)) {
            resultMap.put("topDeptCode", topDeptBmiAmount.get("deptCode"));
            resultMap.put("topDeptName", topDeptBmiAmount.get("deptName"));
            resultMap.put("topDeptBmiAmount", topDeptBmiAmount.get("usedAmount"));
        }
        if (ObjectUtil.isNotNull(topItemBmiAmount)) {
            resultMap.put("topItemId", topItemBmiAmount.get("item_id"));
            resultMap.put("topItemName", topItemBmiAmount.get("item_name"));
            resultMap.put("topItemBmiAmount", topItemBmiAmount.get("usedAmount"));
        }
        if (ObjectUtil.isNotNull(cdsBmiAmount)) {
            resultMap.put("cdsPatients", cdsBmiAmount.get("patients"));
            resultMap.put("cdsAvgUsedAmount", cdsBmiAmount.get("avgUsedAmount"));
        }
        if (ObjectUtil.isNotNull(ciBmiAmount)) {
            resultMap.put("ciPatients", ciBmiAmount.get("patients"));
            resultMap.put("ciAvgUsedAmount", ciBmiAmount.get("avgUsedAmount"));
        }

        return resultMap;
    }

    /**
     * 支出金额 - 总额预付基金
     *
     * @param filterYear
     * @param auditScenario
     * @param hospitalId
     * @return
     */
    public List<Map<String, Object>> usedBmiAmountByMonth(String filterYear, String auditScenario, String hospitalId) {

        String filterMonth = "12";
        Date startTime = DateUtil.beginOfMonth(DateUtil.parseDate(filterYear + "-" + filterMonth + "-" + 1));
        Date endDate = DateUtil.endOfMonth(DateUtil.parseDate(filterYear + "-" + filterMonth + "-" + 1));
        Date startDate = DateUtil.offset(startTime, DateField.MONTH, -11);

        List<String> months = MqsUtils.buildMonths(filterYear, filterMonth, null);
        List<String> fundVisitType = new ArrayList<>();
        List<String> scenarioList = new ArrayList<>();
        switch (auditScenario) {
            case "all":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                fundVisitType.add("1");
                fundVisitType.add("2");
                break;
            case "op":
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                fundVisitType.add("1");
                break;
            case "hp":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                fundVisitType.add("2");
                break;
        }

        List<Map<String, Object>> results = new ArrayList<>();
        //查询单独管理
        FundAloneCodesVO fundAloneCodesVO = sysFundsService.genIgnore(filterYear, hospitalId);
        //查询基础统计的数据
        List<Map<String, Object>> datas = monitorMapper.getUsedBmiAmountByMonth(startDate.toString(), endDate.toString(), scenarioList, fundAloneCodesVO, hospitalId);
        //查询月度定额
        List<Map<String, Object>> amountByMonth = sysFundsService.getAmountByMonth(startDate, endDate, fundVisitType, hospitalId);

        for (String month : months) {
            Map<String, Object> result = new HashMap<>();
            Optional<Map<String, Object>> first = datas.stream().filter(x -> month.equals(x.get("whichMonth"))).findFirst();
            result.put("month", month);
            //补充月度金额
            if (first.isPresent()) {
                result.put("usedAmount", MqsUtils.getBigDecimal(first.get().get("totalAmount")).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP));
            } else {
                result.put("usedAmount", BigDecimal.ZERO);
            }
            //处理定额
            Optional<Map<String, Object>> first1 = amountByMonth.stream().filter(x -> month.split("-")[0].equals(x.get("years"))).findFirst();
            if (first1.isPresent()) {
                result.put("totalAmount", MqsUtils.getBigDecimal(first1.get().get(month.split("-")[1])).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP));
            } else {
                result.put("totalAmount", BigDecimal.ZERO);
            }
            results.add(result);
        }
        return results;
    }


    /**
     * 支出金额 - 特殊基金
     *
     * @param filterYear
     * @param auditScenario
     * @param hospitalId
     * @return
     */
    public Map<String, Object> usedBmiAloneAmount(String filterYear, String auditScenario, String hospitalId) {
        //返回报文结构
        Map<String, Object> result = new HashMap<>();
        List<BigDecimal> warnList = new ArrayList<>();
        List<BigDecimal> amountList = new ArrayList<>();
        List<List<BigDecimal>> list = new ArrayList<>();

        //统计场景范围
        List<String> scenarioList = new ArrayList<>();
        switch (auditScenario) {
            case "all":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                break;
            case "op":
                scenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
                break;
            case "hp":
                scenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
                break;
        }
        QueryWrapper<SysFunds> wrapper = new QueryWrapper<SysFunds>().eq(SysFunds.FIELD_HOSPITAL_ID, hospitalId).eq(SysFunds.FIELD_YEARS, filterYear);
        switch (auditScenario) {
            case "op":
                wrapper.eq(SysFunds.FIELD_VISIT_TYPE, "1");
                break;
            case "hp":
                wrapper.eq(SysFunds.FIELD_VISIT_TYPE, "2");
                break;
        }
        //基金配置
        List<SysFunds> fundsList = sysFundsMapper.selectList(wrapper);
        List<SysFundsAlone> sysFundsAlones = new ArrayList<>();
        if(CollUtil.isNotEmpty(fundsList)){
            List<Long> fundIds = fundsList.stream().map(SysFunds::getId).collect(Collectors.toList());
            //查询单独管理额度
            sysFundsAlones = sysFundsMapper.queryAloneAmount(fundIds, hospitalId);
        }


        Map<String, String> scenarioMap = new HashMap<>();
        scenarioMap.put(AuditScenarioEnum.opPt.getAuditScenario(), "1");
        scenarioMap.put(AuditScenarioEnum.hpSettle.getAuditScenario(), "2");
        //单独基金统计
        List<Map> csDiseases = sysFundsMapper.queryCsDisease(hospitalId);
        //查询大病数据
        List<Map> siDieasesMap = sysFundsMapper.querySiDisease(hospitalId);
        //查询靶向药数据
        List<Map> tdDieasesMap = sysFundsMapper.queryTdDisease(hospitalId);

//        List<String> cdsCiDieasess = csDiseases.stream().map(x -> x.get("cs_disease_code").toString()).collect(Collectors.toList());
        List<String> cdsDieasess = csDiseases.stream().filter(x -> "1".equals(x.get("type"))).map(x -> x.get("cs_disease_code").toString()).collect(Collectors.toList());
        List<String> ciDieasess = csDiseases.stream().filter(x -> "2".equals(x.get("type"))).map(x -> x.get("cs_disease_code").toString()).collect(Collectors.toList());
        List<String> siDieasess = siDieasesMap.stream().map(x -> x.get("code").toString()).collect(Collectors.toList());
        List<String> tdDieasess = tdDieasesMap.stream().map(x -> x.get("item_id").toString()).collect(Collectors.toList());

        List<Map> yearUsedByMonthMap = sysFundsMapper.queryUsedAmountAllByMonth(scenarioList, filterYear, hospitalId);

        Map<Integer, List<Map>> month = yearUsedByMonthMap.stream().collect(Collectors.groupingBy(x -> Integer.valueOf(x.get("month").toString())));
        //获取最大月份
        Integer integer = CollUtil.isNotEmpty(yearUsedByMonthMap) ? month.entrySet().stream().max(Comparator.comparing(x -> x.getKey())).get().getKey() : 1;
        //按月统计基金使用额度
        for (Integer i = 1; i <= integer; i++) {
            List<Map> monthDate = month.get(i);
            BigDecimal CdsUsedAmount = BigDecimal.ZERO;
            BigDecimal CiUsedAmount = BigDecimal.ZERO;
            BigDecimal SdUsedAmount = BigDecimal.ZERO;
            BigDecimal TdUsedAmount = BigDecimal.ZERO;
            BigDecimal SiUsedAmount = BigDecimal.ZERO;

            if (CollUtil.isNotEmpty(monthDate)) {

                for (Map map : monthDate) {
                    BigDecimal amount = MqsUtils.getBigDecimal(map.get("total"));
                    if (cdsDieasess.contains(map.get("cs_disease_code"))) {
                        CdsUsedAmount = CdsUsedAmount.add(amount);
                    }
                    if (ciDieasess.contains(map.get("cs_disease_code"))) {
                        CiUsedAmount = CiUsedAmount.add(amount);
                    }
                    if (siDieasess.contains(map.get("out_diagnosis_code"))) {
                        SiUsedAmount = SiUsedAmount.add(amount);
                    }
                    if (tdDieasess.contains(map.get("item_id"))) {
                        TdUsedAmount = TdUsedAmount.add(amount);
                    }
                    if (StrUtil.isNotEmpty((String)map.get("single_disease_code")) && !"0".equals(map.get("single_disease_code"))) {
                        SdUsedAmount = SdUsedAmount.add(amount);
                    }
                }
            }
            List<BigDecimal> amounts = Arrays.asList(CdsUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP),
                    CiUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP),
                    SdUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP),
                    TdUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP),
                    SiUsedAmount.divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP));
            list.add(amounts);
        }

        //慢病
        BigDecimal CdsTotalAmount = sysFundsAlones.stream().filter(x -> "Cds".equals(x.getAloneCode())).map(SysFundsAlone::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal CdsTotalWarn = sysFundsAlones.stream().filter(x -> "Cds".equals(x.getAloneCode())).map(SysFundsAlone::getWarn).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);

        //特病累计
        BigDecimal CiTotalAmount = sysFundsAlones.stream().filter(x -> "Ci".equals(x.getAloneCode())).map(SysFundsAlone::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal CiTotalWarn = sysFundsAlones.stream().filter(x -> "Ci".equals(x.getAloneCode())).map(SysFundsAlone::getWarn).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);

        //单病种
        BigDecimal SdTotalAmount = sysFundsAlones.stream().filter(x -> "Sd".equals(x.getAloneCode())).map(SysFundsAlone::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal SdTotalWarn = sysFundsAlones.stream().filter(x -> "Sd".equals(x.getAloneCode())).map(SysFundsAlone::getWarn).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);

        //靶向药
        BigDecimal TdTotalAmount = sysFundsAlones.stream().filter(x -> "Td".equals(x.getAloneCode())).map(SysFundsAlone::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal TdTotalWarn = sysFundsAlones.stream().filter(x -> "Td".equals(x.getAloneCode())).map(SysFundsAlone::getWarn).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);

        //大病
        BigDecimal SiTotalAmount = sysFundsAlones.stream().filter(x -> "Si".equals(x.getAloneCode())).map(SysFundsAlone::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal SiTotalWarn = sysFundsAlones.stream().filter(x -> "Si".equals(x.getAloneCode())).map(SysFundsAlone::getWarn).reduce(BigDecimal.ZERO, BigDecimal::add).divide(tenThousand, 4, BigDecimal.ROUND_HALF_UP);
        warnList = Arrays.asList(CdsTotalWarn,
                CiTotalWarn,
                SdTotalWarn,
                TdTotalWarn,
                SiTotalWarn);
        amountList = Arrays.asList(CdsTotalAmount,
                CiTotalAmount,
                SdTotalAmount,
                TdTotalAmount,
                SiTotalAmount);

        result.put("warnList", warnList);
        result.put("amountList", amountList);
        result.put("list", list);

        return result;
    }
}
