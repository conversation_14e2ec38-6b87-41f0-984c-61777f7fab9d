package com.crhms.cloud.mqs.sys.engine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.sys.domain.SysConfig;
import com.crhms.cloud.mqs.sys.domain.SysRules;
import com.crhms.cloud.mqs.sys.dto.EngineProxy;
import com.crhms.cloud.mqs.sys.service.SysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName EngineRestful
 * @Description 请求引擎Restful接口
 * <AUTHOR>
 * @Date 2023/3/1
 **/
@Slf4j
@RestController
@RequestMapping("/api/mqs/engine/restful")
public class EngineRestful {

    @Lazy
    @Autowired
    private SysConfigService configService;

    // 诊间审核引擎restful地址
    public static final String MQS_ENGINE_RESTFUL_URL = "MQS_ENGINE_RESTFUL_URL";

    /**
     * 转发引擎请求
     * <AUTHOR>
     * @date 2023/3/20
     * @param proxy
     **/
    @PostMapping("/proxy")
    public ResponseEntity<String> proxy(@RequestBody EngineProxy proxy){
        String url = getEngineRestfulUrl() + proxy.getUrl();
        HttpRequest req = HttpUtil.createRequest("POST".equals(proxy.getMethod())? Method.POST:Method.GET, url);

        if(MapUtil.isNotEmpty(proxy.getParams())){
            req.form(proxy.getParams());
        }
        if(null!=proxy.getBody()){
            req.body(JSONUtil.toJsonStr(proxy.getBody()));
        }
        if(MapUtil.isNotEmpty(proxy.getHeaders())){
            for(String key : proxy.getHeaders().keySet()){
                req.header(key, proxy.getHeaders().get(key));
            }
        }

        try{
            log.info("[调用引擎Restful接口: 地址:{}, 参数:{} , 请求头:{}, 请求体:{}]", url, proxy.getParams(), req.headers(), JSONUtil.toJsonStr(proxy.getBody()));
            HttpResponse rep = req.execute();

            InputStream stream = rep.bodyStream();

            HttpHeaders repHeaders = new HttpHeaders();
            Map<String, List<String>> headers = rep.headers();
            if(MapUtil.isNotEmpty(headers)){
                for(String key : headers.keySet()){
                    if(StrUtil.isEmpty(key)){
                        continue;
                    }
                    repHeaders.add(key, StrUtil.join(";", headers.get(key)));
                }
            }

            return new ResponseEntity(rep.body(), repHeaders, HttpStatus.OK);
        } catch (Exception e){
            throw new BaseException("调用引擎Restful失败:" + ExceptionUtil.getMessage(e), e);
        }
    }

    /**
     * 转发引擎请求（文件下载）
     * <AUTHOR>
     * @date 2023/3/31
     * @param proxy
     * @param response
     **/
    @PostMapping("/export/proxy")
    public void exportProxy(@RequestBody EngineProxy proxy, HttpServletResponse response){
        String url = getEngineRestfulUrl() + proxy.getUrl();
        HttpRequest req = HttpUtil.createRequest("POST".equals(proxy.getMethod())? Method.POST:Method.GET, url);

        if(MapUtil.isNotEmpty(proxy.getParams())){
            req.form(proxy.getParams());
        }
        if(null!=proxy.getBody()){
            req.body(JSONUtil.toJsonStr(proxy.getBody()));
        }
        if(MapUtil.isNotEmpty(proxy.getHeaders())){
            for(String key : proxy.getHeaders().keySet()){
                req.header(key, proxy.getHeaders().get(key));
            }
        }

        try{
            log.info("[调用引擎Restful接口: 地址:{}, 参数:{} , 请求头:{}, 请求体:{}]", url, proxy.getParams(), req.headers(), JSONUtil.toJsonStr(proxy.getBody()));
            HttpResponse rep = req.execute();
            Map<String, List<String>> headers = rep.headers();
            InputStream stream = rep.bodyStream();

            // 设置请求头
            if(MapUtil.isNotEmpty(headers)){
                for(String key : headers.keySet()){
                    if(StrUtil.isEmpty(key)){
                        continue;
                    }
                    response.setHeader(key,  StrUtil.join(";", headers.get(key)));
                }
            }

            // 写入文件流
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(IoUtil.readBytes(stream));
            response.flushBuffer();
        } catch (Exception e){
            throw new BaseException("调用引擎Restful失败:" + ExceptionUtil.getMessage(e), e);
        }
    }

    /**
     * 转发引擎请求（文件上传）
     * <AUTHOR>
     * @date 2023/3/31
     * @param proxy
     * @param response
     **/
    @PostMapping("/import/proxy")
    public ResponseEntity<String> importProxy(EngineProxy proxy){
        String url = getEngineRestfulUrl() + proxy.getUrl();
        HttpRequest req = HttpUtil.createRequest("POST".equals(proxy.getMethod())? Method.POST:Method.GET, url);

        if(MapUtil.isNotEmpty(proxy.getParams())){
            req.form(proxy.getParams());
        }

        if(null!=proxy.getBody()){
            req.body(JSONUtil.toJsonStr(proxy.getBody()));
        }
        if(MapUtil.isNotEmpty(proxy.getHeaders())){
            for(String key : proxy.getHeaders().keySet()){
                req.header(key, proxy.getHeaders().get(key));
            }
        }
        if(null!=proxy.getFile()){
            req.form("file", MultipartFileToFile(proxy.getFile()));
        }

        try{
            log.info("[调用引擎Restful接口: 地址:{}, 参数:{} , 请求头:{}, 请求体:{}]", url, proxy.getParams(), req.headers(), JSONUtil.toJsonStr(proxy.getBody()));
            HttpResponse rep = req.execute();

            HttpHeaders repHeaders = new HttpHeaders();
            Map<String, List<String>> headers = rep.headers();
            if(MapUtil.isNotEmpty(headers)){
                for(String key : headers.keySet()){
                    if(StrUtil.isEmpty(key)){
                        continue;
                    }
                    repHeaders.add(key, StrUtil.join(";", headers.get(key)));
                }
            }

            return new ResponseEntity(rep.body(), repHeaders, HttpStatus.OK);
        } catch (Exception e){
            throw new BaseException("调用引擎Restful失败:" + ExceptionUtil.getMessage(e), e);
        }
    }


    public static File MultipartFileToFile(MultipartFile multiFile) {
        // 获取文件名
        String fileName = multiFile.getOriginalFilename();
        // 获取文件后缀
        String prefix = fileName.substring(fileName.lastIndexOf("."));
        // 若需要防止生成的临时文件重复,可以在文件名后添加随机码

        try {
            File file = File.createTempFile(fileName, prefix);
            multiFile.transferTo(file);
            return file;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /****************************************** 规则列表 ******************************************/

    /** 
     * 查询规则列表
     * <AUTHOR>
     * @date 2023/3/20
     **/
    public JSONArray queryRuleList(){
        String url = getEngineRestfulUrl() + "/api/rule/rulesList";

        try{
            String repStr = HttpUtil.get(url);

            JSONObject req = JSONUtil.parseObj(repStr, true);
            if(req.containsKey("is_success") && req.getBool("is_success")){
                return req.getJSONArray("result");
            }else {
                log.info("从规则引擎查询规则列表返回内容:{}", repStr);
                return JSONUtil.createArray();
            }
        }catch (Exception e){
            throw new BaseException("从规则引擎查询规则列表异常:" + e.getMessage());
        }
    }


    /** 
     * 新增/提交规则基本信息至引擎
     * <AUTHOR>
     * @date 2023/3/1
     * @param flag       操作类型 1新增 2修改
     **/
    public void submitRule(int flag, SysRules rule){
        JSONObject body = JSONUtil.createObj();

        // 操作类型: 1新增 2修改
        body.set("flag", flag);
        // 规则id
        body.set("id", String.valueOf(rule.getId()));
        // 规则大类编码
        body.set("ruleCategoryCode", rule.getRuleType());
        // 规则编码
        body.set("ruleCode", rule.getRuleCode());
        // 规则名称
        body.set("ruleName", rule.getRuleName());
        // 严重程度编码
        body.set("resultType", Integer.valueOf(rule.getRuleLevel()));
        // 是否作用在主单 0 否 1 是
        body.set("actOnMain", Integer.valueOf(rule.getIsMain()));
        // 是否查询历史数据 0 不查询 1 查主单 2 查明细
        body.set("checkHistory", Integer.valueOf(rule.getIsHis()));
        // 规则说明
        body.set("ruleDescription", rule.getRemake());

        try{
            String url = getEngineRestfulUrl() + "/api/rule/incrementalRules";

            String reqStr = HttpUtil.post(url, body.toString());
            JSONObject req = JSONUtil.parseObj(reqStr);
            if(req.containsKey("is_success") && req.getBool("is_success")){
                log.info("新增/更新规则[{}] 调用引擎restful成功,返回信息:{}", rule.getRuleCode(), reqStr);
            }else {
                throw new BaseException(req.getStr("message"));
            }
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("新增/更新规则信息时调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    /**
     * 新增/修改规则流程图详情
     * <AUTHOR>
     * @date 2023/3/1
     * @param detail    规则流程图详情
     **/
    @PostMapping("/rule/detail/submit")
    public ResponseEntity<String> submitRuleDetail(@RequestParam(value = "ruleId") String ruleId, @RequestBody JSONArray detail){

        JSONObject body = JSONUtil.createObj();
        body.set("id", ruleId);
        body.set("processes", detail);

        try{
            String url = getEngineRestfulUrl() + "/api/rule/incrementalProcess";
            String reqStr = HttpUtil.post(url, body.toString());

            JSONObject req = JSONUtil.parseObj(reqStr, true);
            if(req.containsKey("is_success") && req.getBool("is_success")){
                log.info("新增/更新规则[{}] 调用引擎restful成功,返回信息:{}", ruleId, reqStr);
                return new ResponseEntity(reqStr, HttpStatus.OK);
            }else {
                throw new BaseException("提交规则流程图时调用引擎restful接口异常:"+req.getStr("message"));
            }
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("提交规则流程图时调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    /**
     * 根据规则ID，查询规则详情
     * <AUTHOR>
     * @date 2023/3/1
     * @param ruleId    规则ID
     **/
    @GetMapping("/rule/detail/query")
    public ResponseEntity<JSONObject> queryRuleDetail(@RequestParam(value = "ruleId") String ruleId){
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", ruleId);

        String url = getEngineRestfulUrl() + "/api/rule/getRule";
        String reqStr = HttpUtil.get(url, params);

        return new ResponseEntity(JSONUtil.parse(reqStr), HttpStatus.OK);
    }

    /******************************************************* 引擎数据设置 接口转发 *******************************************************/

    // 获取表名列表
    @GetMapping("/api/custom/tableList")
    public ResponseEntity<String> customTableList(@RequestParam(required = false) Map<String, Object> params){
        try{
            String url = getEngineRestfulUrl() + "/api/custom/tableList";
            String reqStr = HttpUtil.get(url, params);
            return new ResponseEntity(reqStr, HttpStatus.OK);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    // 新建表
    @PostMapping("/api/custom/addTable")
    public ResponseEntity<JSON> customAddTable(@RequestBody JSONObject body){
        try{
            String url = getEngineRestfulUrl() + "/api/custom/addTable";
            String reqStr = HttpUtil.post(url, body.toString());
            return new ResponseEntity(JSONUtil.parseObj(reqStr), HttpStatus.OK);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    // 修改表
    @PostMapping("/api/custom/modifyTable")
    public ResponseEntity<JSON> customModifyTable(@RequestBody JSONObject body){
        try{
            String url = getEngineRestfulUrl() + "/api/custom/modifyTable";
            String reqStr = HttpUtil.post(url, body.toString());
            return new ResponseEntity(JSONUtil.parseObj(reqStr), HttpStatus.OK);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    // 删除表
    @GetMapping("/api/custom/deleteTable")
    public ResponseEntity<JSON> customDeleteTable(@RequestParam(required = false) Map<String, Object> params){
        try{
            String url = getEngineRestfulUrl() + "/api/custom/deleteTable";
            String reqStr = HttpUtil.get(url, params);
            return new ResponseEntity(JSONUtil.parseObj(reqStr), HttpStatus.OK);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    // 添加字段(批量)
    @PostMapping("/api/custom/addColumns")
    public ResponseEntity<JSON> customAddColumns(@RequestBody JSONObject body){
        try{
            String url = getEngineRestfulUrl() + "/api/custom/addColumns";
            String reqStr = HttpUtil.post(url, body.toString());
            return new ResponseEntity(JSONUtil.parseObj(reqStr), HttpStatus.OK);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    // 修改字段
    @PostMapping("/api/custom/modifyColumns")
    public ResponseEntity<JSON> customModifyColumns(@RequestBody JSONObject body){
        try{
            String url = getEngineRestfulUrl() + "/api/custom/modifyColumns";
            String reqStr = HttpUtil.post(url, body.toString());
            return new ResponseEntity(JSONUtil.parseObj(reqStr), HttpStatus.OK);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    // 删除字段
    @PostMapping("/api/custom/deleteColumn")
    public ResponseEntity<JSON> customDeleteColumn(@RequestBody JSONObject body){
        try{
            String url = getEngineRestfulUrl() + "/api/custom/deleteColumn";
            String reqStr = HttpUtil.post(url, body.toString());
            return new ResponseEntity(JSONUtil.parseObj(reqStr), HttpStatus.OK);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    // 获取表数据
    @GetMapping("/api/custom/getData")
    public ResponseEntity<String> customGetData(@RequestParam(required = false) Map<String, Object> params){
        try{
            String url = getEngineRestfulUrl() + "/api/custom/getData";
            String reqStr = HttpUtil.get(url, params);
            return new ResponseEntity(reqStr, HttpStatus.OK);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    // 添加数据
    @PostMapping("/api/custom/addData")
    public ResponseEntity<JSON> customAddData(@RequestBody JSONObject body){
        try{
            String url = getEngineRestfulUrl() + "/api/custom/addData";
            String reqStr = HttpUtil.post(url, body.toString());

            return new ResponseEntity(JSONUtil.parseObj(reqStr), HttpStatus.OK);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    // 修改数据
    @PostMapping("/api/custom/modifyData")
    public ResponseEntity<JSON> customModifyData(@RequestBody JSONObject body){
        try{
            String url = getEngineRestfulUrl() + "/api/custom/modifyData";
            String reqStr = HttpUtil.post(url, body.toString());
            return new ResponseEntity(JSONUtil.parseObj(reqStr), HttpStatus.OK);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }

    // 删除数据
    @GetMapping("/api/custom/deleteData")
    public ResponseEntity<JSON> customDeleteData(@RequestParam(required = false) Map<String, Object> params){
        try{
            String url = getEngineRestfulUrl() + "/api/custom/deleteData";
            String reqStr = HttpUtil.get(url, params);
            return new ResponseEntity(JSONUtil.parseObj(reqStr), HttpStatus.OK);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            throw new BaseException("调用引擎restful接口异常:"+e.getMessage(), e);
        }
    }


    /**
     * 获取引擎restful地址
     * <AUTHOR>
     * @date 2023/3/1
     **/
    private String getEngineRestfulUrl(){
        SysConfig sysConfig = configService.queryByKey(MQS_ENGINE_RESTFUL_URL, LoginContext.getHospitalId());
        if(null==sysConfig){
            throw new BaseException("未配置诊间审核引擎Restful地址");
        }

        String url = sysConfig.getSysValue();

        // 自动添加http
        if(!url.startsWith("http")){
            return "http://" + url;
        }

        return url;
    }


}
