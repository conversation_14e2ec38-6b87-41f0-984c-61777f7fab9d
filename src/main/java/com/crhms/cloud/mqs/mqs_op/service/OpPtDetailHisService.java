package com.crhms.cloud.mqs.mqs_op.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_op.domain.OpPtDetailHis;
import com.crhms.cloud.mqs.mqs_op.mapper.OpPtDetailHisMapper;
import com.crhms.cloud.mqs.mqs_op.service.OpPtDetailHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 门诊审核-处方费用明细(OpPtDetailHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-19 14:40:10
 */
@Service("opPtDetailHisService")
public class OpPtDetailHisService extends ServiceImpl< OpPtDetailHisMapper, OpPtDetailHis> {

}
