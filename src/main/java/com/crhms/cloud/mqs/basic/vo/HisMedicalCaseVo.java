package com.crhms.cloud.mqs.basic.vo;

import com.googlecode.jmapper.annotations.JGlobalMap;
import com.googlecode.jmapper.annotations.JMap;
import com.googlecode.jmapper.annotations.JMapConversion;
import lombok.Data;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class HisMedicalCaseVo {
    // 单据唯一编码		String(256)	是
    @JMap("no")
    private String no;
    // 主单号（ 住院号\门诊号 ）
    @JMap("admissionNo")
    private String admission_no;
    //医保反馈编码
    @JMap("bmiNumber")
    private String bmi_number;
    //DRG编码
    @JMap("drgCode")
    private String drg_code;
    //DIP编码
    @JMap("dipCode")
    private String dip_code;
    // 单据结算日期	门诊单据用实际结算日期；住院单据，如已经出院结算，则用结算日期，如尚未出院结算，用当天费用增量或医嘱增量日期  格式 yyyy-mm-dd hh:mm:ss	String	是
    @JMap("billDate")
    private String bill_date;
    // 医院编号	医保分配的医疗机构_i_d	String(50)	是
    @JMap("hospitalCode")
    private String hospital_code;
    // 医院级别	见数据字典5.4定点机构等级 _integer	是
    @JMap("hospitalLevel")
    private String hospital_level;
    // 定点机构类型	见数据字典5.5定点机构类型 String(50)	是
    @JMap("hospitalType")
    private String hospital_type;
    // 就医方式编码	见数据字典5.1就医方式    String	是
    @JMap("claimTypeId")
    private String claim_type_id;
    // 参保类型编码	见数据字典5.2参保类型    String(2)	是
    @JMap("benefitTypeId")
    private String benefit_type_id;
    //1医保  2自费 3 医保|自费
    @JMap("selfExpense")
    private String self_expense;
    // 医保统筹区编码
    @JMap("bmiCode")
    private String bmi_code;
    // 医保统筹区名称
    @JMap("bmiName")
    private String bmi_name;
    //入院诊断编码 	从当地医疗结构获取的_i_c_d10字典数据（医保版）	String(256)	是
    @JMap("inDiagnosisCode")
    private String in_diagnosis_code;
    //入院诊断名称
    @JMap("inDiagnosisName")
    private String in_diagnosis_name;
    // 出院诊断编码（在院时 为主诊断）		String(256)	是
    @JMap("outDiagnosisCode")
    private String out_diagnosis_code;
    // 出院诊断名称（在院时 为主诊断）		String(256)	是
    @JMap("outDiagnosisName")
    private String out_diagnosis_name;

    // 其它诊断编码1		String(256)	否
    @JMap("diagnosisCode1")
    private String diagnosis_code1;
    // 其它诊断编码2		String(256)	否
    @JMap("diagnosisCode2")
    private String diagnosis_code2;
    // 其它诊断编码3		String(256)	否
    @JMap("diagnosisCode3")
    private String diagnosis_code3;
    // 其它诊断编码4		String(256)	否
    @JMap("diagnosisCode4")
    private String diagnosis_code4;
    // 其它诊断编码5		String(256)	否
    @JMap("diagnosisCode5")
    private String diagnosis_code5;
    // 其它诊断编码6		String(256)	否
    @JMap("diagnosisCode6")
    private String diagnosis_code6;
    // 其它诊断编码7		String(256)	否
    @JMap("diagnosisCode7")
    private String diagnosis_code7;
    // 其它诊断编码8		String(256)	否
    @JMap("diagnosisCode8")
    private String diagnosis_code8;
    // 其它诊断编码9		String(256)	否
    @JMap("diagnosisCode9")
    private String diagnosis_code9;
    // 其它诊断编码10		String(256)	否
    @JMap("diagnosisCode10")
    private String diagnosis_code10;
    // 其它诊断编码11		String(256)	否
    @JMap("diagnosisCode11")
    private String diagnosis_code11;
    // 其它诊断编码12		String(256)	否
    @JMap("diagnosisCode12")
    private String diagnosis_code12;
    // 其它诊断编码13		String(256)	否
    @JMap("diagnosisCode13")
    private String diagnosis_code13;
    // 其它诊断编码14		String(256)	否
    @JMap("diagnosisCode14")
    private String diagnosis_code14;
    // 其它诊断编码15		String(256)	否
    @JMap("diagnosisCode15")
    private String diagnosis_code15;
    // 其它诊断编码16		String(256)	否
    @JMap("diagnosisCode16")
    private String diagnosis_code16;
    @JMap("diagnosisName1")
    private String diagnosis_name1;
    @JMap("diagnosisName2")
    private String diagnosis_name2;
    @JMap("diagnosisName3")
    private String diagnosis_name3;
    @JMap("diagnosisName4")
    private String diagnosis_name4;
    @JMap("diagnosisName5")
    private String diagnosis_name5;
    @JMap("diagnosisName6")
    private String diagnosis_name6;
    @JMap("diagnosisName7")
    private String diagnosis_name7;
    @JMap("diagnosisName8")
    private String diagnosis_name8;
    @JMap("diagnosisName9")
    private String diagnosis_name9;
    @JMap("diagnosisName10")
    private String diagnosis_name10;
    @JMap("diagnosisName11")
    private String diagnosis_name11;
    @JMap("diagnosisName12")
    private String diagnosis_name12;
    @JMap("diagnosisName13")
    private String diagnosis_name13;
    @JMap("diagnosisName14")
    private String diagnosis_name14;
    @JMap("diagnosisName15")
    private String diagnosis_name15;
    @JMap("diagnosisName16")
    private String diagnosis_name16;
    // 入院时间	格式 yyyy-_m_m-dd _h_h:mm:ss入院时间不能晚于出院时间	String	是
    @JMap("admissionDate")
    private String admission_date;
    // 出院时间	格式 yyyy-_m_m-dd _h_h:mm:ss
    @JMap("dischargeDate")
    private String discharge_date;

    // 参保人姓名	预审结果查询条件之一	String(80)	是
    @JMap("patientName")
    private String patient_name;
    // 参保人编码	社保卡号或者身份证号（必须保证编码是患者在本院唯一编码，因要根据当前编码查询关联历史数据。）	String(20)	是
    @JMap("patientId")
    private String patient_id;
    // 性别	0未知的性别、1男、2女、9、未说明的性别	String(4)	是
    @JMap("patientGender")
    private String patient_gender;
    // 出生日期	格式 yyyy-_m_m-dd	String	是
    @JMap("patientBirthday")
    private String patient_birthday;
    //是否黑名单 门诊
    @JMap("isBkl")
    private String is_bkl;
    // 是否孕期	1 是0 否	String(40)	是
    @JMap("isPregnant")
    private String is_pregnant;
    // 是否哺乳期	1 是0 否	String(40)	是
    @JMap("isLactating")
    private String is_lactating;
    //新生儿出生体重 (kg)
    @JMap("newbornWeight")
    private String newborn_weight;
    //新生儿年龄（小于一岁的天数) （天）
    @JMap("newbornAge")
    private String newborn_age;
    // 单据总金额	不能为空，默认为0	_number(128，4)	是
    @JMap("totalAmount")
    private BigDecimal total_amount;
    // 人员类别编码	见数据字典5.3人员类别   String(6)	是
    @JMap("personnelType")
    private String personnel_type;
    // 待遇类型	默认值-99999	String(16)	是
    @JMap("treatmentType")
    private String treatment_type;
    // 医保内金额	非空。    全自费的为0，默认等于总金额	_number(128，4)	是
    @JMap("bmiConveredAmount")
    private BigDecimal bmi_convered_amount;
    //医保统筹金额
    @JMap("bmiOverallAmount")
    private BigDecimal bmi_overall_amount;
    // 是否异地就医	0否；1是 	String(6)	是
    @JMap("unusualFlag")
    private String unusual_flag;
    // 参保人特殊保险类型组编码 	1：生育  2：工伤  -1：其他	String(2)	是
    @JMap("benefitGroupCode")
    private String benefit_group_code;
    // 因战因公标志	0否，1是 默认值  0 	String(2)	是
    @JMap("warBusinessFlag")
    private String war_business_flag;
    // 单病种编码	默认值  0，如果有则传病种编码	String(50)	是
    @JMap("singleDiseaseCode")
    private String single_disease_code;
    // 单病种编码	默认值  0，如果有则传病种编码	String(50)	是
    @JMap("singleDiseaseName")
    private String single_disease_name;
    // 门诊慢特病编码 门诊审核场字段
    @JMap("csDiseaseCode")
    private String cs_disease_code;
    // 门诊慢特病名称 门诊审核场字段
    @JMap("csDiseaseName")
    private String cs_disease_name;
    // 病区编码(ipp数据权限字段-病区编码)
    @JMap("outZoneCode")
    private String out_zone_code;
    // 病案号（病人在医院看病的唯一登记号，只登记一次） String(256)	是
    @JMap("medicalRecordId")
    private String medical_record_id;
    // 是否二次返医/转院 | 按指定病种定额结算
    @JMap("isTransHospital")
    private String is_trans_hospital;
    // 是否公立医改医院
    @JMap("isPublicHosp")
    private String is_public_hosp;
    // 数据来源 0 医生站  1 护士站
    private String source;
    // 证件号码
    @JMap("patientIdno")
    private String patient_idno;
    // 出入院状态   0：在院 1：出院 2：门诊	 String	是
    @JMap("isDischarge")
    private String is_discharge;
    //就诊日期
    @JMap("itemDate")
    private String item_date;
    //入院科室
    @JMap("deptCode")
    private String admission_dept_code;
    @JMap("deptName")
    private String admission_dept_name;
    //出院科室
    @JMap("dischargeDeptCode")
    private String discharge_dept_code;
    @JMap("dischargeDeptName")
    private String discharge_dept_name;
    //医生(ipp数据权限字段-责任医师)
    @JMap("docId")
    private String doc_id;
    //医生名称
    @JMap("docName")
    private String doc_name;
    //医生级别
    @JMap("docLevel")
    private String doc_level;
    //医师小组编码(ipp数据权限字段-医师小组)
    @JMap("docGroupCode")
    private String doc_group_code;
    //医师小组名称
    @JMap("docGroupName")
    private String doc_group_name;
    //院区编码(ipp数据权限字段-院区编码)
    @JMap("hospAreaCode")
    private String hosp_area_code;
    //院区名称
    @JMap("hospAreaName")
    private String hosp_area_name;
    //是否计费(1.是，0否)
    @JMap("chargingFlag")
    private String charging_flag;
    //自付比例：默认0，不能为空
    @JMap("payRatio")
    private BigDecimal pay_ratio;
    //自付金额
    @JMap("payAmount")
    private BigDecimal pay_amount;
    //挂号医生id
    @JMap("registerDocId")
    private String register_doc_id;
    //挂号医生名称
    @JMap("registerDocName")
    private String register_doc_name;
    //挂号 id
    @JMap("registerId")
    private String register_id;
    //挂号时间
    @JMap("registerTime")
    private String register_time;
    //挂号次数
    @JMap("registerNums")
    private String register_nums;
    //挂号费
    @JMap("registrationFee")
    private BigDecimal registration_fee;

    //转出科室编码
    @JMap("tfFromDeptCode")
    private String tf_from_dept_code;
    //转出科室名称
    @JMap("tfFromDeptName")
    private String tf_from_dept_name;
    //转出医生编码
    @JMap("tfFromDocCode")
    private String tf_from_doc_code;
    //转出医生名称
    @JMap("tfFromDocName")
    private String tf_from_doc_name;
    //转入科室编码
    @JMap("tfToDeptCode")
    private String tf_to_dept_code;
    //转入科室名称
    @JMap("tfToDeptName")
    private String tf_to_dept_name;
    //转入医生编码
    @JMap("tfToDocCode")
    private String tf_to_doc_code;
    //转入医生名称
    @JMap("tfToDocName")
    private String tf_to_doc_name;
    //转科日期
    @JMap("tfDate")
    private String tf_date;
    //收费日期
    @JMap("feeDate")
    private String fee_date;

    //当前科室名称
    @JMap("presentDeptName")
    private String present_dept_name;
    //当前科室编码 (ipp数据权限字段-科室)
    @JMap("presentDeptCode")
    private String present_dept_code;
    //专科科室 (ipp数据权限字段-专科)
    @JMap("unitsDeptCode")
    private String units_dept_code;
    @JMap("unitsDeptName")
    private String units_dept_name;
}
