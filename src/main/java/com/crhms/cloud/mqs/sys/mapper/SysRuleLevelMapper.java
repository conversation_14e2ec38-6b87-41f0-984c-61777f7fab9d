package com.crhms.cloud.mqs.sys.mapper;

import com.crhms.cloud.mqs.sys.domain.SysRuleLevel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.sys.domain.SysRuleLevelThird;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 规则级别配置表(SysRuleLevel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-12 16:23:39
 */

@Mapper
public interface SysRuleLevelMapper extends BaseMapper<SysRuleLevel> {

    /**
     * 连表查询三方规则级别配置 (内置 自动定义级别)
     * @param platform
     * @param hospitalId
     * @return
     */
    List<SysRuleLevelThird> queryThirdLevelList(@Param("platform") String platform, @Param("hospitalId") String hospitalId);
}

