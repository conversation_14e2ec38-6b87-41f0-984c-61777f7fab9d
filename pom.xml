<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.crhms.cloud</groupId>
        <artifactId>parent</artifactId>
        <version>1.4</version>
        <relativePath/>
    </parent>

    <groupId>com.crhms.cloud</groupId>
    <artifactId>mqs</artifactId>
    <version>2.0.0</version>

    <name>crhms-mqs</name>
    <description>诊间审核2.0产品</description>

    <properties>
        <java.version>1.8</java.version>
        <thrift.version>0.17.0</thrift.version>
        <jmapper.version>*******</jmapper.version>
    </properties>
    <dependencies>
        <!--核心工具包-->
        <dependency>
            <groupId>com.crhms.cloud</groupId>
            <artifactId>core</artifactId>
            <version>1.5</version>
        </dependency>

        <!--服务间调用包-->
        <dependency>
            <groupId>com.crhms.cloud</groupId>
            <artifactId>client</artifactId>
            <version>1.0.7</version>
        </dependency>

        <dependency>
            <groupId>com.taobao.arthas</groupId>
            <artifactId>arthas-spring-boot-starter</artifactId>
            <version>4.0.5</version>
        </dependency>

        <!--数据库管理-->
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>4.3.3</version>
        </dependency>

        <!-- 引擎调用协议 -->
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
            <version>${thrift.version}</version>
        </dependency>
        <!-- jmapper 映射组件 -->
        <dependency>
            <groupId>com.googlecode.jmapper-framework</groupId>
            <artifactId>jmapper-core</artifactId>
            <version>${jmapper.version}</version>
        </dependency>

        <!--Clickhouse驱动-->
        <dependency>
            <groupId>ru.yandex.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>0.3.1-patch</version>
        </dependency>

        <!-- kingbase  -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.5.0</version>
        </dependency>

        <!--多数据源切换-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.6.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--SFTP连接Linux-->
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.54</version>
        </dependency>

        <!-- 队列 -->
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>3.4.4</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>cloud-cqa</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <configuration>
                            <fork>true</fork>
                            <addResources>true</addResources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.github.core-lib</groupId>
                <artifactId>xjar-maven-plugin</artifactId>
                <version>4.0.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <includes>
                                <include>/com/crhms/cloud/**</include>
                                <include>/mapper/**</include>
                                <include>/db/**</include>
                                <include>/lib/**</include>
                                <include>application.properties</include>
                                <include>bootstrap.properties</include>
                            </includes>
                            <targetJar>cloud-ms.jar</targetJar>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>io.xjar</groupId>
                        <artifactId>xjar</artifactId>
                        <version>4.0.1.2</version>
                    </dependency>
                </dependencies>
            </plugin>

        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

    </build>

</project>
