package com.crhms.cloud.mqs.sys.enums;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 缓存数据关键字
 */
public class GloablData {

    // 全局锁
    public static final String GLOBAL_SYS_LOCK = "CRHMS:MQS:LOCK:{}:resource:{}";
    // 规则编码 - 规则名称映射
    public static final String GLOBAL_SYS_RULES_KEY = "CRHMS:MQS:BASIC:{}:RULES";
    // 审核场景 - 规则编码
    public static final String GLOBAL_SYS_SCENERULE_KEY = "CRHMS:MQS:CONF:{}:SCENERULES";
    // 审核场景单据计数器
    public static final String GLOBAL_SYS_AUDITNUMS_KEY = "CRHMS:MQS:BASIC:{}:AUDITNUMS:{}";
    // 审核场景 - 规则编码
    public static final String GLOBAL_SYS_CONFIG_KEY = "CRHMS:MQS:CONF:{}:SYSCONFIG";

    // 场景配置 - 全场景配置缓存
    public static final String GLOBAL_SYS_SCENE_KEY = "CRHMS:MQS:SYS:SCENE:{}";

    public static final String GLOBAL_SYS_SCENE_FUNCTION_KEY = "CRHMS:MQS:SYS:SCENE:FUNCTION:{}";

    // 人工审核配置缓存
    public static final String GLOBAL_MR_CONFIG_KEY = "CRHMS:MQS:MR:CONFIG:{}";
    //人工审核审核权限配置缓存
    public static final String GLOBAL_MR_CONFIG_AUDIT_KEY = "CRHMS:MQS:MR:CONFIG:AUDIT:{}";

    //字典映射
    @AllArgsConstructor
    public enum LOCAL_DICT {

        LOCAL_YN_CODE("LOCAL_YN_CODE",new HashMap<String, String>(){{
            put("0","否");
            put("1","是");
        }} ,"是否"),
        /*VIOLATION_FLAG("LOCAL_VIOLATION_FLAG",new HashMap<String, String>(){{
            put("0","未违规");
            put("1","机审违规");
            put("2","人工违规");
        }} ,"违规类型"),*/
        VIOLATION_FLAG("LOCAL_VIOLATION_FLAG",new HashMap<String, String>(){{
            put("0","未违规");
            put("1","违规");
        }} ,"违规类型"),
        LOCAL_DOC_LEVEL("LOCAL_DOC_LEVEL",new HashMap<String, String>(){{
            put("1","初级");
            put("2","中级");
            put("3","副高");
            put("4","正高");
        }} ,"医生级别"),
        LOCAL_IS_DISCHARGE("LOCAL_IS_DISCHARGE",new HashMap<String, String>(){{
            put("0","在院");
            put("1","出院");
            put("2","门诊");
        }} ,"住院状态"),
        LOCAL_FUNDS_ALONE_CODE("LOCAL_FUNDS_ALONE_CODE",new HashMap<String, String>(){{
            put("CdsCi","慢病+特病");
            put("Cds","慢病");
            put("Ci","特病");
            put("Si","大病");
            put("Sd","单病种");
            put("Td","靶向药");
        }} ,"基金监控单独管理维度"),
        LOCAL_RULE_ORIGIN("LOCAL_RULE_ORIGIN", new HashMap<String, String>(){{
            put("1","院内");
            put("2","医保");
        }}, "违规来源类型"),
        LOCAL_RULE_TYPE("LOCAL_RULE_TYPE", new HashMap<String, String>(){{
            put("1", "自定义");
            put("2", "内置");
            put("3", "医保平台");
        }}, "规则类别"),
        /*LOCAL_IS_HIS("LOCAL_IS_HIS", new HashMap<String, String>(){{
            put("0", "不查询");
            put("1", "查询明细以主单分组");
            put("2", "查明细");
            put("3", "查门诊信息");
            put("4", "查住院主单");
        }}, "是否查询历史数据"),*/
        /*LOCAL_FUNCTION_CODE("LOCAL_FUNCTION_CODE",new HashMap<String,String>(){{
            put("0","实时审核弹窗");
            put("1","综合弹窗");
            put("2","无弹窗");
        }},"审核弹窗"),
        LOCAL_MI_REVIEW_API("LOCAL_MI_REVIEW_API",new HashMap<String,String>(){{
            put("0","事前接口");
            put("1","事中接口");
            put("2","对接医保接口");
            put("3","反馈服务");
        }},"医保审核接口"),*/
        /*LOCAL_MANUAL_REVIEW("LOCAL_MANUAL_REVIEW",new HashMap<String,String>(){{
            put("0","查看");
            put("1","审核");
            put("2","不人工审核");
        }},"人工审核"),*/
        /*LOCAL_SHOW_DIMENSIONS("LOCAL_SHOW_DIMENSIONS",new HashMap<String,String>(){{
            put("0","按主单展示");
            put("1","按明细展示");
            put("2","不展示");
        }},"展示维度"),*/
        /*LOCAL_SHOWFORMAT("LOCAL_SHOWFORMAT",new HashMap<String,String>(){{
            put("0","分TAB页展示");
            put("1","合并单页展示");
        }},"展示形式"),*/

        LOCLA_PRE_DISCHARGE("LOCLA_PRE_DISCHARGE",new HashMap<String,String>(){{
            put("0","未开预出院");
            put("1","已开预出院");
        }},"预出院状态"),
        LOCLA_DISCHARGE_TODAY("LOCLA_DISCHARGE_TODAY",new HashMap<String,String>(){{
            put("0","否");
            put("1","是");
        }},"是否今日出院"),
        LOCLA_BILL_STATE("LOCLA_BILL_STATE",new HashMap<String,String>(){{
            put("0","未结算");
            put("1","已结算");
        }},"结算状态"),
        LOCLA_LABLES("LOCLA_LABLES",new HashMap<String,String>(){{
            put("0","未标记");
            put("1","已标记");
        }},"标记"),
        LOCLA_MRSTATUS("LOCLA_MRSTATUS",new HashMap<String,String>(){{
            put("0","人工未审核");
            put("1","人工已审核");
            put("2","人工审核完成");
        }},"人工审核状态"),
        LOCLA_CLINICALSTATUS("LOCLA_CLINICALSTATUS",new HashMap<String,String>(){{
            put("0","未处理");
            put("1","处理中");
            put("2","处理完成");
            put("3","无需处理");
            put("4","忽略");
            put("5","转自费");
        }},"临床处理状态"),
        LOCLA_AUDITSOURCE("LOCLA_AUDITSOURCE",new HashMap<String,String>(){{
            put("0","医保");
            put("1","院内");
            put("2","人工");
        }},"审核渠道"),

        LOCLA_SELF_EXPENSE("LOCLA_SELF_EXPENSE",new HashMap<String,String>(){{
            put("0","医保");
            put("1","自费");
            put("2","转自费");
        }},"是否自费");

        public String code;
        public Map<String,String> dicts;
        public String desc;

    }

    public static final List<String> DATE_FORMATS = new ArrayList<>();
    // 支持的日期格式
    static {

        // 常见的日期格式
        DATE_FORMATS.add("yyyy-MM-dd HH:mm:ss");
        DATE_FORMATS.add("yyyy/MM/dd HH:mm:ss");
        DATE_FORMATS.add("yyyy-MM-dd");
        DATE_FORMATS.add("yyyy/MM/dd");
        DATE_FORMATS.add("MM/dd/yyyy");
        DATE_FORMATS.add("MM-dd-yyyy");
        DATE_FORMATS.add("dd-MM-yyyy");
        DATE_FORMATS.add("dd/MM/yyyy");
    }
}
