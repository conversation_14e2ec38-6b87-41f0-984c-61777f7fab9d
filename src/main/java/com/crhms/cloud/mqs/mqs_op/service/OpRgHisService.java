package com.crhms.cloud.mqs.mqs_op.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_op.domain.OpRgHis;
import com.crhms.cloud.mqs.mqs_op.mapper.OpRgHisMapper;
import com.crhms.cloud.mqs.mqs_op.service.OpRgHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 门诊审核-门诊挂号审核-历史表(OpRgHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-19 14:40:10
 */
@Service("opRgHisService")
public class OpRgHisService extends ServiceImpl< OpRgHisMapper, OpRgHis> {

}
