package com.crhms.cloud.mqs.sys.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.excel.dto.ExcelExport;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.service.GlobalCache;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.core.service.EngineService;
import com.crhms.cloud.mqs.sys.domain.SysRuleBroadHeading;
import com.crhms.cloud.mqs.sys.domain.SysRules;
import com.crhms.cloud.mqs.sys.domain.SysSceneRule;
import com.crhms.cloud.mqs.sys.dto.RuleConfigQueryVO;
import com.crhms.cloud.mqs.sys.engine.EngineRestful;
import com.crhms.cloud.mqs.sys.mapper.SysRulesMapper;
import com.crhms.cloud.mqs.sys.utils.DictUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 运营管理-规则管理(SysRules)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-16 16:52:44
 */
@Service("sysRulesService")
@Transactional(rollbackFor = Exception.class)
public class SysRulesService extends ServiceImpl<SysRulesMapper, SysRules> {

    @Autowired
    private SysRuleBroadHeadingService broadHeadingService;

    @Autowired
    private SysSceneRuleService sceneRuleService;

    @Autowired
    private GlobalCache globalCache;

    @Autowired
    private EngineRestful engineRestful;

    @Autowired
    private EngineService engineService;



    /**
     * 分页查询
     *
     * @param rule         编码名称 模糊查询
     * @param broadHeading 规则大类
     * @param ruleLevel    规则级别
     * @param isHis        是否查询历史数据
     * @param isMain       是否作用在主单
     * @param ruleType     规则类型
     * @param pageRequest
     * @return
     */
    public List<SysRules> queryByPage(String rule, String broadHeading, String ruleLevel, String isHis, String isMain, String ruleType, String hospitalId, Page pageRequest) {
        return this.baseMapper.queryByPage(rule, broadHeading, ruleLevel, isHis, isMain, ruleType, hospitalId, pageRequest);
    }

    /**
     * 模糊查询规则列表
     *
     * @param rule
     * @param hospitalId
     * @return
     */
    public List<SysRules> seletRuleList(String rule, String hospitalId) {
        return this.baseMapper.seletRuleList(rule, hospitalId);
    }

    /**
     * 新增/更新 规则
     *
     * @param sysRules
     * @return
     */
    public SysRules insertData(SysRules sysRules) {
        this.saveOrUpdate(sysRules);
        return sysRules;
    }

    /**
     * 规则导入
     *
     * @param file
     * @return
     */
    public ResponseEntity<List<Map<String, String>>> importExcel(MultipartFile file) {
        return null;
    }


    /**
     * 规则设置页面 分页查询
     *
     * @param pageInfo the page info
     * @param queryVO  查询参数
     * @return list
     */
    public List<SysRules> queryPageByVO(Page<SysRules> pageInfo, RuleConfigQueryVO queryVO) {

        List<SysRules> result = baseMapper.queryPageByVO(pageInfo, queryVO, LoginContext.getHospitalId());

        // 处理规则来源字段
        DictUtils.translateDict(result);

        // 处理 broadHeading 字段
        for (SysRules rule : result) {
            List<String> codeList = StrUtil.split(rule.getBroadHeading(), ",");
            // 构造结构, 使用工具类: 将code->name
            List<SysRuleBroadHeading> broadHeadingList = new ArrayList<>();
            for (String code : codeList) {
                broadHeadingList.add(SysRuleBroadHeading.builder()
                        .broadHeading(code)
                        .build());
            }
            DictUtils.translateDict(broadHeadingList);
            StringBuilder sb = new StringBuilder();
            for (SysRuleBroadHeading name : broadHeadingList) {
                sb.append(name.getBroadHeading()).append(",");
            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }

            rule.setBroadHeading(sb.toString());
            rule.setBroadHeadingList(codeList);
        }
        return result;
    }

    /**
     * 从引擎同步规则列表
     * <AUTHOR>
     * @date 2023/3/20
     **/
    public void syncEngineRules(){
        // 从引擎查询规则列表
        JSONArray engineRules = engineRestful.queryRuleList();

        // 查询数据库中的规则列表
        List<SysRules> rules = list(new QueryWrapper<SysRules>().select("rule_code").eq(SysRules.FIELD_HOSPITAL_ID,LoginContext.getHospitalId()));
        List<String> ruleCodes = rules.stream().map(i->i.getRuleCode()).collect(Collectors.toList());

        List<SysRules> waitInsert = new ArrayList<>();
        List<SysRuleBroadHeading> waitInsertBroadHeading = new ArrayList<>();

        for(int i=0; i<engineRules.size(); i++){
            JSONObject engineRule = engineRules.getJSONObject(i);

            if(ruleCodes.contains(engineRule.getStr("ruleCode"))){
                continue;
            }

            // 构造
            waitInsert.add(SysRules.builder()
                    .id(Long.valueOf(engineRule.getStr("id")))
                    .ruleCode(engineRule.getStr("ruleCode"))
                    .ruleName(engineRule.getStr("ruleName"))
                    .broadHeading(engineRule.getStr("ruleCategoryCode"))
                    .ruleLevel(engineRule.getStr("resultType"))
                    .primaryCategoryName(engineRule.getStr("primaryCategoryName"))
                    .primaryCategoryCode(engineRule.getStr("primaryCategoryCode"))
                    .secondaryCategoryName(engineRule.getStr("secondaryCategoryName"))
                    .secondaryCategoryCode(engineRule.getStr("secondaryCategoryCode"))
                    .isHis(engineRule.getStr("checkHistory", "0"))
                    .isMain(engineRule.getStr("actOnMain", "0"))
                    .remake(engineRule.getStr("ruleDescription", ""))
                    .enable("1")
                    .ruleDataDesc(engineRule.getStr("ruleDataDesc"))
                    .ruleLogicDesc(engineRule.getStr("ruleLogicDesc"))
                    .dataConfigCode("OTHER_BUILT_IN")
                    .ruleType(String.valueOf(engineRule.getInt("ruleType", 1)))
                    .hospitalId(LoginContext.getHospitalId())
                    .build());

            if(StrUtil.isNotEmpty(engineRule.getStr("ruleCategoryCode"))){
                waitInsertBroadHeading.add(SysRuleBroadHeading.builder().hospitalId(LoginContext.getHospitalId())
                        .ruleCode(engineRule.getStr("ruleCode"))
                        .broadHeading(engineRule.getStr("ruleCategoryCode")).build());
            }
        }

        if(CollUtil.isNotEmpty(waitInsertBroadHeading)){
            broadHeadingService.saveBatch(waitInsertBroadHeading);
        }

        if(CollUtil.isNotEmpty(waitInsert)){
            saveBatch(waitInsert);
            // 刷新规则缓存
            globalCache.reflashRulesCache(LoginContext.getHospitalId());
        }
    }


    /**
     * 新增/修改规则配置信息
     * @param rule 规则信息
     */
    public void submit(SysRules rule) {
        if (rule.getId() == null) {
            //界面新增自定义规则开头首字母
            rule.setRuleCode("C" + rule.getRuleCode());
        }
        // 规则与规则大类关系
        List<SysRuleBroadHeading> broadHeadingList = new ArrayList<>();
        for (String broadHeading : rule.getBroadHeadingList()) {
            broadHeadingList.add(SysRuleBroadHeading.builder().ruleCode(rule.getRuleCode()).broadHeading(broadHeading).hospitalId(LoginContext.getHospitalId()).build());
        }

        int insertOrUpdate = 1;
        if (rule.getId() == null) {
            // 院区ID
            rule.setHospitalId(LoginContext.getHospitalId());
            // 新增规则的规则类型默认是医院自定义
            rule.setRuleType("1");
            // 新增规则默认禁用
            rule.setEnable("0");
            // 新增规则属于"用户自定义"
            rule.setDataConfigCode("CUSTOM");

            save(rule);
            broadHeadingService.saveBatch(broadHeadingList);
            // 将数据同步至引擎
            engineRestful.submitRule(insertOrUpdate, rule);
        } else {
            insertOrUpdate = 2;
            // 更新规则
            updateById(rule);

            // 删除之前的对应关系
            broadHeadingService.remove(new LambdaQueryWrapper<SysRuleBroadHeading>().eq(SysRuleBroadHeading::getRuleCode, rule.getRuleCode()));
            // 保存新的对应关系
            broadHeadingService.saveBatch(broadHeadingList);
            //
            if ("1".equals(rule.getRuleType())) {
                // 将数据同步至引擎
                //engineRestful.submitRule(insertOrUpdate, rule);
            }
        }


        // 刷新规则缓存
        globalCache.reflashRulesCache(LoginContext.getHospitalId());
    }

    /**
     * 规则导出Excel
     *
     * @param response 请求响应
     * @param queryVO  查询参数
     */
    public void export(HttpServletResponse response, @RequestBody(required = false) RuleConfigQueryVO queryVO) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();


        heads.add(CollUtil.list(true, "RULE_CODE", "RULE_NAME", "RULE_LEVEL", "BROAD_HEADING", "IS_HIS",
                "IS_MAIN", "REMAKE", "RULE_TYPE", "ENABLE"));
        heads.add(CollUtil.list(true, "规则编码", "规则名称", "规则级别", "规则大类", "是否查询历史数据",
                "是否作用在主单", "规则说明", "规则来源", "是否启用"));

        List<SysRules> list = queryPageByVO(null, queryVO);

        StringBuilder fileName = new StringBuilder();
        fileName.append("审核管理-规则设置");

        for (SysRules item : list) {
            List<Object> line = CollUtil.list(true,
                    item.getRuleCode(), item.getRuleName(), item.getRuleLevel(), item.getBroadHeading(), "0".equals(item.getIsHis())?"否":"是",
                    "0".equals(item.getIsMain())?"否":"是", item.getRemake(), item.getRuleType(), "0".equals(item.getEnable())?"否":"是"
            );
            datas.add(line);
        }

        MqsUtils.buildExportFileNameSuffix(fileName, null, null);

        List<ExcelExport> exports = new ArrayList<>(1);
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }

    /**
     * 根据id删除规则
     * @param id 规则id
     * @return true or false
     */
    public boolean deleteById(Long id) {

        // 删除mqs_sys_scene_rule
        SysRules rule = getById(id);
        sceneRuleService.remove(new LambdaQueryWrapper<SysSceneRule>().eq(SysSceneRule::getRuleCode, rule.getRuleCode()));
        // 删除mqs_sys_rule_broad_heading
        List<SysRuleBroadHeading> broadHeadings = broadHeadingService.list(new LambdaQueryWrapper<SysRuleBroadHeading>().eq(SysRuleBroadHeading::getRuleCode, rule.getRuleCode()));
        List<Long> broadHeadingIds = broadHeadings.stream().map(SysRuleBroadHeading::getId).collect(Collectors.toList());
        broadHeadingService.removeByIds(broadHeadingIds);
        // 删除mqs_sys_rules
        this.removeById(id);


        // 删除成功, 更新缓存
        globalCache.reflashRulesCache(LoginContext.getHospitalId());
        globalCache.reflashSceneRuleCache(LoginContext.getHospitalId());
        return true;
    }

    /**
     * 根据id批量删除 - 逻辑删除
     * @param ids id集合
     */
    public void deleteByIds(List<Long> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            // 逻辑删除sys_rule表
            baseMapper.update(new SysRules(), new LambdaUpdateWrapper<SysRules>().set(SysRules::getDeleted, "1").in(SysRules::getId, ids));
            // 刷新规则缓存
            globalCache.reflashRulesCache(LoginContext.getHospitalId());
            // 刷新审核场景规则缓存
            globalCache.reflashSceneRuleCache(LoginContext.getHospitalId());
        }
    }

    public String getRuleLogicDesc(String ruleCode, String hospitalId) {
        SysRules sysRule = this.getOne(new QueryWrapper<SysRules>().select("rule_logic_desc").eq(SysRules.FIELD_RULE_CODE, ruleCode).eq(SysRules.FIELD_HOSPITAL_ID, hospitalId));
        if(!Objects.isNull(sysRule)){
            return sysRule.getRuleLogicDesc();
        }
        return "";
    }


    public String getRuleDataDesc(String ruleCode, String hospitalId) {
        SysRules sysRule = this.getOne(new QueryWrapper<SysRules>().select("rule_data_desc").eq(SysRules.FIELD_RULE_CODE, ruleCode).eq(SysRules.FIELD_HOSPITAL_ID, hospitalId));
        if(!Objects.isNull(sysRule)){
            return sysRule.getRuleDataDesc();
        }
        return "";
    }
}
