/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.17.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.crhms.cloud.mqs.core.engineService;

@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.17.0)", date = "2023-03-01")
@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
public class engineService {

  public interface Iface {

    public String ping() throws org.apache.thrift.TException;

    public String execRules(String jsonStr) throws org.apache.thrift.TException;

    public String execRulesByCodes(String data, String rules) throws org.apache.thrift.TException;

    public String incrementalRules(String jsonStr) throws org.apache.thrift.TException;

    public String removeRules(String ruleCodes) throws org.apache.thrift.TException;

    public String rulesPage(long pageNum, long pageSize, String ruleName, String enable) throws org.apache.thrift.TException;

    public String rulesList() throws org.apache.thrift.TException;

    public String saveRecord(String data) throws org.apache.thrift.TException;

    public String deleteRecord(String patientId, String admissionNo) throws org.apache.thrift.TException;

    public String deleteFeeList(String patientId, String admissionNo, String id) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void ping(org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException;

    public void execRules(String jsonStr, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException;

    public void execRulesByCodes(String data, String rules, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException;

    public void incrementalRules(String jsonStr, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException;

    public void removeRules(String ruleCodes, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException;

    public void rulesPage(long pageNum, long pageSize, String ruleName, String enable, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException;

    public void rulesList(org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException;

    public void saveRecord(String data, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException;

    public void deleteRecord(String patientId, String admissionNo, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException;

    public void deleteFeeList(String patientId, String admissionNo, String id, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      @Override
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      @Override
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    @Override
    public String ping() throws org.apache.thrift.TException
    {
      send_ping();
      return recv_ping();
    }

    public void send_ping() throws org.apache.thrift.TException
    {
      ping_args args = new ping_args();
      sendBase("ping", args);
    }

    public String recv_ping() throws org.apache.thrift.TException
    {
      ping_result result = new ping_result();
      receiveBase(result, "ping");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "ping failed: unknown result");
    }

    @Override
    public String execRules(String jsonStr) throws org.apache.thrift.TException
    {
      send_execRules(jsonStr);
      return recv_execRules();
    }

    public void send_execRules(String jsonStr) throws org.apache.thrift.TException
    {
      execRules_args args = new execRules_args();
      args.setJsonStr(jsonStr);
      sendBase("execRules", args);
    }

    public String recv_execRules() throws org.apache.thrift.TException
    {
      execRules_result result = new execRules_result();
      receiveBase(result, "execRules");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "execRules failed: unknown result");
    }

    @Override
    public String execRulesByCodes(String data, String rules) throws org.apache.thrift.TException
    {
      send_execRulesByCodes(data, rules);
      return recv_execRulesByCodes();
    }

    public void send_execRulesByCodes(String data, String rules) throws org.apache.thrift.TException
    {
      execRulesByCodes_args args = new execRulesByCodes_args();
      args.setData(data);
      args.setRules(rules);
      sendBase("execRulesByCodes", args);
    }

    public String recv_execRulesByCodes() throws org.apache.thrift.TException
    {
      execRulesByCodes_result result = new execRulesByCodes_result();
      receiveBase(result, "execRulesByCodes");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "execRulesByCodes failed: unknown result");
    }

    @Override
    public String incrementalRules(String jsonStr) throws org.apache.thrift.TException
    {
      send_incrementalRules(jsonStr);
      return recv_incrementalRules();
    }

    public void send_incrementalRules(String jsonStr) throws org.apache.thrift.TException
    {
      incrementalRules_args args = new incrementalRules_args();
      args.setJsonStr(jsonStr);
      sendBase("incrementalRules", args);
    }

    public String recv_incrementalRules() throws org.apache.thrift.TException
    {
      incrementalRules_result result = new incrementalRules_result();
      receiveBase(result, "incrementalRules");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "incrementalRules failed: unknown result");
    }

    @Override
    public String removeRules(String ruleCodes) throws org.apache.thrift.TException
    {
      send_removeRules(ruleCodes);
      return recv_removeRules();
    }

    public void send_removeRules(String ruleCodes) throws org.apache.thrift.TException
    {
      removeRules_args args = new removeRules_args();
      args.setRuleCodes(ruleCodes);
      sendBase("removeRules", args);
    }

    public String recv_removeRules() throws org.apache.thrift.TException
    {
      removeRules_result result = new removeRules_result();
      receiveBase(result, "removeRules");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "removeRules failed: unknown result");
    }

    @Override
    public String rulesPage(long pageNum, long pageSize, String ruleName, String enable) throws org.apache.thrift.TException
    {
      send_rulesPage(pageNum, pageSize, ruleName, enable);
      return recv_rulesPage();
    }

    public void send_rulesPage(long pageNum, long pageSize, String ruleName, String enable) throws org.apache.thrift.TException
    {
      rulesPage_args args = new rulesPage_args();
      args.setPageNum(pageNum);
      args.setPageSize(pageSize);
      args.setRuleName(ruleName);
      args.setEnable(enable);
      sendBase("rulesPage", args);
    }

    public String recv_rulesPage() throws org.apache.thrift.TException
    {
      rulesPage_result result = new rulesPage_result();
      receiveBase(result, "rulesPage");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "rulesPage failed: unknown result");
    }

    @Override
    public String rulesList() throws org.apache.thrift.TException
    {
      send_rulesList();
      return recv_rulesList();
    }

    public void send_rulesList() throws org.apache.thrift.TException
    {
      rulesList_args args = new rulesList_args();
      sendBase("rulesList", args);
    }

    public String recv_rulesList() throws org.apache.thrift.TException
    {
      rulesList_result result = new rulesList_result();
      receiveBase(result, "rulesList");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "rulesList failed: unknown result");
    }

    @Override
    public String saveRecord(String data) throws org.apache.thrift.TException
    {
      send_saveRecord(data);
      return recv_saveRecord();
    }

    public void send_saveRecord(String data) throws org.apache.thrift.TException
    {
      saveRecord_args args = new saveRecord_args();
      args.setData(data);
      sendBase("saveRecord", args);
    }

    public String recv_saveRecord() throws org.apache.thrift.TException
    {
      saveRecord_result result = new saveRecord_result();
      receiveBase(result, "saveRecord");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "saveRecord failed: unknown result");
    }

    @Override
    public String deleteRecord(String patientId, String admissionNo) throws org.apache.thrift.TException
    {
      send_deleteRecord(patientId, admissionNo);
      return recv_deleteRecord();
    }

    public void send_deleteRecord(String patientId, String admissionNo) throws org.apache.thrift.TException
    {
      deleteRecord_args args = new deleteRecord_args();
      args.setPatientId(patientId);
      args.setAdmissionNo(admissionNo);
      sendBase("deleteRecord", args);
    }

    public String recv_deleteRecord() throws org.apache.thrift.TException
    {
      deleteRecord_result result = new deleteRecord_result();
      receiveBase(result, "deleteRecord");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "deleteRecord failed: unknown result");
    }

    @Override
    public String deleteFeeList(String patientId, String admissionNo, String id) throws org.apache.thrift.TException
    {
      send_deleteFeeList(patientId, admissionNo, id);
      return recv_deleteFeeList();
    }

    public void send_deleteFeeList(String patientId, String admissionNo, String id) throws org.apache.thrift.TException
    {
      deleteFeeList_args args = new deleteFeeList_args();
      args.setPatientId(patientId);
      args.setAdmissionNo(admissionNo);
      args.setId(id);
      sendBase("deleteFeeList", args);
    }

    public String recv_deleteFeeList() throws org.apache.thrift.TException
    {
      deleteFeeList_result result = new deleteFeeList_result();
      receiveBase(result, "deleteFeeList");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "deleteFeeList failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
    @Override
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    @Override
    public void ping(org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
      checkReady();
      ping_call method_call = new ping_call(resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class ping_call extends org.apache.thrift.async.TAsyncMethodCall<String> {
      public ping_call(org.apache.thrift.async.AsyncMethodCallback<String> resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
      }

      @Override
      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("ping", org.apache.thrift.protocol.TMessageType.CALL, 0));
        ping_args args = new ping_args();
        args.write(prot);
        prot.writeMessageEnd();
      }

      @Override
      public String getResult() throws org.apache.thrift.TException {
        if (getState() != State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_ping();
      }
    }

    @Override
    public void execRules(String jsonStr, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
      checkReady();
      execRules_call method_call = new execRules_call(jsonStr, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class execRules_call extends org.apache.thrift.async.TAsyncMethodCall<String> {
      private String jsonStr;
      public execRules_call(String jsonStr, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.jsonStr = jsonStr;
      }

      @Override
      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("execRules", org.apache.thrift.protocol.TMessageType.CALL, 0));
        execRules_args args = new execRules_args();
        args.setJsonStr(jsonStr);
        args.write(prot);
        prot.writeMessageEnd();
      }

      @Override
      public String getResult() throws org.apache.thrift.TException {
        if (getState() != State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_execRules();
      }
    }

    @Override
    public void execRulesByCodes(String data, String rules, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
      checkReady();
      execRulesByCodes_call method_call = new execRulesByCodes_call(data, rules, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class execRulesByCodes_call extends org.apache.thrift.async.TAsyncMethodCall<String> {
      private String data;
      private String rules;
      public execRulesByCodes_call(String data, String rules, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.data = data;
        this.rules = rules;
      }

      @Override
      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("execRulesByCodes", org.apache.thrift.protocol.TMessageType.CALL, 0));
        execRulesByCodes_args args = new execRulesByCodes_args();
        args.setData(data);
        args.setRules(rules);
        args.write(prot);
        prot.writeMessageEnd();
      }

      @Override
      public String getResult() throws org.apache.thrift.TException {
        if (getState() != State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_execRulesByCodes();
      }
    }

    @Override
    public void incrementalRules(String jsonStr, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
      checkReady();
      incrementalRules_call method_call = new incrementalRules_call(jsonStr, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class incrementalRules_call extends org.apache.thrift.async.TAsyncMethodCall<String> {
      private String jsonStr;
      public incrementalRules_call(String jsonStr, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.jsonStr = jsonStr;
      }

      @Override
      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("incrementalRules", org.apache.thrift.protocol.TMessageType.CALL, 0));
        incrementalRules_args args = new incrementalRules_args();
        args.setJsonStr(jsonStr);
        args.write(prot);
        prot.writeMessageEnd();
      }

      @Override
      public String getResult() throws org.apache.thrift.TException {
        if (getState() != State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_incrementalRules();
      }
    }

    @Override
    public void removeRules(String ruleCodes, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
      checkReady();
      removeRules_call method_call = new removeRules_call(ruleCodes, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class removeRules_call extends org.apache.thrift.async.TAsyncMethodCall<String> {
      private String ruleCodes;
      public removeRules_call(String ruleCodes, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.ruleCodes = ruleCodes;
      }

      @Override
      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("removeRules", org.apache.thrift.protocol.TMessageType.CALL, 0));
        removeRules_args args = new removeRules_args();
        args.setRuleCodes(ruleCodes);
        args.write(prot);
        prot.writeMessageEnd();
      }

      @Override
      public String getResult() throws org.apache.thrift.TException {
        if (getState() != State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_removeRules();
      }
    }

    @Override
    public void rulesPage(long pageNum, long pageSize, String ruleName, String enable, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
      checkReady();
      rulesPage_call method_call = new rulesPage_call(pageNum, pageSize, ruleName, enable, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class rulesPage_call extends org.apache.thrift.async.TAsyncMethodCall<String> {
      private long pageNum;
      private long pageSize;
      private String ruleName;
      private String enable;
      public rulesPage_call(long pageNum, long pageSize, String ruleName, String enable, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.ruleName = ruleName;
        this.enable = enable;
      }

      @Override
      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("rulesPage", org.apache.thrift.protocol.TMessageType.CALL, 0));
        rulesPage_args args = new rulesPage_args();
        args.setPageNum(pageNum);
        args.setPageSize(pageSize);
        args.setRuleName(ruleName);
        args.setEnable(enable);
        args.write(prot);
        prot.writeMessageEnd();
      }

      @Override
      public String getResult() throws org.apache.thrift.TException {
        if (getState() != State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_rulesPage();
      }
    }

    @Override
    public void rulesList(org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
      checkReady();
      rulesList_call method_call = new rulesList_call(resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class rulesList_call extends org.apache.thrift.async.TAsyncMethodCall<String> {
      public rulesList_call(org.apache.thrift.async.AsyncMethodCallback<String> resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
      }

      @Override
      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("rulesList", org.apache.thrift.protocol.TMessageType.CALL, 0));
        rulesList_args args = new rulesList_args();
        args.write(prot);
        prot.writeMessageEnd();
      }

      @Override
      public String getResult() throws org.apache.thrift.TException {
        if (getState() != State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_rulesList();
      }
    }

    @Override
    public void saveRecord(String data, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
      checkReady();
      saveRecord_call method_call = new saveRecord_call(data, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class saveRecord_call extends org.apache.thrift.async.TAsyncMethodCall<String> {
      private String data;
      public saveRecord_call(String data, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.data = data;
      }

      @Override
      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("saveRecord", org.apache.thrift.protocol.TMessageType.CALL, 0));
        saveRecord_args args = new saveRecord_args();
        args.setData(data);
        args.write(prot);
        prot.writeMessageEnd();
      }

      @Override
      public String getResult() throws org.apache.thrift.TException {
        if (getState() != State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_saveRecord();
      }
    }

    @Override
    public void deleteRecord(String patientId, String admissionNo, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
      checkReady();
      deleteRecord_call method_call = new deleteRecord_call(patientId, admissionNo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class deleteRecord_call extends org.apache.thrift.async.TAsyncMethodCall<String> {
      private String patientId;
      private String admissionNo;
      public deleteRecord_call(String patientId, String admissionNo, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.patientId = patientId;
        this.admissionNo = admissionNo;
      }

      @Override
      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("deleteRecord", org.apache.thrift.protocol.TMessageType.CALL, 0));
        deleteRecord_args args = new deleteRecord_args();
        args.setPatientId(patientId);
        args.setAdmissionNo(admissionNo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      @Override
      public String getResult() throws org.apache.thrift.TException {
        if (getState() != State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_deleteRecord();
      }
    }

    @Override
    public void deleteFeeList(String patientId, String admissionNo, String id, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
      checkReady();
      deleteFeeList_call method_call = new deleteFeeList_call(patientId, admissionNo, id, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class deleteFeeList_call extends org.apache.thrift.async.TAsyncMethodCall<String> {
      private String patientId;
      private String admissionNo;
      private String id;
      public deleteFeeList_call(String patientId, String admissionNo, String id, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.patientId = patientId;
        this.admissionNo = admissionNo;
        this.id = id;
      }

      @Override
      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("deleteFeeList", org.apache.thrift.protocol.TMessageType.CALL, 0));
        deleteFeeList_args args = new deleteFeeList_args();
        args.setPatientId(patientId);
        args.setAdmissionNo(admissionNo);
        args.setId(id);
        args.write(prot);
        prot.writeMessageEnd();
      }

      @Override
      public String getResult() throws org.apache.thrift.TException {
        if (getState() != State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_deleteFeeList();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final org.slf4j.Logger _LOGGER = org.slf4j.LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new java.util.HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, java.util.Map<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> java.util.Map<String,  org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>> getProcessMap(java.util.Map<String, org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("ping", new ping());
      processMap.put("execRules", new execRules());
      processMap.put("execRulesByCodes", new execRulesByCodes());
      processMap.put("incrementalRules", new incrementalRules());
      processMap.put("removeRules", new removeRules());
      processMap.put("rulesPage", new rulesPage());
      processMap.put("rulesList", new rulesList());
      processMap.put("saveRecord", new saveRecord());
      processMap.put("deleteRecord", new deleteRecord());
      processMap.put("deleteFeeList", new deleteFeeList());
      return processMap;
    }

    public static class ping<I extends Iface> extends org.apache.thrift.ProcessFunction<I, ping_args> {
      public ping() {
        super("ping");
      }

      @Override
      public ping_args getEmptyArgsInstance() {
        return new ping_args();
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      protected boolean rethrowUnhandledExceptions() {
        return false;
      }

      @Override
      public ping_result getResult(I iface, ping_args args) throws org.apache.thrift.TException {
        ping_result result = new ping_result();
        result.success = iface.ping();
        return result;
      }
    }

    public static class execRules<I extends Iface> extends org.apache.thrift.ProcessFunction<I, execRules_args> {
      public execRules() {
        super("execRules");
      }

      @Override
      public execRules_args getEmptyArgsInstance() {
        return new execRules_args();
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      protected boolean rethrowUnhandledExceptions() {
        return false;
      }

      @Override
      public execRules_result getResult(I iface, execRules_args args) throws org.apache.thrift.TException {
        execRules_result result = new execRules_result();
        result.success = iface.execRules(args.jsonStr);
        return result;
      }
    }

    public static class execRulesByCodes<I extends Iface> extends org.apache.thrift.ProcessFunction<I, execRulesByCodes_args> {
      public execRulesByCodes() {
        super("execRulesByCodes");
      }

      @Override
      public execRulesByCodes_args getEmptyArgsInstance() {
        return new execRulesByCodes_args();
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      protected boolean rethrowUnhandledExceptions() {
        return false;
      }

      @Override
      public execRulesByCodes_result getResult(I iface, execRulesByCodes_args args) throws org.apache.thrift.TException {
        execRulesByCodes_result result = new execRulesByCodes_result();
        result.success = iface.execRulesByCodes(args.data, args.rules);
        return result;
      }
    }

    public static class incrementalRules<I extends Iface> extends org.apache.thrift.ProcessFunction<I, incrementalRules_args> {
      public incrementalRules() {
        super("incrementalRules");
      }

      @Override
      public incrementalRules_args getEmptyArgsInstance() {
        return new incrementalRules_args();
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      protected boolean rethrowUnhandledExceptions() {
        return false;
      }

      @Override
      public incrementalRules_result getResult(I iface, incrementalRules_args args) throws org.apache.thrift.TException {
        incrementalRules_result result = new incrementalRules_result();
        result.success = iface.incrementalRules(args.jsonStr);
        return result;
      }
    }

    public static class removeRules<I extends Iface> extends org.apache.thrift.ProcessFunction<I, removeRules_args> {
      public removeRules() {
        super("removeRules");
      }

      @Override
      public removeRules_args getEmptyArgsInstance() {
        return new removeRules_args();
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      protected boolean rethrowUnhandledExceptions() {
        return false;
      }

      @Override
      public removeRules_result getResult(I iface, removeRules_args args) throws org.apache.thrift.TException {
        removeRules_result result = new removeRules_result();
        result.success = iface.removeRules(args.ruleCodes);
        return result;
      }
    }

    public static class rulesPage<I extends Iface> extends org.apache.thrift.ProcessFunction<I, rulesPage_args> {
      public rulesPage() {
        super("rulesPage");
      }

      @Override
      public rulesPage_args getEmptyArgsInstance() {
        return new rulesPage_args();
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      protected boolean rethrowUnhandledExceptions() {
        return false;
      }

      @Override
      public rulesPage_result getResult(I iface, rulesPage_args args) throws org.apache.thrift.TException {
        rulesPage_result result = new rulesPage_result();
        result.success = iface.rulesPage(args.pageNum, args.pageSize, args.ruleName, args.enable);
        return result;
      }
    }

    public static class rulesList<I extends Iface> extends org.apache.thrift.ProcessFunction<I, rulesList_args> {
      public rulesList() {
        super("rulesList");
      }

      @Override
      public rulesList_args getEmptyArgsInstance() {
        return new rulesList_args();
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      protected boolean rethrowUnhandledExceptions() {
        return false;
      }

      @Override
      public rulesList_result getResult(I iface, rulesList_args args) throws org.apache.thrift.TException {
        rulesList_result result = new rulesList_result();
        result.success = iface.rulesList();
        return result;
      }
    }

    public static class saveRecord<I extends Iface> extends org.apache.thrift.ProcessFunction<I, saveRecord_args> {
      public saveRecord() {
        super("saveRecord");
      }

      @Override
      public saveRecord_args getEmptyArgsInstance() {
        return new saveRecord_args();
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      protected boolean rethrowUnhandledExceptions() {
        return false;
      }

      @Override
      public saveRecord_result getResult(I iface, saveRecord_args args) throws org.apache.thrift.TException {
        saveRecord_result result = new saveRecord_result();
        result.success = iface.saveRecord(args.data);
        return result;
      }
    }

    public static class deleteRecord<I extends Iface> extends org.apache.thrift.ProcessFunction<I, deleteRecord_args> {
      public deleteRecord() {
        super("deleteRecord");
      }

      @Override
      public deleteRecord_args getEmptyArgsInstance() {
        return new deleteRecord_args();
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      protected boolean rethrowUnhandledExceptions() {
        return false;
      }

      @Override
      public deleteRecord_result getResult(I iface, deleteRecord_args args) throws org.apache.thrift.TException {
        deleteRecord_result result = new deleteRecord_result();
        result.success = iface.deleteRecord(args.patientId, args.admissionNo);
        return result;
      }
    }

    public static class deleteFeeList<I extends Iface> extends org.apache.thrift.ProcessFunction<I, deleteFeeList_args> {
      public deleteFeeList() {
        super("deleteFeeList");
      }

      @Override
      public deleteFeeList_args getEmptyArgsInstance() {
        return new deleteFeeList_args();
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      protected boolean rethrowUnhandledExceptions() {
        return false;
      }

      @Override
      public deleteFeeList_result getResult(I iface, deleteFeeList_args args) throws org.apache.thrift.TException {
        deleteFeeList_result result = new deleteFeeList_result();
        result.success = iface.deleteFeeList(args.patientId, args.admissionNo, args.id);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final org.slf4j.Logger _LOGGER = org.slf4j.LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new java.util.HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, java.util.Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> java.util.Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(java.util.Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("ping", new ping());
      processMap.put("execRules", new execRules());
      processMap.put("execRulesByCodes", new execRulesByCodes());
      processMap.put("incrementalRules", new incrementalRules());
      processMap.put("removeRules", new removeRules());
      processMap.put("rulesPage", new rulesPage());
      processMap.put("rulesList", new rulesList());
      processMap.put("saveRecord", new saveRecord());
      processMap.put("deleteRecord", new deleteRecord());
      processMap.put("deleteFeeList", new deleteFeeList());
      return processMap;
    }

    public static class ping<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, ping_args, String> {
      public ping() {
        super("ping");
      }

      @Override
      public ping_args getEmptyArgsInstance() {
        return new ping_args();
      }

      @Override
      public org.apache.thrift.async.AsyncMethodCallback<String> getResultHandler(final org.apache.thrift.server.AbstractNonblockingServer.AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new org.apache.thrift.async.AsyncMethodCallback<String>() {
          @Override
          public void onComplete(String o) {
            ping_result result = new ping_result();
            result.success = o;
            try {
              fcall.sendResponse(fb, result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
            } catch (org.apache.thrift.transport.TTransportException e) {
              _LOGGER.error("TTransportException writing to internal frame buffer", e);
              fb.close();
            } catch (Exception e) {
              _LOGGER.error("Exception writing to internal frame buffer", e);
              onError(e);
            }
          }
          @Override
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TSerializable msg;
            ping_result result = new ping_result();
            if (e instanceof org.apache.thrift.transport.TTransportException) {
              _LOGGER.error("TTransportException inside handler", e);
              fb.close();
              return;
            } else if (e instanceof org.apache.thrift.TApplicationException) {
              _LOGGER.error("TApplicationException inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TApplicationException)e;
            } else {
              _LOGGER.error("Exception inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
            } catch (Exception ex) {
              _LOGGER.error("Exception writing to internal frame buffer", ex);
              fb.close();
            }
          }
        };
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      public void start(I iface, ping_args args, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
        iface.ping(resultHandler);
      }
    }

    public static class execRules<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, execRules_args, String> {
      public execRules() {
        super("execRules");
      }

      @Override
      public execRules_args getEmptyArgsInstance() {
        return new execRules_args();
      }

      @Override
      public org.apache.thrift.async.AsyncMethodCallback<String> getResultHandler(final org.apache.thrift.server.AbstractNonblockingServer.AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new org.apache.thrift.async.AsyncMethodCallback<String>() {
          @Override
          public void onComplete(String o) {
            execRules_result result = new execRules_result();
            result.success = o;
            try {
              fcall.sendResponse(fb, result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
            } catch (org.apache.thrift.transport.TTransportException e) {
              _LOGGER.error("TTransportException writing to internal frame buffer", e);
              fb.close();
            } catch (Exception e) {
              _LOGGER.error("Exception writing to internal frame buffer", e);
              onError(e);
            }
          }
          @Override
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TSerializable msg;
            execRules_result result = new execRules_result();
            if (e instanceof org.apache.thrift.transport.TTransportException) {
              _LOGGER.error("TTransportException inside handler", e);
              fb.close();
              return;
            } else if (e instanceof org.apache.thrift.TApplicationException) {
              _LOGGER.error("TApplicationException inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TApplicationException)e;
            } else {
              _LOGGER.error("Exception inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
            } catch (Exception ex) {
              _LOGGER.error("Exception writing to internal frame buffer", ex);
              fb.close();
            }
          }
        };
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      public void start(I iface, execRules_args args, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
        iface.execRules(args.jsonStr,resultHandler);
      }
    }

    public static class execRulesByCodes<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, execRulesByCodes_args, String> {
      public execRulesByCodes() {
        super("execRulesByCodes");
      }

      @Override
      public execRulesByCodes_args getEmptyArgsInstance() {
        return new execRulesByCodes_args();
      }

      @Override
      public org.apache.thrift.async.AsyncMethodCallback<String> getResultHandler(final org.apache.thrift.server.AbstractNonblockingServer.AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new org.apache.thrift.async.AsyncMethodCallback<String>() {
          @Override
          public void onComplete(String o) {
            execRulesByCodes_result result = new execRulesByCodes_result();
            result.success = o;
            try {
              fcall.sendResponse(fb, result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
            } catch (org.apache.thrift.transport.TTransportException e) {
              _LOGGER.error("TTransportException writing to internal frame buffer", e);
              fb.close();
            } catch (Exception e) {
              _LOGGER.error("Exception writing to internal frame buffer", e);
              onError(e);
            }
          }
          @Override
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TSerializable msg;
            execRulesByCodes_result result = new execRulesByCodes_result();
            if (e instanceof org.apache.thrift.transport.TTransportException) {
              _LOGGER.error("TTransportException inside handler", e);
              fb.close();
              return;
            } else if (e instanceof org.apache.thrift.TApplicationException) {
              _LOGGER.error("TApplicationException inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TApplicationException)e;
            } else {
              _LOGGER.error("Exception inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
            } catch (Exception ex) {
              _LOGGER.error("Exception writing to internal frame buffer", ex);
              fb.close();
            }
          }
        };
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      public void start(I iface, execRulesByCodes_args args, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
        iface.execRulesByCodes(args.data, args.rules,resultHandler);
      }
    }

    public static class incrementalRules<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, incrementalRules_args, String> {
      public incrementalRules() {
        super("incrementalRules");
      }

      @Override
      public incrementalRules_args getEmptyArgsInstance() {
        return new incrementalRules_args();
      }

      @Override
      public org.apache.thrift.async.AsyncMethodCallback<String> getResultHandler(final org.apache.thrift.server.AbstractNonblockingServer.AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new org.apache.thrift.async.AsyncMethodCallback<String>() {
          @Override
          public void onComplete(String o) {
            incrementalRules_result result = new incrementalRules_result();
            result.success = o;
            try {
              fcall.sendResponse(fb, result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
            } catch (org.apache.thrift.transport.TTransportException e) {
              _LOGGER.error("TTransportException writing to internal frame buffer", e);
              fb.close();
            } catch (Exception e) {
              _LOGGER.error("Exception writing to internal frame buffer", e);
              onError(e);
            }
          }
          @Override
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TSerializable msg;
            incrementalRules_result result = new incrementalRules_result();
            if (e instanceof org.apache.thrift.transport.TTransportException) {
              _LOGGER.error("TTransportException inside handler", e);
              fb.close();
              return;
            } else if (e instanceof org.apache.thrift.TApplicationException) {
              _LOGGER.error("TApplicationException inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TApplicationException)e;
            } else {
              _LOGGER.error("Exception inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
            } catch (Exception ex) {
              _LOGGER.error("Exception writing to internal frame buffer", ex);
              fb.close();
            }
          }
        };
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      public void start(I iface, incrementalRules_args args, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
        iface.incrementalRules(args.jsonStr,resultHandler);
      }
    }

    public static class removeRules<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, removeRules_args, String> {
      public removeRules() {
        super("removeRules");
      }

      @Override
      public removeRules_args getEmptyArgsInstance() {
        return new removeRules_args();
      }

      @Override
      public org.apache.thrift.async.AsyncMethodCallback<String> getResultHandler(final org.apache.thrift.server.AbstractNonblockingServer.AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new org.apache.thrift.async.AsyncMethodCallback<String>() {
          @Override
          public void onComplete(String o) {
            removeRules_result result = new removeRules_result();
            result.success = o;
            try {
              fcall.sendResponse(fb, result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
            } catch (org.apache.thrift.transport.TTransportException e) {
              _LOGGER.error("TTransportException writing to internal frame buffer", e);
              fb.close();
            } catch (Exception e) {
              _LOGGER.error("Exception writing to internal frame buffer", e);
              onError(e);
            }
          }
          @Override
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TSerializable msg;
            removeRules_result result = new removeRules_result();
            if (e instanceof org.apache.thrift.transport.TTransportException) {
              _LOGGER.error("TTransportException inside handler", e);
              fb.close();
              return;
            } else if (e instanceof org.apache.thrift.TApplicationException) {
              _LOGGER.error("TApplicationException inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TApplicationException)e;
            } else {
              _LOGGER.error("Exception inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
            } catch (Exception ex) {
              _LOGGER.error("Exception writing to internal frame buffer", ex);
              fb.close();
            }
          }
        };
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      public void start(I iface, removeRules_args args, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
        iface.removeRules(args.ruleCodes,resultHandler);
      }
    }

    public static class rulesPage<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, rulesPage_args, String> {
      public rulesPage() {
        super("rulesPage");
      }

      @Override
      public rulesPage_args getEmptyArgsInstance() {
        return new rulesPage_args();
      }

      @Override
      public org.apache.thrift.async.AsyncMethodCallback<String> getResultHandler(final org.apache.thrift.server.AbstractNonblockingServer.AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new org.apache.thrift.async.AsyncMethodCallback<String>() {
          @Override
          public void onComplete(String o) {
            rulesPage_result result = new rulesPage_result();
            result.success = o;
            try {
              fcall.sendResponse(fb, result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
            } catch (org.apache.thrift.transport.TTransportException e) {
              _LOGGER.error("TTransportException writing to internal frame buffer", e);
              fb.close();
            } catch (Exception e) {
              _LOGGER.error("Exception writing to internal frame buffer", e);
              onError(e);
            }
          }
          @Override
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TSerializable msg;
            rulesPage_result result = new rulesPage_result();
            if (e instanceof org.apache.thrift.transport.TTransportException) {
              _LOGGER.error("TTransportException inside handler", e);
              fb.close();
              return;
            } else if (e instanceof org.apache.thrift.TApplicationException) {
              _LOGGER.error("TApplicationException inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TApplicationException)e;
            } else {
              _LOGGER.error("Exception inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
            } catch (Exception ex) {
              _LOGGER.error("Exception writing to internal frame buffer", ex);
              fb.close();
            }
          }
        };
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      public void start(I iface, rulesPage_args args, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
        iface.rulesPage(args.pageNum, args.pageSize, args.ruleName, args.enable,resultHandler);
      }
    }

    public static class rulesList<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, rulesList_args, String> {
      public rulesList() {
        super("rulesList");
      }

      @Override
      public rulesList_args getEmptyArgsInstance() {
        return new rulesList_args();
      }

      @Override
      public org.apache.thrift.async.AsyncMethodCallback<String> getResultHandler(final org.apache.thrift.server.AbstractNonblockingServer.AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new org.apache.thrift.async.AsyncMethodCallback<String>() {
          @Override
          public void onComplete(String o) {
            rulesList_result result = new rulesList_result();
            result.success = o;
            try {
              fcall.sendResponse(fb, result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
            } catch (org.apache.thrift.transport.TTransportException e) {
              _LOGGER.error("TTransportException writing to internal frame buffer", e);
              fb.close();
            } catch (Exception e) {
              _LOGGER.error("Exception writing to internal frame buffer", e);
              onError(e);
            }
          }
          @Override
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TSerializable msg;
            rulesList_result result = new rulesList_result();
            if (e instanceof org.apache.thrift.transport.TTransportException) {
              _LOGGER.error("TTransportException inside handler", e);
              fb.close();
              return;
            } else if (e instanceof org.apache.thrift.TApplicationException) {
              _LOGGER.error("TApplicationException inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TApplicationException)e;
            } else {
              _LOGGER.error("Exception inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
            } catch (Exception ex) {
              _LOGGER.error("Exception writing to internal frame buffer", ex);
              fb.close();
            }
          }
        };
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      public void start(I iface, rulesList_args args, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
        iface.rulesList(resultHandler);
      }
    }

    public static class saveRecord<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, saveRecord_args, String> {
      public saveRecord() {
        super("saveRecord");
      }

      @Override
      public saveRecord_args getEmptyArgsInstance() {
        return new saveRecord_args();
      }

      @Override
      public org.apache.thrift.async.AsyncMethodCallback<String> getResultHandler(final org.apache.thrift.server.AbstractNonblockingServer.AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new org.apache.thrift.async.AsyncMethodCallback<String>() {
          @Override
          public void onComplete(String o) {
            saveRecord_result result = new saveRecord_result();
            result.success = o;
            try {
              fcall.sendResponse(fb, result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
            } catch (org.apache.thrift.transport.TTransportException e) {
              _LOGGER.error("TTransportException writing to internal frame buffer", e);
              fb.close();
            } catch (Exception e) {
              _LOGGER.error("Exception writing to internal frame buffer", e);
              onError(e);
            }
          }
          @Override
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TSerializable msg;
            saveRecord_result result = new saveRecord_result();
            if (e instanceof org.apache.thrift.transport.TTransportException) {
              _LOGGER.error("TTransportException inside handler", e);
              fb.close();
              return;
            } else if (e instanceof org.apache.thrift.TApplicationException) {
              _LOGGER.error("TApplicationException inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TApplicationException)e;
            } else {
              _LOGGER.error("Exception inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
            } catch (Exception ex) {
              _LOGGER.error("Exception writing to internal frame buffer", ex);
              fb.close();
            }
          }
        };
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      public void start(I iface, saveRecord_args args, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
        iface.saveRecord(args.data,resultHandler);
      }
    }

    public static class deleteRecord<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, deleteRecord_args, String> {
      public deleteRecord() {
        super("deleteRecord");
      }

      @Override
      public deleteRecord_args getEmptyArgsInstance() {
        return new deleteRecord_args();
      }

      @Override
      public org.apache.thrift.async.AsyncMethodCallback<String> getResultHandler(final org.apache.thrift.server.AbstractNonblockingServer.AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new org.apache.thrift.async.AsyncMethodCallback<String>() {
          @Override
          public void onComplete(String o) {
            deleteRecord_result result = new deleteRecord_result();
            result.success = o;
            try {
              fcall.sendResponse(fb, result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
            } catch (org.apache.thrift.transport.TTransportException e) {
              _LOGGER.error("TTransportException writing to internal frame buffer", e);
              fb.close();
            } catch (Exception e) {
              _LOGGER.error("Exception writing to internal frame buffer", e);
              onError(e);
            }
          }
          @Override
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TSerializable msg;
            deleteRecord_result result = new deleteRecord_result();
            if (e instanceof org.apache.thrift.transport.TTransportException) {
              _LOGGER.error("TTransportException inside handler", e);
              fb.close();
              return;
            } else if (e instanceof org.apache.thrift.TApplicationException) {
              _LOGGER.error("TApplicationException inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TApplicationException)e;
            } else {
              _LOGGER.error("Exception inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
            } catch (Exception ex) {
              _LOGGER.error("Exception writing to internal frame buffer", ex);
              fb.close();
            }
          }
        };
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      public void start(I iface, deleteRecord_args args, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
        iface.deleteRecord(args.patientId, args.admissionNo,resultHandler);
      }
    }

    public static class deleteFeeList<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, deleteFeeList_args, String> {
      public deleteFeeList() {
        super("deleteFeeList");
      }

      @Override
      public deleteFeeList_args getEmptyArgsInstance() {
        return new deleteFeeList_args();
      }

      @Override
      public org.apache.thrift.async.AsyncMethodCallback<String> getResultHandler(final org.apache.thrift.server.AbstractNonblockingServer.AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new org.apache.thrift.async.AsyncMethodCallback<String>() {
          @Override
          public void onComplete(String o) {
            deleteFeeList_result result = new deleteFeeList_result();
            result.success = o;
            try {
              fcall.sendResponse(fb, result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
            } catch (org.apache.thrift.transport.TTransportException e) {
              _LOGGER.error("TTransportException writing to internal frame buffer", e);
              fb.close();
            } catch (Exception e) {
              _LOGGER.error("Exception writing to internal frame buffer", e);
              onError(e);
            }
          }
          @Override
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TSerializable msg;
            deleteFeeList_result result = new deleteFeeList_result();
            if (e instanceof org.apache.thrift.transport.TTransportException) {
              _LOGGER.error("TTransportException inside handler", e);
              fb.close();
              return;
            } else if (e instanceof org.apache.thrift.TApplicationException) {
              _LOGGER.error("TApplicationException inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TApplicationException)e;
            } else {
              _LOGGER.error("Exception inside handler", e);
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
            } catch (Exception ex) {
              _LOGGER.error("Exception writing to internal frame buffer", ex);
              fb.close();
            }
          }
        };
      }

      @Override
      protected boolean isOneway() {
        return false;
      }

      @Override
      public void start(I iface, deleteFeeList_args args, org.apache.thrift.async.AsyncMethodCallback<String> resultHandler) throws org.apache.thrift.TException {
        iface.deleteFeeList(args.patientId, args.admissionNo, args.id,resultHandler);
      }
    }

  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class ping_args implements org.apache.thrift.TBase<ping_args, ping_args._Fields>, java.io.Serializable, Cloneable, Comparable<ping_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ping_args");


    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new ping_argsStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new ping_argsTupleSchemeFactory();


    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
;

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ping_args.class, metaDataMap);
    }

    public ping_args() {
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public ping_args(ping_args other) {
    }

    @Override
    public ping_args deepCopy() {
      return new ping_args(this);
    }

    @Override
    public void clear() {
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof ping_args)
        return this.equals((ping_args)that);
      return false;
    }

    public boolean equals(ping_args that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      return hashCode;
    }

    @Override
    public int compareTo(ping_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    @Override
    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("ping_args(");
      boolean first = true;

      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class ping_argsStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public ping_argsStandardScheme getScheme() {
        return new ping_argsStandardScheme();
      }
    }

    private static class ping_argsStandardScheme extends org.apache.thrift.scheme.StandardScheme<ping_args> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, ping_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, ping_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class ping_argsTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public ping_argsTupleScheme getScheme() {
        return new ping_argsTupleScheme();
      }
    }

    private static class ping_argsTupleScheme extends org.apache.thrift.scheme.TupleScheme<ping_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, ping_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, ping_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class ping_result implements org.apache.thrift.TBase<ping_result, ping_result._Fields>, java.io.Serializable, Cloneable, Comparable<ping_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ping_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRING, (short)0);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new ping_resultStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new ping_resultTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ping_result.class, metaDataMap);
    }

    public ping_result() {
    }

    public ping_result(
      String success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public ping_result(ping_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    @Override
    public ping_result deepCopy() {
      return new ping_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getSuccess() {
      return this.success;
    }

    public ping_result setSuccess(@org.apache.thrift.annotation.Nullable String success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof ping_result)
        return this.equals((ping_result)that);
      return false;
    }

    public boolean equals(ping_result that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetSuccess()) ? 131071 : 524287);
      if (isSetSuccess())
        hashCode = hashCode * 8191 + success.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(ping_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetSuccess(), other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("ping_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class ping_resultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public ping_resultStandardScheme getScheme() {
        return new ping_resultStandardScheme();
      }
    }

    private static class ping_resultStandardScheme extends org.apache.thrift.scheme.StandardScheme<ping_result> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, ping_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.success = iprot.readString();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, ping_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeString(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class ping_resultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public ping_resultTupleScheme getScheme() {
        return new ping_resultTupleScheme();
      }
    }

    private static class ping_resultTupleScheme extends org.apache.thrift.scheme.TupleScheme<ping_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, ping_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeString(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, ping_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readString();
          struct.setSuccessIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class execRules_args implements org.apache.thrift.TBase<execRules_args, execRules_args._Fields>, java.io.Serializable, Cloneable, Comparable<execRules_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("execRules_args");

    private static final org.apache.thrift.protocol.TField JSON_STR_FIELD_DESC = new org.apache.thrift.protocol.TField("jsonStr", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new execRules_argsStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new execRules_argsTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String jsonStr; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      JSON_STR((short)1, "jsonStr");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // JSON_STR
            return JSON_STR;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.JSON_STR, new org.apache.thrift.meta_data.FieldMetaData("jsonStr", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(execRules_args.class, metaDataMap);
    }

    public execRules_args() {
    }

    public execRules_args(
      String jsonStr)
    {
      this();
      this.jsonStr = jsonStr;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public execRules_args(execRules_args other) {
      if (other.isSetJsonStr()) {
        this.jsonStr = other.jsonStr;
      }
    }

    @Override
    public execRules_args deepCopy() {
      return new execRules_args(this);
    }

    @Override
    public void clear() {
      this.jsonStr = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getJsonStr() {
      return this.jsonStr;
    }

    public execRules_args setJsonStr(@org.apache.thrift.annotation.Nullable String jsonStr) {
      this.jsonStr = jsonStr;
      return this;
    }

    public void unsetJsonStr() {
      this.jsonStr = null;
    }

    /** Returns true if field jsonStr is set (has been assigned a value) and false otherwise */
    public boolean isSetJsonStr() {
      return this.jsonStr != null;
    }

    public void setJsonStrIsSet(boolean value) {
      if (!value) {
        this.jsonStr = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case JSON_STR:
        if (value == null) {
          unsetJsonStr();
        } else {
          setJsonStr((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case JSON_STR:
        return getJsonStr();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case JSON_STR:
        return isSetJsonStr();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof execRules_args)
        return this.equals((execRules_args)that);
      return false;
    }

    public boolean equals(execRules_args that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_jsonStr = true && this.isSetJsonStr();
      boolean that_present_jsonStr = true && that.isSetJsonStr();
      if (this_present_jsonStr || that_present_jsonStr) {
        if (!(this_present_jsonStr && that_present_jsonStr))
          return false;
        if (!this.jsonStr.equals(that.jsonStr))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetJsonStr()) ? 131071 : 524287);
      if (isSetJsonStr())
        hashCode = hashCode * 8191 + jsonStr.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(execRules_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetJsonStr(), other.isSetJsonStr());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetJsonStr()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.jsonStr, other.jsonStr);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    @Override
    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("execRules_args(");
      boolean first = true;

      sb.append("jsonStr:");
      if (this.jsonStr == null) {
        sb.append("null");
      } else {
        sb.append(this.jsonStr);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class execRules_argsStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public execRules_argsStandardScheme getScheme() {
        return new execRules_argsStandardScheme();
      }
    }

    private static class execRules_argsStandardScheme extends org.apache.thrift.scheme.StandardScheme<execRules_args> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, execRules_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // JSON_STR
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.jsonStr = iprot.readString();
                struct.setJsonStrIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, execRules_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.jsonStr != null) {
          oprot.writeFieldBegin(JSON_STR_FIELD_DESC);
          oprot.writeString(struct.jsonStr);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class execRules_argsTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public execRules_argsTupleScheme getScheme() {
        return new execRules_argsTupleScheme();
      }
    }

    private static class execRules_argsTupleScheme extends org.apache.thrift.scheme.TupleScheme<execRules_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, execRules_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetJsonStr()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetJsonStr()) {
          oprot.writeString(struct.jsonStr);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, execRules_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.jsonStr = iprot.readString();
          struct.setJsonStrIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class execRules_result implements org.apache.thrift.TBase<execRules_result, execRules_result._Fields>, java.io.Serializable, Cloneable, Comparable<execRules_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("execRules_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRING, (short)0);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new execRules_resultStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new execRules_resultTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(execRules_result.class, metaDataMap);
    }

    public execRules_result() {
    }

    public execRules_result(
      String success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public execRules_result(execRules_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    @Override
    public execRules_result deepCopy() {
      return new execRules_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getSuccess() {
      return this.success;
    }

    public execRules_result setSuccess(@org.apache.thrift.annotation.Nullable String success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof execRules_result)
        return this.equals((execRules_result)that);
      return false;
    }

    public boolean equals(execRules_result that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetSuccess()) ? 131071 : 524287);
      if (isSetSuccess())
        hashCode = hashCode * 8191 + success.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(execRules_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetSuccess(), other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("execRules_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class execRules_resultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public execRules_resultStandardScheme getScheme() {
        return new execRules_resultStandardScheme();
      }
    }

    private static class execRules_resultStandardScheme extends org.apache.thrift.scheme.StandardScheme<execRules_result> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, execRules_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.success = iprot.readString();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, execRules_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeString(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class execRules_resultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public execRules_resultTupleScheme getScheme() {
        return new execRules_resultTupleScheme();
      }
    }

    private static class execRules_resultTupleScheme extends org.apache.thrift.scheme.TupleScheme<execRules_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, execRules_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeString(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, execRules_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readString();
          struct.setSuccessIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class execRulesByCodes_args implements org.apache.thrift.TBase<execRulesByCodes_args, execRulesByCodes_args._Fields>, java.io.Serializable, Cloneable, Comparable<execRulesByCodes_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("execRulesByCodes_args");

    private static final org.apache.thrift.protocol.TField DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("data", org.apache.thrift.protocol.TType.STRING, (short)1);
    private static final org.apache.thrift.protocol.TField RULES_FIELD_DESC = new org.apache.thrift.protocol.TField("rules", org.apache.thrift.protocol.TType.STRING, (short)2);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new execRulesByCodes_argsStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new execRulesByCodes_argsTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String data; // required
    public @org.apache.thrift.annotation.Nullable String rules; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      DATA((short)1, "data"),
      RULES((short)2, "rules");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // DATA
            return DATA;
          case 2: // RULES
            return RULES;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.DATA, new org.apache.thrift.meta_data.FieldMetaData("data", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      tmpMap.put(_Fields.RULES, new org.apache.thrift.meta_data.FieldMetaData("rules", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(execRulesByCodes_args.class, metaDataMap);
    }

    public execRulesByCodes_args() {
    }

    public execRulesByCodes_args(
      String data,
      String rules)
    {
      this();
      this.data = data;
      this.rules = rules;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public execRulesByCodes_args(execRulesByCodes_args other) {
      if (other.isSetData()) {
        this.data = other.data;
      }
      if (other.isSetRules()) {
        this.rules = other.rules;
      }
    }

    @Override
    public execRulesByCodes_args deepCopy() {
      return new execRulesByCodes_args(this);
    }

    @Override
    public void clear() {
      this.data = null;
      this.rules = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getData() {
      return this.data;
    }

    public execRulesByCodes_args setData(@org.apache.thrift.annotation.Nullable String data) {
      this.data = data;
      return this;
    }

    public void unsetData() {
      this.data = null;
    }

    /** Returns true if field data is set (has been assigned a value) and false otherwise */
    public boolean isSetData() {
      return this.data != null;
    }

    public void setDataIsSet(boolean value) {
      if (!value) {
        this.data = null;
      }
    }

    @org.apache.thrift.annotation.Nullable
    public String getRules() {
      return this.rules;
    }

    public execRulesByCodes_args setRules(@org.apache.thrift.annotation.Nullable String rules) {
      this.rules = rules;
      return this;
    }

    public void unsetRules() {
      this.rules = null;
    }

    /** Returns true if field rules is set (has been assigned a value) and false otherwise */
    public boolean isSetRules() {
      return this.rules != null;
    }

    public void setRulesIsSet(boolean value) {
      if (!value) {
        this.rules = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case DATA:
        if (value == null) {
          unsetData();
        } else {
          setData((String)value);
        }
        break;

      case RULES:
        if (value == null) {
          unsetRules();
        } else {
          setRules((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case DATA:
        return getData();

      case RULES:
        return getRules();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case DATA:
        return isSetData();
      case RULES:
        return isSetRules();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof execRulesByCodes_args)
        return this.equals((execRulesByCodes_args)that);
      return false;
    }

    public boolean equals(execRulesByCodes_args that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_data = true && this.isSetData();
      boolean that_present_data = true && that.isSetData();
      if (this_present_data || that_present_data) {
        if (!(this_present_data && that_present_data))
          return false;
        if (!this.data.equals(that.data))
          return false;
      }

      boolean this_present_rules = true && this.isSetRules();
      boolean that_present_rules = true && that.isSetRules();
      if (this_present_rules || that_present_rules) {
        if (!(this_present_rules && that_present_rules))
          return false;
        if (!this.rules.equals(that.rules))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetData()) ? 131071 : 524287);
      if (isSetData())
        hashCode = hashCode * 8191 + data.hashCode();

      hashCode = hashCode * 8191 + ((isSetRules()) ? 131071 : 524287);
      if (isSetRules())
        hashCode = hashCode * 8191 + rules.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(execRulesByCodes_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetData(), other.isSetData());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetData()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.data, other.data);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.compare(isSetRules(), other.isSetRules());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRules()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rules, other.rules);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    @Override
    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("execRulesByCodes_args(");
      boolean first = true;

      sb.append("data:");
      if (this.data == null) {
        sb.append("null");
      } else {
        sb.append(this.data);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("rules:");
      if (this.rules == null) {
        sb.append("null");
      } else {
        sb.append(this.rules);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class execRulesByCodes_argsStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public execRulesByCodes_argsStandardScheme getScheme() {
        return new execRulesByCodes_argsStandardScheme();
      }
    }

    private static class execRulesByCodes_argsStandardScheme extends org.apache.thrift.scheme.StandardScheme<execRulesByCodes_args> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, execRulesByCodes_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // DATA
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.data = iprot.readString();
                struct.setDataIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // RULES
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.rules = iprot.readString();
                struct.setRulesIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, execRulesByCodes_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.data != null) {
          oprot.writeFieldBegin(DATA_FIELD_DESC);
          oprot.writeString(struct.data);
          oprot.writeFieldEnd();
        }
        if (struct.rules != null) {
          oprot.writeFieldBegin(RULES_FIELD_DESC);
          oprot.writeString(struct.rules);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class execRulesByCodes_argsTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public execRulesByCodes_argsTupleScheme getScheme() {
        return new execRulesByCodes_argsTupleScheme();
      }
    }

    private static class execRulesByCodes_argsTupleScheme extends org.apache.thrift.scheme.TupleScheme<execRulesByCodes_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, execRulesByCodes_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetData()) {
          optionals.set(0);
        }
        if (struct.isSetRules()) {
          optionals.set(1);
        }
        oprot.writeBitSet(optionals, 2);
        if (struct.isSetData()) {
          oprot.writeString(struct.data);
        }
        if (struct.isSetRules()) {
          oprot.writeString(struct.rules);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, execRulesByCodes_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(2);
        if (incoming.get(0)) {
          struct.data = iprot.readString();
          struct.setDataIsSet(true);
        }
        if (incoming.get(1)) {
          struct.rules = iprot.readString();
          struct.setRulesIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class execRulesByCodes_result implements org.apache.thrift.TBase<execRulesByCodes_result, execRulesByCodes_result._Fields>, java.io.Serializable, Cloneable, Comparable<execRulesByCodes_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("execRulesByCodes_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRING, (short)0);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new execRulesByCodes_resultStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new execRulesByCodes_resultTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(execRulesByCodes_result.class, metaDataMap);
    }

    public execRulesByCodes_result() {
    }

    public execRulesByCodes_result(
      String success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public execRulesByCodes_result(execRulesByCodes_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    @Override
    public execRulesByCodes_result deepCopy() {
      return new execRulesByCodes_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getSuccess() {
      return this.success;
    }

    public execRulesByCodes_result setSuccess(@org.apache.thrift.annotation.Nullable String success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof execRulesByCodes_result)
        return this.equals((execRulesByCodes_result)that);
      return false;
    }

    public boolean equals(execRulesByCodes_result that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetSuccess()) ? 131071 : 524287);
      if (isSetSuccess())
        hashCode = hashCode * 8191 + success.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(execRulesByCodes_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetSuccess(), other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("execRulesByCodes_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class execRulesByCodes_resultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public execRulesByCodes_resultStandardScheme getScheme() {
        return new execRulesByCodes_resultStandardScheme();
      }
    }

    private static class execRulesByCodes_resultStandardScheme extends org.apache.thrift.scheme.StandardScheme<execRulesByCodes_result> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, execRulesByCodes_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.success = iprot.readString();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, execRulesByCodes_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeString(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class execRulesByCodes_resultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public execRulesByCodes_resultTupleScheme getScheme() {
        return new execRulesByCodes_resultTupleScheme();
      }
    }

    private static class execRulesByCodes_resultTupleScheme extends org.apache.thrift.scheme.TupleScheme<execRulesByCodes_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, execRulesByCodes_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeString(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, execRulesByCodes_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readString();
          struct.setSuccessIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class incrementalRules_args implements org.apache.thrift.TBase<incrementalRules_args, incrementalRules_args._Fields>, java.io.Serializable, Cloneable, Comparable<incrementalRules_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("incrementalRules_args");

    private static final org.apache.thrift.protocol.TField JSON_STR_FIELD_DESC = new org.apache.thrift.protocol.TField("jsonStr", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new incrementalRules_argsStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new incrementalRules_argsTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String jsonStr; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      JSON_STR((short)1, "jsonStr");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // JSON_STR
            return JSON_STR;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.JSON_STR, new org.apache.thrift.meta_data.FieldMetaData("jsonStr", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(incrementalRules_args.class, metaDataMap);
    }

    public incrementalRules_args() {
    }

    public incrementalRules_args(
      String jsonStr)
    {
      this();
      this.jsonStr = jsonStr;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public incrementalRules_args(incrementalRules_args other) {
      if (other.isSetJsonStr()) {
        this.jsonStr = other.jsonStr;
      }
    }

    @Override
    public incrementalRules_args deepCopy() {
      return new incrementalRules_args(this);
    }

    @Override
    public void clear() {
      this.jsonStr = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getJsonStr() {
      return this.jsonStr;
    }

    public incrementalRules_args setJsonStr(@org.apache.thrift.annotation.Nullable String jsonStr) {
      this.jsonStr = jsonStr;
      return this;
    }

    public void unsetJsonStr() {
      this.jsonStr = null;
    }

    /** Returns true if field jsonStr is set (has been assigned a value) and false otherwise */
    public boolean isSetJsonStr() {
      return this.jsonStr != null;
    }

    public void setJsonStrIsSet(boolean value) {
      if (!value) {
        this.jsonStr = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case JSON_STR:
        if (value == null) {
          unsetJsonStr();
        } else {
          setJsonStr((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case JSON_STR:
        return getJsonStr();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case JSON_STR:
        return isSetJsonStr();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof incrementalRules_args)
        return this.equals((incrementalRules_args)that);
      return false;
    }

    public boolean equals(incrementalRules_args that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_jsonStr = true && this.isSetJsonStr();
      boolean that_present_jsonStr = true && that.isSetJsonStr();
      if (this_present_jsonStr || that_present_jsonStr) {
        if (!(this_present_jsonStr && that_present_jsonStr))
          return false;
        if (!this.jsonStr.equals(that.jsonStr))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetJsonStr()) ? 131071 : 524287);
      if (isSetJsonStr())
        hashCode = hashCode * 8191 + jsonStr.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(incrementalRules_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetJsonStr(), other.isSetJsonStr());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetJsonStr()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.jsonStr, other.jsonStr);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    @Override
    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("incrementalRules_args(");
      boolean first = true;

      sb.append("jsonStr:");
      if (this.jsonStr == null) {
        sb.append("null");
      } else {
        sb.append(this.jsonStr);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class incrementalRules_argsStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public incrementalRules_argsStandardScheme getScheme() {
        return new incrementalRules_argsStandardScheme();
      }
    }

    private static class incrementalRules_argsStandardScheme extends org.apache.thrift.scheme.StandardScheme<incrementalRules_args> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, incrementalRules_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // JSON_STR
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.jsonStr = iprot.readString();
                struct.setJsonStrIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, incrementalRules_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.jsonStr != null) {
          oprot.writeFieldBegin(JSON_STR_FIELD_DESC);
          oprot.writeString(struct.jsonStr);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class incrementalRules_argsTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public incrementalRules_argsTupleScheme getScheme() {
        return new incrementalRules_argsTupleScheme();
      }
    }

    private static class incrementalRules_argsTupleScheme extends org.apache.thrift.scheme.TupleScheme<incrementalRules_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, incrementalRules_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetJsonStr()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetJsonStr()) {
          oprot.writeString(struct.jsonStr);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, incrementalRules_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.jsonStr = iprot.readString();
          struct.setJsonStrIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class incrementalRules_result implements org.apache.thrift.TBase<incrementalRules_result, incrementalRules_result._Fields>, java.io.Serializable, Cloneable, Comparable<incrementalRules_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("incrementalRules_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRING, (short)0);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new incrementalRules_resultStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new incrementalRules_resultTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(incrementalRules_result.class, metaDataMap);
    }

    public incrementalRules_result() {
    }

    public incrementalRules_result(
      String success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public incrementalRules_result(incrementalRules_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    @Override
    public incrementalRules_result deepCopy() {
      return new incrementalRules_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getSuccess() {
      return this.success;
    }

    public incrementalRules_result setSuccess(@org.apache.thrift.annotation.Nullable String success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof incrementalRules_result)
        return this.equals((incrementalRules_result)that);
      return false;
    }

    public boolean equals(incrementalRules_result that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetSuccess()) ? 131071 : 524287);
      if (isSetSuccess())
        hashCode = hashCode * 8191 + success.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(incrementalRules_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetSuccess(), other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("incrementalRules_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class incrementalRules_resultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public incrementalRules_resultStandardScheme getScheme() {
        return new incrementalRules_resultStandardScheme();
      }
    }

    private static class incrementalRules_resultStandardScheme extends org.apache.thrift.scheme.StandardScheme<incrementalRules_result> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, incrementalRules_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.success = iprot.readString();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, incrementalRules_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeString(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class incrementalRules_resultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public incrementalRules_resultTupleScheme getScheme() {
        return new incrementalRules_resultTupleScheme();
      }
    }

    private static class incrementalRules_resultTupleScheme extends org.apache.thrift.scheme.TupleScheme<incrementalRules_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, incrementalRules_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeString(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, incrementalRules_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readString();
          struct.setSuccessIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class removeRules_args implements org.apache.thrift.TBase<removeRules_args, removeRules_args._Fields>, java.io.Serializable, Cloneable, Comparable<removeRules_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("removeRules_args");

    private static final org.apache.thrift.protocol.TField RULE_CODES_FIELD_DESC = new org.apache.thrift.protocol.TField("ruleCodes", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new removeRules_argsStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new removeRules_argsTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String ruleCodes; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      RULE_CODES((short)1, "ruleCodes");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // RULE_CODES
            return RULE_CODES;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.RULE_CODES, new org.apache.thrift.meta_data.FieldMetaData("ruleCodes", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(removeRules_args.class, metaDataMap);
    }

    public removeRules_args() {
    }

    public removeRules_args(
      String ruleCodes)
    {
      this();
      this.ruleCodes = ruleCodes;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public removeRules_args(removeRules_args other) {
      if (other.isSetRuleCodes()) {
        this.ruleCodes = other.ruleCodes;
      }
    }

    @Override
    public removeRules_args deepCopy() {
      return new removeRules_args(this);
    }

    @Override
    public void clear() {
      this.ruleCodes = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getRuleCodes() {
      return this.ruleCodes;
    }

    public removeRules_args setRuleCodes(@org.apache.thrift.annotation.Nullable String ruleCodes) {
      this.ruleCodes = ruleCodes;
      return this;
    }

    public void unsetRuleCodes() {
      this.ruleCodes = null;
    }

    /** Returns true if field ruleCodes is set (has been assigned a value) and false otherwise */
    public boolean isSetRuleCodes() {
      return this.ruleCodes != null;
    }

    public void setRuleCodesIsSet(boolean value) {
      if (!value) {
        this.ruleCodes = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case RULE_CODES:
        if (value == null) {
          unsetRuleCodes();
        } else {
          setRuleCodes((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case RULE_CODES:
        return getRuleCodes();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case RULE_CODES:
        return isSetRuleCodes();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof removeRules_args)
        return this.equals((removeRules_args)that);
      return false;
    }

    public boolean equals(removeRules_args that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_ruleCodes = true && this.isSetRuleCodes();
      boolean that_present_ruleCodes = true && that.isSetRuleCodes();
      if (this_present_ruleCodes || that_present_ruleCodes) {
        if (!(this_present_ruleCodes && that_present_ruleCodes))
          return false;
        if (!this.ruleCodes.equals(that.ruleCodes))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetRuleCodes()) ? 131071 : 524287);
      if (isSetRuleCodes())
        hashCode = hashCode * 8191 + ruleCodes.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(removeRules_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetRuleCodes(), other.isSetRuleCodes());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRuleCodes()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ruleCodes, other.ruleCodes);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    @Override
    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("removeRules_args(");
      boolean first = true;

      sb.append("ruleCodes:");
      if (this.ruleCodes == null) {
        sb.append("null");
      } else {
        sb.append(this.ruleCodes);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class removeRules_argsStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public removeRules_argsStandardScheme getScheme() {
        return new removeRules_argsStandardScheme();
      }
    }

    private static class removeRules_argsStandardScheme extends org.apache.thrift.scheme.StandardScheme<removeRules_args> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, removeRules_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // RULE_CODES
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.ruleCodes = iprot.readString();
                struct.setRuleCodesIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, removeRules_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.ruleCodes != null) {
          oprot.writeFieldBegin(RULE_CODES_FIELD_DESC);
          oprot.writeString(struct.ruleCodes);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class removeRules_argsTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public removeRules_argsTupleScheme getScheme() {
        return new removeRules_argsTupleScheme();
      }
    }

    private static class removeRules_argsTupleScheme extends org.apache.thrift.scheme.TupleScheme<removeRules_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, removeRules_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetRuleCodes()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetRuleCodes()) {
          oprot.writeString(struct.ruleCodes);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, removeRules_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.ruleCodes = iprot.readString();
          struct.setRuleCodesIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class removeRules_result implements org.apache.thrift.TBase<removeRules_result, removeRules_result._Fields>, java.io.Serializable, Cloneable, Comparable<removeRules_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("removeRules_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRING, (short)0);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new removeRules_resultStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new removeRules_resultTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(removeRules_result.class, metaDataMap);
    }

    public removeRules_result() {
    }

    public removeRules_result(
      String success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public removeRules_result(removeRules_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    @Override
    public removeRules_result deepCopy() {
      return new removeRules_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getSuccess() {
      return this.success;
    }

    public removeRules_result setSuccess(@org.apache.thrift.annotation.Nullable String success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof removeRules_result)
        return this.equals((removeRules_result)that);
      return false;
    }

    public boolean equals(removeRules_result that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetSuccess()) ? 131071 : 524287);
      if (isSetSuccess())
        hashCode = hashCode * 8191 + success.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(removeRules_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetSuccess(), other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("removeRules_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class removeRules_resultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public removeRules_resultStandardScheme getScheme() {
        return new removeRules_resultStandardScheme();
      }
    }

    private static class removeRules_resultStandardScheme extends org.apache.thrift.scheme.StandardScheme<removeRules_result> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, removeRules_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.success = iprot.readString();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, removeRules_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeString(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class removeRules_resultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public removeRules_resultTupleScheme getScheme() {
        return new removeRules_resultTupleScheme();
      }
    }

    private static class removeRules_resultTupleScheme extends org.apache.thrift.scheme.TupleScheme<removeRules_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, removeRules_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeString(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, removeRules_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readString();
          struct.setSuccessIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class rulesPage_args implements org.apache.thrift.TBase<rulesPage_args, rulesPage_args._Fields>, java.io.Serializable, Cloneable, Comparable<rulesPage_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("rulesPage_args");

    private static final org.apache.thrift.protocol.TField PAGE_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("pageNum", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField PAGE_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("pageSize", org.apache.thrift.protocol.TType.I64, (short)2);
    private static final org.apache.thrift.protocol.TField RULE_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("ruleName", org.apache.thrift.protocol.TType.STRING, (short)3);
    private static final org.apache.thrift.protocol.TField ENABLE_FIELD_DESC = new org.apache.thrift.protocol.TField("enable", org.apache.thrift.protocol.TType.STRING, (short)4);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new rulesPage_argsStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new rulesPage_argsTupleSchemeFactory();

    public long pageNum; // required
    public long pageSize; // required
    public @org.apache.thrift.annotation.Nullable String ruleName; // required
    public @org.apache.thrift.annotation.Nullable String enable; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PAGE_NUM((short)1, "pageNum"),
      PAGE_SIZE((short)2, "pageSize"),
      RULE_NAME((short)3, "ruleName"),
      ENABLE((short)4, "enable");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PAGE_NUM
            return PAGE_NUM;
          case 2: // PAGE_SIZE
            return PAGE_SIZE;
          case 3: // RULE_NAME
            return RULE_NAME;
          case 4: // ENABLE
            return ENABLE;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __PAGENUM_ISSET_ID = 0;
    private static final int __PAGESIZE_ISSET_ID = 1;
    private byte __isset_bitfield = 0;
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PAGE_NUM, new org.apache.thrift.meta_data.FieldMetaData("pageNum", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.PAGE_SIZE, new org.apache.thrift.meta_data.FieldMetaData("pageSize", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.RULE_NAME, new org.apache.thrift.meta_data.FieldMetaData("ruleName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      tmpMap.put(_Fields.ENABLE, new org.apache.thrift.meta_data.FieldMetaData("enable", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(rulesPage_args.class, metaDataMap);
    }

    public rulesPage_args() {
    }

    public rulesPage_args(
      long pageNum,
      long pageSize,
      String ruleName,
      String enable)
    {
      this();
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
      this.ruleName = ruleName;
      this.enable = enable;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public rulesPage_args(rulesPage_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.pageNum = other.pageNum;
      this.pageSize = other.pageSize;
      if (other.isSetRuleName()) {
        this.ruleName = other.ruleName;
      }
      if (other.isSetEnable()) {
        this.enable = other.enable;
      }
    }

    @Override
    public rulesPage_args deepCopy() {
      return new rulesPage_args(this);
    }

    @Override
    public void clear() {
      setPageNumIsSet(false);
      this.pageNum = 0;
      setPageSizeIsSet(false);
      this.pageSize = 0;
      this.ruleName = null;
      this.enable = null;
    }

    public long getPageNum() {
      return this.pageNum;
    }

    public rulesPage_args setPageNum(long pageNum) {
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      return this;
    }

    public void unsetPageNum() {
      __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    /** Returns true if field pageNum is set (has been assigned a value) and false otherwise */
    public boolean isSetPageNum() {
      return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    public void setPageNumIsSet(boolean value) {
      __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PAGENUM_ISSET_ID, value);
    }

    public long getPageSize() {
      return this.pageSize;
    }

    public rulesPage_args setPageSize(long pageSize) {
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
      return this;
    }

    public void unsetPageSize() {
      __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    /** Returns true if field pageSize is set (has been assigned a value) and false otherwise */
    public boolean isSetPageSize() {
      return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    public void setPageSizeIsSet(boolean value) {
      __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PAGESIZE_ISSET_ID, value);
    }

    @org.apache.thrift.annotation.Nullable
    public String getRuleName() {
      return this.ruleName;
    }

    public rulesPage_args setRuleName(@org.apache.thrift.annotation.Nullable String ruleName) {
      this.ruleName = ruleName;
      return this;
    }

    public void unsetRuleName() {
      this.ruleName = null;
    }

    /** Returns true if field ruleName is set (has been assigned a value) and false otherwise */
    public boolean isSetRuleName() {
      return this.ruleName != null;
    }

    public void setRuleNameIsSet(boolean value) {
      if (!value) {
        this.ruleName = null;
      }
    }

    @org.apache.thrift.annotation.Nullable
    public String getEnable() {
      return this.enable;
    }

    public rulesPage_args setEnable(@org.apache.thrift.annotation.Nullable String enable) {
      this.enable = enable;
      return this;
    }

    public void unsetEnable() {
      this.enable = null;
    }

    /** Returns true if field enable is set (has been assigned a value) and false otherwise */
    public boolean isSetEnable() {
      return this.enable != null;
    }

    public void setEnableIsSet(boolean value) {
      if (!value) {
        this.enable = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case PAGE_NUM:
        if (value == null) {
          unsetPageNum();
        } else {
          setPageNum((Long)value);
        }
        break;

      case PAGE_SIZE:
        if (value == null) {
          unsetPageSize();
        } else {
          setPageSize((Long)value);
        }
        break;

      case RULE_NAME:
        if (value == null) {
          unsetRuleName();
        } else {
          setRuleName((String)value);
        }
        break;

      case ENABLE:
        if (value == null) {
          unsetEnable();
        } else {
          setEnable((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PAGE_NUM:
        return getPageNum();

      case PAGE_SIZE:
        return getPageSize();

      case RULE_NAME:
        return getRuleName();

      case ENABLE:
        return getEnable();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PAGE_NUM:
        return isSetPageNum();
      case PAGE_SIZE:
        return isSetPageSize();
      case RULE_NAME:
        return isSetRuleName();
      case ENABLE:
        return isSetEnable();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof rulesPage_args)
        return this.equals((rulesPage_args)that);
      return false;
    }

    public boolean equals(rulesPage_args that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_pageNum = true;
      boolean that_present_pageNum = true;
      if (this_present_pageNum || that_present_pageNum) {
        if (!(this_present_pageNum && that_present_pageNum))
          return false;
        if (this.pageNum != that.pageNum)
          return false;
      }

      boolean this_present_pageSize = true;
      boolean that_present_pageSize = true;
      if (this_present_pageSize || that_present_pageSize) {
        if (!(this_present_pageSize && that_present_pageSize))
          return false;
        if (this.pageSize != that.pageSize)
          return false;
      }

      boolean this_present_ruleName = true && this.isSetRuleName();
      boolean that_present_ruleName = true && that.isSetRuleName();
      if (this_present_ruleName || that_present_ruleName) {
        if (!(this_present_ruleName && that_present_ruleName))
          return false;
        if (!this.ruleName.equals(that.ruleName))
          return false;
      }

      boolean this_present_enable = true && this.isSetEnable();
      boolean that_present_enable = true && that.isSetEnable();
      if (this_present_enable || that_present_enable) {
        if (!(this_present_enable && that_present_enable))
          return false;
        if (!this.enable.equals(that.enable))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(pageNum);

      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(pageSize);

      hashCode = hashCode * 8191 + ((isSetRuleName()) ? 131071 : 524287);
      if (isSetRuleName())
        hashCode = hashCode * 8191 + ruleName.hashCode();

      hashCode = hashCode * 8191 + ((isSetEnable()) ? 131071 : 524287);
      if (isSetEnable())
        hashCode = hashCode * 8191 + enable.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(rulesPage_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetPageNum(), other.isSetPageNum());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageNum()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageNum, other.pageNum);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.compare(isSetPageSize(), other.isSetPageSize());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageSize()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageSize, other.pageSize);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.compare(isSetRuleName(), other.isSetRuleName());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRuleName()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ruleName, other.ruleName);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.compare(isSetEnable(), other.isSetEnable());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetEnable()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.enable, other.enable);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    @Override
    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("rulesPage_args(");
      boolean first = true;

      sb.append("pageNum:");
      sb.append(this.pageNum);
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageSize:");
      sb.append(this.pageSize);
      first = false;
      if (!first) sb.append(", ");
      sb.append("ruleName:");
      if (this.ruleName == null) {
        sb.append("null");
      } else {
        sb.append(this.ruleName);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("enable:");
      if (this.enable == null) {
        sb.append("null");
      } else {
        sb.append(this.enable);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class rulesPage_argsStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public rulesPage_argsStandardScheme getScheme() {
        return new rulesPage_argsStandardScheme();
      }
    }

    private static class rulesPage_argsStandardScheme extends org.apache.thrift.scheme.StandardScheme<rulesPage_args> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, rulesPage_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PAGE_NUM
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.pageNum = iprot.readI64();
                struct.setPageNumIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // PAGE_SIZE
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.pageSize = iprot.readI64();
                struct.setPageSizeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // RULE_NAME
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.ruleName = iprot.readString();
                struct.setRuleNameIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 4: // ENABLE
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.enable = iprot.readString();
                struct.setEnableIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, rulesPage_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(PAGE_NUM_FIELD_DESC);
        oprot.writeI64(struct.pageNum);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(PAGE_SIZE_FIELD_DESC);
        oprot.writeI64(struct.pageSize);
        oprot.writeFieldEnd();
        if (struct.ruleName != null) {
          oprot.writeFieldBegin(RULE_NAME_FIELD_DESC);
          oprot.writeString(struct.ruleName);
          oprot.writeFieldEnd();
        }
        if (struct.enable != null) {
          oprot.writeFieldBegin(ENABLE_FIELD_DESC);
          oprot.writeString(struct.enable);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class rulesPage_argsTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public rulesPage_argsTupleScheme getScheme() {
        return new rulesPage_argsTupleScheme();
      }
    }

    private static class rulesPage_argsTupleScheme extends org.apache.thrift.scheme.TupleScheme<rulesPage_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, rulesPage_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetPageNum()) {
          optionals.set(0);
        }
        if (struct.isSetPageSize()) {
          optionals.set(1);
        }
        if (struct.isSetRuleName()) {
          optionals.set(2);
        }
        if (struct.isSetEnable()) {
          optionals.set(3);
        }
        oprot.writeBitSet(optionals, 4);
        if (struct.isSetPageNum()) {
          oprot.writeI64(struct.pageNum);
        }
        if (struct.isSetPageSize()) {
          oprot.writeI64(struct.pageSize);
        }
        if (struct.isSetRuleName()) {
          oprot.writeString(struct.ruleName);
        }
        if (struct.isSetEnable()) {
          oprot.writeString(struct.enable);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, rulesPage_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(4);
        if (incoming.get(0)) {
          struct.pageNum = iprot.readI64();
          struct.setPageNumIsSet(true);
        }
        if (incoming.get(1)) {
          struct.pageSize = iprot.readI64();
          struct.setPageSizeIsSet(true);
        }
        if (incoming.get(2)) {
          struct.ruleName = iprot.readString();
          struct.setRuleNameIsSet(true);
        }
        if (incoming.get(3)) {
          struct.enable = iprot.readString();
          struct.setEnableIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class rulesPage_result implements org.apache.thrift.TBase<rulesPage_result, rulesPage_result._Fields>, java.io.Serializable, Cloneable, Comparable<rulesPage_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("rulesPage_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRING, (short)0);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new rulesPage_resultStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new rulesPage_resultTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(rulesPage_result.class, metaDataMap);
    }

    public rulesPage_result() {
    }

    public rulesPage_result(
      String success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public rulesPage_result(rulesPage_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    @Override
    public rulesPage_result deepCopy() {
      return new rulesPage_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getSuccess() {
      return this.success;
    }

    public rulesPage_result setSuccess(@org.apache.thrift.annotation.Nullable String success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof rulesPage_result)
        return this.equals((rulesPage_result)that);
      return false;
    }

    public boolean equals(rulesPage_result that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetSuccess()) ? 131071 : 524287);
      if (isSetSuccess())
        hashCode = hashCode * 8191 + success.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(rulesPage_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetSuccess(), other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("rulesPage_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class rulesPage_resultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public rulesPage_resultStandardScheme getScheme() {
        return new rulesPage_resultStandardScheme();
      }
    }

    private static class rulesPage_resultStandardScheme extends org.apache.thrift.scheme.StandardScheme<rulesPage_result> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, rulesPage_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.success = iprot.readString();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, rulesPage_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeString(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class rulesPage_resultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public rulesPage_resultTupleScheme getScheme() {
        return new rulesPage_resultTupleScheme();
      }
    }

    private static class rulesPage_resultTupleScheme extends org.apache.thrift.scheme.TupleScheme<rulesPage_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, rulesPage_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeString(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, rulesPage_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readString();
          struct.setSuccessIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class rulesList_args implements org.apache.thrift.TBase<rulesList_args, rulesList_args._Fields>, java.io.Serializable, Cloneable, Comparable<rulesList_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("rulesList_args");


    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new rulesList_argsStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new rulesList_argsTupleSchemeFactory();


    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
;

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(rulesList_args.class, metaDataMap);
    }

    public rulesList_args() {
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public rulesList_args(rulesList_args other) {
    }

    @Override
    public rulesList_args deepCopy() {
      return new rulesList_args(this);
    }

    @Override
    public void clear() {
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof rulesList_args)
        return this.equals((rulesList_args)that);
      return false;
    }

    public boolean equals(rulesList_args that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      return hashCode;
    }

    @Override
    public int compareTo(rulesList_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    @Override
    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("rulesList_args(");
      boolean first = true;

      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class rulesList_argsStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public rulesList_argsStandardScheme getScheme() {
        return new rulesList_argsStandardScheme();
      }
    }

    private static class rulesList_argsStandardScheme extends org.apache.thrift.scheme.StandardScheme<rulesList_args> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, rulesList_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, rulesList_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class rulesList_argsTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public rulesList_argsTupleScheme getScheme() {
        return new rulesList_argsTupleScheme();
      }
    }

    private static class rulesList_argsTupleScheme extends org.apache.thrift.scheme.TupleScheme<rulesList_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, rulesList_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, rulesList_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class rulesList_result implements org.apache.thrift.TBase<rulesList_result, rulesList_result._Fields>, java.io.Serializable, Cloneable, Comparable<rulesList_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("rulesList_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRING, (short)0);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new rulesList_resultStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new rulesList_resultTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(rulesList_result.class, metaDataMap);
    }

    public rulesList_result() {
    }

    public rulesList_result(
      String success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public rulesList_result(rulesList_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    @Override
    public rulesList_result deepCopy() {
      return new rulesList_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getSuccess() {
      return this.success;
    }

    public rulesList_result setSuccess(@org.apache.thrift.annotation.Nullable String success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof rulesList_result)
        return this.equals((rulesList_result)that);
      return false;
    }

    public boolean equals(rulesList_result that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetSuccess()) ? 131071 : 524287);
      if (isSetSuccess())
        hashCode = hashCode * 8191 + success.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(rulesList_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetSuccess(), other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("rulesList_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class rulesList_resultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public rulesList_resultStandardScheme getScheme() {
        return new rulesList_resultStandardScheme();
      }
    }

    private static class rulesList_resultStandardScheme extends org.apache.thrift.scheme.StandardScheme<rulesList_result> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, rulesList_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.success = iprot.readString();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, rulesList_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeString(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class rulesList_resultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public rulesList_resultTupleScheme getScheme() {
        return new rulesList_resultTupleScheme();
      }
    }

    private static class rulesList_resultTupleScheme extends org.apache.thrift.scheme.TupleScheme<rulesList_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, rulesList_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeString(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, rulesList_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readString();
          struct.setSuccessIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class saveRecord_args implements org.apache.thrift.TBase<saveRecord_args, saveRecord_args._Fields>, java.io.Serializable, Cloneable, Comparable<saveRecord_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("saveRecord_args");

    private static final org.apache.thrift.protocol.TField DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("data", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new saveRecord_argsStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new saveRecord_argsTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String data; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      DATA((short)1, "data");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // DATA
            return DATA;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.DATA, new org.apache.thrift.meta_data.FieldMetaData("data", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(saveRecord_args.class, metaDataMap);
    }

    public saveRecord_args() {
    }

    public saveRecord_args(
      String data)
    {
      this();
      this.data = data;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public saveRecord_args(saveRecord_args other) {
      if (other.isSetData()) {
        this.data = other.data;
      }
    }

    @Override
    public saveRecord_args deepCopy() {
      return new saveRecord_args(this);
    }

    @Override
    public void clear() {
      this.data = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getData() {
      return this.data;
    }

    public saveRecord_args setData(@org.apache.thrift.annotation.Nullable String data) {
      this.data = data;
      return this;
    }

    public void unsetData() {
      this.data = null;
    }

    /** Returns true if field data is set (has been assigned a value) and false otherwise */
    public boolean isSetData() {
      return this.data != null;
    }

    public void setDataIsSet(boolean value) {
      if (!value) {
        this.data = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case DATA:
        if (value == null) {
          unsetData();
        } else {
          setData((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case DATA:
        return getData();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case DATA:
        return isSetData();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof saveRecord_args)
        return this.equals((saveRecord_args)that);
      return false;
    }

    public boolean equals(saveRecord_args that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_data = true && this.isSetData();
      boolean that_present_data = true && that.isSetData();
      if (this_present_data || that_present_data) {
        if (!(this_present_data && that_present_data))
          return false;
        if (!this.data.equals(that.data))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetData()) ? 131071 : 524287);
      if (isSetData())
        hashCode = hashCode * 8191 + data.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(saveRecord_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetData(), other.isSetData());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetData()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.data, other.data);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    @Override
    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("saveRecord_args(");
      boolean first = true;

      sb.append("data:");
      if (this.data == null) {
        sb.append("null");
      } else {
        sb.append(this.data);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class saveRecord_argsStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public saveRecord_argsStandardScheme getScheme() {
        return new saveRecord_argsStandardScheme();
      }
    }

    private static class saveRecord_argsStandardScheme extends org.apache.thrift.scheme.StandardScheme<saveRecord_args> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, saveRecord_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // DATA
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.data = iprot.readString();
                struct.setDataIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, saveRecord_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.data != null) {
          oprot.writeFieldBegin(DATA_FIELD_DESC);
          oprot.writeString(struct.data);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class saveRecord_argsTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public saveRecord_argsTupleScheme getScheme() {
        return new saveRecord_argsTupleScheme();
      }
    }

    private static class saveRecord_argsTupleScheme extends org.apache.thrift.scheme.TupleScheme<saveRecord_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, saveRecord_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetData()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetData()) {
          oprot.writeString(struct.data);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, saveRecord_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.data = iprot.readString();
          struct.setDataIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class saveRecord_result implements org.apache.thrift.TBase<saveRecord_result, saveRecord_result._Fields>, java.io.Serializable, Cloneable, Comparable<saveRecord_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("saveRecord_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRING, (short)0);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new saveRecord_resultStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new saveRecord_resultTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(saveRecord_result.class, metaDataMap);
    }

    public saveRecord_result() {
    }

    public saveRecord_result(
      String success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public saveRecord_result(saveRecord_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    @Override
    public saveRecord_result deepCopy() {
      return new saveRecord_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getSuccess() {
      return this.success;
    }

    public saveRecord_result setSuccess(@org.apache.thrift.annotation.Nullable String success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof saveRecord_result)
        return this.equals((saveRecord_result)that);
      return false;
    }

    public boolean equals(saveRecord_result that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetSuccess()) ? 131071 : 524287);
      if (isSetSuccess())
        hashCode = hashCode * 8191 + success.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(saveRecord_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetSuccess(), other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("saveRecord_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class saveRecord_resultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public saveRecord_resultStandardScheme getScheme() {
        return new saveRecord_resultStandardScheme();
      }
    }

    private static class saveRecord_resultStandardScheme extends org.apache.thrift.scheme.StandardScheme<saveRecord_result> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, saveRecord_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.success = iprot.readString();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, saveRecord_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeString(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class saveRecord_resultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public saveRecord_resultTupleScheme getScheme() {
        return new saveRecord_resultTupleScheme();
      }
    }

    private static class saveRecord_resultTupleScheme extends org.apache.thrift.scheme.TupleScheme<saveRecord_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, saveRecord_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeString(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, saveRecord_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readString();
          struct.setSuccessIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class deleteRecord_args implements org.apache.thrift.TBase<deleteRecord_args, deleteRecord_args._Fields>, java.io.Serializable, Cloneable, Comparable<deleteRecord_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("deleteRecord_args");

    private static final org.apache.thrift.protocol.TField PATIENT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("patientId", org.apache.thrift.protocol.TType.STRING, (short)1);
    private static final org.apache.thrift.protocol.TField ADMISSION_NO_FIELD_DESC = new org.apache.thrift.protocol.TField("admissionNo", org.apache.thrift.protocol.TType.STRING, (short)2);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new deleteRecord_argsStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new deleteRecord_argsTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String patientId; // required
    public @org.apache.thrift.annotation.Nullable String admissionNo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PATIENT_ID((short)1, "patientId"),
      ADMISSION_NO((short)2, "admissionNo");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PATIENT_ID
            return PATIENT_ID;
          case 2: // ADMISSION_NO
            return ADMISSION_NO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PATIENT_ID, new org.apache.thrift.meta_data.FieldMetaData("patientId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      tmpMap.put(_Fields.ADMISSION_NO, new org.apache.thrift.meta_data.FieldMetaData("admissionNo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(deleteRecord_args.class, metaDataMap);
    }

    public deleteRecord_args() {
    }

    public deleteRecord_args(
      String patientId,
      String admissionNo)
    {
      this();
      this.patientId = patientId;
      this.admissionNo = admissionNo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public deleteRecord_args(deleteRecord_args other) {
      if (other.isSetPatientId()) {
        this.patientId = other.patientId;
      }
      if (other.isSetAdmissionNo()) {
        this.admissionNo = other.admissionNo;
      }
    }

    @Override
    public deleteRecord_args deepCopy() {
      return new deleteRecord_args(this);
    }

    @Override
    public void clear() {
      this.patientId = null;
      this.admissionNo = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getPatientId() {
      return this.patientId;
    }

    public deleteRecord_args setPatientId(@org.apache.thrift.annotation.Nullable String patientId) {
      this.patientId = patientId;
      return this;
    }

    public void unsetPatientId() {
      this.patientId = null;
    }

    /** Returns true if field patientId is set (has been assigned a value) and false otherwise */
    public boolean isSetPatientId() {
      return this.patientId != null;
    }

    public void setPatientIdIsSet(boolean value) {
      if (!value) {
        this.patientId = null;
      }
    }

    @org.apache.thrift.annotation.Nullable
    public String getAdmissionNo() {
      return this.admissionNo;
    }

    public deleteRecord_args setAdmissionNo(@org.apache.thrift.annotation.Nullable String admissionNo) {
      this.admissionNo = admissionNo;
      return this;
    }

    public void unsetAdmissionNo() {
      this.admissionNo = null;
    }

    /** Returns true if field admissionNo is set (has been assigned a value) and false otherwise */
    public boolean isSetAdmissionNo() {
      return this.admissionNo != null;
    }

    public void setAdmissionNoIsSet(boolean value) {
      if (!value) {
        this.admissionNo = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case PATIENT_ID:
        if (value == null) {
          unsetPatientId();
        } else {
          setPatientId((String)value);
        }
        break;

      case ADMISSION_NO:
        if (value == null) {
          unsetAdmissionNo();
        } else {
          setAdmissionNo((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PATIENT_ID:
        return getPatientId();

      case ADMISSION_NO:
        return getAdmissionNo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PATIENT_ID:
        return isSetPatientId();
      case ADMISSION_NO:
        return isSetAdmissionNo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof deleteRecord_args)
        return this.equals((deleteRecord_args)that);
      return false;
    }

    public boolean equals(deleteRecord_args that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_patientId = true && this.isSetPatientId();
      boolean that_present_patientId = true && that.isSetPatientId();
      if (this_present_patientId || that_present_patientId) {
        if (!(this_present_patientId && that_present_patientId))
          return false;
        if (!this.patientId.equals(that.patientId))
          return false;
      }

      boolean this_present_admissionNo = true && this.isSetAdmissionNo();
      boolean that_present_admissionNo = true && that.isSetAdmissionNo();
      if (this_present_admissionNo || that_present_admissionNo) {
        if (!(this_present_admissionNo && that_present_admissionNo))
          return false;
        if (!this.admissionNo.equals(that.admissionNo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetPatientId()) ? 131071 : 524287);
      if (isSetPatientId())
        hashCode = hashCode * 8191 + patientId.hashCode();

      hashCode = hashCode * 8191 + ((isSetAdmissionNo()) ? 131071 : 524287);
      if (isSetAdmissionNo())
        hashCode = hashCode * 8191 + admissionNo.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(deleteRecord_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetPatientId(), other.isSetPatientId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPatientId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.patientId, other.patientId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.compare(isSetAdmissionNo(), other.isSetAdmissionNo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetAdmissionNo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.admissionNo, other.admissionNo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    @Override
    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("deleteRecord_args(");
      boolean first = true;

      sb.append("patientId:");
      if (this.patientId == null) {
        sb.append("null");
      } else {
        sb.append(this.patientId);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("admissionNo:");
      if (this.admissionNo == null) {
        sb.append("null");
      } else {
        sb.append(this.admissionNo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class deleteRecord_argsStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public deleteRecord_argsStandardScheme getScheme() {
        return new deleteRecord_argsStandardScheme();
      }
    }

    private static class deleteRecord_argsStandardScheme extends org.apache.thrift.scheme.StandardScheme<deleteRecord_args> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, deleteRecord_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PATIENT_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.patientId = iprot.readString();
                struct.setPatientIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // ADMISSION_NO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.admissionNo = iprot.readString();
                struct.setAdmissionNoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, deleteRecord_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.patientId != null) {
          oprot.writeFieldBegin(PATIENT_ID_FIELD_DESC);
          oprot.writeString(struct.patientId);
          oprot.writeFieldEnd();
        }
        if (struct.admissionNo != null) {
          oprot.writeFieldBegin(ADMISSION_NO_FIELD_DESC);
          oprot.writeString(struct.admissionNo);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class deleteRecord_argsTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public deleteRecord_argsTupleScheme getScheme() {
        return new deleteRecord_argsTupleScheme();
      }
    }

    private static class deleteRecord_argsTupleScheme extends org.apache.thrift.scheme.TupleScheme<deleteRecord_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, deleteRecord_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetPatientId()) {
          optionals.set(0);
        }
        if (struct.isSetAdmissionNo()) {
          optionals.set(1);
        }
        oprot.writeBitSet(optionals, 2);
        if (struct.isSetPatientId()) {
          oprot.writeString(struct.patientId);
        }
        if (struct.isSetAdmissionNo()) {
          oprot.writeString(struct.admissionNo);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, deleteRecord_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(2);
        if (incoming.get(0)) {
          struct.patientId = iprot.readString();
          struct.setPatientIdIsSet(true);
        }
        if (incoming.get(1)) {
          struct.admissionNo = iprot.readString();
          struct.setAdmissionNoIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class deleteRecord_result implements org.apache.thrift.TBase<deleteRecord_result, deleteRecord_result._Fields>, java.io.Serializable, Cloneable, Comparable<deleteRecord_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("deleteRecord_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRING, (short)0);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new deleteRecord_resultStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new deleteRecord_resultTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(deleteRecord_result.class, metaDataMap);
    }

    public deleteRecord_result() {
    }

    public deleteRecord_result(
      String success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public deleteRecord_result(deleteRecord_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    @Override
    public deleteRecord_result deepCopy() {
      return new deleteRecord_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getSuccess() {
      return this.success;
    }

    public deleteRecord_result setSuccess(@org.apache.thrift.annotation.Nullable String success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof deleteRecord_result)
        return this.equals((deleteRecord_result)that);
      return false;
    }

    public boolean equals(deleteRecord_result that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetSuccess()) ? 131071 : 524287);
      if (isSetSuccess())
        hashCode = hashCode * 8191 + success.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(deleteRecord_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetSuccess(), other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("deleteRecord_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class deleteRecord_resultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public deleteRecord_resultStandardScheme getScheme() {
        return new deleteRecord_resultStandardScheme();
      }
    }

    private static class deleteRecord_resultStandardScheme extends org.apache.thrift.scheme.StandardScheme<deleteRecord_result> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, deleteRecord_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.success = iprot.readString();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, deleteRecord_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeString(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class deleteRecord_resultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public deleteRecord_resultTupleScheme getScheme() {
        return new deleteRecord_resultTupleScheme();
      }
    }

    private static class deleteRecord_resultTupleScheme extends org.apache.thrift.scheme.TupleScheme<deleteRecord_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, deleteRecord_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeString(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, deleteRecord_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readString();
          struct.setSuccessIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class deleteFeeList_args implements org.apache.thrift.TBase<deleteFeeList_args, deleteFeeList_args._Fields>, java.io.Serializable, Cloneable, Comparable<deleteFeeList_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("deleteFeeList_args");

    private static final org.apache.thrift.protocol.TField PATIENT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("patientId", org.apache.thrift.protocol.TType.STRING, (short)1);
    private static final org.apache.thrift.protocol.TField ADMISSION_NO_FIELD_DESC = new org.apache.thrift.protocol.TField("admissionNo", org.apache.thrift.protocol.TType.STRING, (short)2);
    private static final org.apache.thrift.protocol.TField ID_FIELD_DESC = new org.apache.thrift.protocol.TField("id", org.apache.thrift.protocol.TType.STRING, (short)3);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new deleteFeeList_argsStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new deleteFeeList_argsTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String patientId; // required
    public @org.apache.thrift.annotation.Nullable String admissionNo; // required
    public @org.apache.thrift.annotation.Nullable String id; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PATIENT_ID((short)1, "patientId"),
      ADMISSION_NO((short)2, "admissionNo"),
      ID((short)3, "id");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PATIENT_ID
            return PATIENT_ID;
          case 2: // ADMISSION_NO
            return ADMISSION_NO;
          case 3: // ID
            return ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PATIENT_ID, new org.apache.thrift.meta_data.FieldMetaData("patientId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      tmpMap.put(_Fields.ADMISSION_NO, new org.apache.thrift.meta_data.FieldMetaData("admissionNo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      tmpMap.put(_Fields.ID, new org.apache.thrift.meta_data.FieldMetaData("id", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(deleteFeeList_args.class, metaDataMap);
    }

    public deleteFeeList_args() {
    }

    public deleteFeeList_args(
      String patientId,
      String admissionNo,
      String id)
    {
      this();
      this.patientId = patientId;
      this.admissionNo = admissionNo;
      this.id = id;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public deleteFeeList_args(deleteFeeList_args other) {
      if (other.isSetPatientId()) {
        this.patientId = other.patientId;
      }
      if (other.isSetAdmissionNo()) {
        this.admissionNo = other.admissionNo;
      }
      if (other.isSetId()) {
        this.id = other.id;
      }
    }

    @Override
    public deleteFeeList_args deepCopy() {
      return new deleteFeeList_args(this);
    }

    @Override
    public void clear() {
      this.patientId = null;
      this.admissionNo = null;
      this.id = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getPatientId() {
      return this.patientId;
    }

    public deleteFeeList_args setPatientId(@org.apache.thrift.annotation.Nullable String patientId) {
      this.patientId = patientId;
      return this;
    }

    public void unsetPatientId() {
      this.patientId = null;
    }

    /** Returns true if field patientId is set (has been assigned a value) and false otherwise */
    public boolean isSetPatientId() {
      return this.patientId != null;
    }

    public void setPatientIdIsSet(boolean value) {
      if (!value) {
        this.patientId = null;
      }
    }

    @org.apache.thrift.annotation.Nullable
    public String getAdmissionNo() {
      return this.admissionNo;
    }

    public deleteFeeList_args setAdmissionNo(@org.apache.thrift.annotation.Nullable String admissionNo) {
      this.admissionNo = admissionNo;
      return this;
    }

    public void unsetAdmissionNo() {
      this.admissionNo = null;
    }

    /** Returns true if field admissionNo is set (has been assigned a value) and false otherwise */
    public boolean isSetAdmissionNo() {
      return this.admissionNo != null;
    }

    public void setAdmissionNoIsSet(boolean value) {
      if (!value) {
        this.admissionNo = null;
      }
    }

    @org.apache.thrift.annotation.Nullable
    public String getId() {
      return this.id;
    }

    public deleteFeeList_args setId(@org.apache.thrift.annotation.Nullable String id) {
      this.id = id;
      return this;
    }

    public void unsetId() {
      this.id = null;
    }

    /** Returns true if field id is set (has been assigned a value) and false otherwise */
    public boolean isSetId() {
      return this.id != null;
    }

    public void setIdIsSet(boolean value) {
      if (!value) {
        this.id = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case PATIENT_ID:
        if (value == null) {
          unsetPatientId();
        } else {
          setPatientId((String)value);
        }
        break;

      case ADMISSION_NO:
        if (value == null) {
          unsetAdmissionNo();
        } else {
          setAdmissionNo((String)value);
        }
        break;

      case ID:
        if (value == null) {
          unsetId();
        } else {
          setId((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PATIENT_ID:
        return getPatientId();

      case ADMISSION_NO:
        return getAdmissionNo();

      case ID:
        return getId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PATIENT_ID:
        return isSetPatientId();
      case ADMISSION_NO:
        return isSetAdmissionNo();
      case ID:
        return isSetId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof deleteFeeList_args)
        return this.equals((deleteFeeList_args)that);
      return false;
    }

    public boolean equals(deleteFeeList_args that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_patientId = true && this.isSetPatientId();
      boolean that_present_patientId = true && that.isSetPatientId();
      if (this_present_patientId || that_present_patientId) {
        if (!(this_present_patientId && that_present_patientId))
          return false;
        if (!this.patientId.equals(that.patientId))
          return false;
      }

      boolean this_present_admissionNo = true && this.isSetAdmissionNo();
      boolean that_present_admissionNo = true && that.isSetAdmissionNo();
      if (this_present_admissionNo || that_present_admissionNo) {
        if (!(this_present_admissionNo && that_present_admissionNo))
          return false;
        if (!this.admissionNo.equals(that.admissionNo))
          return false;
      }

      boolean this_present_id = true && this.isSetId();
      boolean that_present_id = true && that.isSetId();
      if (this_present_id || that_present_id) {
        if (!(this_present_id && that_present_id))
          return false;
        if (!this.id.equals(that.id))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetPatientId()) ? 131071 : 524287);
      if (isSetPatientId())
        hashCode = hashCode * 8191 + patientId.hashCode();

      hashCode = hashCode * 8191 + ((isSetAdmissionNo()) ? 131071 : 524287);
      if (isSetAdmissionNo())
        hashCode = hashCode * 8191 + admissionNo.hashCode();

      hashCode = hashCode * 8191 + ((isSetId()) ? 131071 : 524287);
      if (isSetId())
        hashCode = hashCode * 8191 + id.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(deleteFeeList_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetPatientId(), other.isSetPatientId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPatientId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.patientId, other.patientId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.compare(isSetAdmissionNo(), other.isSetAdmissionNo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetAdmissionNo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.admissionNo, other.admissionNo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.compare(isSetId(), other.isSetId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.id, other.id);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    @Override
    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("deleteFeeList_args(");
      boolean first = true;

      sb.append("patientId:");
      if (this.patientId == null) {
        sb.append("null");
      } else {
        sb.append(this.patientId);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("admissionNo:");
      if (this.admissionNo == null) {
        sb.append("null");
      } else {
        sb.append(this.admissionNo);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("id:");
      if (this.id == null) {
        sb.append("null");
      } else {
        sb.append(this.id);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class deleteFeeList_argsStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public deleteFeeList_argsStandardScheme getScheme() {
        return new deleteFeeList_argsStandardScheme();
      }
    }

    private static class deleteFeeList_argsStandardScheme extends org.apache.thrift.scheme.StandardScheme<deleteFeeList_args> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, deleteFeeList_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PATIENT_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.patientId = iprot.readString();
                struct.setPatientIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // ADMISSION_NO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.admissionNo = iprot.readString();
                struct.setAdmissionNoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // ID
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.id = iprot.readString();
                struct.setIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, deleteFeeList_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.patientId != null) {
          oprot.writeFieldBegin(PATIENT_ID_FIELD_DESC);
          oprot.writeString(struct.patientId);
          oprot.writeFieldEnd();
        }
        if (struct.admissionNo != null) {
          oprot.writeFieldBegin(ADMISSION_NO_FIELD_DESC);
          oprot.writeString(struct.admissionNo);
          oprot.writeFieldEnd();
        }
        if (struct.id != null) {
          oprot.writeFieldBegin(ID_FIELD_DESC);
          oprot.writeString(struct.id);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class deleteFeeList_argsTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public deleteFeeList_argsTupleScheme getScheme() {
        return new deleteFeeList_argsTupleScheme();
      }
    }

    private static class deleteFeeList_argsTupleScheme extends org.apache.thrift.scheme.TupleScheme<deleteFeeList_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, deleteFeeList_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetPatientId()) {
          optionals.set(0);
        }
        if (struct.isSetAdmissionNo()) {
          optionals.set(1);
        }
        if (struct.isSetId()) {
          optionals.set(2);
        }
        oprot.writeBitSet(optionals, 3);
        if (struct.isSetPatientId()) {
          oprot.writeString(struct.patientId);
        }
        if (struct.isSetAdmissionNo()) {
          oprot.writeString(struct.admissionNo);
        }
        if (struct.isSetId()) {
          oprot.writeString(struct.id);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, deleteFeeList_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(3);
        if (incoming.get(0)) {
          struct.patientId = iprot.readString();
          struct.setPatientIdIsSet(true);
        }
        if (incoming.get(1)) {
          struct.admissionNo = iprot.readString();
          struct.setAdmissionNoIsSet(true);
        }
        if (incoming.get(2)) {
          struct.id = iprot.readString();
          struct.setIdIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

  @SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
  public static class deleteFeeList_result implements org.apache.thrift.TBase<deleteFeeList_result, deleteFeeList_result._Fields>, java.io.Serializable, Cloneable, Comparable<deleteFeeList_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("deleteFeeList_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRING, (short)0);

    private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new deleteFeeList_resultStandardSchemeFactory();
    private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new deleteFeeList_resultTupleSchemeFactory();

    public @org.apache.thrift.annotation.Nullable String success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final java.util.Map<String, _Fields> byName = new java.util.HashMap<String, _Fields>();

      static {
        for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      @org.apache.thrift.annotation.Nullable
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      @Override
      public short getThriftFieldId() {
        return _thriftId;
      }

      @Override
      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(deleteFeeList_result.class, metaDataMap);
    }

    public deleteFeeList_result() {
    }

    public deleteFeeList_result(
      String success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public deleteFeeList_result(deleteFeeList_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    @Override
    public deleteFeeList_result deepCopy() {
      return new deleteFeeList_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    @org.apache.thrift.annotation.Nullable
    public String getSuccess() {
      return this.success;
    }

    public deleteFeeList_result setSuccess(@org.apache.thrift.annotation.Nullable String success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    @Override
    public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((String)value);
        }
        break;

      }
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    @Override
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that instanceof deleteFeeList_result)
        return this.equals((deleteFeeList_result)that);
      return false;
    }

    public boolean equals(deleteFeeList_result that) {
      if (that == null)
        return false;
      if (this == that)
        return true;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      int hashCode = 1;

      hashCode = hashCode * 8191 + ((isSetSuccess()) ? 131071 : 524287);
      if (isSetSuccess())
        hashCode = hashCode * 8191 + success.hashCode();

      return hashCode;
    }

    @Override
    public int compareTo(deleteFeeList_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.compare(isSetSuccess(), other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    @org.apache.thrift.annotation.Nullable
    @Override
    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      scheme(iprot).read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      scheme(oprot).write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("deleteFeeList_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class deleteFeeList_resultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public deleteFeeList_resultStandardScheme getScheme() {
        return new deleteFeeList_resultStandardScheme();
      }
    }

    private static class deleteFeeList_resultStandardScheme extends org.apache.thrift.scheme.StandardScheme<deleteFeeList_result> {

      @Override
      public void read(org.apache.thrift.protocol.TProtocol iprot, deleteFeeList_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.success = iprot.readString();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      @Override
      public void write(org.apache.thrift.protocol.TProtocol oprot, deleteFeeList_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeString(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class deleteFeeList_resultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
      @Override
      public deleteFeeList_resultTupleScheme getScheme() {
        return new deleteFeeList_resultTupleScheme();
      }
    }

    private static class deleteFeeList_resultTupleScheme extends org.apache.thrift.scheme.TupleScheme<deleteFeeList_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, deleteFeeList_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet optionals = new java.util.BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeString(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, deleteFeeList_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
        java.util.BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readString();
          struct.setSuccessIsSet(true);
        }
      }
    }

    private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
      return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
    }
  }

}
