package com.crhms.cloud.mqs.basic.common;

import java.io.Serializable;
import java.util.Map;

/**
 * Created by zhaoyachao on 2022/12/15.
 * 返回结果封装类
 */
public class MqsResult<T extends Object> implements Serializable {
    private int code;
    private T data;
    private T warn;
    private String msg;

    public MqsResult() {
    }

    public MqsResult(int code, T data) {
        this.code = code;
        this.data = data;
    }

    public MqsResult(int code, T data, T warn){
        this.code = code;
        this.data = data;
        this.warn = warn;
    }

    public MqsResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public MqsResult(int code, T data, String msg) {
        this.code = code;
        this.data = data;
        this.msg = msg;
    }

    public MqsResult(int code, T data, String msg, Map permission) {
        this.code = code;
        this.data = data;
        this.msg = msg;
    }

    public MqsResult(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public T getWarn() {
        return warn;
    }

    public void setWarn(T warn) {
        this.warn = warn;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public static MqsResult ERROR() {
        return new MqsResult(201);
    }

    public static MqsResult ERROR(String msg) {
        return new MqsResult(201, msg);
    }

    public static MqsResult ERROR(String msg, String errorInfo) {
        return new MqsResult(201, msg, errorInfo);
    }

    public static MqsResult OK(Object data) {
        return new MqsResult(200, data);
    }

    public static MqsResult OK(Object data,Object warn){return new MqsResult(200,data,warn);}

    public static MqsResult OK(String msg) {
        return new MqsResult(200, msg);
    }

    public static MqsResult OK(Object data, String msg) {
        return new MqsResult(200, data, msg);
    }

    public static MqsResult OK(Object data, String msg, Map permission) {
        return new MqsResult(200, data, msg, permission);
    }

    public static MqsResult Exception (String msg, String errorInfo) {
        return new MqsResult(500, msg, errorInfo);
    }
}
