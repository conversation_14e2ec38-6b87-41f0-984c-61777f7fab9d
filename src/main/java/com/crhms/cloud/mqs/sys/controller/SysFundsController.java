package com.crhms.cloud.mqs.sys.controller;

import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.sys.domain.SysFunds;
import com.crhms.cloud.mqs.sys.domain.SysFundsAlone;
import com.crhms.cloud.mqs.sys.service.SysFundsAloneService;
import com.crhms.cloud.mqs.sys.service.SysFundsService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 运营管理-基金管理(SysFunds)表控制层
 *
 * <AUTHOR>
 * @since 2022-12-15 16:43:03
 */
@RestController
@RequestMapping("api/mqs/sysFunds")
public class SysFundsController {
    /**
     * 服务对象
     */
    @Resource
    private SysFundsService sysFundsService;
    @Resource
    private SysFundsAloneService SysFundsAloneService;

    /**
     * 查询年度计划列表
     *
     * @return 查询结果
     */
    @GetMapping("/queryList")
    public ResponseEntity<List<SysFunds>> queryList() {
        return ResponseEntity.ok(this.sysFundsService.queryList());
    }

    /**
     * 查询年度计划明细列
     *
     * @return 查询结果
     */
    @GetMapping("/queryDetail/{id}")
    public ResponseEntity<SysFunds> queryDetail(@PathVariable("id") Long id) {
        return ResponseEntity.ok(this.sysFundsService.queryDetail(id));
    }

    /**
     * 编辑、新增年度计划
     *
     * @return
     */
    @PostMapping("/edit")
    public ResponseEntity<SysFunds> editData(@RequestBody SysFunds sysFunds) {
        return ResponseEntity.ok(this.sysFundsService.editData(sysFunds));
    }

    /**
     * 删除年度计划
     *
     * @return
     */
    @GetMapping("/delete/{id}")
    public ResponseEntity deleteData(@PathVariable("id") Long id) {
        this.sysFundsService.deleteData(id);
        return ResponseEntity.ok("删除成功！");
    }


    /**
     * 手动执行 统计年度基金
     *
     * @return 查询结果
     */
    @GetMapping("/upDateUsedAmountByYear")
    public ResponseEntity upDateUsedAmountByYear(@RequestParam(value = "year") String year) {
        this.sysFundsService.upDateUsedAmountByYear(year, LoginContext.getHospitalId());
        return ResponseEntity.ok("执行成功!");
    }

    /**
     * 查询可配置的单独管理维度
     *
     * @return 查询结果
     */
    @GetMapping("/alone/typeList")
    public ResponseEntity<List<Map<String,String>>> aloneTypeList() {

        return ResponseEntity.ok(this.sysFundsService.aloneTypeList());
    }

    /**
     * 删除单个单独管理维度
     *
     * @return 查询结果
     */
    @DeleteMapping("/alone/delete/{id}")
    public ResponseEntity aloneDelete(@PathVariable("id") Long id) {
        this.SysFundsAloneService.deleteById(id);
        return ResponseEntity.ok("已删除！");
    }

}

