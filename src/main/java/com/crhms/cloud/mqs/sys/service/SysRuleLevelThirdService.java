package com.crhms.cloud.mqs.sys.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.sys.domain.SysRuleLevelThird;
import com.crhms.cloud.mqs.sys.mapper.SysRuleLevelThirdMapper;
import com.crhms.cloud.mqs.sys.service.SysRuleLevelThirdService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 三方规则级别映射(SysRuleLevelThird)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-21 17:53:36
 */
@Service("sysRuleLevelThirdService")
public class SysRuleLevelThirdService extends ServiceImpl< SysRuleLevelThirdMapper, SysRuleLevelThird> {

}
