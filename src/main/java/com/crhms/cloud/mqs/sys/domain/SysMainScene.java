package com.crhms.cloud.mqs.sys.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.dto.SysSceneDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.List;

/**
 * 运营管理-主审核场景(SysMainScene)实体类
 *
 * <AUTHOR>
 * @since 2024-09-12 13:37:48
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_main_scene")
public class SysMainScene extends BaseDomain implements Serializable {

    private static final long serialVersionUID = -83110375245574117L;

    /**
     * 表id
     */
    @TableId
    private Long id;

    /**
     * 审核场景
     */
    @TableField
    private String mainSceneCode;
    /**
     * 审核场景名称
     */
    @TableField
    private String mainSceneName;

    /**
     * 排序
     */
    @TableField
    private Integer orderNum;

    /**
     * 院区id
     */
    @TableField
    private String hospitalId;


    /**
     * 院区id
     */
    @TableField(exist = false)
    private List<SysSceneDTO> childScene;
}
