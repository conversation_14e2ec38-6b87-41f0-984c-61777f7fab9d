<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_kbm.mapper.CgGuideMapper">

    <select id="selectDiseaseList" resultType="java.util.Map">
        SELECT
            disease_code,
            disease_name
        FROM
            mqs_kbm_cg_guide_disease t
        <where>
            <if test="diseaseName != null and diseaseName != ''">
                t.disease_name LIKE CONCAT('%',#{diseaseName},'%')
            </if>
        </where>
        GROUP BY
            disease_code,
            disease_name
    </select>
    <select id="selectSummary" resultType="com.crhms.cloud.mqs.mqs_kbm.vo.CgGuideSummary">
        SELECT
            disease_code,
            disease_name,
            disease_ename,
            other_name1,
            other_name2,
            other_name3,
            other_name4,
            other_name5,
            clinical_class,
            treatment,
            data_source
        FROM
            mqs_kbm_cg_guide_disease
        WHERE
            disease_code = #{diseaseCode}
    </select>
    <select id="selectCheckItemList" resultType="com.crhms.cloud.mqs.mqs_kbm.vo.CgGuideCheckItem">
        SELECT
            disease_code,
            service_name,
            useladder,
            service_detail
        FROM
            mqs_kbm_cg_guide_check
        WHERE
            disease_code = #{diseaseCode}
          AND service_class = "1"
        GROUP BY
            disease_code,
            service_name,
            useladder,
            service_detail
    </select>
    <select id="selectGeneralItemList" resultType="com.crhms.cloud.mqs.mqs_kbm.vo.CgGuideGeneralItem">
        SELECT
            disease_code,
            service_name,
            service_number,
            service_process,
            service_detail,
            remarks
        FROM
            mqs_kbm_cg_guide_check
        WHERE
            disease_code = #{diseaseCode}
          AND service_class = "2"
        GROUP BY
            disease_code,
            service_name,
            service_number,
            service_process,
            service_detail,
            remarks
    </select>
    <select id="selectOperationItemList" resultType="com.crhms.cloud.mqs.mqs_kbm.vo.CgGuideOperationItem">
        SELECT
            disease_code,
            service_name,
            infectionpre,
            service_detail,
            remarks
        FROM
            mqs_kbm_cg_guide_check
        WHERE
            disease_code = #{diseaseCode}
          AND service_class = "4"
        GROUP BY
            disease_code,
            service_name,
            infectionpre,
            service_detail,
            remarks
    </select>
    <select id="selectDrugItemList" resultType="com.crhms.cloud.mqs.mqs_kbm.vo.CgGuideDrugItem">
        SELECT
            disease_code,
            service_name,
            service_type_name,
            service_union,
            useladder,
            medication,
            frequency_class,
            service_number,
            service_process,
            service_detail,
            remarks
        FROM
            mqs_kbm_cg_guide_check
        WHERE
            disease_code = #{diseaseCode}
          AND service_class = "3"
        GROUP BY
            disease_code,
            service_name,
            service_type_name,
            service_union,
            useladder,
            medication,
            frequency_class,
            service_number,
            service_process,
            service_detail,
            remarks
    </select>
</mapper>