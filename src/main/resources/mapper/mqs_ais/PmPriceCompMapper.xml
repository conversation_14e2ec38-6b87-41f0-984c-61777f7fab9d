<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_ais.mapper.MqsAuditMonitorMapper">


    <!--年度使用情况:查询审核明细数和审核费用-->
    <select id="nAuditDetailAndAuditFee" resultType="java.util.Map">
        SELECT
        count(detail.detail_no) as nAuditDetail, -- 审核明细数
        sum(detail.costs) as nAuditFee -- 审核费用总金额
        FROM mqs_fee_list detail
        <if test="psnTypes != null and psnTypes != '' ">
        LEFT JOIN mqs_medical_case head ON head.`no` = detail.`no` and head.audit_scenario = detail.audit_scenario
        </if>
        <where>
            detail.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and detail.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and detail.bill_date &lt;= #{endTime}
            </if>
            <if test="auditScenario != null and auditScenario!= '' and auditScenario != 'all' ">
                and detail.audit_scenario like concat(#{auditScenario},'%')
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
    </select>

    <!--年度使用情况:查询强制保存的明细单量和强制保存的项目费用-->
    <select id="nForcedSaveAndProjectCost" resultType="java.util.Map">
        SELECT
        count(detail.detail_no) as nForcedSave, -- 强制保存的明细单量
        sum(detail.costs) as costForcedSave -- 强制保存的项目费用
        FROM mqs_fee_list detail
        <if test="psnTypes != null and psnTypes != '' ">
            LEFT JOIN mqs_medical_case head ON head.`no` = audit.`no` and head.audit_scenario = detail.audit_scenario
        </if>
        <where>
            detail.violation_flag = '1'
            and detail.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and detail.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and detail.bill_date &lt;= #{endTime}
            </if>
            <if test="auditScenario != null and auditScenario!= '' and auditScenario != 'all' ">
                and detail.audit_scenario like concat(#{auditScenario},'%')
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
    </select>

    <!--违规规则排名: 查询违规规则排名-->
    <select id="rankRule" resultType="java.util.Map">
        SELECT
            detail.rule_code as ruleCode,
            detail.rule_name as ruleName,
            count(distinct detail.detail_no) as numberOfViolations,
            sum(detail.costs) as violationAmount
        FROM mqs_audit_fee_list detail
        <if test="psnTypes != null and psnTypes != '' ">
            LEFT JOIN mqs_medical_case head ON head.`no` = detail.`no` and head.audit_scenario = detail.audit_scenario
        </if>
        <where>
            detail.detail_no is not null
            <if test="startTime != null and startTime != '' ">
                and detail.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and detail.bill_date &lt;= #{endTime}
            </if>
            <if test="auditScenarioList != null and auditScenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="auditScenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
        GROUP BY
            ruleCode ,
            ruleName
        ORDER BY
            <choose>
                <when test="rankFlag == 'number'">
                    numberOfViolations
                </when>
                <otherwise>
                    violationAmount
                </otherwise>
            </choose>
            DESC
        LIMIT 10
    </select>


    <!--违规规则: 查询所有违规规则的违规金额-->
    <select id="ruleTotalAmount" resultType="java.math.BigDecimal">
        SELECT
        sum(detail.costs) as totalAmount
        FROM mqs_fee_list detail
        <if test="psnTypes != null and psnTypes != '' ">
            LEFT JOIN mqs_medical_case head ON head.audit_scenario = detail.audit_scenario and head.`no` = detail.`no`
        </if>
        <where>
            detail.hospital_id = #{hospitalId}
            and detail.violation_flag = '1'
            <if test="startTime != null and startTime != '' ">
                and detail.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and detail.bill_date &lt;= #{endTime}
            </if>
            <if test="auditScenarioList != null and auditScenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="auditScenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
    </select>


    <!--违规项目: 查询违规项目排名-->
    <select id="rankProject" resultType="java.util.Map">
        SELECT
        detail.item_id as projectCode,
        detail.item_name as projectName,
        count(detail.detail_no) as numberOfViolations,
        sum(detail.costs) as violationAmount
        FROM mqs_fee_list detail
        <if test="psnTypes != null and psnTypes != '' ">
            LEFT JOIN mqs_medical_case head ON  head.audit_scenario = detail.audit_scenario and head.`no` = detail.`no`
        </if>
        <where>
            detail.violation_flag = '1'
            and detail.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and detail.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and detail.bill_date &lt;= #{endTime}
            </if>
            <if test="auditScenarioList != null and auditScenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="auditScenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
        GROUP BY
        projectCode ,
        projectName
        ORDER BY
        <choose>
            <when test="rankFlag == 'number'">
                numberOfViolations
            </when>
            <otherwise>
                violationAmount
            </otherwise>
        </choose>
        DESC
        LIMIT 10
    </select>


    <!--违规项目: 查询所有违规项目的违规金额-->
    <select id="projectTotalAmount" resultType="java.math.BigDecimal">
        SELECT
        sum(detail.costs) as totalAmount
        FROM mqs_fee_list detail
        LEFT JOIN mqs_medical_case head ON head.audit_scenario = detail.audit_scenario and head.`no` = detail.`no`
        <where>
            detail.violation_flag = '1'
            and detail.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and head.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and head.bill_date &lt;= #{endTime}
            </if>
            <if test="auditScenarioList != null and auditScenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="auditScenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
    </select>


    <!--科室违规明细-->
    <select id="deptViolationDetails" resultType="java.util.Map">
        SELECT
            detail.apply_dept_code as deptCode,
            detail.apply_dept_name as deptName,
            count(detail.detail_no) as details,
            countIf(detail.detail_no, detail.violation_flag = '1') as violationDetails
        FROM mqs_fee_list detail
        <if test="psnTypes != null and psnTypes != '' ">
        LEFT JOIN mqs_medical_case head ON head.audit_scenario = detail.audit_scenario and head.`no` = detail.`no`
        </if>
        <where>
            detail.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and detail.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and detail.bill_date &lt;= #{endTime}
            </if>
            <if test="auditScenarioList != null and auditScenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="auditScenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
        GROUP BY
        deptCode ,
        deptName
    </select>

    <!--科室违规费用-->
    <select id="deptViolationFees" resultType="java.util.Map">
        SELECT
        detail.apply_dept_code as deptCode,
        detail.apply_dept_name as deptName,
        sum(detail.costs) as details,
        sumIf(detail.costs, detail.violation_flag = '1') as violationDetails
        FROM mqs_fee_list detail
        <if test="psnTypes != null and psnTypes != '' ">
            LEFT JOIN mqs_medical_case head ON head.audit_scenario = detail.audit_scenario and head.`no` = detail.`no`
        </if>
        <where>
            detail.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and detail.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and detail.bill_date &lt;= #{endTime}
            </if>
            <if test="auditScenarioList != null and auditScenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="auditScenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
        GROUP BY
        deptCode ,
        deptName
    </select>

    <!--查询违规明细趋势-->
    <select id="detailsTendency" resultType="java.util.Map">
        SELECT
        formatDateTime(t.bill_date,'%Y-%m') as whichMonth,
        count(t.detail_no) as details,
        countIf(t.detail_no, t.violation_flag = '1') as violationDetails
        FROM (
            SELECT
            *
            FROM mqs_fee_list detail
            <if test="psnTypes != null and psnTypes != '' ">
                LEFT JOIN mqs_medical_case head ON head.audit_scenario = detail.audit_scenario and head.`no` = detail.`no`
            </if>
            <where>
                detail.hospital_id = #{hospitalId}
                <if test="startTime != null and startTime != '' ">
                    and detail.bill_date &gt;= #{startTime}
                </if>
                <if test="endTime != null and endTime != '' ">
                    and detail.bill_date &lt;= #{endTime}
                </if>

                <if test="auditScenarioList != null and auditScenarioList.size > 0">
                    and detail.audit_scenario in
                    <foreach collection="auditScenarioList" open="(" close=")" separator="," item="auditScenario">
                        #{auditScenario}
                    </foreach>
                </if>
                <if test="psnTypes != null and psnTypes != '' ">
                    and head.personnel_type = #{psnTypes}
                </if>
            </where>
             ) t
        GROUP BY
        whichMonth
        ORDER BY whichMonth
    </select>


    <!--查询违规费用趋势-->
    <select id="feesTendency" resultType="java.util.Map">
        SELECT
        formatDateTime(detail.bill_date,'%Y-%m') as whichMonth,
        sum(detail.costs) as details,
        sumIf(detail.costs, detail.violation_flag = '1') as violationDetails
        FROM mqs_fee_list detail
        <if test="psnTypes != null and psnTypes != '' ">
            LEFT JOIN mqs_medical_case head ON head.audit_scenario = detail.audit_scenario and head.`no` = detail.`no`
        </if>
        <where>
            detail.hospital_id = #{hospitalId}
            <if test="months != null and months.size > 0">
                and whichMonth in
                <foreach collection="months" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="auditScenarioList != null and auditScenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="auditScenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
        GROUP BY
        whichMonth
        ORDER BY whichMonth
    </select>

    <!--查询费用结构异常占比-->
    <select id="costStructure" resultType="java.util.Map">
        SELECT
            detail.item_type_code as item_type_code ,
            detail.item_type_name as item_type_name ,
            sum(detail.costs) as violationAmount
        FROM mqs_fee_list detail
        <if test="psnTypes != null and psnTypes != '' ">
        LEFT JOIN mqs_medical_case head ON head.audit_scenario = detail.audit_scenario and head.`no` = detail.`no`
        </if>
        <where>
            detail.violation_flag = '1'
            and detail.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and detail.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and detail.bill_date &lt;= #{endTime}
            </if>
            <if test="auditScenarioList != null and auditScenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="auditScenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
        GROUP BY
        item_type_code,
        item_type_name
    </select>

    <!--查询场景审核人次-->
    <select id="nAuditScenario" resultType="java.util.Map">
        SELECT
            head .audit_scenario ,
            count(1) as nAuditScenario
        FROM
            mqs_medical_case_his head
        <where>
            <if test="startTime != null and startTime != '' ">
                and head.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and head.bill_date &lt;= #{endTime}
            </if>
            <if test="auditScenarioList != null and auditScenarioList.size > 0">
                and head.audit_scenario in
                <foreach collection="auditScenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
        GROUP BY
            head.audit_scenario

    </select>


    <!--查询每个违规主单的规则名称、规则分类、违规主单数量、违规金额-->
    <select id="violationHead" resultType="java.util.Map">
        SELECT
        audit.rule_code as ruleCode,
        audit.rule_name as ruleName,
        audit.rule_type as rule_type ,
        audit.rule_type_name as rule_type_name ,
        count(head.no) as numberOfViolations,
        sum(head.total_amount) as violationAmount
        FROM mqs_audit_result audit
        LEFT JOIN mqs_medical_case head ON head.`no` = audit.`no` and head.audit_scenario = audit.audit_scenario
        <where>
            audit.detail_no == '0'
            and audit.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and head.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and head.bill_date &lt;= #{endTime}
            </if>
            <if test="auditScenarioList != null and auditScenarioList.size > 0">
                and audit.audit_scenario in
                <foreach collection="auditScenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="psnTypes != null and psnTypes != '' ">
                and head.personnel_type = #{psnTypes}
            </if>
        </where>
        GROUP BY
        ruleCode ,
        ruleName ,
        rule_type,
        rule_type_name
        ORDER BY
        <choose>
            <when test="rankFlag == 'number'">
                numberOfViolations
            </when>
            <otherwise>
                violationAmount
            </otherwise>
        </choose>
        DESC
    </select>



    <select id="selectTotalAuditTimes" resultType="java.math.BigDecimal">
        select sum(times)
        from (
        <foreach collection="scenarioList" separator="union all" item="table">
            (
            select count(1) as times
            from ${table}_his
            where
            hospital_id = #{hospitalId}
            and audit_time &gt;= concat(#{now},' 00:00:00')
            and audit_time &lt;= concat(#{now},' 23:59:59')
            <if test="psnTypes != null and psnTypes != '' ">
               and personnel_type = #{psnTypes}
            </if>
            )
        </foreach>
        )c

    </select>
    <select id="selectTotalInNums" resultType="java.math.BigDecimal">
        select count(distinct patient_id)
        from mqs_base_medical_stats
        where
        hospital_id = #{hospitalId}
        and discharged = '0'
        <if test="psnTypes != null and psnTypes != '' ">
            and personnel_type = #{psnTypes}
        </if>
    </select>
    <select id="selectTotalOutNums" resultType="java.math.BigDecimal">

        select count(distinct patient_id)
        from (
        <foreach collection="scenarioList" separator="union all" item="table">
            (
            select distinct patient_id as patient_id
            from ${table}
            where
            hospital_id = #{hospitalId}
            and discharge_date is not null
            and discharge_date &gt;= concat(#{now},' 00:00:00')
            and discharge_date &lt;= concat(#{now},' 23:59:59')
            <if test="psnTypes != null and psnTypes != '' ">
               and personnel_type = #{psnTypes}
            </if>
            )
        </foreach>
        )c

    </select>
    <select id="selectAuditReturn" resultType="java.util.Map">
        SELECT
        COUNT(1) as totalReturnNums,
        SUM(amount) as totalReturnAmount
        FROM mqs_sys_audit_return_log
        WHERE
        hospital_id = #{hospitalId}
        and return_date &gt;= #{startTime}
        and return_date &lt;= #{endTime}
        and audit_scenario in
        <foreach collection="scenarioList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="psnTypes != null and psnTypes != '' ">
            and personnel_type = #{psnTypes}
        </if>

    </select>




</mapper>