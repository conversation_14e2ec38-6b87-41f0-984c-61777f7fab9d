package com.crhms.cloud.mqs.mqs_pm.mapper;

import com.crhms.cloud.mqs.mqs_pm.domain.PmStdConsume;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdDrug;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdServiceItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物价管理-院内物价对比 mapper接口
 *
 * @author: wangxingang
 * @date: 2023/02/02
 */

@Mapper
public interface PmPriceCompMapper {

    /**
     * 查询服务项目
     * @param projectCode 项目编码
     * @param startPeriod 时间周期-开始时间
     * @param endPeriod 时间周期-结束时间
     */
    List<PmStdServiceItem> queryServiceItem(@Param("projectCode") String projectCode,
                                            @Param("startPeriod") String startPeriod,
                                            @Param("endPeriod") String endPeriod);

    List<PmStdDrug> queryDrug(@Param("projectCode") String projectCode,
                              @Param("startPeriod") String startPeriod,
                              @Param("endPeriod") String endPeriod);

    List<PmStdConsume> queryConsume(String projectCode, String startPeriod, String endPeriod);
}
