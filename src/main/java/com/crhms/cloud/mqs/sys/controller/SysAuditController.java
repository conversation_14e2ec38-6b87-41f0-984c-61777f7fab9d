package com.crhms.cloud.mqs.sys.controller;


import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.sys.service.SysAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 集成平台 审核结果查询控制层
 *
 * <AUTHOR>
 * @since 2023-01-12 09:43:03
 */
@RestController
@RequestMapping("api/mqs/sysAudit")
public class SysAuditController {

    @Autowired
    SysAuditService sysAuditService;

    /**
     * 查询违规规则
     *
     * @param no      主单号
     * @param batchNo 批次号
     * @return 查询结果
     */
    @GetMapping("/queryAuditRules")
    public ResponseEntity<List<Map<String, Object>>> queryAuditRules(@RequestParam(value = "auditScenario") String auditScenario,
                                                                     @RequestParam(value = "no") String no,
                                                                     @RequestParam(value = "isHis") String isHis,
                                                                     @RequestParam(value = "batchNo") String batchNo) {
        return ResponseEntity.ok(sysAuditService.queryAuditRules(auditScenario, no, isHis, batchNo, LoginContext.getHospitalId()));
    }

    /**
     * 查询违规结果明细
     * @param auditScenario
     * @param no
     * @param isHis
     * @param batchNo
     * @return
     */
    @GetMapping("/queryAuditResult")
    public ResponseEntity<Map<String, Object>> queryAuditResult(@RequestParam(value = "auditScenario") String auditScenario,
                                                                @RequestParam(value = "no") String no,
                                                                @RequestParam(value = "ruleCode") String ruleCode,
                                                                @RequestParam(value = "detail_no") String detailNo,
                                                                @RequestParam(value = "isHis") String isHis,
                                                                @RequestParam(value = "batchNo") String batchNo) {
        return ResponseEntity.ok(sysAuditService.queryAuditResult(auditScenario, no, ruleCode, detailNo, isHis, batchNo, LoginContext.getHospitalId()));
    }
    /**
     * 查询主单违规明细
     * @param auditScenario
     * @param no
     * @return
     */
    @GetMapping("/queryCaseAuditResult")
    public ResponseEntity<List<Map<String, Object>>> queryCaseAuditResult(@RequestParam(value = "auditScenario") String auditScenario,
                                                                    @RequestParam(value = "no") String no ){
        return ResponseEntity.ok(sysAuditService.queryCaseAuditResult(auditScenario, no, LoginContext.getHospitalId()));
    }


}
