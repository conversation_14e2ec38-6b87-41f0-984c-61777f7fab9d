package com.crhms.cloud.mqs.ai;

import cn.hutool.core.convert.Convert;
import com.crhms.cloud.mqs.ai.service.AiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Formatter;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Andy
 * @Date: 2025/2/13 09:27
 * @Description:
 **/
@Slf4j
@RestController
@RequestMapping("/api/mqs/ai")
public class AiUtils {

    @Value("${crhms.mqs.ai.url2}")
    private String aiUrl;
    @Value("${crhms.mqs.ai.enable}")
    private Boolean enable;

    @Autowired
    AiService aiService;

    @GetMapping("/enable")
    public ResponseEntity<String> generateSign( ) {
        return new ResponseEntity(enable,HttpStatus.OK);
    }


    @GetMapping("/generateSign")
    public ResponseEntity<String> generateSign(@RequestParam("userId") String userId) throws InvalidKeyException, NoSuchAlgorithmException {
        long ts = System.currentTimeMillis();
        String hmacSha1 = aiService.hmacSha1("TS76JDH109U", userId + ts);
        log.info("时间戳->{} ", ts);
        log.info("签名-> {}", hmacSha1);
        Map<String, Object> map = new HashMap<>();
        map.put("api-key", "GDFS4682");
        map.put("api-timestamp", Convert.toStr(ts));
        map.put("api-sign", hmacSha1);
        map.put("userId", userId);
        return new ResponseEntity(map, HttpStatus.OK);
    }

    @GetMapping("/iframeUrl")
    public ResponseEntity<String> iframeUrl() {
        return new ResponseEntity(aiUrl, HttpStatus.OK);
    }


    @GetMapping("/audit")
    public ResponseEntity<Map<String,Object>> aiAudit(@RequestParam("no") String no){
        return ResponseEntity.ok(aiService.aiAudit(no));
    }

    @GetMapping("/audit/reflush")
    public ResponseEntity<Map<String,Object>> aiAuditReflush(@RequestParam("no") String no){
        return ResponseEntity.ok(aiService.aiAuditReflush(no));
    }
}