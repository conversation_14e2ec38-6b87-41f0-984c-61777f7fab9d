# 服务名
spring.application.name=mqs
server.port=9005
# 远程测试 指定本机ip
# spring.cloud.nacos.discovery.ip = *************
# spring.cloud.nacos.discovery.port = 9005
##########################################################################################
###########################     青岛产品服务通用配置（禁止修改）     ###########################
##########################################################################################
# Nacos配置,指定注册中心地址及命名空间,关闭配置中心
spring.cloud.nacos.server-addr=${NACOS_SERVER:************:8848}
spring.cloud.nacos.config.enabled=false
spring.cloud.nacos.discovery.enabled=true
spring.cloud.nacos.discovery.namespace=${NACOS_NAMESPACE:}
spring.cloud.nacos.discovery.cluster-name=DEFAULT
spring.cloud.nacos.discovery.username=${NACOS_USERNAME:nacos}
spring.cloud.nacos.discovery.password=${NACOS_PASSWORD:nacos}

# 应用版本,数据库初始化配置
app.version=@project.version@
app.data-dir=${DATA_DIR:E:\\data\}
spring.liquibase.enabled=${LIQUIBASE_ENABLED:false}
spring.liquibase.change-log=classpath:db/master.xml

# 单点登录配置
crhms.sso-url=${SSO_URL:https://172.26.0.100:10801}
crhms.sso-redirect_uri=${SSO_REDIRECT:https://172.26.0.100:11100/callback}

# 数据库连接池配置
#spring.datasource.url=${DATASOURCE_URL:*****************************************************************************************************************************************}
#spring.datasource.username=${DATASOURCE_USERNAME:root}
#spring.datasource.password=${DATASOURCE_PASSWORD:<EMAIL>}
#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.druid.initial-size=50
#spring.datasource.druid.max-active=500
#spring.datasource.druid.min-idle=20

# Redis连接池配置
spring.redis.host=${REDIS_HOST:************}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.database=${REDIS_DB:1}
spring.redis.password=${REDIS_PASSWORD:}
spring.redis.lettuce.pool.max-active=500
spring.redis.lettuce.pool.max-idle=100
spring.redis.lettuce.pool.min-idle=20

# 日志配置
logging.level.root=${LOG_LEVEL:info}
logging.level.com.crhms=${LOG_LEVEL:info}
logging.level.com.alibaba.nacos.client.naming=warn
logging.level.com.alibaba.nacos.common.remote.client=warn
logging.file.path=${LOG_PATH:/app/logs}
logging.file.name=${LOG_PATH:/app/logs}/mqs.log

# 服务间Feign调用配置
feign.httpclient.enabled=true
feign.httpclient.max-connections=200
feign.httpclient.max-connections-per-route=50
feign.client.config.default.connectTimeout=30000
feign.client.config.default.readTimeout=30000
feign.client.config.default.logger-level=FULL

# mybatis-plus配置
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.global-config.db-config.id-type=ASSIGN_ID

#arthas
arthas.agent-id: 123 
arthas.app-name: mqs2.0
arthas.username: crhms
arthas.password: Gxjk@2023
# 禁用 Arthas 自动启动
spring.arthas.enabled=false
## 安装的arthas-tunnel-server服务，地址和端口根据自身情况修改
arthas.tunnel-server: ws://localhost:7777/ws


# 是否启用多租户
mybatis-plus.tenant-enable=false
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# 请求大小配置
spring.servlet.multipart.max-file-size=1000MB
spring.servlet.multipart.max-request-size=1000MB
server.max-http-header-size=1024000

spring.main.allow-bean-definition-overriding=true

# 引擎审核-超时时间s
mqs.conf.engAudit.timeout=${ENGAUDIT_TIMEOUT:30}

#是否启用数据权限
crhms.mqs.data.permissions = ${DATA_PERMISSIONS:false}

# AI
crhms.mqs.ai.url=${AI_URL:http://************:1024}
crhms.mqs.ai.enable=${AI_ENABLE:false}
crhms.mqs.ai.url2=${AI_URL2:https://************:1026}
