package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpTfHis;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpTfHisMapper;
import com.crhms.cloud.mqs.mqs_hp.service.HpTfHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-转院科室审核-历史表(HpTfHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 10:22:14
 */
@Service("hpTfHisService")
public class HpTfHisService extends ServiceImpl< HpTfHisMapper, HpTfHis> {

}
