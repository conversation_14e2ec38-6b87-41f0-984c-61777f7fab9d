package com.crhms.cloud.mqs.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.mqs.sys.domain.SysLogs;
import com.crhms.cloud.mqs.sys.domain.SysRules;
import com.crhms.cloud.mqs.sys.dto.SysLogsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SysLogsMapper extends BaseMapper<SysLogs> {


    /*
    *  分页查询日志
    * */
    List<SysLogs> queryPage(@Param("pageInfo") Page<Object> pageInfo,
                                     @Param("queryVO") SysLogsVO queryVO,
                                     @Param("hospitalId") String hospitalId);
}
