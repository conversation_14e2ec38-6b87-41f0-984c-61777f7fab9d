package com.crhms.cloud.mqs.basic.vo;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * 审核接口基础类
 */
@Data
@Getter
@Setter
@Builder
public class BaseAuditResultDto {

    /**
     * 200100.无违规 200200 单据违规 200300 处理异常
     */
    private String result_code;
    /**
     * 审核批次号
     */
    private String batch;
    /**
     * 描述
     */
    private String msg;
    /**
     * 弹窗地址
     */
    private String url;
    /**
     * 审核结果
     */
    private Object violation_result;

}
