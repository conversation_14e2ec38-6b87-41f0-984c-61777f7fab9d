package com.crhms.cloud.mqs.basic.vo;

import com.googlecode.jmapper.annotations.JMap;
import lombok.Data;

import java.io.Serializable;

@Data
public class HisDiagnosisVo implements Serializable {

    /**
     * 诊断号 诊断全局唯一号
     */
    @JMap("diagnosisNo")
    private String diagnosis_no;

    /**
     * 单据号
     */
    @JMap("no")
    private String no;
    /**
     * 就诊流水号
     */
    @JMap("admissionNo")
    private String admission_no;
    /**
     * 患者编码
     */
    @JMap("patientId")
    private String patient_id;
    /**
     * 患者姓名
     */
    @JMap("patientName")
    private String patient_name;

    /**
     * 诊断编码
     */
    @JMap("diagnosisCode")
    private String diagnosis_code;

    /**
     * 诊断名称
     */
    @JMap("diagnosisName")
    private String diagnosis_name;

    /**
     * 诊断科室编码
     */
    @JMap("diagDeptCode")
    private String diag_dept_code;

    /**
     * 诊断科室名称
     */
    @JMap("diagDeptName")
    private String diag_dept_name;

    /**
     * 主诊断标志;0:否 1:是
     */
    @JMap("mainFlag")
    private String main_flag;

    /**
     * 出入院诊断类型 1.入院 2.出院
     */
    @JMap("diagInoutType")
    private String diag_inout_type;
    /**
     * 诊断类别
     */
    @JMap("diagType")
    private String diag_type;

    /**
     * 诊断排序号
     */
    @JMap("diagnosisOrder")
    private String diagnosis_order;

    /**
     * 诊断时间
     */
    @JMap("diagnosisTime")
    private String diagnosis_time;

    /**
     * 入院病情
     */
    @JMap("diagnosisSituation")
    private String diagnosis_situation;
}
