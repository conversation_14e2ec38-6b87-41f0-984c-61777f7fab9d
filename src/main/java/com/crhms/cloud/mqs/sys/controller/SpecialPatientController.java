package com.crhms.cloud.mqs.sys.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.sys.domain.SpecialPatient;
import com.crhms.cloud.mqs.sys.dto.SpecialPatientQueryVO;
import com.crhms.cloud.mqs.sys.service.SpecialPatientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 特殊患者 controller
 *
 * @author: 王新刚
 * @date: 2023/03/24
 */
@RestController
@RequestMapping("/api/mqs/specialPatient")
public class SpecialPatientController {

    @Autowired
    private SpecialPatientService service;

    /**
     * 新增或编辑特殊患者信息
     * @param specialPatient 特殊患者信息
     * @return OK
     */
    @PostMapping("/submit")
    public ResponseEntity<?> submit(@RequestBody SpecialPatient specialPatient) {
        service.submit(specialPatient);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    /**
     * 分页查询
     * @param queryVO 查询参数
     * @return 分页结果
     */
    @PostMapping("/page")
    public ResponseEntity<List<SpecialPatient>> queryPage(@RequestBody(required = false) SpecialPatientQueryVO queryVO) {
        Page<SpecialPatient> pageInfo = PageUtil.getPage(queryVO.getPage(), queryVO.getPageSize());
        List<SpecialPatient> result = service.queryPage(queryVO, pageInfo);
        return new ResponseEntity<>(result, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * 删除
     * @param ids id集合
     * @return OK
     */
    @PostMapping("/delete")
    public ResponseEntity<?> deleteById(@RequestBody List<Long> ids) {
        service.removeByIds(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    /**
     * 特殊患者 导出接口 导出模板
     * @param response 响应
     * @param queryVO  查询条件
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody SpecialPatientQueryVO queryVO) {
        service.export(response, queryVO);
    }

    /**
     * 特殊患者 数据导入
     * @param file excel文件
     * @return 成功就OK, 失败就异常
     */
    @PostMapping("/import")
    public ResponseEntity<?> importExcel(MultipartFile file) {
        service.importExcel(file);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
