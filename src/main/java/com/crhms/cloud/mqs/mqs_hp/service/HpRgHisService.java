package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpRgHis;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpRgHisMapper;
import com.crhms.cloud.mqs.mqs_hp.service.HpRgHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-住院登记历史表(HpRgHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 10:22:14
 */
@Service("hpRgHisService")
public class HpRgHisService extends ServiceImpl< HpRgHisMapper, HpRgHis> {

}
