package com.crhms.cloud.mqs.sys.domain;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 医生表(IppDoctor)实体类
 *
 * <AUTHOR>
 * @since 2023-01-12 09:26:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("ipp_doctor")
public class IppDoctor extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 672144657782747331L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_ORGANIZATION_CODE="organization_code";
    public static final String FIELD_CODE="code";
    public static final String FIELD_NAME="name";
    public static final String FIELD_CREATE_TIME="create_time";
    public static final String FIELD_THIRD_DEPT_CODE="third_dept_code";
    public static final String FIELD_THIRD_DEPT_NAME="third_dept_name";
    public static final String FIELD_EMPLOYEE_CODE="employee_code";
    public static final String FIELD_EMPLOYEE_NAME="employee_name";
    public static final String FIELD_EMPLOYEE_PHONE="employee_phone";
    public static final String FIELD_ZRYS="zrys";
    public static final String FIELD_MAGOR_JOB="magor_job";
    public static final String FIELD_POST_CODE="post_code";
    public static final String FIELD_POST_NAME="post_name";
    public static final String FIELD_MAGOR_TITLE="magor_title";
    public static final String FIELD_PERSON_TYPE="person_type";
    public static final String FIELD_PRACTICE_TYPE="practice_type";
    public static final String FIELD_ADMIN_JOB="admin_job";
    public static final String FIELD_PERSON_MOBILITY="person_mobility";
    public static final String FIELD_PREPARATION="preparation";
    public static final String FIELD_OUTSOURCE="outsource";
    public static final String FIELD_PYM="pym";
    public static final String FIELD_HOSPITAL_ID="hospital_id";
    public static final String FIELD_DATA_SOURCE="data_source";
    public static final String FIELD_DELETED="deleted";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_DELETE_BY="delete_by";
    public static final String FIELD_DELETE_DATE="delete_date";

        
    @TableId
    private Integer id;

    /**
     * 机构编码
     */    
    @TableField
    private String organizationCode;
    /**
     * 医生编码
     */    
    @TableField
    private String code;
    /**
     * 医生名称
     */    
    @TableField
    private String name;
        
    @TableField
    private Date createTime;
    /**
     * 第三方科室编码
     */    
    @TableField
    private String thirdDeptCode;
    /**
     * 第三方科室名称
     */    
    @TableField
    private String thirdDeptName;
    /**
     * 职工编号
     */    
    @TableField
    private String employeeCode;
    /**
     * 职工姓名
     */    
    @TableField
    private String employeeName;
    /**
     * 手机号
     */    
    @TableField
    private String employeePhone;
    /**
     * 是否责任医师 1:是 0:否
     */    
    @TableField
    private String zrys;
    /**
     * 专业技术职务（从事专业类别）
     */    
    @TableField
    private String magorJob;
    /**
     * 岗位编码
     */    
    @TableField
    private String postCode;
    /**
     * 岗位名称	
     */    
    @TableField
    private String postName;
    /**
     * 职称-dic_hr_magor_title
     */    
    @TableField
    private String magorTitle;
    /**
     * 人员类别-dic_hr_person_type
     */    
    @TableField
    private String personType;
    /**
     * 医师执业类别-dic_hr_practice_type
     */    
    @TableField
    private String practiceType;
    /**
     * 行政/业务管理职务dic_hr_admin_job
     */    
    @TableField
    private String adminJob;
    /**
     * 人员流动情况-dic_hr_person_mobility
     */    
    @TableField
    private String personMobility;
    /**
     * 编制情况-dic_hr_preparation
     */    
    @TableField
    private String preparation;
    /**
     * 是否外包
     */    
    @TableField
    private String outsource;
    /**
     * 拼音码
     */    
    @TableField
    private String pym;
    /**
     * 医院ID
     */    
    @TableField
    private String hospitalId;
    /**
     * 数据来源   0内置；1 公司内部接口；2外部接口 ；3文件 ；4界面
     */    
    @TableField
    private Integer dataSource;
    /**
     * 删除标识
     */    
    @TableField
    private Integer deleted;


}

