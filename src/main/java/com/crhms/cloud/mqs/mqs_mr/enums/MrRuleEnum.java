package com.crhms.cloud.mqs.mqs_mr.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MrRuleEnum {

    G000001("1","严重违规","G000001"),

    G000002("2","一般违规","G000002"),

    G000003("3","高度可疑","G000003"),

    G000004("4","轻度可疑","G000004"),

    G000005("5","提醒1","G000005"),

    G000006("6","提醒2","G000006");

    private String ruleType;

    private String ruleTypeName;

    private String ruleCode;

    public static String getRuleCode(String ruleType) {
        for (MrRuleEnum ruleEnum : MrRuleEnum.values()) {
            if(ruleType.equals(ruleEnum.getRuleType())) {
                return ruleEnum.getRuleCode();
            }
        }
        return null;
    }

}
