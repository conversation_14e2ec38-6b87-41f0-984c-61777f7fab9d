package com.crhms.cloud.mqs.sys.mapper;

import com.crhms.cloud.mqs.sys.domain.SysReasonType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 系统管理-反馈类型配置(SysReasonType)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-24 15:33:10
 */

@Mapper
public interface SysReasonTypeMapper extends BaseMapper<SysReasonType> {
}

