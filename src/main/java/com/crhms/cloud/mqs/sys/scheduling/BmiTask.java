package com.crhms.cloud.mqs.sys.scheduling;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiDeductionTemp;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFb;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFbTemp;
import com.crhms.cloud.mqs.mqs_bmi.service.BmiDeductionTempService;
import com.crhms.cloud.mqs.mqs_bmi.service.BmiFbService;
import com.crhms.cloud.mqs.mqs_bmi.service.BmiFbTempService;
import com.crhms.cloud.mqs.sys.domain.SysConfig;
import com.crhms.cloud.mqs.sys.service.SysConfigService;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Description    清除医保导入缓存数据定时任务
 * @ClassName BmiTask
 * <AUTHOR>
 * @date 2023.02.10 15:23
 */
@Log4j2
@Component
@EnableScheduling
public class BmiTask implements SchedulingConfigurer {

    @Autowired
    protected SysConfigService sysConfigService;
    @Autowired
    protected BmiFbTempService bmiFbTempService;
    @Autowired
    protected BmiDeductionTempService bmiDeductionTempService;
    @Autowired
    protected BmiFbService bmiFbService;

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        scheduledTaskRegistrar.addTriggerTask(() -> process(),
                triggerContext -> {
                    SysConfig sysConfig = sysConfigService.queryByKey("BMI_FB_TEMP_TIME", "1");
                    if (sysConfig == null || Strings.isEmpty(sysConfig.getSysValue())) {
                        log.warn("BMI_FB_TEMP_TIME cron is null");
                    }
                    return new CronTrigger(sysConfig.getSysValue()).nextExecutionTime(triggerContext);
                });
    }

    private void process() {
        log.info("执行 清除医保导入缓存数据 定时任务 ===== start");
        bmiFbTempService.remove(new QueryWrapper<BmiFbTemp>().lt(BmiFbTemp.FIELD_CREATED_DATE, new Date()));
        log.info("执行 清除医保导入缓存数据 定时任务 ===== end");
        log.info("执行 清除医保扣费缓存数据 定时任务 ===== start");
        bmiDeductionTempService.remove(new QueryWrapper<BmiDeductionTemp>().lt(BmiDeductionTemp.FIELD_CREATED_DATE, new Date()));
        log.info("执行 清除医保扣费缓存数据 定时任务 ===== end");

        log.info("执行 更新医保反馈已过期状态 定时任务 ===== start");
        // 0.待分配 1.待反馈 2.已反馈 3.已退回 4.已确认 5. 已过期 6.已申诉
        bmiFbService.update(new UpdateWrapper<BmiFb>().set(BmiFb.FIELD_STATUS, "5").notIn(BmiFb.FIELD_STATUS, "4", "5", "6").lt(BmiFb.FIELD_APPEAL_DATE, new Date()));
        log.info("执行 清除医保导入缓存数据定 时任务 ===== end");
    }
}
