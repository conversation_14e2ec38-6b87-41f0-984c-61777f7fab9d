package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.util.StrUtil;
import com.crhms.cloud.mqs.mqs_kbm.mapper.CgGuideMapper;
import com.crhms.cloud.mqs.mqs_kbm.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 知识库管理-临床指南service
 *
 * @author: wangxingang
 * @date: 2023.02.07
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CgGuideService {
    @Autowired
    private CgGuideMapper mapper;

    /**
     * 查询疾病列表
     *
     * @param diseaseName 疾病名称
     */
    public List<Map<String, String>> queryDiseaseList(String diseaseName) {
        return mapper.selectDiseaseList(diseaseName);
    }

    /**
     * "概述"页面信息
     *
     * @param diseaseCode 疾病编码
     */
    public CgGuideSummary querySummary(String diseaseCode) {
        return mapper.selectSummary(diseaseCode);
    }

    /**
     * "检查"页面信息
     *
     * @param diseaseCode 疾病编码
     */
    public CgGuideCheck queryCheck(String diseaseCode) {
        // 查询列表
        List<CgGuideCheckItem> itemList = mapper.selectCheckItemList(diseaseCode);
        // 拼接字段
        StringBuilder sb = new StringBuilder();
        for (CgGuideCheckItem item : itemList) {
            sb.append(item.getServiceName()).append("、");
        }
        sb.deleteCharAt(sb.length() - 1);
        return CgGuideCheck.builder().checkItem(sb.toString()).itemList(itemList).build();
    }

    /**
     * "一般治疗"页面信息
     * @param diseaseCode 疾病编码
     */
    public CgGuideGeneral queryGeneral(String diseaseCode) {
        // 查询列表
        List<CgGuideGeneralItem> itemList = mapper.selectGeneralItemList(diseaseCode);
        // 拼接字段
        StringBuilder sb = new StringBuilder();
        for (CgGuideGeneralItem item : itemList) {
            sb.append(item.getServiceName()).append("、");
        }
        if (StrUtil.isNotEmpty(sb.toString())) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return CgGuideGeneral.builder().treatmentItem(sb.toString()).itemList(itemList).build();
    }

    /**
     * "手术治疗"页面信息
     * @param diseaseCode 疾病编码
     */
    public CgGuideOperation queryOperation(String diseaseCode) {
        // 查询列表
        List<CgGuideOperationItem> itemList = mapper.selectOperationItemList(diseaseCode);
        // 拼接字段
        StringBuilder sb = new StringBuilder();
        for (CgGuideOperationItem item : itemList) {
            sb.append(item.getServiceName()).append("、");
        }
        if (StrUtil.isNotEmpty(sb.toString())) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return CgGuideOperation.builder().operationItem(sb.toString()).itemList(itemList).build();
    }

    /**
     * "药品"页面信息
     * @param diseaseCode 疾病编码
     */
    public CgGuideDrug queryDrug(String diseaseCode) {
        // 查询列表
        List<CgGuideDrugItem> itemList = mapper.selectDrugItemList(diseaseCode);
        // 拼接字段
        StringBuilder sb = new StringBuilder();
        for (CgGuideDrugItem item : itemList) {
            sb.append(item.getServiceName()).append("、");
        }
        if (StrUtil.isNotEmpty(sb.toString())) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return CgGuideDrug.builder().drugItem(sb.toString()).itemList(itemList).build();
    }
}
