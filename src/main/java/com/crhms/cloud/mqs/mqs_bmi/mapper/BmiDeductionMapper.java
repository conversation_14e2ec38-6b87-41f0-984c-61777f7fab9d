package com.crhms.cloud.mqs.mqs_bmi.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiDeduction;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.mqs_bmi.vo.BmiQueryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 医保管理-医保扣费(BmiDeduction)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-14 10:43:09
 */

@Mapper
public interface BmiDeductionMapper extends BaseMapper<BmiDeduction> {
    /**
     * 查询指定行数据
     * @return 对象列表
     */
    List<BmiDeduction> queryByPage(@Param("queryVo") BmiQueryVo bmiQueryVo, Page myPage);

    List<Map<String, String>> selectDeptList(@Param("dept") String dept, @Param("hospitalId") String hospitalId);

    List<Map<String, String>> selectDoctorList(@Param("doctor") String doctor, @Param("hospitalId") String hospitalId);

    List<String> selectRules(@Param("rule") String rule, @Param("hospitalId") String hospitalId);


    List<String> selectClaimTypeList(@Param("claimType") String claimType, @Param("hospitalId") String hospitalId);

    Map indStatistics(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("hospitalId") String hospitalId);

    List<Map> genViolationTop(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("orderBy") String orderBy, @Param("hospitalId") String hospitalId);

    List<Map> getAnalysisDetails(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("orderBy") String orderBy, @Param("hospitalId") String hospitalId);
    BigDecimal getAnalysisTotal(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("hospitalId") String hospitalId);

    List<Map> getAnalysisByMonth(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("hospitalId") String hospitalId);
}

