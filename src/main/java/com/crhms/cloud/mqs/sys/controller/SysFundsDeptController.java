package com.crhms.cloud.mqs.sys.controller;

import com.crhms.cloud.mqs.sys.domain.SysFundsDept;
import com.crhms.cloud.mqs.sys.service.SysFundsDeptService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 运营管理-基金管理-部门(SysFundsDept)表控制层
 *
 * <AUTHOR>
 * @since 2022-12-15 16:43:03
 */
@RestController
@RequestMapping("api/mqs/sysFundsDept")
public class SysFundsDeptController {
    /**
     * 服务对象
     */
    @Resource
    private SysFundsDeptService sysFundsDeptService;
    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.ok(this.sysFundsDeptService.deleteById(id));
    }

}

