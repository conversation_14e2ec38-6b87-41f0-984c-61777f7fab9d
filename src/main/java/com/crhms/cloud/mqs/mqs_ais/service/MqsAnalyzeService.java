package com.crhms.cloud.mqs.mqs_ais.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.client.cdmp.dic.CdmpDicInterface;
import com.crhms.cloud.core.excel.dto.ExcelExport;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.mqs_ais.mapper.MqsAnalyzeMapper;
import com.crhms.cloud.mqs.mqs_ais.vo.*;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.mapper.IppMapper;
import com.crhms.cloud.mqs.sys.mapper.SysFundsMapper;
import com.crhms.cloud.mqs.sys.service.SysFundsAloneService;
import com.crhms.cloud.mqs.sys.utils.DictUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 决策支持 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-15 14:40:08
 */

@Service("MqsAnalyzeService")
public class MqsAnalyzeService {
    @Autowired
    MqsAnalyzeMapper mqsAnalyzeMapper;
    @Autowired
    CdmpDicInterface cdmpDicInterface;
    @Autowired
    BaseMedicalMapper baseMedicalMapper;
    @Autowired
    IppMapper ippMapper;
    @Autowired
    SysFundsMapper sysFundsMapper;
    @Autowired
    SysFundsAloneService sysFundsAloneService;
    // 统计单位 "万"
    private static BigDecimal tenThousand = new BigDecimal(10000l);

    public Map<String, Object> busTitleTotal(String itemDateFrom, String itemDateTo, String dept, String type, String hospitalId) {

        if (!"opPt".equals(type) && !"hpSettle".equals(type)) {
            throw new BaseException("仅支持 opPt、hpSettle两个业务！");
        }

        String tableName = AuditScenarioEnum.valueOf(type).getAuditScenario();


        Map<String, Object> totalMap = mqsAnalyzeMapper.totalTimes(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
        BigDecimal totalTimes = MqsUtils.getBigDecimal(totalMap.get("totalTimes"));
        BigDecimal violationTimes = MqsUtils.getBigDecimal(totalMap.get("vTimes"));
        totalMap.put("timeRatio", BigDecimal.ZERO.compareTo(totalTimes) == 0 ? 0 : violationTimes.divide(totalTimes, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));

        Map<String, Object> busDetailMap = mqsAnalyzeMapper.busDetailTotal(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
        BigDecimal totalDetails = MqsUtils.getBigDecimal(busDetailMap.get("totalDetails"));
        BigDecimal violationDetails = MqsUtils.getBigDecimal(busDetailMap.get("vDetails"));
        BigDecimal totalCosts = MqsUtils.getBigDecimal(busDetailMap.get("totalCosts"));
        BigDecimal violationCosts = MqsUtils.getBigDecimal(busDetailMap.get("vCosts"));
        BigDecimal ICosts = MqsUtils.getBigDecimal(busDetailMap.get("ICosts"));
        BigDecimal ABMCosts = MqsUtils.getBigDecimal(busDetailMap.get("ABMCosts"));

        busDetailMap.put("detailsRatio", BigDecimal.ZERO.compareTo(totalDetails) == 0 ? 0 : violationDetails.divide(totalDetails, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        busDetailMap.put("costsRatio", BigDecimal.ZERO.compareTo(totalCosts) == 0 ? 0 : violationCosts.divide(totalCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        busDetailMap.put("ICostsRatio", BigDecimal.ZERO.compareTo(totalCosts) == 0 ? 0 : ICosts.divide(totalCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        busDetailMap.put("ABMCostsRatio", BigDecimal.ZERO.compareTo(totalCosts) == 0 ? 0 : ABMCosts.divide(totalCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        busDetailMap.put("totalCosts", MqsUtils.getBigDecimal(busDetailMap.get("totalCosts")).divide(new BigDecimal(10000l), 4, BigDecimal.ROUND_HALF_UP));
        busDetailMap.put("vCosts", MqsUtils.getBigDecimal(busDetailMap.get("vCosts")).divide(new BigDecimal(10000l), 4, BigDecimal.ROUND_HALF_UP));
        totalMap.putAll(busDetailMap);
        return totalMap;
    }

    public List<Map> getAnalysisDetails(String itemDateFrom, String itemDateTo, String dept, String type, String order, String hospitalId) {
        if (!"opPt".equals(type) && !"hpSettle".equals(type)) {
            throw new BaseException("仅支持 opPt、hpSettle两个业务！");
        }
        if (!"dept".equals(order) && !"doc".equals(order)) {
            throw new BaseException("仅支持 dept、doc两个维度！");
        }
        String tableName = AuditScenarioEnum.valueOf(type).getAuditScenario();
        //获取院区名称
        String hospitalName = ippMapper.selectHospitalName(hospitalId);

        List<Map> analysisDetails = new ArrayList<>();
        List<Map> analysisHisTotal = new ArrayList<>();
        List<Map> ruleNums = new ArrayList<>();
        if ("dept".equals(order)) {
            //查询分组数据
            analysisDetails = mqsAnalyzeMapper.getAnalysisDetailsByDept(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
            //查询结算单量
            analysisHisTotal = mqsAnalyzeMapper.getAnalysisHisByDept(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
            //查询违规条数
            ruleNums = mqsAnalyzeMapper.getRuleNumsByDept(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
        } else {
            analysisDetails = mqsAnalyzeMapper.getAnalysisDetailsByDoc(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
            analysisHisTotal = mqsAnalyzeMapper.getAnalysisHisByDoc(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
            ruleNums = mqsAnalyzeMapper.getRuleNumsByDoc(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
        }
        for (Map analysisDetail : analysisDetails) {
            Optional<Map> first = ruleNums.stream().filter(x -> analysisDetail.get("code").equals(x.get("code"))
                    && analysisDetail.get("name").equals(x.get("name"))).findFirst();
            if (first.isPresent()) {
                analysisDetail.put("ruleNums", first.get().get("ruleNums"));
            } else {
                analysisDetail.put("ruleNums", "0");
            }
        }

        BigDecimal allCosts = analysisDetails.stream().map(x -> MqsUtils.getBigDecimal(x.get("totalCosts"))).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal allBmiAmount = analysisDetails.stream().map(x -> MqsUtils.getBigDecimal(x.get("bmiAmount"))).reduce(BigDecimal.ZERO, BigDecimal::add);

        //计算统计比例
        for (Map analysisDetail : analysisDetails) {
            analysisDetail.put("hospital", hospitalName);
            Optional<Map> first = analysisHisTotal.stream().filter(x -> x.get("code").equals(analysisDetail.get("code")) && x.get("name").equals(analysisDetail.get("name"))).findFirst();
            //结算人次
            BigDecimal total = BigDecimal.ZERO;
            if (first.isPresent()) {
                total = MqsUtils.getBigDecimal(first.get().get("total"));
            }
            BigDecimal totalDetails = MqsUtils.getBigDecimal(analysisDetail.get("totalDetails"));
            BigDecimal vDetails = MqsUtils.getBigDecimal(analysisDetail.get("vDetails"));
            BigDecimal totalCosts = MqsUtils.getBigDecimal(analysisDetail.get("totalCosts"));
            BigDecimal vCosts = MqsUtils.getBigDecimal(analysisDetail.get("vCosts"));
            BigDecimal bmiAmount = MqsUtils.getBigDecimal(analysisDetail.get("bmiAmount"));
            analysisDetail.put("ptTotal", total);

            //费用占比
            analysisDetail.put("allCostsRatio", BigDecimal.ZERO.compareTo(allCosts) == 0 ? 0 : totalCosts.divide(allCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            // 统筹金额占比
            analysisDetail.put("allBmiAmountRatio", BigDecimal.ZERO.compareTo(allBmiAmount) == 0 ? 0 : bmiAmount.divide(allBmiAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            //违规单量占比
            analysisDetail.put("detailsRatio", BigDecimal.ZERO.compareTo(totalDetails) == 0 ? 0 : vDetails.divide(totalDetails, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            //违规费用占比
            analysisDetail.put("costsRatio", BigDecimal.ZERO.compareTo(totalCosts) == 0 ? 0 : vCosts.divide(totalCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        }
        return analysisDetails;
    }


    public List<Map> getAnalysisType(String itemDateFrom, String itemDateTo, String dept, String type, String pro, String order, String hospitalId) {

        if (!"opPt".equals(type) && !"hpSettle".equals(type)) {
            throw new BaseException("仅支持 opPt、hpSettle两个业务！");
        }
        if (!"dept".equals(order) && !"doc".equals(order)) {
            throw new BaseException("仅支持 dept、doc两个维度！");
        }
        String tableName = AuditScenarioEnum.valueOf(type).getAuditScenario();

        List<Map> analysisDetails = new ArrayList<>();
        List<Map> analysisHisTotal = new ArrayList<>();
        List<Map> ruleNums = new ArrayList<>();


        if ("dept".equals(order)) {
            analysisDetails = mqsAnalyzeMapper.getAnalysisType(itemDateFrom, itemDateTo, pro, null, dept, tableName, hospitalId);
            analysisHisTotal = mqsAnalyzeMapper.getAnalysisTypeHis(itemDateFrom, itemDateTo, pro, null, dept, tableName, hospitalId);
            ruleNums = mqsAnalyzeMapper.getAnalysisTypeRuleNums(itemDateFrom, itemDateTo, pro, null, dept, tableName, hospitalId);
        } else {
            analysisDetails = mqsAnalyzeMapper.getAnalysisType(itemDateFrom, itemDateTo, null, pro, dept, tableName, hospitalId);
            analysisHisTotal = mqsAnalyzeMapper.getAnalysisTypeHis(itemDateFrom, itemDateTo, null, pro, dept, tableName, hospitalId);
            ruleNums = mqsAnalyzeMapper.getAnalysisTypeRuleNums(itemDateFrom, itemDateTo, null, pro, dept, tableName, hospitalId);

        }
        BigDecimal allCosts = analysisDetails.stream().map(x -> MqsUtils.getBigDecimal(x.get("totalCosts"))).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal allBmiAmount = analysisDetails.stream().map(x -> MqsUtils.getBigDecimal(x.get("bmiAmount"))).reduce(BigDecimal.ZERO, BigDecimal::add);

        List<Map<String, Object>> mapList = cdmpDicInterface.queryDicDataList("DIC_FEE_TYPE");
        Map<String, String> collect = mapList.stream().collect(Collectors.toMap(e -> (String) e.get("DATA_CODE"), e -> (String) e.get("DATA_NAME")));
        //计算统计比例
        for (Map analysisDetail : analysisDetails) {
            Optional<Map> first1 = ruleNums.stream().filter(x -> x.get("item_type_code").equals(analysisDetail.get("item_type_code"))
                    && x.get("item_type_name").equals(analysisDetail.get("item_type_name"))).findFirst();

            Optional<Map> first = analysisHisTotal.stream().filter(x -> x.get("item_type_code").equals(analysisDetail.get("item_type_code"))
                    && x.get("item_type_name").equals(analysisDetail.get("item_type_name"))).findFirst();
            //结算人次
            BigDecimal total = BigDecimal.ZERO;
            if (first.isPresent()) {
                total = MqsUtils.getBigDecimal(first.get().get("total"));
            }
            //违规次数
            if (first1.isPresent()) {
                analysisDetail.put("ruleNums", first1.get().get("ruleNums"));
            } else {
                analysisDetail.put("ruleNums", "0");
            }
            analysisDetail.put("item_type_name", collect.get(analysisDetail.get("item_type_code")));

            BigDecimal totalDetails = MqsUtils.getBigDecimal(analysisDetail.get("totalDetails"));
            BigDecimal vDetails = MqsUtils.getBigDecimal(analysisDetail.get("vDetails"));
            BigDecimal totalCosts = MqsUtils.getBigDecimal(analysisDetail.get("totalCosts"));
            BigDecimal vCosts = MqsUtils.getBigDecimal(analysisDetail.get("vCosts"));
            BigDecimal bmiAmount = MqsUtils.getBigDecimal(analysisDetail.get("bmiAmount"));
            analysisDetail.put("ptTotal", total);

            //费用占比
            analysisDetail.put("allCostsRatio", BigDecimal.ZERO.compareTo(allCosts) == 0 ? 0 : totalCosts.divide(allCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            // 统筹金额占比
            analysisDetail.put("allBmiAmountRatio", BigDecimal.ZERO.compareTo(allBmiAmount) == 0 ? 0 : bmiAmount.divide(allBmiAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            //违规单量占比
            analysisDetail.put("detailsRatio", BigDecimal.ZERO.compareTo(totalDetails) == 0 ? 0 : vDetails.divide(totalDetails, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            //违规费用占比
            analysisDetail.put("costsRatio", BigDecimal.ZERO.compareTo(totalCosts) == 0 ? 0 : vCosts.divide(totalCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        }
        return analysisDetails;
    }

    public List<Map> genptTrend(String itemDateFrom, String itemDateTo, String dept, String type, String hospitalId) {
        if (!"opPt".equals(type) && !"hpSettle".equals(type)) {
            throw new BaseException("仅支持 opPt、hpSettle两个业务！");
        }
        String tableName = AuditScenarioEnum.valueOf(type).getAuditScenario();


        List<Map> maps = mqsAnalyzeMapper.genptTrend(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
        for (Map map : maps) {
            BigDecimal totalTimes = MqsUtils.getBigDecimal(map.get("totalTimes"));
            BigDecimal vTimes = MqsUtils.getBigDecimal(map.get("vTimes"));

            map.put("timesRatio", BigDecimal.ZERO.compareTo(totalTimes) == 0 ? 0 : vTimes.divide(totalTimes, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        }
        return maps;
    }

    public List<Map> gendpTrend(String itemDateFrom, String itemDateTo, String dept, String type, String order, String hospitalId) {

        if (!"opPt".equals(type) && !"hpSettle".equals(type)) {
            throw new BaseException("仅支持 opPt、hpSettle两个业务！");
        }
        String tableName = AuditScenarioEnum.valueOf(type).getAuditScenario();
        List<Map> resultMap = new ArrayList<>();
        if ("dept".equals(order)) {
            resultMap = mqsAnalyzeMapper.gendpTrendDept(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
        } else {
            resultMap = mqsAnalyzeMapper.gendpTrendDoc(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
        }
        return resultMap;
    }

    public List<Map> feeTrend(String itemDateFrom, String itemDateTo, String dept, String type, String hospitalId) {
        if (!"opPt".equals(type) && !"hpSettle".equals(type)) {
            throw new BaseException("仅支持 opPt、hpSettle两个业务！");
        }
        String tableName = AuditScenarioEnum.valueOf(type).getAuditScenario();
        List<Map> resultMap = mqsAnalyzeMapper.feeTrend(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
        return resultMap;
    }

    public List<Map> feeTypeTrend(String itemDateFrom, String itemDateTo, String dept, String type, String hospitalId) {
        if (!"opPt".equals(type) && !"hpSettle".equals(type)) {
            throw new BaseException("仅支持 opPt、hpSettle两个业务！");
        }
        String tableName = AuditScenarioEnum.valueOf(type).getAuditScenario();
        List<Map> resultMap = mqsAnalyzeMapper.feeTypeTrend(itemDateFrom, itemDateTo, dept, tableName, hospitalId);
        List<Map<String, Object>> mapList = cdmpDicInterface.queryDicDataList("DIC_FEE_TYPE");
        Map<String, String> collect = mapList.stream().collect(Collectors.toMap(e -> (String) e.get("DATA_CODE"), e -> (String) e.get("DATA_NAME")));

        for (Map map : resultMap) {
            map.put("typeName", collect.get(map.get("typeCode")));
        }
        return resultMap;
    }

    public List<Map> getFeeAnalysis(ViolationQueryVo queryVo, Page page, String hospitalId) {
        queryVo.setHospitalId(hospitalId);
        AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(queryVo.getAuditScenario());
        //查询违规总量
        Map totalMap = mqsAnalyzeMapper.getFeeAnalysisTotal(queryVo, auditScenarioEnum.getAuditScenario());
        //查询违规统计
        List<Map> result = mqsAnalyzeMapper.getFeeAnalysis(queryVo, auditScenarioEnum.getAuditScenario(), page);

        BigDecimal totalDetails = MqsUtils.getBigDecimal(totalMap.get("totalDetails"));
        BigDecimal totalCosts = MqsUtils.getBigDecimal(totalMap.get("totalCosts"));
        BigDecimal totalBmiAmount = MqsUtils.getBigDecimal(totalMap.get("totalBmiAmount"));


        List<Map<String, Object>> mapList = cdmpDicInterface.queryDicDataList("DIC_FEE_TYPE");
        Map<String, String> collect = mapList.stream().collect(Collectors.toMap(e -> (String) e.get("DATA_CODE"), e -> (String) e.get("DATA_NAME")));
        for (Map map : result) {
            map.put("typeName", collect.get(map.get("typeCode")));
            map.put("detailRatio", BigDecimal.ZERO.compareTo(totalDetails) == 0 ? 0 : MqsUtils.getBigDecimal(map.get("totalDetails")).divide(totalDetails, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            map.put("costsRatio", BigDecimal.ZERO.compareTo(totalCosts) == 0 ? 0 : MqsUtils.getBigDecimal(map.get("totalCosts")).divide(totalCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            map.put("bmiAmountRatio", BigDecimal.ZERO.compareTo(totalBmiAmount) == 0 ? 0 : MqsUtils.getBigDecimal(map.get("totalBmiAmount")).divide(totalCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        }

        return result;
    }

    public Map getVRuleTital(ViolationQueryVo queryVo, String hospitalId) {
        AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(queryVo.getAuditScenario());
        String tableName = auditScenarioEnum.getAuditScenario();
        queryVo.setHospitalId(hospitalId);
        Map vRuleTital = mqsAnalyzeMapper.getVRuleTital(queryVo, tableName);
        Map vRuleTital2 = mqsAnalyzeMapper.getVRuleTital2(queryVo, tableName);

        BigDecimal totalRule = ObjectUtil.isNull(vRuleTital2) ? new BigDecimal(0) : MqsUtils.getBigDecimal(vRuleTital2.get("totalRule"));
        BigDecimal patientTotal = MqsUtils.getBigDecimal(vRuleTital.get("patientTotal"));
        BigDecimal totalDetail = MqsUtils.getBigDecimal(vRuleTital.get("totalDetail"));
        BigDecimal totalCosts = MqsUtils.getBigDecimal(vRuleTital.get("totalCosts"));
        BigDecimal vCosts = MqsUtils.getBigDecimal(vRuleTital.get("vCosts"));

        vRuleTital.put("patientRuleAvg", BigDecimal.ZERO.compareTo(patientTotal) == 0 ? 0 : totalRule.divide(patientTotal, 4, BigDecimal.ROUND_HALF_UP));
        vRuleTital.put("detailRuleAvg", BigDecimal.ZERO.compareTo(totalDetail) == 0 ? 0 : totalRule.divide(totalDetail, 4, BigDecimal.ROUND_HALF_UP));
        vRuleTital.put("vCostsRatio", BigDecimal.ZERO.compareTo(totalCosts) == 0 ? 0 : vCosts.divide(totalCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));


        return vRuleTital;

    }

    public List<Map> getVRuleListByPage(ViolationQueryVo queryVo, Page page, String hospitalId) {
        queryVo.setHospitalId(hospitalId);
        AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(queryVo.getAuditScenario());
        Map vRuleListTotal = mqsAnalyzeMapper.getVRuleListTotal(queryVo, auditScenarioEnum.getAuditScenario());
        List<Map> result = mqsAnalyzeMapper.getVRuleListByPage(queryVo, auditScenarioEnum.getAuditScenario(), page);

        BigDecimal totalCosts = MqsUtils.getBigDecimal(vRuleListTotal.get("totalCosts"));
        BigDecimal totalDetail = MqsUtils.getBigDecimal(vRuleListTotal.get("totalDetail"));

        for (Map map : result) {
            map.put("detailRatio", BigDecimal.ZERO.compareTo(totalDetail) == 0 ? 0 : MqsUtils.getBigDecimal(map.get("totalDetail")).divide(totalDetail, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            map.put("costRatio", BigDecimal.ZERO.compareTo(totalCosts) == 0 ? 0 : MqsUtils.getBigDecimal(map.get("totalCosts")).divide(totalCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        }

        return result;

    }

    public List<Map> getVRuleList(ViolationQueryVo queryVo, String hospitalId) {
        queryVo.setHospitalId(hospitalId);
        AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(queryVo.getAuditScenario());
        List<Map> result = mqsAnalyzeMapper.getVRuleList(queryVo, auditScenarioEnum.getAuditScenario());

        BigDecimal totalCosts = result.stream().map(x -> MqsUtils.getBigDecimal(x.get("totalCosts"))).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalDetail = result.stream().map(x -> MqsUtils.getBigDecimal(x.get("totalDetail"))).reduce(BigDecimal.ZERO, BigDecimal::add);

        for (Map map : result) {
            map.put("detailRatio", BigDecimal.ZERO.compareTo(totalDetail) == 0 ? 0 : MqsUtils.getBigDecimal(map.get("totalDetail")).divide(totalDetail, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
            map.put("costRatio", BigDecimal.ZERO.compareTo(totalCosts) == 0 ? 0 : MqsUtils.getBigDecimal(map.get("totalCosts")).divide(totalCosts, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100l)));
        }

        return result;
    }

    public List<Map> getVItemTop10(ViolationQueryVo queryVo, String hospitalId) {
        AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(queryVo.getAuditScenario());
        queryVo.setHospitalId(hospitalId);
        return mqsAnalyzeMapper.getVItemTop10(queryVo, auditScenarioEnum.getAuditScenario(), hospitalId);
    }

    public List<Map> getVRuleTop10(ViolationQueryVo queryVo, String hospitalId) {
        AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(queryVo.getAuditScenario());
        queryVo.setHospitalId(hospitalId);
        return mqsAnalyzeMapper.getVRuleTop10(queryVo, auditScenarioEnum.getAuditScenario(), hospitalId);
    }

    public List<Map> getcsDiseaseTop5(String year, String hospitalId) {
        //查询 慢特病总金额top5
        List<Map> maps = mqsAnalyzeMapper.getcsDiseaseTop5(year, hospitalId);
        //查询慢特病基础数据
        List<Map> csDiseases = sysFundsMapper.queryCsDisease(hospitalId);
        Map<String, String> csDiseasesMap = csDiseases.stream().collect(Collectors.toMap(x -> x.get("cs_disease_code").toString(), x -> x.get("type").toString()));
        //查询慢特病基金
        List<Map<String, Object>> aloneMapList = sysFundsMapper.selectCdsCiAmount(year, hospitalId);
        Map<String, BigDecimal> aloneAmountMap = aloneMapList.stream().collect(Collectors.toMap(x -> x.get("aloneCode").toString(), x -> MqsUtils.getBigDecimal(x.get("amount"))));

        if (CollectionUtil.isNotEmpty(maps)) {
            // 查询年内慢特病医保总金额
//            BigDecimal bmiAmountYear = MqsUtils.getBigDecimal(mqsAnalyzeMapper.getcsTotalBmiYear(year, hospitalId));
            List<String> csDiseaseCodes = maps.stream().map(x -> (String) x.get("cs_disease_code")).collect(Collectors.toList());
            List<Map> detailTop5 = mqsAnalyzeMapper.getcsDiseaseDetailTop5(year, csDiseaseCodes, hospitalId);
            for (Map map : maps) {
                BigDecimal totalDetailCosts = BigDecimal.ZERO;
                Optional<Map> first = detailTop5.stream().filter(x -> x.get("cs_disease_code").equals(map.get("cs_disease_code"))).findFirst();
                if (first.isPresent()) {
                    totalDetailCosts = MqsUtils.getBigDecimal(first.get().get("totalCosts"));
                    map.put("deptNames", first.get().get("deptNames"));
                }

                BigDecimal totalBmiAmount = MqsUtils.getBigDecimal(map.get("totalBmiAmount"));
                BigDecimal patientNums = MqsUtils.getBigDecimal(map.get("patientNums"));
                String type = csDiseasesMap.get(map.get("cs_disease_code"));
                BigDecimal bmiAmountYearAmount = BigDecimal.ZERO;
                if(type != null){
                    //维护了慢特病类型才能统计基金占比
                    bmiAmountYearAmount = aloneAmountMap.get("CdsCi");
                    if(bmiAmountYearAmount == null){
                        bmiAmountYearAmount = "1".equals(type) ? aloneAmountMap.get("Cds") : aloneAmountMap.get("Ci");
                    }
                    if(bmiAmountYearAmount == null){
                        bmiAmountYearAmount = BigDecimal.ZERO;
                    }
                }
                //bmiYearRatio 年度定额占比 （单独管理的慢特病基金占比）
                map.put("bmiYearRatio", BigDecimal.ZERO.compareTo(bmiAmountYearAmount) == 0 ? 0 : totalBmiAmount.multiply(new BigDecimal(100l)).divide(bmiAmountYearAmount, 4, BigDecimal.ROUND_HALF_UP));
                //patientBmiRatio 人均医保统筹金额
                map.put("patientBmiRatio", BigDecimal.ZERO.compareTo(patientNums) == 0 ? 0 : totalBmiAmount.divide(patientNums, 4, BigDecimal.ROUND_HALF_UP));

                //totalAmount 总金额
                map.put("totalAmount", MqsUtils.getBigDecimal(map.get("totalAmount")).divide(tenThousand,4, RoundingMode.HALF_UP));

                //totalDetailCosts 违规费用
                map.put("totalDetailCosts", totalDetailCosts.divide(tenThousand,4, RoundingMode.HALF_UP));
                //totalBmiAmount 医保统筹金额
                map.put("totalBmiAmount", totalBmiAmount.divide(tenThousand,4, RoundingMode.HALF_UP));

            }


        }
        return maps;

    }

    public List<CsDiseaseDto> getcsgetListByPage(CsDiseaseQueryVo queryVo, Page page) {
        List<CsDiseaseDto> csDiseaseDtos = mqsAnalyzeMapper.getcsgetListByPage(queryVo, page);
        DictUtils.translateDict(csDiseaseDtos);
        return csDiseaseDtos;
    }

    public List<DetailDto> analyzeQueryDetail(DetailQueryVo queryVo, Page mypage) {
        queryVo.setHospitalId(LoginContext.getHospitalId());
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), queryVo.getAuditScenario())) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(queryVo.getAuditScenario()).getTableName();
        List<DetailDto> detailDtos = baseMedicalMapper.analyzeQueryDetail(tableName, queryVo, mypage);
        DictUtils.translateDict(detailDtos);
        return detailDtos;
    }


    /**
     * 业务分析 - 导出excel
     *
     * @param itemDateFrom 开始日期
     * @param itemDateTo   结束日期
     * @param dept         科室名称
     * @param type         业务 - 门诊:opPt 住院:hpSettle
     * @param order        维度筛选 - 科室:dept 医生:doc
     */
    public void busExport(HttpServletResponse response, String itemDateFrom, String itemDateTo, String dept, String type, String order, String hospitalId) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        List<Map> list = getAnalysisDetails(itemDateFrom, itemDateTo, dept, type, order, LoginContext.getHospitalId());

        StringBuilder fileName = new StringBuilder();

        // 构造表头和数据
        if (StrUtil.equals(order, "dept")) {
            fileName.append("决策支持-业务分析(科室)");
            heads.add(CollUtil.list(true,
                    "HOSPITAL", "CODE", "NAME", "ITEM_TYPE_CODE", "ITEM_TYPE_NAME", "PT_TOTAL", "TOTAL_DETAILS", "TOTAL_COSTS", "ALL_COSTS_RATIO",
                    "BMI_AMOUNT", "ALL_BMI_AMOUNT_RATIO", "DETAILS_RATIO", "RULE_NUMS", "COSTS_RATIO", "V_BMI_AMOUNT"));
            heads.add(CollUtil.list(true,
                    "院区", "科室编码", "科室名称", "明细编码", "明细名称", "结算人次数", "明细单量", "总费用", "费用占比",
                    "医保统筹金额", "统筹金额占比", "违规单量占比", "违反规则条例", "违规总费用占比", "违规医保统筹金额"));

            for (Map item : list) {
                List<Object> line = CollUtil.list(true,
                        item.get("hospital"), item.get("code"), item.get("name"), item.get("item_type_code"), item.get("item_type_name"), item.get("ptTotal"), item.get("totalDetails"), item.get("totalCosts"), item.get("allCostsRatio"),
                        String.valueOf(item.get("bmiAmount")), item.get("allBmiAmountRatio"), item.get("detailsRatio"), item.get("ruleNums"), item.get("costsRatio"), item.get("vBmiAmount"));
                datas.add(line);
            }

        } else if (StrUtil.equals(order, "doc")) {
            fileName.append("决策支持-业务分析(医生)");
            heads.add(CollUtil.list(true,
                    "HOSPITAL", "CODE", "NAME", "APPLY_DEPT_NAME", "ITEM_TYPE_CODE", "ITEM_TYPE_NAME", "PT_TOTAL", "TOTAL_DETAILS", "TOTAL_COSTS", "ALL_COSTS_RATIO",
                    "BMI_AMOUNT", "ALL_BMI_AMOUNT_RATIO", "DETAILS_RATIO", "RULE_NUMS", "COSTS_RATIO", "V_BMI_AMOUNT"));
            heads.add(CollUtil.list(true,
                    "院区", "医生编码", "医生名称", "科室名称", "明细编码", "明细名称", "结算人次数", "明细单量", "总费用", "费用占比",
                    "医保统筹金额", "统筹金额占比", "违规单量占比", "违反规则条例", "违规总费用占比", "违规医保统筹金额"));

            for (Map item : list) {
                List<Object> line = CollUtil.list(true,
                        item.get("hospital"), item.get("code"), item.get("name"), item.get("apply_dept_name"), item.get("item_type_code"), item.get("item_type_name"), item.get("ptTotal"), item.get("totalDetails"), item.get("totalCosts"), item.get("allCostsRatio")+"%",
                        String.valueOf(item.get("bmiAmount")), item.get("allBmiAmountRatio")+"%", item.get("detailsRatio")+"%", item.get("ruleNums"), item.get("costsRatio")+"%", item.get("vBmiAmount"));
                datas.add(line);
            }

        }

        MqsUtils.buildExportFileNameSuffix(fileName, itemDateFrom, itemDateTo);


        List<ExcelExport> exports = new ArrayList<>(1);
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }


    /**
     * 违规分析 - 导出excel
     *
     * @param queryVo  查询条件
     * @param response 响应
     */
    public void violationExcel(HttpServletResponse response, ViolationQueryVo queryVo) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        List<Map> list = getFeeAnalysis(queryVo, null, LoginContext.getHospitalId());

        StringBuilder fileName = new StringBuilder();
        fileName.append("决策支持-违规分析");

        // 构造表头和数据
        heads.add(CollUtil.list(true,
                "ITEM_ID", "ITEM_NAME", "TYPE_NAME", "RULES", "TOTAL_NO", "TOTAL_DETAILS", "DEPT_NAMES", "DOC_NAMES",
                "DETAIL_RATIO", "COSTS_RATIO", "BMI_AMOUNT_RATIO"));
        heads.add(CollUtil.list(true,
                "项目编码", "项目名称", "项目类型", "违规类型(多个)", "使用人数", "使用数量", "涉及科室(多个)", "涉及医生(多个)",
                "违规单量占比", "违规费用占比", "医保内金额占比"));

        for (Map item : list) {
            List<Object> line = CollUtil.list(true,
                    item.get("item_id"), item.get("item_name"), item.get("typeName"), item.get("rules"), item.get("totalNo"), item.get("totalDetails"), item.get("deptNames"), item.get("docNames"),
                    item.get("detailRatio")+"%", item.get("costsRatio")+"%", item.get("bmiAmountRatio")+"%"
            );
            datas.add(line);
        }

        MqsUtils.buildExportFileNameSuffix(fileName, queryVo.getItemDateFrom(), queryVo.getItemDateTo());


        List<ExcelExport> exports = new ArrayList<>(1);
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }

    /**
     * 慢特病分析 - 导出excel
     *
     * @param response 响应
     * @param queryVo  查询条件
     */
    public void csDiseaseExport(HttpServletResponse response, CsDiseaseQueryVo queryVo) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        List<CsDiseaseDto> list = getcsgetListByPage(queryVo, null);

        StringBuilder fileName = new StringBuilder();
        fileName.append("决策支持-慢特病分析");


        // 构造表头和数据
        heads.add(CollUtil.list(true,
                "NO", "PATIENT_ID", "PATIENT_NAME", "PATIENT_GENDER", "ITEM_TYPE_NAME", "ITEM_DATE", "ITEM_NAME", "VIOLATION_FLAG", "CS_DISEASE_CODE", "CS_DISEASE_NAME",
                "BILL_DATE", "ADMISSION_NO", "BENEFIT_TYPE_ID", "APPLY_DEPT_NAME", "APPLY_DOCTOR_NAME", "PRICE", "NUMBERS", "COSTS", "BMI_OVERALL_AMOUNT"));
        heads.add(CollUtil.list(true,
                "单据号", "参保人编码", "参保人姓名", "性别", "项目类型", "项目日期", "项目名称", "是否违规", "病种编码", "病种名称",
                "结算日期", "住院号/门诊号", "参保类型", "科室名称", "医生名称", "单价", "数量", "总金额", "医保统筹金额"));

        for (CsDiseaseDto item : list) {
            List<Object> line = CollUtil.list(true,
                    item.getNo(), item.getPatientId(), item.getPatientName(), item.getPatientGender(), item.getItemTypeName(), item.getItemDate(), item.getItemName(), item.getViolationFlag(), item.getCsDiseaseCode(), item.getCsDiseaseName(),
                    item.getBillDate(), item.getAdmissionNo(), item.getBenefitTypeId(), item.getApplyDeptName(), item.getApplyDoctorName(), item.getPrice(), item.getNumbers(), item.getCosts(), item.getBmiOverallAmount()
            );
            datas.add(line);
        }

        MqsUtils.buildExportFileNameSuffix(fileName, queryVo.getItemDateFrom(), queryVo.getItemDateTo());

        List<ExcelExport> exports = new ArrayList<>(1);
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }


    /**
     * 明细查询 - 导出excel
     *
     * @param response 响应
     * @param queryVo  查询条件
     */
    public void analyzeExport(HttpServletResponse response, DetailQueryVo queryVo) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        List<DetailDto> list = analyzeQueryDetail(queryVo, null);

        StringBuilder fileName = new StringBuilder();
        fileName.append("决策支持-明细查询");

        // 构造表头和数据
        heads.add(CollUtil.list(true,
                "NO", "ADMISSION_NO", "PATIENT_ID", "PATIENT_NAME", "BENEFIT_TYPE_ID", "CLAIM_TYPE_ID", "PATIENT_GENDER", "PATIENT_BIRTHDAY",
                "IN_DIAGNOSIS_CODE", "IN_DIAGNOSIS_NAME", "OUT_DIAGNOSIS_CODE", "OUT_DIAGNOSIS_NAME", "SECONDARY_DISEASE_ID", "SECONDARY_DISEASE_ZH",
                "ADMISSION_DATE", "DISCHARGE_DATE", "BILL_DATE", "APPLY_DEPT_NAME", "APPLY_DOCTOR_NAME", "RULE_NAMES", "RULE_REASONS", "ITEM_TYPE_NAME", "ITEM_DATE", "ITEM_NAME",
                "SPECIFICATION", "REASON_TYPES", "REASON_DESS", "USAGE_UNIT", "USAGE", "PRICE", "NUMBERS", "COSTS", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT"));
        heads.add(CollUtil.list(true,
                "单据号", "住院号/门诊号", "参保人编码", "参保人姓名", "参保类型", "医疗类别", "性别", "出生日期",
                "入院诊断编码", "入院诊断名称", "出院主诊断编码", "出院主诊断名称", "出院次诊断编码", "出院次诊断名称",
                "入院日期", "出院日期", "结算日期", "科室名称", "医生名称", "规则名称", "违规原因", "项目类型", "项目日期", "项目名称",
                "规格", "反馈类型", "反馈理由", "包装单位", "用量", "单价", "数量", "总金额", "医保内金额", "医保统筹金额"));

        for (DetailDto item : list) {
            List<Object> line = CollUtil.list(true,
                    item.getNo(), item.getAdmissionNo(), item.getPatientId(), item.getPatientName(), item.getBenefitTypeId(), item.getClaimTypeId(), item.getPatientGender(), item.getPatientBirthday(),
                    item.getInDiagnosisCode(), item.getInDiagnosisName(), item.getOutDiagnosisCode(), item.getOutDiagnosisName(), item.getSecondaryDiseaseId(), item.getSecondaryDiseaseZh(),
                    DateUtil.format(item.getAdmissionDate(), "yyyy-MM-dd HH:mm:ss"), DateUtil.format(item.getDischargeDate(), "yyyy-MM-dd HH:mm:ss"), DateUtil.format(item.getBillDate(), "yyyy-MM-dd HH:mm:ss"), item.getApplyDeptName(), item.getApplyDoctorName(), item.getRuleNames(), item.getRuleReasons(), item.getItemTypeName(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"), item.getItemName(),
                    item.getSpecification(), item.getReasonTypes(), item.getReasonDess(), item.getUsageUnit(), item.getUsage(), item.getPrice(), item.getNumbers(), item.getCosts(), item.getBmiConveredAmount(), item.getBmiOverallAmount()
            );
            datas.add(line);
        }

        MqsUtils.buildExportFileNameSuffix(fileName, queryVo.getItemDateFrom(), queryVo.getItemDateTo());

        List<ExcelExport> exports = new ArrayList<>(1);
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }
}
