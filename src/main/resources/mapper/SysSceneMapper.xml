<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.sys.mapper.SysSceneMapper">

    <resultMap type="com.crhms.cloud.mqs.sys.domain.SysScene" id="SysSceneMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="sceneCode" column="scene_code" jdbcType="VARCHAR"/>
        <result property="sceneName" column="scene_name" jdbcType="VARCHAR"/>
        <result property="enable" column="enable" jdbcType="VARCHAR"/>
        <result property="lastUpdatedBy" column="last_updated_by" jdbcType="INTEGER"/>
        <result property="lastUpdatedDate" column="last_updated_date" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="created_by" jdbcType="INTEGER"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="hospitalId" column="hospital_id" jdbcType="VARCHAR"/>
    </resultMap>


    <!--查询指定行数据-->
    <select id="queryList" resultType="com.crhms.cloud.mqs.sys.domain.SysScene">
        SELECT
        mssr.scene_code,
        mss.scene_name,
        GROUP_CONCAT(rule_name,',') as ruleNames,
        mssr.last_updated_date,
        mssr.last_updated_by
        FROM
        mqs_sys_scene_rule mssr
        LEFT JOIN mqs_sys_rules msr ON msr.rule_code = mssr.rule_code and mssr.hospital_id = msr.hospital_id and msr.deleted = '0'
        LEFT JOIN mqs_sys_scene mss ON mss.scene_code = mssr.scene_code and mss.hospital_id = mssr.hospital_id
        <where>
            <if test="hospitalId != null and hospitalId != ''">
                and mssr.hospital_id = #{hospitalId}
            </if>
        </where>
        GROUP BY
        mssr.scene_code,mss.scene_name,mssr.last_updated_date,mssr.last_updated_by
    </select>

    <select id="queryRulesByScene" resultType="com.crhms.cloud.mqs.sys.domain.SysRules">
        SELECT
        mssr.id as id,
        mssr.rule_code AS ruleCode,
        msr.rule_name AS ruleName,
        GROUP_CONCAT( msrbh.broad_heading ) AS broadHeading,
        msrl.`name` AS ruleLevelName,
        msr.is_his,
        msr.is_main,
        msr.remake,
        msr.`enable`
        FROM
        mqs_sys_scene_rule mssr
        LEFT JOIN mqs_sys_rules msr ON mssr.rule_code = msr.rule_code AND mssr.hospital_id = msr.hospital_id
        LEFT JOIN mqs_sys_rule_level_third msrlt ON msrlt.platform = msr.rule_type AND msrlt.`code` = msr.rule_level
        AND msrlt.hospital_id = msr.hospital_id
        LEFT JOIN mqs_sys_rule_level msrl ON msrl.`code` = msrlt.sys_code AND msrl.hospital_id = msrlt.hospital_id
        LEFT JOIN mqs_sys_rule_broad_heading msrbh ON msrbh.rule_code = msr.rule_code AND msrbh.hospital_id = msr.hospital_id
        <where>
            AND mssr.scene_code = #{sceneCode}
            <if test="rule != null and rule != ''">
                and ( msr.rule_code like CONCAT('%',#{rule,jdbcType = VARCHAR},'%')
                or msr.rule_name like CONCAT('%',#{rule,jdbcType = VARCHAR},'%'))
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and msr.hospital_id = #{hospitalId}
            </if>
            <if test="ruleLevelList != null and ruleLevelList.size > 0 ">
                and msrlt.sys_code in
                <foreach collection="ruleLevelList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        mssr.id,
        mssr.rule_code,
        msr.rule_name,
        msrl.`name`,
        msr.is_his,
        msr.is_main,
        msr.remake,
        msr.`enable`
    </select>

    <select id="querySceneConfig" resultType="com.crhms.cloud.mqs.sys.domain.SysScene">
        SELECT
            t.id,
            t.scene_code,
            t.scene_name,
            t.`enable`
        FROM
            mqs_sys_scene t
        where t.hospital_id = #{hospitalId}
    </select>

    <select id="queryRulesNotInScene" resultType="com.crhms.cloud.mqs.sys.domain.SysRules">
        SELECT
        msr.id,
        msr.rule_code,
        msr.rule_name,
        GROUP_CONCAT( msrbh.broad_heading ) AS broadHeading,
        rule_level,
        msrl.`name` AS ruleLevelName,
        remake
        FROM
        mqs_sys_rules msr
        LEFT JOIN mqs_sys_rule_broad_heading msrbh ON msrbh.rule_code = msr.rule_code AND msrbh.hospital_id = msr.hospital_id
        LEFT JOIN mqs_sys_rule_level_third msrlt ON msrlt.platform = msr.rule_type AND msrlt.`code` = msr.rule_level
        AND msrlt.hospital_id = msr.hospital_id
        LEFT JOIN mqs_sys_rule_level msrl ON msrl.`code` = msrlt.sys_code AND msrl.hospital_id = msrlt.hospital_id
        WHERE
            msr.hospital_id = #{hospitalId}
            and msr.rule_code NOT IN ( SELECT mssr.rule_code FROM mqs_sys_scene_rule mssr WHERE mssr.scene_code = #{sceneCode} AND mssr.hospital_id = #{hospitalId})
            AND deleted = '0'
            AND msr.`enable` = '1'
            and msr.rule_type != '0'
            <if test="rule != null and rule != '' ">
                and (msr.rule_code like CONCAT('%', #{rule}, '%')
                or msr.rule_name like CONCAT('%', #{rule}, '%'))
            </if>
        GROUP BY
        msr.id,
        msr.rule_code,
        msr.rule_name,
        rule_level,
        msrl.`name`,
        remake
        ORDER BY rule_code DESC
    </select>
    <select id="queryEnableScene" resultType="java.util.Map">
        select LEFT(scene_code,2) sceneType
        from mqs_sys_scene
        where enable = '1'
        and hospital_id = #{hospitalId}
        GROUP BY LEFT(scene_code,2)
    </select>


</mapper>

