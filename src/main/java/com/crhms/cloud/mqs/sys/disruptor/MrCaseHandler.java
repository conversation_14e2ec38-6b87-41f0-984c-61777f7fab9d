package com.crhms.cloud.mqs.sys.disruptor;

import cn.hutool.core.collection.CollectionUtil;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.mqs_mr.domain.MrCase;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlanAudit;
import com.crhms.cloud.mqs.mqs_mr.service.MrCaseService;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName SysLogHandler
 * @Description 人工质控处理类
 * <AUTHOR>
 * @Date 2024-12-24
 **/
@Component
@Slf4j
public class MrCaseHandler implements EventHandler<MrCaseHandlerDTO> {

    @Autowired
    private MrCaseService mrCaseService;

    @Override
    public void onEvent(MrCaseHandlerDTO mrCaseHandlerDTO, long l, boolean b) throws Exception {
        //处理业务数据
        List<MrCase> updates = new ArrayList<>();
        try{
            if(Objects.isNull(mrCaseHandlerDTO)){
                return;
            }
            List<BaseMedicalCase> baseMedicalCaseList = mrCaseHandlerDTO.getBaseMedicalCaseList();
            List<MrPlanAudit> mrPlanAudits = mrCaseHandlerDTO.getMrPlanAudits();
            Map<String, MrCase> caseMap = mrCaseHandlerDTO.getCaseMap();
            List<String> sceneCodes = mrCaseHandlerDTO.getSceneCodes();
            String auditScenario = mrCaseHandlerDTO.getAuditScenario();
            String hospitalId = mrCaseHandlerDTO.getHospitalId();
            for (BaseMedicalCase aCase : baseMedicalCaseList) {
                //根据当前科室获取分配审核人名称
                String mrTo = "";
                String mrToName = "";
                if(CollectionUtil.isNotEmpty(mrPlanAudits)) {
                    //根据当前科室找到对应的审核人
                    List<MrPlanAudit> filterMrPlanAudits = mrPlanAudits.stream().filter(mrPlanAudit -> mrPlanAudit.getMrPlanDepartment().contains(aCase.getPresentDeptCode())).collect(Collectors.toList());
                    if(CollectionUtil.isNotEmpty(filterMrPlanAudits)){
                        //去重并重新拼接
                        mrTo = filterMrPlanAudits.stream()
                                .map(MrPlanAudit::getMrPlanUser)
                                .flatMap(name -> Arrays.stream(name.split(",")))
                                .distinct()
                                .collect(Collectors.joining(","));
                        mrToName = filterMrPlanAudits.stream()
                                .map(MrPlanAudit::getMrPlanUserName)
                                .flatMap(name -> Arrays.stream(name.split(",")))
                                .distinct()
                                .collect(Collectors.joining(","));
                    }
                }
                MrCase mrCase = caseMap.get(aCase.getNo());
                //按照场景排序判断，如果当前场景排序大于现有记录场景就更新，否则不更新
                if( CollectionUtil.isNotEmpty(sceneCodes) && sceneCodes.contains(mrCase.getAuditScenario()) && !mrCase.getAuditScenario().equals(auditScenario)){
                    mrCase.setAuditScenario(auditScenario);
                }
                mrCase.setMrTo(mrTo);
                mrCase.setMrToName(mrToName);
                updates.add(mrCase);
            }
            //批量更新临床处理状态
            updates = mrCaseService.getCaseClinicalStatus(updates,hospitalId);
            mrCaseService.updateBatchById(updates);
        }catch (Exception e){
            log.error("人工质控记录消费发生异常: ");
            e.printStackTrace();
        } finally {
            updates = null;
        }
    }
}
