package com.crhms.cloud.mqs.mqs_kbm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.mqs_kbm.domain.GroupCharge;
import com.crhms.cloud.mqs.mqs_kbm.domain.GroupCodeValidity;
import com.crhms.cloud.mqs.mqs_kbm.service.GroupChargeService;
import com.crhms.cloud.mqs.mqs_kbm.service.GroupCodeValidityService;
import com.crhms.cloud.mqs.mqs_kbm.vo.GroupChargeVo;
import com.crhms.cloud.mqs.mqs_kbm.vo.GroupCodeValidityVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 知识库管理-分组合理性
 * 编码有效性
 * 收费与入组合理性
 *
 * @author: wangxingang
 * @date: 2023.02.06
 */

@RestController
@RequestMapping("/api/mqs/kbm/group")
public class GroupController {

    @Autowired
    GroupCodeValidityService codeValidityService;

    @Autowired
    GroupChargeService chargeService;

    /**
     * 查询编码有效性列表
     *
     * @param filter 查询条件
     * @return 查询列表
     */
    @PostMapping("/code/page")
    public ResponseEntity<List<GroupCodeValidity>> queryCodeValidity(@RequestBody(required = false) GroupCodeValidityVo filter,
                                                                     @RequestParam(value = "page", defaultValue = "1") int page,
                                                                     @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<GroupCodeValidity> pageInfo = PageUtil.getPage(page, pageSize);
        List<GroupCodeValidity> list = codeValidityService.queryCodeValidity(pageInfo, filter);
        return new ResponseEntity<>(list, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }


    /**
     * 编码有效性-查询版本号下拉列表
     */
    @GetMapping("/code/versionNo")
    public ResponseEntity<List<String>> queryVersionNoList() {
        List<String> versionNoList = codeValidityService.queryVersionNoList();
        return new ResponseEntity<>(versionNoList, HttpStatus.OK);
    }

    /**
     * 编码有效性-查询类型下拉列表
     */
    @GetMapping("/code/type")
    public ResponseEntity<List<String>> queryTypeList() {
        List<String> typeList = codeValidityService.queryTypeList();
        return new ResponseEntity<>(typeList, HttpStatus.OK);
    }

    /**
     * 编码有效性-查询是否有效下拉列表
     */
    @GetMapping("/code/validFlag")
    public ResponseEntity<List<String>> queryValidFlagList() {
        List<String> validFlagList = codeValidityService.queryValidFlagList();
        return new ResponseEntity<>(validFlagList, HttpStatus.OK);
    }


    /**
     * 查询收费与入组合理性
     *
     * @param filter 查询条件
     * @return 查询列表
     */
    @PostMapping("/charge/page")
    public ResponseEntity<List<GroupCharge>> queryCharge(@RequestBody(required = false) GroupChargeVo filter,
                                                         @RequestParam(value = "page", defaultValue = "1") int page,
                                                         @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<GroupCharge> pageInfo = PageUtil.getPage(page, pageSize);
        List<GroupCharge> list = chargeService.queryCharge(pageInfo, filter);
        return new ResponseEntity<>(list, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }


    /**
     * 收费与入组合理性-查询类型下拉列表
     */
    @GetMapping("/charge/type")
    public ResponseEntity<List<String>> queryTypeList2() {
        List<String> typeList = chargeService.queryTypeList();
        return new ResponseEntity<>(typeList, HttpStatus.OK);
    }

    /**
     * 收费与入组合理性-查询ADRG下拉列表
     */
    @GetMapping("/charge/adrg")
    public ResponseEntity<List<String>> queryAdrgList() {
        List<String> adrgList = chargeService.queryAdrgList();
        return new ResponseEntity<>(adrgList, HttpStatus.OK);
    }

    /**
     * 收费与入组合理性-查询ADRG名称下拉列表
     */
    @GetMapping("/charge/adrgName")
    public ResponseEntity<List<String>> queryAdrgNameList() {
        List<String> adrgNameList = chargeService.queryAdrgNameList();
        return new ResponseEntity<>(adrgNameList, HttpStatus.OK);
    }
}
