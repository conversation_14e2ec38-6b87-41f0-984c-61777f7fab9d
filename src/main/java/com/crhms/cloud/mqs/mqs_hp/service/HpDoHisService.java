package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpDoHis;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpDoHisMapper;
import com.crhms.cloud.mqs.mqs_hp.service.HpDoHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-医嘱审核-历史表(HpDoHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 10:22:14
 */
@Service("hpDoHisService")
public class HpDoHisService extends ServiceImpl< HpDoHisMapper, HpDoHis> {

}
