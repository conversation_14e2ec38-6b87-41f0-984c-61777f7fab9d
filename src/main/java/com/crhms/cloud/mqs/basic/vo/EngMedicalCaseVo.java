package com.crhms.cloud.mqs.basic.vo;
import com.crhms.cloud.mqs.core.annotation.InitEngineFile;
import com.googlecode.jmapper.annotations.JMap;
import com.googlecode.jmapper.annotations.JMapConversion;
import lombok.Data;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class EngMedicalCaseVo {
    // 主单唯一编码		String(256)	是
    @InitEngineFile(needIndex = false)
    @JMap("no")
    private String admission_no;

    // 住院号\门诊号	预审结果查询条件之一	String(256)	是
    @InitEngineFile(needIndex = true, tableColName = "admission_no", colFileName = "hospitalizedno")
    @JMap("admissionNo")
    private String hospitalized_no;

    //单据类型 与审核场景字段一致
    @InitEngineFile(needIndex = false)
    private String record_type;

    // 参保人编码	社保卡号或者身份证号（必须保证编码是患者在本院唯一编码，因要根据当前编码查询关联历史数据。）	String(20)	是
    @InitEngineFile(needIndex = true, tableColName = "patient_id", colFileName = "patientid")
    @JMap("patientId")
    private String patient_id;

    // 单据结算日期	门诊单据用实际结算日期；住院单据，如已经出院结算，则用结算日期，如尚未出院结算，用当天费用增量或医嘱增量日期  格式 yyyy-MM-dd HH:mm:ss	String	是
    @InitEngineFile(needIndex = false)
    @JMap("billDate")
    private String billdate;

    // 医院编号	医保分配的医疗机构ID	String(50)	是
    @InitEngineFile(needIndex = true, tableColName = "hospital_id", colFileName = "hospitalid")
    @JMap("hospitalCode")
    private String hospital_id;

    // 医院级别	见数据字典5.4定点机构等级 Integer	是
    @InitEngineFile(needIndex = true, tableColName = "hospital_level", colFileName = "hospitallevel")
    @JMap("hospitalLevel")
    private Integer hospital_level;

    // 定点机构类型	见数据字典5.5定点机构类型 String(50)	是
    @InitEngineFile(needIndex = true, tableColName = "hospital_type", colFileName = "hospitaltype")
    @JMap("hospitalType")
    private String hospital_type;

    // 就医方式编码	见数据字典5.1就医方式    String	是
    @InitEngineFile(needIndex = true, tableColName = "claim_type_id", colFileName = "claimtypeid")
    @JMap("claimTypeId")
    private String claim_type_id;
    
    // 参保类型编码	见数据字典5.2参保类型    String(2)	是
    @InitEngineFile(needIndex = true, tableColName = "benefit_type_id", colFileName = "benefittypeid")
    @JMap("benefitTypeId")
    private String benefit_type_id;

    @InitEngineFile(needIndex = false)
    @JMap("registerTime")
    private String register_time;

    @InitEngineFile(needIndex = true, tableColName = "dept_code", colFileName = "admissiondeptcode")
    @JMap("deptCode")
    private String admission_dept_code;

    @InitEngineFile(needIndex = true, tableColName = "dept_name", colFileName = "admissiondeptname")
    @JMap("deptName")
    private String admission_dept_name;

    @InitEngineFile(needIndex = true, tableColName = "doc_id", colFileName = "docid")
    @JMap("docId")
    private String doc_id;

    @InitEngineFile(needIndex = true, tableColName = "doc_name", colFileName = "docname")
    @JMap("docName")
    private String doc_name;

    @InitEngineFile(needIndex = true, tableColName = "doc_level", colFileName = "doclevel")
    @JMap("docLevel")
    private String doc_level;

    @InitEngineFile(needIndex = true, tableColName = "discharge_dept_code", colFileName = "dischargedeptcode")
    @JMap("dischargeDeptCode")
    private String discharge_dept_code;

    @InitEngineFile(needIndex = true, tableColName = "discharge_dept_name", colFileName = "dischargedeptname")
    @JMap("dischargeDeptName")
    private String discharge_dept_name;

    // 入院诊断编码	从当地医疗结构获取的ICD10字典数据（医保版）	String(256)	是
    @InitEngineFile(needIndex = true, tableColName = "in_diagnosis_code", colFileName = "diagnosiscode")
    @JMap("inDiagnosisCode")
    private String admission_disease_id;

    // 入院诊断名称		String(256)	是
    @InitEngineFile(needIndex = true, tableColName = "in_diagnosis_name", colFileName = "diagnosisname")
    @JMap("inDiagnosisName")
    private String admission_disease_name;

    // 出院诊断编码		String(256)	是
    @InitEngineFile(needIndex = true, tableColName = "out_diagnosis_code", colFileName = "diagnosiscode")
    @JMap("outDiagnosisCode")
    private String discharge_disease_id	;

    // 出院诊断名称		String(256)	是
    @InitEngineFile(needIndex = true, tableColName = "out_diagnosis_name", colFileName = "diagnosisname")
    @JMap("outDiagnosisName")
    private String discharge_disease_name;

    // 其它诊断编码1		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code1", colFileName = "diagnosiscode")
    @JMap("diagnosisCode1")
    private String diagnosis_code1;

    // 其它诊断编码2		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code2", colFileName = "diagnosiscode")
    @JMap("diagnosisCode2")
    private String diagnosis_code2;

    // 其它诊断编码3		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code3", colFileName = "diagnosiscode")
    @JMap("diagnosisCode3")
    private String diagnosis_code3;

    // 其它诊断编码4		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code4", colFileName = "diagnosiscode")
    @JMap("diagnosisCode4")
    private String diagnosis_code4;

    // 其它诊断编码5		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code5", colFileName = "diagnosiscode")
    @JMap("diagnosisCode5")
    private String diagnosis_code5;

    // 其它诊断编码6		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code6", colFileName = "diagnosiscode")
    @JMap("diagnosisCode6")
    private String diagnosis_code6;

    // 其它诊断编码7		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code7", colFileName = "diagnosiscode")
    @JMap("diagnosisCode7")
    private String diagnosis_code7;

    // 其它诊断编码8		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code8", colFileName = "diagnosiscode")
    @JMap("diagnosisCode8")
    private String diagnosis_code8;

    // 其它诊断编码9		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code9", colFileName = "diagnosiscode")
    @JMap("diagnosisCode9")
    private String diagnosis_code9;

    // 其它诊断编码10		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code10", colFileName = "diagnosiscode")
    @JMap("diagnosisCode10")
    private String diagnosis_code10;

    // 其它诊断编码11		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code11", colFileName = "diagnosiscode")
    @JMap("diagnosisCode11")
    private String diagnosis_code11;

    // 其它诊断编码12		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code12", colFileName = "diagnosiscode")
    @JMap("diagnosisCode12")
    private String diagnosis_code12;

    // 其它诊断编码13		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code13", colFileName = "diagnosiscode")
    @JMap("diagnosisCode13")
    private String diagnosis_code13;

    // 其它诊断编码14		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code14", colFileName = "diagnosiscode")
    @JMap("diagnosisCode14")
    private String diagnosis_code14;

    // 其它诊断编码15		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code15", colFileName = "diagnosiscode")
    @JMap("diagnosisCode15")
    private String diagnosis_code15;

    // 其它诊断编码16		String(256)	否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_code16", colFileName = "diagnosiscode")
    @JMap("diagnosisCode16")
    private String diagnosis_code16;

    // 其它诊断名称1      String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name1", colFileName = "diagnosisname")
    @JMap("diagnosisName1")
    private String diagnosis_name1;

    // 其它诊断名称2      String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name2", colFileName = "diagnosisname")
    @JMap("diagnosisName2")
    private String diagnosis_name2;

    // 其它诊断名称3      String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name3", colFileName = "diagnosisname")
    @JMap("diagnosisName3")
    private String diagnosis_name3;

    // 其它诊断名称4      String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name4", colFileName = "diagnosisname")
    @JMap("diagnosisName4")
    private String diagnosis_name4;

    // 其它诊断名称5      String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name5", colFileName = "diagnosisname")
    @JMap("diagnosisName5")
    private String diagnosis_name5;

    // 其它诊断名称6      String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name6", colFileName = "diagnosisname")
    @JMap("diagnosisName6")
    private String diagnosis_name6;

    // 其它诊断名称7      String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name7", colFileName = "diagnosisname")
    @JMap("diagnosisName7")
    private String diagnosis_name7;

    // 其它诊断名称8      String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name8", colFileName = "diagnosisname")
    @JMap("diagnosisName8")
    private String diagnosis_name8;

    // 其它诊断名称9      String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name9", colFileName = "diagnosisname")
    @JMap("diagnosisName9")
    private String diagnosis_name9;

    // 其它诊断名称10     String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name10", colFileName = "diagnosisname")
    @JMap("diagnosisName10")
    private String diagnosis_name10;

    // 其它诊断名称11     String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name11", colFileName = "diagnosisname")
    @JMap("diagnosisName11")
    private String diagnosis_name11;

    // 其它诊断名称12     String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name12", colFileName = "diagnosisname")
    @JMap("diagnosisName12")
    private String diagnosis_name12;

    // 其它诊断名称13     String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name13", colFileName = "diagnosisname")
    @JMap("diagnosisName13")
    private String diagnosis_name13;

    // 其它诊断名称14     String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name14", colFileName = "diagnosisname")
    @JMap("diagnosisName14")
    private String diagnosis_name14;

    // 其它诊断名称15     String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name15", colFileName = "diagnosisname")
    @JMap("diagnosisName15")
    private String diagnosis_name15;

    // 其它诊断名称16     String(256) 否
    @InitEngineFile(needIndex = true, tableColName = "diagnosis_name16", colFileName = "diagnosisname")
    @JMap("diagnosisName16")
    private String diagnosis_name16;

    // 入院时间	格式 yyyy-MM-dd HH:mm:ss入院时间不能晚于出院时间	String	是
    @InitEngineFile(needIndex = false)
    @JMap("admissionDate")
    private String admission_date;

    // 出院时间	门诊单据用实际结算日期；住院单据，如已经出院结算，则用结算日期，如尚未出院结算，用当天费用增量或医嘱增量日期。格式 yyyy-MM-dd HH:mm:ss	String	是
    @InitEngineFile(needIndex = false)
    @JMap("dischargeDate")
    private String discharge_date;

    // 性别	0未知的性别、1男、2女、9、未说明的性别	String(4)	是
    @InitEngineFile(needIndex = true, tableColName = "patient_gender", colFileName = "patientgender")
    @JMap("patientGender")
    private String patient_gender;

    // 出生日期	格式 yyyy-MM-dd	String	是
    @InitEngineFile(needIndex = false)
    @JMap("patientBirthday")
    private String patient_birthday;

    // 是否孕期	1 是0 否	String(40)	是
    @InitEngineFile(needIndex = true, tableColName = "is_pregnant", colFileName = "ispregnant")
    @JMap("isPregnant")
    private String is_pregnant;

    // 是否哺乳期	1 是0 否	String(40)	是
    @InitEngineFile(needIndex = true, tableColName = "is_lactating", colFileName = "islactating")
    @JMap("isLactating")
    private String is_lactating	;

    // 单据总金额	不能为空，默认为0	Number(128，4)	是
    @InitEngineFile(needIndex = false)
    @JMap("totalAmount")
    private Double total_amount;

    // 人员类别编码	见数据字典5.3人员类别   String(6)	是
    @InitEngineFile(needIndex = true, tableColName = "personnel_type", colFileName = "benefitgroupid")
    @JMap("personnelType")
    private String benefit_group_id;

    // 待遇类型	默认值-99999	String(16)	是
    @InitEngineFile(needIndex = true, tableColName = "treatment_type", colFileName = "treatmenttype")
    @JMap("treatmentType")
    private String treatment_type;

    // 医保内金额	非空。    全自费的为0，默认等于总金额	Number(128，4)	是
    @InitEngineFile(needIndex = false)
    @JMap("bmiConveredAmount")
    private Double bmi_convered_amount ;

    // 是否异地就医	0否；1是 	String(6)	是
    @InitEngineFile(needIndex = true, tableColName = "unusual_flag", colFileName = "unusualflag")
    @JMap("unusualFlag")
    private String unusual_flag;

    // 参保人特殊保险类型组编码 	1：生育  2：工伤  -1：其他	String(2)	是
    @InitEngineFile(needIndex = true, tableColName = "benefit_group_code", colFileName = "benefitgroupcode")
    @JMap("benefitGroupCode")
    private String benefit_group_code;

    // 预留字段	默认值  0	C	Y
//    @JMap("Z_AACT007")
    @InitEngineFile(needIndex = false)
    private String Z_AACT007;

    // 因战因公标志	0否，1是 默认值  0 	String(2)	是
    @InitEngineFile(needIndex = true, tableColName = "war_business_flag", colFileName = "warbusinessflag")
    @JMap("warBusinessFlag")
    private String war_business_flag;

    // 单病种编码	默认值  0，如果有则传病种编码	String(50)	是
    @InitEngineFile(needIndex = true, tableColName = "single_disease_code", colFileName = "singlediseasecode")
    @JMap("singleDiseaseCode")
    private String single_disease_code;

    // 出院病区编码	预审结果查询条件之一	String(50)	是
    @InitEngineFile(needIndex = true, tableColName = "out_zone_code", colFileName = "outzonecode")
    @JMap("outZoneCode")
    private String out_zone_code;

    // 病案号（病人在医院看病的唯一登记号，只登记一次）	预审结果查询条件之一	String(256)	是
    @InitEngineFile(needIndex = true, tableColName = "medical_record_id", colFileName = "medicalrecordid")
    @JMap("medicalRecordId")
    private String medical_record_id;

    // 参保人姓名	预审结果查询条件之一	String(80)	是
    @InitEngineFile(needIndex = true, tableColName = "patient_name", colFileName = "patientname")
    @JMap("patientName")
    private String patient_name	;

    // 出入院状态   0：在院 1：出院 2：门诊	预审结果查询条件之一	String	是
    @InitEngineFile(needIndex = true, tableColName = "is_discharge", colFileName = "isdischarge")
    @JMap("isDischarge")
    private String is_discharge	;

    @InitEngineFile(needIndex = true, tableColName = "bmi_code", colFileName = "bmicode")
    @JMap("bmiCode")
    private String bmi_code;

    @InitEngineFile(needIndex = true, tableColName = "bmi_name", colFileName = "bminame")
    @JMap("bmiName")
    private String bmi_name;

    // 是否二次返院/转院/指定病种定额结算 0：正常 1：转院 2：二次返院 4：90天或180天结算|中间库字段pka127=3
    @InitEngineFile(needIndex = true, tableColName = "is_trans_hospital", colFileName = "istranshospital")
    @JMap("isTransHospital")
    private String is_trans_hospital;

    // 是否公立医改医院 1：是 0：否
    @InitEngineFile(needIndex = true, tableColName = "is_public_hosp", colFileName = "ispublichosp")
    @JMap("isPublicHosp")
    private String is_public_hosp;

    // 数据来源 0 医生站  1 护士站
    @InitEngineFile(needIndex = false)
    @JMap("source")
    private String source;

    // 证件号码
    @InitEngineFile(needIndex = true, tableColName = "patient_idno", colFileName = "patientidno")
    @JMap("patientIdno")
    private String patient_idno;

    // 医保统筹金额
    @InitEngineFile(needIndex = false)
    @JMap("bmiOverallAmount")
    private Double hifp_payamt;

    // 医疗类别
    @InitEngineFile(needIndex = true, tableColName = "claim_type_id", colFileName = "claimtypeid")
    @JMap("claimTypeId")
    private String med_type;

    // 异地结算标志
    @InitEngineFile(needIndex = true, tableColName = "benefit_group_code", colFileName = "outsetlflag")
    @JMap("benefitGroupCode")
    private String out_setl_flag;

    // 自付金额
    @InitEngineFile(needIndex = false)
    @JMap("payAmount")
    private Double selfpay_amt;

    // 转科日期
    @InitEngineFile(needIndex = false)
    @JMap("tfDate")
    private String tf_date;

    // 转出科室编码
    @InitEngineFile(needIndex = true, tableColName = "tf_from_dept_code", colFileName = "tffromdeptcode")
    @JMap("tfFromDeptCode")
    private String tf_from_dept_code;

    // 转出科室名称
    @InitEngineFile(needIndex = true, tableColName = "tf_from_dept_name", colFileName = "tffromdeptname")
    @JMap("tfFromDeptName")
    private String tf_from_dept_name;

    // 转出医生编码
    @InitEngineFile(needIndex = true, tableColName = "tf_from_doc_code", colFileName = "tffromdoccode")
    @JMap("tfFromDocCode")
    private String tf_from_doc_code;

    // 转出医生编码
    @InitEngineFile(needIndex = true, tableColName = "tf_from_doc_name", colFileName = "tffromdocname")
    @JMap("tfFromDocName")
    private String tf_from_doc_name;

    // 转入科室编码
    @InitEngineFile(needIndex = true, tableColName = "tf_to_dept_code", colFileName = "tftodeptcode")
    @JMap("tfToDeptCode")
    private String tf_to_dept_code;

    // 转入科室名称
    @InitEngineFile(needIndex = true, tableColName = "tf_to_dept_name", colFileName = "tftodeptname")
    @JMap("tfToDeptName")
    private String tf_to_dept_name;

    // 转入医生编码
    @InitEngineFile(needIndex = true, tableColName = "tf_to_doc_code", colFileName = "tftodoccode")
    @JMap("tfToDocCode")
    private String tf_to_doc_code;

    // 转入医生名称
    @InitEngineFile(needIndex = true, tableColName = "tf_to_doc_name", colFileName = "tftodocname")
    @JMap("tfToDocName")
    private String tf_to_doc_name;

    // 险种
    @InitEngineFile(needIndex = false)
    private String insutype;

    // 救助金支付金额
    @InitEngineFile(needIndex = false)
    private Double ma_amt;

    //生育状态
    @InitEngineFile(needIndex = false)
    private String matn_stas;

    //就诊类型
    @InitEngineFile(needIndex = false)
    private String med_mdtrt_type;

    //自费金额
    @InitEngineFile(needIndex = false)
    private Double ownpay_amt;

    //报销标志
    @InitEngineFile(needIndex = false)
    private String reim_flag;

    //结算总次数
    @InitEngineFile(needIndex = false)
    private String setl_totlnum;


    /**
     * jmapper类型转换器
     *
     */
    @JMapConversion(from = {"billDate", "admissionDate","dischargeDate", "registerTime","tfDate"},
            to = {"billdate", "admission_date","discharge_date", "register_time","tf_date"})
    public String convertDateToString(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        return date == null ? null : format.format(date);
    }
    @JMapConversion(from = {"totalAmount", "payAmount","bmiOverallAmount", "bmiConveredAmount"},
            to = {"total_amount", "selfpay_amt","hifp_payamt", "bmi_convered_amount"})
    public Double convertBigToDou(BigDecimal num) {
        return num == null ? null : num.doubleValue();
    }
}
