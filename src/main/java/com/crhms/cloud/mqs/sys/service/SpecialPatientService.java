package com.crhms.cloud.mqs.sys.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.client.cdmp.dic.CdmpDicInterface;
import com.crhms.cloud.core.excel.dto.ExcelData;
import com.crhms.cloud.core.excel.dto.ExcelExport;
import com.crhms.cloud.core.excel.dto.SheetData;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.sys.domain.SpecialPatient;
import com.crhms.cloud.mqs.sys.dto.SpecialPatientQueryVO;
import com.crhms.cloud.mqs.sys.mapper.SpecialPatientMapper;
import com.crhms.cloud.mqs.sys.utils.DictUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 特殊患者 service
 *
 * @author: 王新刚
 * @date: 2023/03/24
 */
@Service
public class SpecialPatientService extends ServiceImpl<SpecialPatientMapper, SpecialPatient> {

    @Autowired
    private CdmpDicInterface cdmpDicInterface;


    /**
     * 新增或编辑特殊患者信息
     *
     * @param specialPatient 特殊患者信息
     */
    public void submit(SpecialPatient specialPatient) {
        specialPatient.setHospitalId(LoginContext.getHospitalId());
        // 新增时传入多个患者类型, 逗号拼接存储
        specialPatient.setPatientType(StrUtil.join(",", specialPatient.getPatientTypeList()));
        this.saveOrUpdate(specialPatient);
    }

    /**
     * 分页查询
     *
     * @param queryVO  查询条件
     * @param pageInfo 分页信息
     * @return 分页结果
     */
    public List<SpecialPatient> queryPage(SpecialPatientQueryVO queryVO, Page<SpecialPatient> pageInfo) {
        Page<SpecialPatient> resultPage = baseMapper.selectPage(pageInfo, new LambdaQueryWrapper<SpecialPatient>()
                .like(StrUtil.isNotEmpty(queryVO.getPatientName()), SpecialPatient::getPatientName, queryVO.getPatientName())
                .in(CollUtil.isNotEmpty(queryVO.getPatientTypeList()), SpecialPatient::getPatientType, queryVO.getPatientTypeList())
                .eq(StrUtil.isNotEmpty(queryVO.getGender()), SpecialPatient::getGender, queryVO.getGender())
                .like(StrUtil.isNotEmpty(queryVO.getDocumentNo()), SpecialPatient::getDocumentNo, queryVO.getDocumentNo())
                .eq(SpecialPatient::getHospitalId, LoginContext.getHospitalId())
                .orderByDesc(SpecialPatient::getLastUpdatedDate)
        );

        // 处理年龄
        for (SpecialPatient record : resultPage.getRecords()) {
            int age = DateUtil.age(record.getBirthDate(), DateUtil.date());
            record.setAge(age);
        }

        DictUtils.translateDict(resultPage.getRecords());
        return resultPage.getRecords();
    }

    /**
     * 特殊患者 导出接口 导出模板
     * @param response 响应
     * @param queryVO  查询条件
     */
    public void export(HttpServletResponse response, SpecialPatientQueryVO queryVO) {
        if (null == queryVO.getIsModel()) {
            throw new BaseException("传参错误");
        }

        List<ExcelExport> exports = new ArrayList<>();
        // 导出模板
        if (queryVO.getIsModel()) {
            // 特殊患者信息
            exports.add(buildSpecialPatientModel());
            // 患者类型字典
            ExcelExport mqsPatientType = buildDictSheet(cdmpDicInterface.queryDicDataList("MQS_PATIENT_TYPE"));
            mqsPatientType.setSheetName("患者类型字典");
            exports.add(mqsPatientType);
            // 证件类型字典
            ExcelExport dicCardType = buildDictSheet(cdmpDicInterface.queryDicDataList("DIC_CARD_TYPE"));
            dicCardType.setSheetName("证件类型字典");
            exports.add(dicCardType);
            // 性别字典
            ExcelExport dicGender = buildDictSheet(cdmpDicInterface.queryDicDataList("DIC_GENDER"));
            dicGender.setSheetName("性别字典");
            exports.add(dicGender);
        }
        // 导出列表数据
        else {
            List<List<String>> heads = new ArrayList<>();
            heads.add(CollUtil.list(true,
                    "PATIENT_NAME", "PATIENT_TYPE", "PATIENT_TYPE_NAME",
                    "DOCUMENT_TYPE","DOCUMENT_TYPE_NAME", "DOCUMENT_NO",
                    "BIRTH_DATE", "AGE",
                    "GENDER", "GENDER_NAME", "DESCRIPTION"));
            heads.add(CollUtil.list(true,
                    "患者姓名（必填）", "患者类型字典编码（必填:多个编码之间用半角逗号隔开）", "患者类型字典名称（非必填）",
                    "证件类型字典编码（必填）", "证件类型字典名称（非必填）", "证件号码（必填）",
                    "出生日期(yyyy-MM-dd 必填)", "年龄(非必填)",
                    "性别字典编码（非必填）", "性别字典名称（非必填）", "描述（非必填）"));

            List<List<Object>> datas = new ArrayList<>();
            List<SpecialPatient> specialPatientList = baseMapper.selectList(new LambdaQueryWrapper<SpecialPatient>()
                    .like(StrUtil.isNotEmpty(queryVO.getPatientName()), SpecialPatient::getPatientName, queryVO.getPatientName())
                    .in(CollUtil.isNotEmpty(queryVO.getPatientTypeList()), SpecialPatient::getPatientType, queryVO.getPatientTypeList())
                    .eq(StrUtil.isNotEmpty(queryVO.getGender()), SpecialPatient::getGender, queryVO.getGender())
                    .like(StrUtil.isNotEmpty(queryVO.getDocumentNo()), SpecialPatient::getDocumentNo, queryVO.getDocumentNo())
                    .eq(SpecialPatient::getHospitalId, LoginContext.getHospitalId())
                    .orderByDesc(SpecialPatient::getLastUpdatedDate)
            );
            DictUtils.translateDict(specialPatientList);
            specialPatientList.forEach(i -> datas.add(CollUtil.list(true,
                    i.getPatientName(), i.getPatientType(), i.getPatientTypeName(),
                    i.getDocumentType(), i.getDocumentTypeName(), i.getDocumentNo(),
                    DateUtil.format(i.getBirthDate(), "yyyy-MM-dd"), DateUtil.age(i.getBirthDate(), DateUtil.date()),
                    i.getGender(), i.getGenderName(), i.getDescription())));

            exports.add(ExcelExport.builder().sheetName("特殊患者信息").heads(heads).datas(datas).build());
        }

        StringBuilder fileName = new StringBuilder().append("运营管理-特殊患者维护");
        MqsUtils.buildExportFileNameSuffix(fileName, null, null);
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);

    }

    private ExcelExport buildDictSheet(List<Map<String, Object>> mqsPatientType) {
        List<List<String>> heads = new ArrayList<>();
        heads.add(CollUtil.list(true, "DATA_CODE", "DATA_NAME", "DATA_DESC"));
        heads.add(CollUtil.list(true, "值编码", "值名称", "值描述"));

        List<List<Object>> datas = new ArrayList<>();
        for (Map<String, Object> map : mqsPatientType) {
            datas.add(CollUtil.list(true, map.get("DATA_CODE"), map.get("DATA_NAME")));
        }

        return ExcelExport.builder().heads(heads).datas(datas).build();
    }

    private ExcelExport buildSpecialPatientModel() {
        List<List<String>> heads = new ArrayList<>();
        // 表头
        heads.add(CollUtil.list(true,
                "PATIENT_NAME", "PATIENT_TYPE", "PATIENT_TYPE_NAME",
                "DOCUMENT_TYPE","DOCUMENT_TYPE_NAME", "DOCUMENT_NO",
                "BIRTH_DATE", "AGE",
                "GENDER", "GENDER_NAME", "DESCRIPTION"));
        heads.add(CollUtil.list(true,
                "患者姓名（必填）", "患者类型字典编码（必填:多个编码之间用半角逗号隔开）", "患者类型字典名称（非必填）",
                "证件类型字典编码（必填）", "证件类型字典名称（非必填）", "证件号码（必填）",
                "出生日期(yyyy-MM-dd 必填)", "年龄(非必填)",
                "性别字典编码（非必填）", "性别字典名称（非必填）", "描述（非必填）"));

        return ExcelExport.builder().sheetName("特殊患者信息导入模板").heads(heads).datas(new ArrayList<>()).build();
    }



    /**
     * 特殊患者 数据导入
     * @param file excel文件
     */
    public void importExcel(MultipartFile file) {
        ExcelData excelData = BigExcelUtil.parseExcel(file);
        SheetData sheetData = excelData.getSheets().get(0);

        // excel内容校验
        List<String> errorMsg = checkExcelContent(sheetData);
        if (CollUtil.isNotEmpty(errorMsg)) {
            throw new BaseException(StrUtil.format("表数据存在异常:{}", StrUtil.join(",", errorMsg)));
        }



        // 数据保存到数据库中
        List<SpecialPatient> saveData = new ArrayList<>();
        for (int i = 2; i < sheetData.getLines().size(); i++) {
            List<Object> line = sheetData.getLines().get(i);

            // 字典值去重
            String patientType = BigExcelUtil.getItemStr(line, 1);
            String documentType = BigExcelUtil.getItemStr(line, 3);
            String gender = BigExcelUtil.getItemStr(line, 8);
            patientType = buildNoRepeatStr(patientType);
            documentType = buildNoRepeatStr(documentType);
            gender = buildNoRepeatStr(gender);

            saveData.add(SpecialPatient.builder()
                    .id(IdWorker.getId())
                    .patientName(BigExcelUtil.getItemStr(line, 0))
                    .patientType(patientType)
                    .documentType(documentType)
                    .documentNo(BigExcelUtil.getItemStr(line, 5))
                    .birthDate(DateUtil.parse(BigExcelUtil.getItemStr(line, 6), "yyyy-MM-dd"))
                    .gender(gender)
                    .description(BigExcelUtil.getItemStr(line, 10))
                    .hospitalId(LoginContext.getHospitalId())
                    .build());
        }

        // 保存前先删除重复数据
        List<String> toolArray = new ArrayList<>();
        for (SpecialPatient saveDatum : saveData) {
            String s = saveDatum.getDocumentType() + saveDatum.getDocumentNo();
            // 待保存数据 -> 唯一标识集合
            toolArray.add(s);
        }

        List<SpecialPatient> dbData = this.list();
        List<Long> removeIds = new ArrayList<>();
        for (SpecialPatient data : dbData) {
            // 数据库数据 -> 唯一标识集合
            String s = data.getDocumentType() + data.getDocumentNo();
            if (toolArray.contains(s)) {
                removeIds.add(data.getId());
            }
        }
        this.removeByIds(removeIds);

        this.saveBatch(saveData);
    }

    private String buildNoRepeatStr(String repeatStr) {
        List<String> list = StrUtil.split(repeatStr, ",");
        List<String> noRepeatList = new ArrayList<>();
        for (String s : list) {
            if (!noRepeatList.contains(s)) {
                noRepeatList.add(s);
            }
        }
        return StrUtil.join(",", noRepeatList);
    }

    /**
     * 校验表中数据
     * @param sheetData sheet页信息
     * @return errorMsg
     */
    private List<String> checkExcelContent(SheetData sheetData) {
        List<String> errorMsg = new ArrayList<>();
        List<List<Object>> lines = sheetData.getLines();

        // 校验表头
        String checkHead = BigExcelUtil.checkHead(sheetData, CollUtil.list(true,
                "PATIENT_NAME", "PATIENT_TYPE", "PATIENT_TYPE_NAME",
                "DOCUMENT_TYPE","DOCUMENT_TYPE_NAME", "DOCUMENT_NO",
                "BIRTH_DATE", "AGE",
                "GENDER", "GENDER_NAME", "DESCRIPTION"));
        if (StrUtil.isNotEmpty(checkHead)) {
            errorMsg.add(checkHead);
        }

        List<String> toolList = new ArrayList<>();
        // 逐行判断
        for (int i = 2; i < lines.size(); i++) {
            // 表中第i+1行数据
            List<Object> line = lines.get(i);

            // 仅做证件号码重复判断
            String documentNo = BigExcelUtil.getItemStr(line, 5);
            if (toolList.contains(documentNo)) {
                errorMsg.add("第" + (i + 1) + "行数据证件号码重复");
            }
            toolList.add(documentNo);
        }


        return errorMsg;
    }

}
