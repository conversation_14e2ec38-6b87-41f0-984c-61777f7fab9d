package com.crhms.cloud.mqs.basic.controller;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.basic.common.ClinicParams;
import com.crhms.cloud.mqs.basic.common.MqsResult;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDiag;
import com.crhms.cloud.mqs.basic.service.BaseMedicalService;
import com.crhms.cloud.mqs.basic.service.BaseMedicalServiceExport;
import com.crhms.cloud.mqs.basic.vo.BaseCaseQueryVO;
import com.crhms.cloud.mqs.basic.vo.BaseDetailQueryVO;
import com.crhms.cloud.mqs.basic.vo.ComprePopupQueryVO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 多场景审核 数据查询控制层
 *
 * <AUTHOR>
 * @since 2023-01-17 09:48:16
 */
@Slf4j
@RestController
@RequestMapping("api/mqs/medicalCase")
public class BaseMedicalController {
    /**
     * 服务对象
     */
    @Autowired
    private BaseMedicalService baseMedicalService;
    @Autowired
    private BaseMedicalServiceExport baseMedicalServiceExport;
    /**
     * 分页查询
     *
     * @param queryVO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryCaseByPage")
    public ResponseEntity<List<BaseMedicalCase>> queryByPage(@RequestBody BaseCaseQueryVO queryVO) {
        Page page = PageUtil.getPage(queryVO.getPage(), queryVO.getPageSize());
        return new ResponseEntity(this.baseMedicalService.queryCaseListByPage(queryVO,page),PageUtil.getTotalHeader(page), HttpStatus.OK);
    }


    /**
     * 查询历史单据
     *
     * @param no      主单号
     * @param batchNo 批次号
     * @return 查询结果
     */
    @GetMapping("/queryCaseHis")
    public ResponseEntity<List<BaseMedicalCase>> queryHis(@RequestParam(value = "auditScenario") String auditScenario,
                                                          @RequestParam(value = "no") String no,
                                                          @RequestParam(value = "batchNo") String batchNo) {
        return ResponseEntity.ok(this.baseMedicalService.queryCaseHisList(auditScenario, no, batchNo, LoginContext.getHospitalId()));
    }

    /**
     * 查询费用明细
     *
     * @return
     */
    @PostMapping("/queryDetail")
    public ResponseEntity<List<BaseMedicalDetail>> queryDetail(@RequestBody BaseDetailQueryVO baseDetailQueryVO) {
        return new ResponseEntity(this.baseMedicalService.queryDetail(baseDetailQueryVO), HttpStatus.OK);
    }

    /**
     * 查询所有费用明细
     *
     * @return
     */
    @PostMapping("/queryAllDetail")
    public ResponseEntity<List<BaseMedicalDetail>> queryAllDetail(@RequestBody BaseDetailQueryVO baseDetailQueryVO) {
        Page page = PageUtil.getPage(baseDetailQueryVO.getPage(), baseDetailQueryVO.getPageSize());
        return new ResponseEntity(this.baseMedicalService.queryAllDetail(baseDetailQueryVO,page),PageUtil.getTotalHeader(page), HttpStatus.OK);
    }

    /**
     * 查询单据所有诊断
     *
     * @return
     */
    @GetMapping("/queryDiagnosisList")
    public ResponseEntity<List<BaseMedicalDiag>> queryDiagnosisList(@RequestParam(value = "admissionNo") String admissionNo,
                                                                    @RequestParam(value = "no" ,required = false) String no,
                                                                    @RequestParam(value = "hospitalId") String hospitalId) {

        return new ResponseEntity(this.baseMedicalService.queryDiagnosisList(admissionNo,no,hospitalId), HttpStatus.OK);
    }


    /**
     * 导出excel
     *
     * @param response 响应
     * @param queryVO  筛选条件
     */
    @PostMapping("/export")
    public void exportExcel(HttpServletResponse response, @RequestBody BaseCaseQueryVO queryVO) {
        baseMedicalServiceExport.exportExcel(response, queryVO);
    }


    /**
     * 导出费用明细
     *
     * @param response          响应
     * @param baseDetailQueryVO 筛选条件
     */
    @PostMapping("/exportDetail")
    public void exportDetail(HttpServletResponse response, @RequestBody BaseDetailQueryVO baseDetailQueryVO) {
        baseMedicalServiceExport.exportDetail(response, baseDetailQueryVO);
    }


    @GetMapping("/queryMedicalAuditRules")
    public MqsResult queryMedicalAuditRules(@RequestParam(value = "admission_no") String admissionNo,
                                            @RequestParam(value = "batch", required = false) String batch,
                                            @RequestParam(value = "detail_no") String detailNo,
                                            @RequestParam(value = "audit_scenario") String auditScenario,
                                            @RequestParam(value = "hospital_id",required = false) String hospitalId,
                                            @RequestParam(value = "isHis",required = false) String isHis,
                                            @RequestParam(value = "ruleOrigin",required = false) String ruleOrigin) {
        return MqsResult.OK(baseMedicalService.queryMedicalAuditRules4(detailNo, admissionNo, isHis, batch, auditScenario,ruleOrigin, hospitalId));
    }


    @PostMapping("/exportRelatedRecords")
    public void exportRelatedRecords(@RequestBody ClinicParams clinicParams, HttpServletResponse response){
        log.info(JSONUtil.toJsonStr(clinicParams));
        ComprePopupQueryVO comprePopupQueryVO = new ComprePopupQueryVO();
        comprePopupQueryVO.setAdmissionNo(clinicParams.getAdmission_no());
        comprePopupQueryVO.setHospitalId(clinicParams.getHospital_id());
        comprePopupQueryVO.setAuditScenario(clinicParams.getAudit_scenario());
        comprePopupQueryVO.setBatchNo(clinicParams.getBatch());
        comprePopupQueryVO.setIsIncrement(clinicParams.getIsIncrement());
        comprePopupQueryVO.setClinicalStatus(clinicParams.getClinicalStatus());
        comprePopupQueryVO.setViolationFlag("1");
        comprePopupQueryVO.setAuditSource(clinicParams.getAuditSource());
        comprePopupQueryVO.setSelfExpense(clinicParams.getSelfExpense());
        comprePopupQueryVO.setItemName(clinicParams.getItemName());
        comprePopupQueryVO.setItemTypeCode(clinicParams.getItemTypeCode());
        comprePopupQueryVO.setCategoryName(clinicParams.getCategoryName());
        comprePopupQueryVO.setOutpatientMedication(clinicParams.getOutpatientMedication());
        comprePopupQueryVO.setRuleTypes(clinicParams.getRuleTypes());
        comprePopupQueryVO.setIsHis(clinicParams.getIsHis());
//        comprePopupQueryVO.setBatchNo(null);
        if(StringUtil.isNotBlank(clinicParams.getDetail_no())){
            List<String> stringList = new ArrayList<>();
            stringList.add(clinicParams.getDetail_no());
            comprePopupQueryVO.setDetailNos(stringList);
        }

        log.info(JSONUtil.toJsonStr(comprePopupQueryVO));
        baseMedicalService.exportRelatedRecords4(comprePopupQueryVO, clinicParams.getDetail_no(), clinicParams.getAdmission_no(), null,clinicParams.getAudit_scenario(),clinicParams.getRuleOrigin(),clinicParams.getHospital_id(),response);
    }
}

