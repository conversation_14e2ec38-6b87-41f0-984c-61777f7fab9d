package com.crhms.cloud.mqs.mqs_kbm.controller;

import com.crhms.cloud.mqs.mqs_kbm.service.CgGuideService;
import com.crhms.cloud.mqs.mqs_kbm.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 知识库管理-临床指南
 *
 * @author: wangxingang
 * @date: 2023.02.07
 */
@RestController
@RequestMapping("/api/mqs/kbm/cgGuide")
public class CgGuideController {

    /**
     * 临床指南service
     */
    @Autowired
    private CgGuideService service;

    /**
     * 查询疾病列表
     *
     * @param diseaseName 疾病名称
     */
    @GetMapping("/list")
    public ResponseEntity<List<Map<String, String>>> queryDiseaseList(@RequestParam(value = "diseaseName", required = false) String diseaseName) {
        List<Map<String, String>> diseaseList = service.queryDiseaseList(diseaseName);
        return new ResponseEntity<>(diseaseList, HttpStatus.OK);
    }

    /**
     * "概述"页面信息
     *
     * @param diseaseCode 疾病编码
     */
    @GetMapping("/summary")
    public ResponseEntity<CgGuideSummary> querySummary(@RequestParam(value = "diseaseCode") String diseaseCode) {
        CgGuideSummary result = service.querySummary(diseaseCode);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * "检查"页面信息
     *
     * @param diseaseCode 疾病编码
     */
    @GetMapping("/check")
    public ResponseEntity<CgGuideCheck> queryCheck(@RequestParam(value = "diseaseCode") String diseaseCode) {
        CgGuideCheck result = service.queryCheck(diseaseCode);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * "一般治疗"页面信息
     *
     * @param diseaseCode 疾病编码
     */
    @GetMapping("/general")
    public ResponseEntity<CgGuideGeneral> queryGeneral(@RequestParam(value = "diseaseCode") String diseaseCode) {
        CgGuideGeneral result = service.queryGeneral(diseaseCode);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * "手术治疗"页面信息
     *
     * @param diseaseCode 疾病编码
     */
    @GetMapping("/operation")
    public ResponseEntity<CgGuideOperation> queryOperation(@RequestParam(value = "diseaseCode") String diseaseCode) {
        CgGuideOperation result = service.queryOperation(diseaseCode);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * "药品"页面信息
     *
     * @param diseaseCode 疾病编码
     */
    @GetMapping("/drug")
    public ResponseEntity<CgGuideDrug> queryDrug(@RequestParam(value = "diseaseCode") String diseaseCode) {
        CgGuideDrug result = service.queryDrug(diseaseCode);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
