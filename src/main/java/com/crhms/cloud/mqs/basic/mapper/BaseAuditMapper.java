package com.crhms.cloud.mqs.basic.mapper;

import com.crhms.cloud.mqs.basic.domain.BaseAudit;
import com.crhms.cloud.mqs.basic.domain.BaseAuditReason;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-01-18 15:40:47
 */

@Mapper
public interface BaseAuditMapper {

    /**
     * 删除审核结果
     * @param table
     * @param no
     * @param hospitalId
     * @return
     */
    int delAudit(@Param("table") String table, @Param("detailNos") List<String> detailNos,@Param("no") String no,@Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);


    /**
     * 删除反馈原因
     * @param table
     * @param no
     * @param hospitalId
     * @return
     */
    int delAuditReason(@Param("table") String table, @Param("detailNos") List<String> detailNos,@Param("no") String no,@Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);


    /**
     * 批量保存反馈原因
     *
     * @param baseAuditReasonList
     * @return
     */
    void batchInsertAuditReason(@Param("table") String table,@Param("entities") List<BaseAuditReason> baseAuditReasonList);


    /**
     * 批量保存审核结果历史
     * @param table
     * @param baseAuditList
     */
    void batchInsertAuditHis(@Param("table") String table, @Param("entities") List<BaseAudit> baseAuditList);

    /**
     * 批量保存至审核结果表
     * @param table
     * @param baseAuditList
     */
    void batchInsertAudit(@Param("table") String table, @Param("entities") List<BaseAudit> baseAuditList);


    /**
     * 删除流水号数据
     * @param table
     * @param admissionNo
     * @param hospitalId
     */
    void deleteAuditByAdNo(@Param("table") String table, @Param("detailNos") List<String> detailNos,@Param("nos") List<String> nos,@Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    /**
     * 删除流水号下数据
     * @param table
     * @param admissionNo
     * @param hospitalId
     */
    void deleteReasonByAdNo(@Param("table") String table, @Param("detailNos") List<String> detailNos,@Param("nos") List<String> nos,@Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    List<BaseAudit> queryAuditHisByBatchNo(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("batchNo") String batchNo, @Param("hospitalId") String hospitalId);

    List<BaseMedicalCase> querySavedCase(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("batch") String batch, @Param("hospitalId") String hospitalId);

    List<Map> getClaimRes(@Param("table") String tableName, @Param("no") String no, @Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    /**
     * 查询单据上的违规项
     * @param tableName
     * @param nos
     * @param hospitalId
     * @return
     */
    List<BaseAudit> selectCaseAudit(@Param("table") String tableName, @Param("nos") Collection<String> nos, @Param("detailNos") Collection<String> detailNos, @Param("hospitalId") String hospitalId);

    List<BaseAudit> selectCaseAuditHis(@Param("table") String tableName, @Param("no") String no, @Param("hospitalId") String hospitalId);

    void insertAuditReturnLog(@Param("admissionNo") String admissionNo, @Param("totalCosts") BigDecimal totalCosts,@Param("personnelTypeId") String  personnelTypeId,@Param("auditScenario") String auditScenario, @Param("hospitalId") String hospitalId);

    List<BaseAudit> selectAudit(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("nos") List<String> nos, @Param("hospitalId") String hospitalId);
}

