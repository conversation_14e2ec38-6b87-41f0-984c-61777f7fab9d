package com.crhms.cloud.mqs.mqs_kbm.mapper;

import com.crhms.cloud.mqs.mqs_kbm.vo.*;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 知识库管理-临床指南mapper
 *
 * @author: wangxingang
 * @date: 2023.02.07
 */
@Mapper
public interface CgGuideMapper {

    /**
     * 查询疾病列表
     * @param diseaseName 疾病名称
     */
    @MapKey("")
    List<Map<String, String>> selectDiseaseList(@Param("diseaseName") String diseaseName);

    /**
     * 查询概述页面信息
     * @param diseaseCode 疾病编码
     */
    CgGuideSummary selectSummary(@Param("diseaseCode") String diseaseCode);

    /**
     * 查询项目列表 - 检查
     * @param diseaseCode 疾病编码
     */
    List<CgGuideCheckItem> selectCheckItemList(String diseaseCode);

    /**
     * 查询项目列表 - 一般性治疗
     * @param diseaseCode 疾病编码
     */
    List<CgGuideGeneralItem> selectGeneralItemList(String diseaseCode);

    /**
     * 查询项目列表 - 手术治疗
     * @param diseaseCode 疾病编码
     */
    List<CgGuideOperationItem> selectOperationItemList(String diseaseCode);

    /**
     * 查询项目列表 - 药品
     * @param diseaseCode 疾病编码
     */
    List<CgGuideDrugItem> selectDrugItemList(String diseaseCode);
}
