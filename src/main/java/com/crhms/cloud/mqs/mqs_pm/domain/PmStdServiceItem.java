package com.crhms.cloud.mqs.mqs_pm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 物价管理-市级标准物价-服务项目 实体类
 *
 * @author: 王新刚
 * @date: 2023/01/30
 */


@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("mqs_pm_std_service_item")
public class PmStdServiceItem extends BaseDomain {
    @TableId
    private Long id;

    /**
     * 生效时间
     */
    @TableField
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private Date effectTime;

    /**
     * 财务分类
     */
    @TableField
    @DictTranslate(dictCode = "DIC_FEE_TYPE")
    private String financialClassification;

    /**
     * 项目编码
     */
    @TableField
    private String projectCode;

    /**
     * 项目名称
     */
    @TableField
    private String projectName;

    /**
     * 项目内涵
     */
    @TableField
    private String projectConnotation;

    /**
     * 除外内容
     */
    @TableField
    private String otherContent;

    /**
     * 计价单位
     */
    @TableField
    private String unit;

    /**
     * 说明
     */
    @TableField
    private String description;

    /**
     * 价格
     */
    @TableField
    private BigDecimal price;

}
