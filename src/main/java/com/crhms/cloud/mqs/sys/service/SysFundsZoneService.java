package com.crhms.cloud.mqs.sys.service;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.sys.domain.SysFundsZone;
import com.crhms.cloud.mqs.sys.mapper.SysFundsZoneMapper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Future;

/**
 * 运营管理-基金管理-院区(SysFundsZone)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-20 16:56:42
 */

@Service("sysFundsZoneService")
public class SysFundsZoneService extends ServiceImpl< SysFundsZoneMapper, SysFundsZone> {
    @Resource
    private SysFundsZoneMapper sysFundsZoneMapper;

    @Async
    @Transactional(rollbackFor = Exception.class)
    public Future updateBatchById(List<SysFundsZone> entityList, int batchSize) {
        List<List<SysFundsZone>> partition = ListUtil.partition(entityList, batchSize);
        for (List<SysFundsZone> zoneList : partition) {
            this.baseMapper.batchUpdate(zoneList);
        }
        return new AsyncResult(true);
    }

}
