package com.crhms.cloud.mqs.basic.vo;

import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDiag;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalOrders;
import com.googlecode.jmapper.annotations.JGlobalMap;
import com.googlecode.jmapper.annotations.JMap;
import lombok.Data;

import java.util.List;

@Data
@JGlobalMap
public class BaseMedicalParam {
    /**
     * 主单
     */
    public BaseMedicalCase medical_case;
    /**
     * 缴费明细
     */
    public List<BaseMedicalDetail> fee_list;
    /**
     * 医嘱明细
     */
    public List<BaseMedicalOrders> orders_list;
    /**
     * 是否增量标识
     */
    public Boolean is_increment;

    /**
     * 手术记录
     */
    public List<HisOperationMainVo> operation_list;

    /**
     * 诊断记录
     */
    public List<BaseMedicalDiag> diagnosis_list;

    /**
     * 未交费记录
     */
    public List<UnpaidRecordsVo> unpaid_list;

    /**
     * 未就诊记录
     */
    public List<UnVisitVo> unvisit_list;
}
