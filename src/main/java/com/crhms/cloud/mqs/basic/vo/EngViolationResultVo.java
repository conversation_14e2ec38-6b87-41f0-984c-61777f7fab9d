package com.crhms.cloud.mqs.basic.vo;

import lombok.Data;

import java.util.List;

/**
 * 引擎审核返回结果类
 */
@Data
public class EngViolationResultVo {
    //规则编码
    private String rule_no;
    //参保人ID
    private String rule_name;
    //总花销时间
    private String result_type;
    //结果类型名称
    private String result_type_name;
    //违规费用明细id
    private String detail_id;
    //医嘱明细id
    private String order_id;
    //项目类型编码
    private String item_type_code;
    //项目类型名称
    private String item_type_name;
    //数量
    private String numbers;
    //费用
    private String costs;
    //完整提示
    private String full_tip;
    //违规理由
    private String reason;
    //分组编码
    private String group_code;
    //违规相关明细
    private String related;
    //费用明细编码
    private String item_id;
    //费用明细名称
    private String item_name;
    //规则设置提示
    private String tipsCode4Hospital;
    //项目时间
    private String item_time;

    private String usageUnit;

    private String specification;

    private String display_item_id;

    private String display_item_name;

    private String admission_no;

    //违规比例
    private String ViolationRatio;

    //违规计费标记，0未违规 1违规，默认1
    private String violation_type;

    //1.院内 2 医保
    private String rule_origin;

    //医保审核结果唯一id 用于反馈
    private String jr_id;

}
