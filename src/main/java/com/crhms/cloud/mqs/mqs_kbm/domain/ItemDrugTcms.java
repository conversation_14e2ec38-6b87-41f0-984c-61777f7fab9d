package com.crhms.cloud.mqs.mqs_kbm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 知识库管理-医保项目目录-国家医保药品分类与代码(中药饮片)
 *
 * @author: wangxingang
 * @date: 2023/02/03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("mqs_kbm_item_drug_tcms")
public class ItemDrugTcms {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 中药饮片代码
     */
    @TableField
    private String tcmsCode;

    /**
     * 中药饮片名称
     */
    @TableField
    private String tcmsName;

    /**
     * 药材名称
     */
    @TableField
    private String medMaterialName;

    /**
     * 炮制方法
     */
    @TableField
    private String processMethod;

    /**
     * 功效分类
     */
    @TableField
    private String efficacyClassify;

    /**
     * 药材科(族)来源
     */
    @TableField
    private String medFamily;

    /**
     * 药材种来源
     */
    @TableField
    private String medSpecies;

    /**
     * 药用部位
     */
    @TableField
    private String medPart;

    /**
     * 性味与归经
     */
    @TableField
    private String medFlavor;

    /**
     * 功能与主治
     */
    @TableField
    private String funcIndicate;

    /**
     * 用法与用量
     */
    @TableField
    private String methodDosage;

    /**
     * 医保支付政策
     */
    @TableField
    private String ybPayPolicy;
}
