package com.crhms.cloud.mqs.sys.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 特殊患者表实体类
 *
 * @author: 王新刚
 * @date: 2023/03/24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("mqs_special_patient")
public class SpecialPatient extends BaseDomain {

    @TableId
    private Long id;

    /**
     * 患者姓名
     */
    @TableField
    private String patientName;

    /**
     * 患者类型
     */
    @TableField
    @DictTranslate(dictCode = "MQS_PATIENT_TYPE", dictText = "patientTypeName")
    private String patientType;

    /**
     * 患者类型
     */
    @TableField(exist = false)
    private String patientTypeName;

    /**
     * 证件类型
     */
    @TableField
    @DictTranslate(dictCode = "DIC_CARD_TYPE", dictText = "documentTypeName")
    private String documentType;

    /**
     * 证件类型
     */
    @TableField(exist = false)
    private String documentTypeName;

    /**
     * 证件号码
     */
    @TableField
    private String documentNo;

    /**
     * 出生日期
     */
    @TableField
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GTM+8")
    private Date birthDate;

    /**
     * 年龄
     */
    @TableField(exist = false)
    private Integer age;

    /**
     * 性别
     */
    @TableField
    @DictTranslate(dictCode = "DIC_GENDER", dictText = "genderName")
    private String gender;

    /**
     * 性别
     */
    @TableField(exist = false)
    private String genderName;

    /**
     * 描述
     */
    @TableField
    private String description;

    /**
     * 院区id
     */
    @TableField
    private String hospitalId;

    /**
     * 患者类型列表
     */
    @TableField(exist = false)
    private List<String> patientTypeList;

}
