package com.crhms.cloud.mqs.mqs_ais.vo;

import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.Data;

@Data
public class CsDiseaseDto {

    private String detailNo;
    private String no;
    private String patientId;
    private String patientName;
    @DictTranslate(dictCode = "DIC_GENDER")
    private String patientGender;
    private String ptype;
    private String itemTypeCode;
    private String itemTypeName;
    private String itemDate;
    private String itemName;
    @DictTranslate(dictCode = "LOCAL_YN_CODE")
    private String violationFlag;
    private String csDiseaseCode;
    private String csDiseaseName;
    private String billDate;
    private String admissionNo;
    @DictTranslate(dictCode = "DIC_INSUR_TYPE")
    private String benefitTypeId;
    private String applyDeptName;
    private String applyDoctorName;
    private String price;
    private String numbers;
    private String costs;
    private String bmiOverallAmount;
}
