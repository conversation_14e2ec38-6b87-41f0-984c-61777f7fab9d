package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.TcmDisease;
import com.crhms.cloud.mqs.mqs_kbm.mapper.TcmDiseaseMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 知识库管理-中医诊断-中医疾病分类与代码 service
 *
 * @author: wangxingang
 * @date: 2023.02.03
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class TcmDiseaseService extends ServiceImpl<TcmDiseaseMapper, TcmDisease> {
    /**
     * 查询中医疾病分类与代码
     *
     * @param pageInfo    分页信息
     * @param diseaseCode 疾病名编码
     * @param diseaseName 疾病名
     */
    public List<TcmDisease> queryList(Page<TcmDisease> pageInfo, String diseaseCode, String diseaseName) {
        return baseMapper.selectPage(pageInfo, new LambdaQueryWrapper<TcmDisease>()
                .like(StrUtil.isNotEmpty(diseaseCode), TcmDisease::getDiseaseCode, diseaseCode)
                .like(StrUtil.isNotEmpty(diseaseName), TcmDisease::getDiseaseName, diseaseName)
        ).getRecords();
    }
}
