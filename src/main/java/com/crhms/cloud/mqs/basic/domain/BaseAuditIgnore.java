package com.crhms.cloud.mqs.basic.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

/**
 * 审核结果表(BaseAudit)实体类
 *
 * <AUTHOR>
 * @since 2023-01-18 15:53:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class BaseAuditIgnore {

    private String admissionNo;

    private String no;

    private String detailNo;

    private String itemId;

    private String itemName;

    private String hospitalId;

    private String enable;

}
