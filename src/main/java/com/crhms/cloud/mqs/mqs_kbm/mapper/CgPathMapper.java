package com.crhms.cloud.mqs.mqs_kbm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.mqs_kbm.domain.CgPath;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 临床指南-临床路径
 *
 * @author: wangxing<PERSON>
 * @date: 2023.02.08
 */

@Mapper
public interface CgPathMapper extends BaseMapper<CgPath> {
    /**
     * 查询适用科室下拉列表
     *
     * @return 适用科室下拉列表
     */
    List<String> queryDeptNameList();


    /**
     * 查询发布时间下拉列表
     *
     * @return 发布时间下拉列表
     */
    List<String> queryReleaseTimeList();
}
