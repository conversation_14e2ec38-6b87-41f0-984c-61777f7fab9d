package com.crhms.cloud.mqs.basic.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalOrders;
import com.crhms.cloud.mqs.basic.mapper.BaseMedicalOrdersMapper;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 医嘱明细(BaseMedicalOrders)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-10 13:47:54
 */
@Service
public class BaseMedicalOrdersService extends ServiceImpl<BaseMedicalOrdersMapper, BaseMedicalOrders> {

}
