<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.sys.mapper.SysRuleLevelMapper">

    <select id="queryThirdLevelList" resultType="com.crhms.cloud.mqs.sys.domain.SysRuleLevelThird">
       SELECT
        a.id,
        a.platform,
        a.`code`,
        a.default_name,
        a.sys_code,
        b.`name`
    FROM
        mqs_sys_rule_level_third a
        LEFT JOIN mqs_sys_rule_level b ON a.sys_code = b.`code` AND a.hospital_id = b.hospital_id
        WHERE a.hospital_id = #{hospitalId}
        <if test="platform != null and platform != ''">
            a.platform = #{platform}
        </if>

    </select>

</mapper>

