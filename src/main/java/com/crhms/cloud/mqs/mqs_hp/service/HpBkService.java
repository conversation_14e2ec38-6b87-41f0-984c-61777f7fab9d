package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.service.BaseAuditService;
import com.crhms.cloud.mqs.basic.vo.BaseMedicalParam;
import com.crhms.cloud.mqs.basic.vo.HisBaseAuditDto;
import com.crhms.cloud.mqs.basic.vo.EngAuditResultVo;
import com.crhms.cloud.mqs.mqs_hp.domain.HpBk;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpBkMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 住院审核-每日记账审核(HpBk)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 09:48:16
 */
@Service("hpBkService")
public class HpBkService extends ServiceImpl<HpBkMapper, HpBk> implements BaseAuditService {



    @Override
    public void beforeAudit(String batch, HisBaseAuditDto hisBaseAuditDto) {

    }


    @Override
    public void afterAudit(String batch, EngAuditResultVo result, HisBaseAuditDto hisBaseAuditDto, List<BaseMedicalParam> data) {

    }
}
