package com.crhms.cloud.mqs.mqs_hp.domain;

import java.util.Date;
import java.io.Serializable;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 住院审核-结算审核-表单-历史(HpSettleHis)实体类
 *
 * <AUTHOR>
 * @since 2023-02-09 13:16:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_hp_settle_his")
public class HpSettleHis extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -15766687670915217L;
    
        public static final String FIELD_BATCH_NO="batch_no";
    public static final String FIELD_NO="no";
    public static final String FIELD_ADMISSION_NO="admission_no";
    public static final String FIELD_BMI_NUMBER="bmi_number";
    public static final String FIELD_DRG_CODE="drg_code";
    public static final String FIELD_DIP_CODE="dip_code";
    public static final String FIELD_BILL_DATE="bill_date";
    public static final String FIELD_HOSPITAL_LEVEL="hospital_level";
    public static final String FIELD_HOSPITAL_TYPE="hospital_type";
    public static final String FIELD_CLAIM_TYPE_ID="claim_type_id";
    public static final String FIELD_BENEFIT_TYPE_ID="benefit_type_id";
    public static final String FIELD_SELF_EXPENSE="self_expense";
    public static final String FIELD_BMI_CODE="bmi_code";
    public static final String FIELD_BMI_NAME="bmi_name";
    public static final String FIELD_IN_DIAGNOSIS_CODE="in_diagnosis_code";
    public static final String FIELD_IN_DIAGNOSIS_NAME="in_diagnosis_name";
    public static final String FIELD_OUT_DIAGNOSIS_CODE="out_diagnosis_code";
    public static final String FIELD_OUT_DIAGNOSIS_NAME="out_diagnosis_name";
    public static final String FIELD_ADMISSION_DATE="admission_date";
    public static final String FIELD_DISCHARGE_DATE="discharge_date";
    public static final String FIELD_PATIENT_ID="patient_id";
    public static final String FIELD_PATIENT_NAME="patient_name";
    public static final String FIELD_PATIENT_GENDER="patient_gender";
    public static final String FIELD_PATIENT_BIRTHDAY="patient_birthday";
    public static final String FIELD_IS_BKL="is_bkl";
    public static final String FIELD_IS_PREGNANT="is_pregnant";
    public static final String FIELD_IS_LACTATING="is_lactating";
    public static final String FIELD_TOTAL_AMOUNT="total_amount";
    public static final String FIELD_PERSONNEL_TYPE="personnel_type";
    public static final String FIELD_TREATMENT_TYPE="treatment_type";
    public static final String FIELD_BMI_CONVERED_AMOUNT="bmi_convered_amount";
    public static final String FIELD_BMI_OVERALL_AMOUNT="bmi_overall_amount";
    public static final String FIELD_UNUSUAL_FLAG="unusual_flag";
    public static final String FIELD_BENEFIT_GROUP_CODE="benefit_group_code";
    public static final String FIELD_WAR_BUSINESS_FLAG="war_business_flag";
    public static final String FIELD_SINGLE_DISEASE_CODE="single_disease_code";
    public static final String FIELD_SINGLE_DISEASE_NAME="single_disease_name";
    public static final String FIELD_CS_DISEASE_CODE="cs_disease_code";
    public static final String FIELD_CS_DISEASE_NAME="cs_disease_name";
    public static final String FIELD_OUT_ZONE_CODE="out_zone_code";
    public static final String FIELD_MEDICAL_RECORD_ID="medical_record_id";
    public static final String FIELD_IS_TRANS_HOSPITAL="is_trans_hospital";
    public static final String FIELD_IS_PUBLIC_HOSP="is_public_hosp";
    public static final String FIELD_PATIENT_IDNO="patient_idno";
    public static final String FIELD_IS_DISCHARGE="is_discharge";
    public static final String FIELD_ITEM_DATE="item_date";
    public static final String FIELD_DEPT_CODE="dept_code";
    public static final String FIELD_DEPT_NAME="dept_name";
    public static final String FIELD_DOC_ID="doc_id";
    public static final String FIELD_DOC_NAME="doc_name";
    public static final String FIELD_DOC_LEVEL="doc_level";
    public static final String FIELD_CHARGING_FLAG="charging_flag";
    public static final String FIELD_PAY_RATIO="pay_ratio";
    public static final String FIELD_PAY_AMOUNT="pay_amount";
    public static final String FIELD_REGISTER_DOC_ID="register_doc_id";
    public static final String FIELD_REGISTER_DOC_NAME="register_doc_name";
    public static final String FIELD_REGISTER_ID="register_id";
    public static final String FIELD_REGISTER_TIME="register_time";
    public static final String FIELD_REGISTER_NUMS="register_nums";
    public static final String FIELD_REGISTRATION_FEE="registration_fee";
    public static final String FIELD_TF_FROM_DEPT_CODE="tf_from_dept_code";
    public static final String FIELD_TF_FROM_DEPT_NAME="tf_from_dept_name";
    public static final String FIELD_TF_FROM_DOC_CODE="tf_from_doc_code";
    public static final String FIELD_TF_FROM_DOC_NAME="tf_from_doc_name";
    public static final String FIELD_TF_TO_DEPT_CODE="tf_to_dept_code";
    public static final String FIELD_TF_TO_DEPT_NAME="tf_to_dept_name";
    public static final String FIELD_TF_TO_DOC_CODE="tf_to_doc_code";
    public static final String FIELD_TF_TO_DOC_NAME="tf_to_doc_name";
    public static final String FIELD_TF_DATE="tf_date";
    public static final String FIELD_FEE_DATE="fee_date";
    public static final String FIELD_DIAGNOSIS_CODE1="diagnosis_code1";
    public static final String FIELD_DIAGNOSIS_CODE2="diagnosis_code2";
    public static final String FIELD_DIAGNOSIS_CODE3="diagnosis_code3";
    public static final String FIELD_DIAGNOSIS_CODE4="diagnosis_code4";
    public static final String FIELD_DIAGNOSIS_CODE5="diagnosis_code5";
    public static final String FIELD_DIAGNOSIS_CODE6="diagnosis_code6";
    public static final String FIELD_DIAGNOSIS_CODE7="diagnosis_code7";
    public static final String FIELD_DIAGNOSIS_CODE8="diagnosis_code8";
    public static final String FIELD_DIAGNOSIS_CODE9="diagnosis_code9";
    public static final String FIELD_DIAGNOSIS_CODE10="diagnosis_code10";
    public static final String FIELD_DIAGNOSIS_CODE11="diagnosis_code11";
    public static final String FIELD_DIAGNOSIS_CODE12="diagnosis_code12";
    public static final String FIELD_DIAGNOSIS_CODE13="diagnosis_code13";
    public static final String FIELD_DIAGNOSIS_CODE14="diagnosis_code14";
    public static final String FIELD_DIAGNOSIS_CODE15="diagnosis_code15";
    public static final String FIELD_DIAGNOSIS_CODE16="diagnosis_code16";
    public static final String FIELD_DIAGNOSIS_NAME1="diagnosis_name1";
    public static final String FIELD_DIAGNOSIS_NAME2="diagnosis_name2";
    public static final String FIELD_DIAGNOSIS_NAME3="diagnosis_name3";
    public static final String FIELD_DIAGNOSIS_NAME4="diagnosis_name4";
    public static final String FIELD_DIAGNOSIS_NAME5="diagnosis_name5";
    public static final String FIELD_DIAGNOSIS_NAME6="diagnosis_name6";
    public static final String FIELD_DIAGNOSIS_NAME7="diagnosis_name7";
    public static final String FIELD_DIAGNOSIS_NAME8="diagnosis_name8";
    public static final String FIELD_DIAGNOSIS_NAME9="diagnosis_name9";
    public static final String FIELD_DIAGNOSIS_NAME10="diagnosis_name10";
    public static final String FIELD_DIAGNOSIS_NAME11="diagnosis_name11";
    public static final String FIELD_DIAGNOSIS_NAME12="diagnosis_name12";
    public static final String FIELD_DIAGNOSIS_NAME13="diagnosis_name13";
    public static final String FIELD_DIAGNOSIS_NAME14="diagnosis_name14";
    public static final String FIELD_DIAGNOSIS_NAME15="diagnosis_name15";
    public static final String FIELD_DIAGNOSIS_NAME16="diagnosis_name16";
    public static final String FIELD_EXPAND_FIELD1="expand_field1";
    public static final String FIELD_EXPAND_FIELD2="expand_field2";
    public static final String FIELD_EXPAND_FIELD3="expand_field3";
    public static final String FIELD_EXPAND_FIELD4="expand_field4";
    public static final String FIELD_EXPAND_FIELD5="expand_field5";
    public static final String FIELD_VIOLATION_FLAG="violation_flag";
    public static final String FIELD_AUDIT_NUMS="audit_nums";
    public static final String FIELD_AUDIT_TIME="audit_time";
    public static final String FIELD_MR_STATUS="mr_status";
    public static final String FIELD_MR_OPINION="mr_opinion";
    public static final String FIELD_MR_TIME="mr_time";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";


    /**
     * 批次号
     */    
    @TableField
    private String batchNo;
    /**
     * 单据号
     */    
    @TableField
    private String no;
    /**
     * 就诊流水号
     */    
    @TableField
    private String admissionNo;
    /**
     * 医保反馈编码
     */    
    @TableField
    private String bmiNumber;
    /**
     * DRG编码
     */    
    @TableField
    private String drgCode;
    /**
     * DIP编码
     */    
    @TableField
    private String dipCode;
    /**
     * 单据结算日期
     */    
    @TableField
    private Date billDate;
    /**
     * 医院级别
     */    
    @TableField
    private String hospitalLevel;
    /**
     * 定点机构类型
     */    
    @TableField
    private String hospitalType;
    /**
     * 医疗类别（原就医方式编码）
     */    
    @TableField
    private String claimTypeId;
    /**
     * 参保类型编码
     */    
    @TableField
    private String benefitTypeId;
    /**
     * 自费标识
     */    
    @TableField
    private String selfExpense;
    /**
     * 医保报销统筹区编码
     */    
    @TableField
    private String bmiCode;
    /**
     * 医保报销统筹区名称
     */    
    @TableField
    private String bmiName;
    /**
     * 入院诊断编码
     */    
    @TableField
    private String inDiagnosisCode;
    /**
     * 入院诊断名称
     */    
    @TableField
    private String inDiagnosisName;
    /**
     * 出院诊断编码（在院时 为主诊断）
     */    
    @TableField
    private String outDiagnosisCode;
    /**
     * 出院诊断名称（在院时 为主诊断）
     */    
    @TableField
    private String outDiagnosisName;
    /**
     * 入院时间
     */    
    @TableField
    private Date admissionDate;
    /**
     * 出院时间
     */    
    @TableField
    private Date dischargeDate;
    /**
     * 参保人编码
     */    
    @TableField
    private String patientId;
    /**
     * 参保人姓名
     */    
    @TableField
    private String patientName;
    /**
     * 性别
     */    
    @TableField
    private String patientGender;
    /**
     * 出生日期
     */    
    @TableField
    private String patientBirthday;
    /**
     * 是否黑名单
     */    
    @TableField
    private String isBkl;
    /**
     * 是否孕期
     */    
    @TableField
    private String isPregnant;
    /**
     * 是否哺乳期
     */    
    @TableField
    private String isLactating;
    /**
     * 单据总金额
     */    
    @TableField
    private BigDecimal totalAmount;
    /**
     * 人员类别编码
     */    
    @TableField
    private String personnelType;
    /**
     * 待遇类型
     */    
    @TableField
    private String treatmentType;
    /**
     * 医保内金额
     */    
    @TableField
    private BigDecimal bmiConveredAmount;
    /**
     * 医保统筹金额
     */    
    @TableField
    private BigDecimal bmiOverallAmount;
    /**
     * 参保人特殊保险类型组编码 1：生育  2：工伤  -1：其他
     */    
    @TableField
    private String unusualFlag;
    /**
     * 是否异地就医
     */    
    @TableField
    private String benefitGroupCode;
    /**
     * 因战因公标志
     */    
    @TableField
    private String warBusinessFlag;
    /**
     * 单病种编码
     */    
    @TableField
    private String singleDiseaseCode;
    /**
     * 单病种名称
     */    
    @TableField
    private String singleDiseaseName;
    /**
     * 门诊慢特病病种编码
     */    
    @TableField
    private String csDiseaseCode;
    /**
     * 门诊慢特病病种名称
     */    
    @TableField
    private String csDiseaseName;
    /**
     * 出院病区编码
     */    
    @TableField
    private String outZoneCode;
    /**
     * 病案号（病人在医院看病的唯一登记号，只登记一次）
     */    
    @TableField
    private String medicalRecordId;
    /**
     * 是否二次返医/转院 | 按指定病种定额结算0：正常  1：转院  2：二次返院  4:90天或180天结算 
     */    
    @TableField
    private String isTransHospital;
    /**
     * 是否公立医改医院
     */    
    @TableField
    private String isPublicHosp;
    /**
     * 证件号码
     */    
    @TableField
    private String patientIdno;
    /**
     * 出入院状态
     */    
    @TableField
    private String isDischarge;
    /**
     * 就诊日期
     */    
    @TableField
    private Date itemDate;
    /**
     * 开单科室编码
     */    
    @TableField
    private String deptCode;
    /**
     * 开单科室名称
     */    
    @TableField
    private String deptName;
    /**
     * 出院科室编码
     */
    @TableField
    private String dischargeDeptCode;
    /**
     * 出院科室名称
     */
    @TableField
    private String dischargeDeptName;
    /**
     * 开单医生编码
     */    
    @TableField
    private String docId;
    /**
     * 开单医生名称
     */    
    @TableField
    private String docName;
    /**
     * 开单医生级别
     */    
    @TableField
    private String docLevel;
    /**
     * 是否计费
     */    
    @TableField
    private String chargingFlag;
    /**
     * 自付比例
     */    
    @TableField
    private BigDecimal payRatio;
    /**
     * 自付金额
     */    
    @TableField
    private BigDecimal payAmount;
    /**
     * 挂号医生编码
     */    
    @TableField
    private String registerDocId;
    /**
     * 挂号医生姓名
     */    
    @TableField
    private String registerDocName;
    /**
     * 挂号id
     */    
    @TableField
    private String registerId;
    /**
     * 挂号时间
     */    
    @TableField
    private Date registerTime;
    /**
     * 挂号次数
     */    
    @TableField
    private Integer registerNums;
    /**
     * 挂号费
     */    
    @TableField
    private BigDecimal registrationFee;
    /**
     * 转出科室编码
     */    
    @TableField
    private String tfFromDeptCode;
    /**
     * 转出科室名称
     */    
    @TableField
    private String tfFromDeptName;
    /**
     * 转出医生编码
     */    
    @TableField
    private String tfFromDocCode;
    /**
     * 转出医生编码
     */    
    @TableField
    private String tfFromDocName;
    /**
     * 转入科室编码
     */    
    @TableField
    private String tfToDeptCode;
    /**
     * 转入科室名称
     */    
    @TableField
    private String tfToDeptName;
    /**
     * 转入医生编码
     */    
    @TableField
    private String tfToDocCode;
    /**
     * 转入医生名称
     */    
    @TableField
    private String tfToDocName;
    /**
     * 转科日期
     */    
    @TableField
    private Date tfDate;
    /**
     * 收费日期
     */    
    @TableField
    private Date feeDate;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode1;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode2;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode3;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode4;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode5;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode6;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode7;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode8;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode9;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode10;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode11;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode12;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode13;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode14;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode15;
    /**
     * 其它诊断编码
     */    
    @TableField
    private String diagnosisCode16;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName1;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName2;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName3;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName4;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName5;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName6;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName7;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName8;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName9;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName10;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName11;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName12;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName13;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName14;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName15;
    /**
     * 其它诊断名称
     */    
    @TableField
    private String diagnosisName16;
    /**
     * 预留字段1
     */    
    @TableField
    private String expandField1;
    /**
     * 预留字段2
     */    
    @TableField
    private String expandField2;
    /**
     * 预留字段3
     */    
    @TableField
    private String expandField3;
    /**
     * 预留字段4
     */    
    @TableField
    private String expandField4;
    /**
     * 预留字段5
     */    
    @TableField
    private String expandField5;
    /**
     * 是否违规
     */    
    @TableField
    private String violationFlag;
    /**
     * 审核次数
     */    
    @TableField
    private Integer auditNums;
    /**
     * 审核时间
     */    
    @TableField
    private Date auditTime;
    /**
     * 人工审核状态
     */    
    @TableField
    private String mrStatus;
    /**
     * 人工审核意见
     */    
    @TableField
    private String mrOpinion;
    /**
     * 人工审核时间
     */    
    @TableField
    private Date mrTime;
    /**
     * 更新人
     */    
    @TableField
    private Long lastUpdatedBy;
    /**
     * 更新时间
     */    
    @TableField
    private Date lastUpdatedDate;
    /**
     * 创建人
     */    
    @TableField
    private Long createdBy;
    /**
     * 创建时间
     */    
    @TableField
    private Date createdDate;
    /**
     * 医院id
     */    
    @TableField
    private String hospitalId;


}

