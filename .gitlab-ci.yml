#
# GitLab CI：构建镜像 | 更新部署 | 通知推送
#

# CI环境变量:
# CI_BASE_IMAGE             运行CI流水线的镜像(镜像需包含node、yarn、mvn、rancher-cli、kubectl等环境)

# DOCKER_REP:               镜像仓库地址
# DOCKER_REP_USERNAME：     镜像仓库用户名
# DOCKER_REP_PASSWORD：     镜像仓库密码
# IMAGE_NAME：              镜像名称(例如填写: pms/hospital-ui-pms，最终打包成的镜像为：DOCKER_REP/pms/hospital-ui-pms:latest)

# RANCHER_URL：             rancher地址
# RANCHER_TOKEN：           rancher访问token
# RANCHER_PROJECT:          项目标签
# SERVICE_NAME:             服务名，即部署的工作负载名称

# RANCHER_DEV_NAMESPACE：   开发命名空间
# RANCHER_TEST_NAMESPACE:   测试环境命名空间
# RANCHER_DEMO_NAMESPACE:   demo环境命名空间
image: $CI_BASE_IMAGE

cache:
 key: maven-repository-cache
 paths:
  - .m2/repository

# 声明CI阶段：1.构建镜像 2.推送至开发/测试/demo环境
stages:
 - build-image

 - deploy-dev
 - deploy-test
 - deploy-demo

###### 构建镜像任务，触发条件：dev分支变更、tag名以dev-、test-、demo-开头、v开头
build-image-job:
 stage: build-image
 only:
  - /^dev-.*$/
  - /^test-.*$/
  - /^demo-.*$/
  - /^v.*$/
 script:
  - funBuildImage

###### 发布至开发环境任务，触发条件：dev分支变更、tag名以dev-开头
deploy-dev-job:
 stage: deploy-dev
 only:
  - /^dev-.*$/
 script:
  - funDeployDev

###### 发布至测试环境，触发条件：tag名以test-开头
deploy-test-job:
 stage: deploy-test
 only:
  - /^test-.*$/
 script:
  - funDeployTest

###### 发布至demo环境，触发条件：tag名以demo-开头
deploy-demo-job:
 stage: deploy-demo
 only:
  - /^demo-.*$/
 script:
  - funDeployDemo

.auto_devops: &auto_devops |
 function funBuildImage(){
   echo "----------------------构建镜像----------------------"
   docker login -u $DOCKER_REP_USERNAME -p $DOCKER_REP_PASSWORD $DOCKER_REP

   mvn clean package -DskipTests=true -s /app/settings.xml -Dmaven.repo.local=.m2/repository -Dxjar.password=Qs3sYPp4anchenqCvoQgz
   CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ./target/jar_decode ./target/xjar.go

   docker build -t $DOCKER_REP/$IMAGE_NAME:$CI_COMMIT_REF_NAME -f ./Dockerfile4Rancher .
   docker push $DOCKER_REP/$IMAGE_NAME:$CI_COMMIT_REF_NAME
 }

 function funDeployDev(){
     echo "----------------------推送服务到开发环境----------------------"

     echo yes | rancher login $RANCHER_URL --token $RANCHER_TOKEN --context $RANCHER_PROJECT
     rancher kubectl set image deployment/$SERVICE_NAME *=$DOCKER_REP/$IMAGE_NAME:$CI_COMMIT_REF_NAME --all --namespace=$RANCHER_DEV_NAMESPACE
     rancher kubectl rollout restart deployment/$SERVICE_NAME --namespace=$RANCHER_DEV_NAMESPACE
 }

 function funDeployTest(){
   echo "----------------------推送服务到测试环境----------------------"
   echo yes | rancher login $RANCHER_URL --token $RANCHER_TOKEN --context $RANCHER_PROJECT
   rancher kubectl set image deployment/$SERVICE_NAME *=$DOCKER_REP/$IMAGE_NAME:$CI_COMMIT_REF_NAME --all --namespace=$RANCHER_TEST_NAMESPACE
 }


 function funDeployDemo(){
    echo "----------------------推送服务到Demo环境----------------------"
    echo yes | rancher login $RANCHER_URL --token $RANCHER_TOKEN --context $RANCHER_PROJECT
    rancher kubectl set image deployment/$SERVICE_NAME *=$DOCKER_REP/$IMAGE_NAME:$CI_COMMIT_REF_NAME --all --namespace=$RANCHER_DEMO_NAMESPACE
 }
before_script:
 - *auto_devops
