package com.crhms.cloud.mqs.sys.disruptor;

import com.lmax.disruptor.ExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据管道消费异常全局处理器
 * <AUTHOR>
 * @date 2024-12-24 16:38:11
 */
@Slf4j
@Component
public class MrCaseHandlerException implements ExceptionHandler<MrCaseHandlerDTO> {

    @Override
    public void handleEventException(Throwable ex, long sequence, MrCaseHandlerDTO mrCaseHandlerDTO) {
        log.error("消费人工质控事件时发生异常");
    }

    @Override
    public void handleOnStartException(Throwable ex) {
        ex.printStackTrace();
    }

    @Override
    public void handleOnShutdownException(Throwable ex) {
        ex.printStackTrace();
    }
}
