package com.crhms.cloud.mqs.core.interceptor;

import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.crhms.cloud.core.user.dto.CustomUser;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.core.impl.SelectVisitorImpl;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.parser.CCJSqlParserManager;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.StringReader;
import java.sql.Connection;
import java.util.Arrays;

@Slf4j
@Component
@Order(1)
@Intercepts({@Signature(method = "prepare", type = StatementHandler.class, args = {Connection.class, Integer.class})})
public class MybatisSqlInterceptor implements Interceptor {

    CCJSqlParserManager parserManager = new CCJSqlParserManager();

    private final static String DEFAULT_USER = "crhms";
    private final static String SYS_USER = "admin";

    // 是否开启数据权限
    @Value("${crhms.mqs.data.permissions:}")
    private Boolean data_permissions;

    //维护权限范围方法集
    private final static String[] EXCLUDE_METHODS = new String[]{
            //门诊/住院审核-主单查询功能
            "com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper.queryCaseListByPage",
            "com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper.queryCaseListByPage_mpCount",
            //决策支持-明细查询功能
            "com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper.analyzeQueryDetail",
            "com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper.analyzeQueryDetail_mpCount"
    };

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = PluginUtils.realTarget(invocation.getTarget());
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");
        //获取方法id
        String id = mappedStatement.getId();
        //检查是否为权限控制
        if (checkSqlId(id) && data_permissions) {
            CustomUser user = LoginContext.getUser();
            //默认系统用户不限制
            if(! DEFAULT_USER.equals(user.getUsername()) && !SYS_USER.equals(user.getUsername())){
                // 对SELECT操作进行增强
                if (SqlCommandType.SELECT.equals(mappedStatement.getSqlCommandType())) {
                    BoundSql boundSql = (BoundSql) metaObject.getValue("delegate.boundSql");
                    Select select = (Select) parserManager.parse(new StringReader(boundSql.getSql()));
                    select.getSelectBody().accept(new SelectVisitorImpl());
                    //将增强后的sql放回
                    metaObject.setValue("delegate.boundSql.sql", select.toString());
                    log.info(select.toString());
                }
            }
        }
        return invocation.proceed();
    }

    private boolean checkSqlId(String id) {
        return Arrays.asList(EXCLUDE_METHODS).contains(id);
    }
}
