package com.crhms.cloud.mqs.mqs_ais.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 违规分析查询类
 */
@Data
public class ViolationQueryVo {

    //项目日期从
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String itemDateFrom;
    //项目日期至
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String itemDateTo;
    //审核场景
    private String auditScenario;
    //查询条件 项目名称 模糊
    private String item;
    //规则编码
    private String ruleCode;
    //查询条件 项目类型
    private List<String> itemTypeCodes;

    //科室集
    private List<String> deptCodes;
    //医生id集
    private List<String> docIds;
    //院区id
    private String hospitalId;

    //分页条件
    private int page;
    private int pageSize;

}
