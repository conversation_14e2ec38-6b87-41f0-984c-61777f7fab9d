package com.crhms.cloud.mqs.mqs_op.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.service.BaseAuditService;
import com.crhms.cloud.mqs.basic.vo.BaseMedicalParam;
import com.crhms.cloud.mqs.basic.vo.HisBaseAuditDto;
import com.crhms.cloud.mqs.basic.vo.EngAuditResultVo;
import com.crhms.cloud.mqs.mqs_op.domain.OpPt;
import com.crhms.cloud.mqs.mqs_op.mapper.OpPtDetailMapper;
import com.crhms.cloud.mqs.mqs_op.mapper.OpPtMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 门诊审核-门诊缴费审核(OpPt)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-19 09:39:24
 */
@Service("opPtService")
public class OpPtService extends ServiceImpl<OpPtMapper, OpPt> implements BaseAuditService {

    @Autowired
    private OpPtHisService opPtHisService;

    @Autowired
    private OpPtDetailMapper opPtDetailMapper;


    @Override
    public void beforeAudit(String batch, HisBaseAuditDto hisBaseAuditDto) {

    }

    @Override
    public void afterAudit(String batch, EngAuditResultVo result, HisBaseAuditDto hisBaseAuditDto, List<BaseMedicalParam> data) {

    }

}
