package com.crhms.cloud.mqs.basic.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.basic.domain.BaseAudit;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalStats;
import com.crhms.cloud.mqs.basic.mapper.*;
import com.crhms.cloud.mqs.basic.vo.BaseMedicalParam;
import com.crhms.cloud.mqs.core.service.CdmpServiceInterface;
import com.crhms.cloud.mqs.core.service.EngineService;
import com.crhms.cloud.mqs.mqs_mr.domain.MrCase;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlan;
import com.crhms.cloud.mqs.mqs_mr.manager.MrPlanFilterImpl;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrAuditMapper;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrReviewMapper;
import com.crhms.cloud.mqs.mqs_mr.service.MrCaseService;
import com.crhms.cloud.mqs.mqs_mr.service.MrPlanService;
import com.crhms.cloud.mqs.sys.mapper.SysRulesMapper;
import com.crhms.cloud.mqs.sys.service.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
public class BaseMedicalStatsService extends ServiceImpl<BaseMedicalStatsMapper, BaseMedicalStats> {


    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private BaseMedicalMapper baseMedicalMapper;
    @Autowired
    private MrPlanService mrPlanService;

    @Autowired
    private MrPlanFilterImpl mrPlanFilterImpl;

    @Autowired
    private SysSceneService sysSceneService;

    @Autowired
    private BaseMedicalStatsService baseMedicalStatsService;
    /*
    *  批量获取业务流程结果
    * */
    public List<BaseMedicalStats> getMedicalStats(List<BaseMedicalParam> medicalParamList,String hospitalId) {

        if(CollectionUtil.isEmpty(medicalParamList)){
            return Collections.emptyList();
        }

        List<BaseMedicalStats> list = new ArrayList<>(100);

        for(BaseMedicalParam baseMedicalParam :medicalParamList){
            //获取主单
            BaseMedicalCase baseMedicalCase = baseMedicalParam.getMedical_case();
            //获取明细列表
            List<BaseMedicalDetail> fee_list = baseMedicalParam.getFee_list();

            if(CollectionUtil.isNotEmpty(fee_list)){
                BaseMedicalStats baseMedicalStats = new BaseMedicalStats();
                String preDischarge = "0";
                try {
                    //获取系统配置里的关键字配置信息
                    String sysValue = sysConfigService.queryValueByKey("MQS_DIS_CHARGE",hospitalId);
                    if(StringUtils.isNotBlank(sysValue)){
                        List<Map> values = JSONUtil.toList(sysValue,Map.class);
                        //将权重与关键词转换成新的map
                        Map<String, List<String>> map = values.stream().collect(Collectors.toMap(entry -> entry.get("weight").toString(), entry -> (List)entry.get("keyword")));
                        //与明细列表的项目名称进行对比（按照权重（权重越小，优先级越高）进行过滤）
                        Map<String, BaseMedicalDetail> result = map.entrySet().stream()
                                .filter(entry -> fee_list.stream()
                                        .anyMatch(fee -> entry.getValue().stream()
                                                .anyMatch(value -> fee.getItemName().contains(value))))
                                .collect(Collectors.toMap(
                                        Map.Entry::getKey,
                                        entry -> fee_list.stream()
                                                .filter(fee -> entry.getValue().stream()
                                                        .anyMatch(value -> fee.getItemName().contains(value)))
                                                .findFirst()
                                                .orElse(null)
                                ));
                        //获取最先匹配的BaseMedicalDetail
                        Optional<Map.Entry<String, BaseMedicalDetail>> first = result.entrySet().stream().findFirst();
                        if(first.isPresent()){
                            //获取对应的key以及value
                            String key = first.get().getKey();
                            BaseMedicalDetail value = first.get().getValue();
                            //根据key获取对应的天数配置
                            Optional<String> date = values.stream().filter(entry-> entry.get("weight").toString().equals(key)).map(entry-> entry.get("date").toString()).findFirst();
                            // 预出院时间
                            baseMedicalStats.setPreDischargeDate(DateUtil.offsetDay(new Date(),date.isPresent() ? Integer.parseInt(date.get()) : 0));
                            preDischarge = "1";
                            baseMedicalStats.setDischargeOrderDate(value.getItemDate());
                        }
                    }
                } catch (Exception e) {
                    log.info("判断是否预出院发生未知异常,打印堆栈信息：{}",e.getStackTrace());
                }
                // 预出院状态(有预出院时间则为预出院)和住院医嘱时间
                baseMedicalStats.setPreDischarge(preDischarge);
                baseMedicalStats.setPatientId(baseMedicalCase.getPatientId());
                baseMedicalStats.setBenefitTypeId(baseMedicalCase.getBenefitTypeId());
                baseMedicalStats.setPersonnelType(baseMedicalCase.getPersonnelType());
                //已出院 主单出院时间不为空
                baseMedicalStats.setDischarged(Objects.nonNull(baseMedicalCase.getDischargeDate())?"1":"0");
                //已结算  主单结算时间不为空
                baseMedicalStats.setSettled(Objects.nonNull(baseMedicalCase.getBillDate())?"1":"0");
                baseMedicalStats.setNo(baseMedicalCase.getNo());
                baseMedicalStats.setHospitalId(hospitalId);
                list.add(baseMedicalStats);
            }
        }
        return list;
    }

    /**
     * 获取人工审核标记
     * 更新单据状态
     * 保存业务流程结果
     */
    public void invokeMrFlagMap( List<BaseAudit> baseAuditList,String auditScenario, List<BaseMedicalParam> medicalParamList, List<String> sceneCodes , String hospitalId) {


        //获取业务流程结果
        List<BaseMedicalStats> baseMedicalStats = this.getMedicalStats(medicalParamList, hospitalId);
        Map<String, List<BaseMedicalStats>> baseMedicalStatsMap = baseMedicalStats.stream().collect(Collectors.groupingBy(BaseMedicalStats::getNo));
        //获取场景下维护的人工审核配置
        /* --------------------------------------------v1.1.0新添加的人工配置过滤start---------------------------------------     */
        String sceneClassification = auditScenario.substring(0, 2);
        //获取人工审核配置map（已经按照门诊\住院划分好）
        Map<String, List<MrPlan>> mrPlanMap = mrPlanService.queryByHospitalIdAndType(hospitalId, "1");
        Map<String, List<MrPlan>> mrPlanAuditMap = mrPlanService.queryByHospitalIdAndType(hospitalId, "2");
        //获取人工审核配置

        List<String> sceneCodeAudits = new ArrayList<>();
        //根据场景前缀获取对应的数据进入/数据审核人工审核配置
        List<MrPlan> mrPlans = mrPlanMap.get(sceneClassification);
        List<MrPlan> mrPlanAudits = mrPlanAuditMap.get(sceneClassification);
        //如果存在审核配置并且已启用
        if (CollectionUtil.isNotEmpty(mrPlans) && "1".equals(mrPlans.get(0).getEnable())) {
            //获取场景以及后续所有场景
            sceneCodes.addAll(sysSceneService.getNextScenes(hospitalId, mrPlans.get(0).getAuditScenario()));
        }
        if (CollectionUtil.isNotEmpty(mrPlanAudits) && "1".equals(mrPlanAudits.get(0).getEnable())) {
            //获取场景以及后续所有场景
            sceneCodeAudits.addAll(sysSceneService.getNextScenes(hospitalId, mrPlanAudits.get(0).getAuditScenario()));
        }
        Map<String, String> mrFlagMap = null;
        List<String> nos = medicalParamList.stream().map(medicalParam -> medicalParam.getMedical_case().getNo()).collect(Collectors.toList());
        List<BaseMedicalCase> medicalCases = baseMedicalMapper.queryMedicalByHospitalId(MrCase.BASE_TABLE, hospitalId, nos);
        if (CollectionUtil.isNotEmpty(medicalCases)) {
            mrFlagMap = medicalCases.stream().collect(Collectors.toMap(BaseMedicalCase::getNo, BaseMedicalCase::getMrFlag));
        }
        for (BaseMedicalParam medicalParam : medicalParamList) {
            //统计审核次数
            BaseMedicalCase medicalCase = medicalParam.getMedical_case();
            List<BaseAudit> baseAudits = baseAuditList.stream().filter(x -> ObjectUtil.isNotEmpty(x.getNo()) && x.getNo().equals(medicalCase.getNo())).collect(Collectors.toList());
            baseAudits.stream().forEach(
                    x -> x.setAdmissionNo(medicalCase.getAdmissionNo())
            );
            medicalCase.setViolationFlag(CollectionUtil.isNotEmpty(baseAudits) ? "1" : "0");
            //判断是否需要人工审核
            /* --------------------------------------------v1.1.0新添加的人工配置过滤start---------------------------------------     */
            //查看当前主单是否已经存在人工审核表里,如果已存在还延续原先状态
            if (Objects.nonNull(mrFlagMap) && StringUtils.isNotBlank(mrFlagMap.get(medicalCase.getNo()))) {
                medicalCase.setMrFlag(mrFlagMap.get(medicalCase.getNo()));
            } else {
                //如果规则配置不为空则过滤数据  （数据审核）
                boolean auditResult = getMrFlag(mrPlanAudits, medicalCase, baseAudits, sceneCodeAudits, auditScenario, baseMedicalStatsMap);
                if (auditResult) {
                    medicalCase.setMrFlag("2");
                } else {
                    //如果规则配置不为空则过滤数据   （数据进入）
                    boolean result = getMrFlag(mrPlans, medicalCase, baseAudits, sceneCodes, auditScenario, baseMedicalStatsMap);
                    if (result) {
                        medicalCase.setMrFlag("1");
                    } else {
                        medicalCase.setMrFlag("0");
                    }
                }
            }
        }
        /* --------------------------------------------v1.1.0新添加的人工配置过滤end---------------------------------------     */


        //保存业务流程结果
        baseMedicalStatsService.saveOrUpdateBatch(baseMedicalStats);
    }

    /*
     *  根据人工审核配置对数据进入以及数据权限进行过滤
     *
     * */
    private boolean getMrFlag (List<MrPlan> mrPlans,BaseMedicalCase medicalCase,List<BaseAudit> baseAudits,List<String> sceneCodes,String auditScenario,Map<String,List<BaseMedicalStats>> baseMedicalStatsMap){
        if(CollectionUtil.isEmpty(mrPlans) || !"1".equals(mrPlans.get(0).getEnable())){
            return false;
        }
        return mrPlanFilterImpl.exec(mrPlans.get(0),medicalCase,baseAudits,sceneCodes,auditScenario, baseMedicalStatsMap);
    }
}
