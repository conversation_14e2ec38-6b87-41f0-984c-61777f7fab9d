package com.crhms.cloud.mqs.basic.controller;

import cn.hutool.core.util.ObjectUtil;
import com.crhms.cloud.client.cdmp.dic.CdmpDicInterface;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.common.ClinicParams;
import com.crhms.cloud.mqs.basic.common.MqsResult;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.service.BaseMedicalService;
import com.crhms.cloud.mqs.basic.vo.ClinicalQueryVo;
import com.crhms.cloud.mqs.basic.vo.ComprePopupDetailVO;
import com.crhms.cloud.mqs.basic.vo.ComprePopupQueryVO;
import com.crhms.cloud.mqs.sys.domain.SysConfig;
import com.crhms.cloud.mqs.sys.domain.SysRuleLevel;
import com.crhms.cloud.mqs.sys.domain.SysSceneFunction;
import com.crhms.cloud.mqs.sys.service.SysConfigService;
import com.crhms.cloud.mqs.sys.service.SysSceneService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * 对外功能api
 */
@RestController
@RequestMapping(value = "/api/mqs/ForeginHospitalAuditService")
@Slf4j
public class ApiController {
    @Autowired
    SysSceneService sysSceneService;
    @Autowired
    BaseMedicalService baseMedicalService;
    @Autowired
    CdmpDicInterface cdmpDicInterface;
    @Autowired
    SysConfigService sysConfigService;

    /**
     * 查询审核场景 - 功能权限配置
     * @param auditScenario     场景编码
     * @param functionCode      功能（1.审核弹窗。2.融合平台）
     * @param hospitalId        医院id
     * @return
     */
    @GetMapping("function/queryOne")
    public ResponseEntity<SysSceneFunction> querySceneFunctionOne(@RequestParam(value = "auditScenario") String auditScenario,
                                                                  @RequestParam(value = "functionCode") String functionCode,
                                                                  @RequestParam(value = "hospitalId") String hospitalId) {
        return ResponseEntity.ok(sysSceneService.querySceneFunctionOne(auditScenario, functionCode, hospitalId));
    }

    /**
     *  获取系统标题
     *
     * @return
     */
    @GetMapping("/querySysTitle")
    public ResponseEntity<String> querySysTitle(@RequestParam(value = "hospitalId") String hospitalId) {


        SysConfig sysConfig = sysConfigService.queryByKey("MQS_SYS_TITLE", hospitalId);

        return ResponseEntity.ok(ObjectUtil.isNotEmpty(sysConfig)? sysConfig.getSysValue(): "医院诊间审核管理系统");
    }

    /**
     * 临床融合界面接口 - 字典查询
     * @param dictCode     字典编码
     * @return
     */
    @GetMapping("queryDicDataList")
    public ResponseEntity<List<Map<String, Object>>> queryDicDataList(@RequestParam(value = "dictCode") String dictCode) {
        return ResponseEntity.ok(cdmpDicInterface.queryDicDataList(dictCode));
    }


    /**
     * 临床融合界面接口 - 查询所有场景下最新审核单据
     * @param admissionNo       就诊流水号
     * @param hospitalId        院区id
     * @param auditScenario     支持指定场景
     * @return
     */
    @GetMapping("/clinical/queryCase")
    public ResponseEntity<BaseMedicalCase> queryClinicalCase(@RequestParam(value = "admissionNo") String admissionNo,
                                                             @RequestParam(value = "hospitalId") String hospitalId,
                                                             @RequestParam(value = "auditScenario",required = false) String auditScenario) {

        return ResponseEntity.ok(baseMedicalService.queryClinicalCase(admissionNo, hospitalId, auditScenario));
    }

    /**
     *
     * 临床融合界面接口 - 查询质控风险项目
     *
     * @return
     */
    @PostMapping("/clinical/queryViolationItem")
    public ResponseEntity<List<BaseMedicalDetail>> queryClinicalDetail(@RequestBody ClinicalQueryVo clinicalQueryVo) {

        return ResponseEntity.ok(baseMedicalService.queryClinicalDetail(clinicalQueryVo.getAuditScenario(), clinicalQueryVo.getAdmissionNo(), clinicalQueryVo.getViolationFlag()
                , clinicalQueryVo.getSelfExpense(), clinicalQueryVo.getItem(), clinicalQueryVo.getItemTypes(), clinicalQueryVo.getHospitalId()));
    }

    /**
     *
     * 临床融合界面接口 - 查询丙类项目
     *
     * @return
     */
    @PostMapping("/clinical/querySelfItem")
    public ResponseEntity<List<BaseMedicalDetail>> queryClinicalSelfItem(@RequestBody ClinicalQueryVo clinicalQueryVo) {
        return ResponseEntity.ok(baseMedicalService.queryClinicalSelfItem(clinicalQueryVo.getAuditScenario(), clinicalQueryVo.getAdmissionNo(),
                clinicalQueryVo.getItem(), clinicalQueryVo.getItemTypes(), clinicalQueryVo.getHospitalId()));
    }

    /**
     *
     * 临床融合界面接口 - 查询漏收费项目（违规记录为 C990106 C990107）
     *
     * @return
     */
    @PostMapping("/clinical/queryMissedItem")
    public ResponseEntity<List<BaseMedicalDetail>> queryMissedItem(@RequestBody ClinicalQueryVo clinicalQueryVo) {
        return ResponseEntity.ok(baseMedicalService.queryMissedItem(clinicalQueryVo.getAuditScenario(), clinicalQueryVo.getAdmissionNo(),
                clinicalQueryVo.getItem(), clinicalQueryVo.getItemTypes(), clinicalQueryVo.getHospitalId()));
    }

    /**
     *
     * 临床融合界面接口 - 导出自费协议
     *
     * @param auditScenario     审核场景
     * @param no                单据号
     * @param hospitalId        院区id
     * @param detailNos         已勾选明细
     * @param response
     * @return
     */
    @PostMapping("/clinical/exportSelfAgreement")
    public ResponseEntity exportClinicalSelfAgreement(@RequestParam(value = "auditScenario") String auditScenario,
                                                      @RequestParam(value = "no") String no,
                                                      @RequestParam(value = "hospitalId") String hospitalId,
                                                      @RequestParam(value = "codePrefix",required = false) String codePrefix,
                                                      @RequestBody List<String> detailNos,
                                                      HttpServletResponse response) {
        //导出自费协议
        baseMedicalService.exportSelfAgreement(response, no, detailNos, auditScenario, null, hospitalId,codePrefix);
        return ResponseEntity.ok("success");

    }

    /**
     *
     * 临床融合界面接口 - 确认转自费
     *
     * @param auditScenario     审核场景
     * @param hospitalId        院区id
     * @param changeDetails     自费状态变更行
     * @return
     */
    @PostMapping("/clinical/changeSelfAgreement")
    public ResponseEntity changeSelfAgreement(@RequestParam(value = "auditScenario") String auditScenario,
                                              @RequestParam(value = "hospitalId") String hospitalId,
                                              @RequestBody List<BaseMedicalDetail> changeDetails) {
        List<BaseMedicalDetail> selfDetail = new ArrayList<>();
        List<BaseMedicalDetail> noSelfDetail = new ArrayList<>();
        for (BaseMedicalDetail changeDetail : changeDetails) {
            if ("1".equals(changeDetail.getSelfExpense())) {
                selfDetail.add(changeDetail);
            } else if ("0".equals(changeDetail.getSelfExpense())) {
                noSelfDetail.add(changeDetail);
            }
        }
        baseMedicalService.changeSelfAgreement(auditScenario, selfDetail, noSelfDetail, hospitalId);
        return ResponseEntity.ok("success self change");

    }

    /**
     * 模拟his自费接口 (实施阶段需要his提供)
     * @param changeDetails
     * @return
     */
    @PostMapping("/clinical/hisSelfAgreement")
    public ResponseEntity hisSelfAgreement(@RequestBody Object changeDetails) {
        return ResponseEntity.ok("200100");
    }


    /**
     * 综合弹窗------质控患者列表查询
     *
     * @return
     */
    @GetMapping("/getPendingPatient")
    public MqsResult getPendingPatient(@RequestParam(value = "codePrefix") String codePrefix,
                                       @RequestParam(value = "hospitalId") String hospitalId,
                                       @RequestParam(value = "patientName" ,required = false) String patientName,
                                       @RequestParam(value = "presentDeptCode" ,required = false) String presentDeptCode,
                                       @RequestParam(value = "docId" ,required = false) String docId) {
        return baseMedicalService.getPendingPatient(codePrefix, hospitalId,patientName,presentDeptCode,docId);
    }

    /**
     * 综合弹窗------查询主单信息
     *
     * @return
     */
    @GetMapping("/getBaseMedical")
    public MqsResult getBaseMedical(@RequestParam(value = "no" ,required = false) String no,
                                    @RequestParam(value = "batch" ,required = false) String batch,
                                    @RequestParam(value = "auditScenario" ,required = false) String auditScenario,
                                    @RequestParam(value = "hospitalId") String hospitalId,
                                    @RequestParam(value = "admissionNo") String admissionNo) {
        return baseMedicalService.getBaseMedical(no,batch,auditScenario,hospitalId,admissionNo);
    }

    /**
     * 综合弹窗---审核渠道---未处理，已处理明细查询（分组）
     *
     * @return
     */
    @PostMapping("/queryAuditClinicalStatus")
    public MqsResult queryAuditClinicalStatus( @RequestParam(value = "admissionNo") String admissionNo,
                                               @RequestParam(value = "hospitalId") String hospitalId,
                                               @RequestParam(value = "auditScenario") String auditScenario,
                                               @RequestParam(value = "batch") String batchNo,
                                               @RequestParam(value = "isIncrement") String isIncrement,
                                               @RequestParam(value = "clinicalStatus") String clinicalStatus,
                                               @RequestParam(value = "auditSource",required = false) String auditSource,
                                               @RequestParam(value = "selfExpense",required = false) String selfExpense,
                                               @RequestParam(value = "itemName",required = false) String itemName,
                                               @RequestParam(value = "itemTypeCode",required = false) String itemTypeCode,
                                               @RequestParam(value = "categoryName",required = false) String categoryName,
                                               @RequestParam(value = "outpatientMedication",required = false) String outpatientMedication,
                                               @RequestBody(required = false) List<String> ruleTypes) {
        ComprePopupQueryVO comprePopupQueryVO = new ComprePopupQueryVO();
        comprePopupQueryVO.setAdmissionNo(admissionNo);
        comprePopupQueryVO.setHospitalId(hospitalId);
        comprePopupQueryVO.setAuditScenario(auditScenario);
        comprePopupQueryVO.setBatchNo(batchNo);
        comprePopupQueryVO.setIsIncrement(isIncrement);
        comprePopupQueryVO.setClinicalStatus(clinicalStatus);
        comprePopupQueryVO.setViolationFlag("1");
        comprePopupQueryVO.setAuditSource(auditSource);
        comprePopupQueryVO.setSelfExpense(selfExpense);
        comprePopupQueryVO.setItemName(itemName);
        comprePopupQueryVO.setItemTypeCode(itemTypeCode);
        comprePopupQueryVO.setCategoryName(categoryName);
        comprePopupQueryVO.setOutpatientMedication(outpatientMedication);
        comprePopupQueryVO.setRuleTypes(ruleTypes);
        return MqsResult.OK(baseMedicalService.queryMedicalDetailByItem(comprePopupQueryVO));
    }

    /**
     * 综合弹窗---审核渠道---医保、自费明细查询（分组）
     *
     * @return
     */
    @GetMapping("/queryAuditSelfExpense")
    public MqsResult queryAuditSelfExpense(@RequestParam(value = "admissionNo") String admissionNo,
                                           @RequestParam(value = "hospitalId") String hospitalId,
                                           @RequestParam(value = "auditScenario") String auditScenario,
                                           @RequestParam(value = "batch") String batchNo,
                                           @RequestParam(value = "isIncrement") String isIncrement,
                                           @RequestParam(value = "selfExpense") String selfExpense,
                                           @RequestParam(value = "itemName",required = false) String itemName,
                                           @RequestParam(value = "itemTypeCode",required = false) String itemTypeCode,
                                           @RequestParam(value = "categoryName",required = false) String categoryName) {
        ComprePopupQueryVO comprePopupQueryVO = new ComprePopupQueryVO();
        comprePopupQueryVO.setAdmissionNo(admissionNo);
        comprePopupQueryVO.setHospitalId(hospitalId);
        comprePopupQueryVO.setAuditScenario(auditScenario);
        comprePopupQueryVO.setBatchNo(batchNo);
        comprePopupQueryVO.setIsIncrement(isIncrement);
        comprePopupQueryVO.setSelfExpense(selfExpense);
        comprePopupQueryVO.setViolationFlag("0");
        comprePopupQueryVO.setItemName(itemName);
        comprePopupQueryVO.setItemTypeCode(itemTypeCode);
        comprePopupQueryVO.setCategoryName(categoryName);
        return MqsResult.OK(baseMedicalService.queryMedicalDetailByItem(comprePopupQueryVO));
    }


    /**
     * 综合弹窗---质控患者---未处理、已处理明细查询（分组）
     *
     * @return
     */
    @PostMapping("/queryPatientClinicalStatus")
    public MqsResult queryPatientClinicalStatus(@RequestParam(value = "admissionNo") String admissionNo,
                                                @RequestParam(value = "hospitalId") String hospitalId,
                                                @RequestParam(value = "auditScenario") String auditScenario,
                                                @RequestParam(value = "no") String no,
                                                @RequestParam(value = "docId") String docId,
                                                @RequestParam(value = "presentDeptCode") String presentDeptCode,
                                                @RequestParam(value = "clinicalStatus") String clinicalStatus,
                                                @RequestParam(value = "auditSource",required = false) String auditSource,
                                                @RequestParam(value = "selfExpense",required = false) String selfExpense,
                                                @RequestParam(value = "itemName",required = false) String itemName,
                                                @RequestParam(value = "itemTypeCode",required = false) String itemTypeCode,
                                                @RequestParam(value = "categoryName",required = false) String categoryName,
                                                @RequestParam(value = "outpatientMedication",required = false) String outpatientMedication,
                                                @RequestBody(required = false) List<String> ruleTypes) {
        ComprePopupQueryVO comprePopupQueryVO = new ComprePopupQueryVO();
        comprePopupQueryVO.setAdmissionNo(admissionNo);
        comprePopupQueryVO.setHospitalId(hospitalId);
        comprePopupQueryVO.setAuditScenario(auditScenario);
        comprePopupQueryVO.setNo(no);
        comprePopupQueryVO.setDocId(docId);
        comprePopupQueryVO.setPresentDeptCode(presentDeptCode);
        comprePopupQueryVO.setClinicalStatus(clinicalStatus);
        comprePopupQueryVO.setViolationFlag("1");
        comprePopupQueryVO.setAuditSource(auditSource);
        comprePopupQueryVO.setSelfExpense(selfExpense);
        comprePopupQueryVO.setItemName(itemName);
        comprePopupQueryVO.setItemTypeCode(itemTypeCode);
        comprePopupQueryVO.setCategoryName(categoryName);
        comprePopupQueryVO.setOutpatientMedication(outpatientMedication);
        comprePopupQueryVO.setRuleTypes(ruleTypes);
        return MqsResult.OK(baseMedicalService.queryMedicalDetailByItem(comprePopupQueryVO));
    }


    /**
     * 综合弹窗---质控患者---医保、自费明细查询（分组）
     *
     * @return
     */
    @GetMapping("/queryPatientSelfExpense")
    public MqsResult queryPatientSelfExpense(@RequestParam(value = "admissionNo") String admissionNo,
                                             @RequestParam(value = "hospitalId") String hospitalId,
                                             @RequestParam(value = "auditScenario") String auditScenario,
                                             @RequestParam(value = "no") String no,
                                             @RequestParam(value = "docId") String docId,
                                             @RequestParam(value = "presentDeptCode") String presentDeptCode,
                                             @RequestParam(value = "selfExpense") String selfExpense,
                                             @RequestParam(value = "itemName",required = false) String itemName,
                                             @RequestParam(value = "itemTypeCode",required = false) String itemTypeCode,
                                             @RequestParam(value = "categoryName",required = false) String categoryName) {
        ComprePopupQueryVO comprePopupQueryVO = new ComprePopupQueryVO();
        comprePopupQueryVO.setAdmissionNo(admissionNo);
        comprePopupQueryVO.setHospitalId(hospitalId);
        comprePopupQueryVO.setAuditScenario(auditScenario);
        comprePopupQueryVO.setNo(no);
        comprePopupQueryVO.setDocId(docId);
        comprePopupQueryVO.setPresentDeptCode(presentDeptCode);
        comprePopupQueryVO.setSelfExpense(selfExpense);
        comprePopupQueryVO.setViolationFlag("0");
        comprePopupQueryVO.setItemName(itemName);
        comprePopupQueryVO.setItemTypeCode(itemTypeCode);
        comprePopupQueryVO.setCategoryName(categoryName);
        return MqsResult.OK(baseMedicalService.queryMedicalDetailByItem(comprePopupQueryVO));
    }


    /**
     * 综合弹窗------批量忽略
     *
     * @return
     */
    @PostMapping("/batchInsertIgnore")
    public MqsResult batchInsertIgnore(@RequestBody List<ComprePopupDetailVO> comprePopupSaveVO,
                                       @RequestParam(value = "auditScenario") String auditScenario,
                                       @RequestParam(value = "admissionNo") String admissionNo,
                                       @RequestParam(value = "hospitalId") String hospitalId,
                                       @RequestParam(value = "batch",required = false) String batchNo,
                                       @RequestParam(value = "patientId",required = false) String patientId,
                                       @RequestParam(value = "patientName",required = false) String patientName,
                                       @RequestParam(value = "codePrefix",required = false) String codePrefix) {
        return baseMedicalService.batchInsertIgnore(comprePopupSaveVO,auditScenario,admissionNo,hospitalId,batchNo,patientId,patientName,codePrefix);
    }

    /**
     * 综合弹窗------批量撤销忽略
     *
     * @return
     */
    @PostMapping("/unBatchInsertIgnore")
    public MqsResult unBatchIgnore(@RequestBody List<ComprePopupDetailVO> comprePopupSaveVO,
                                   @RequestParam(value = "auditScenario") String auditScenario,
                                   @RequestParam(value = "hospitalId") String hospitalId,
                                   @RequestParam(value = "patientId",required = false) String patientId,
                                   @RequestParam(value = "patientName",required = false) String patientName,
                                   @RequestParam(value = "codePrefix",required = false) String codePrefix) {
        return baseMedicalService.unBatchIgnore(comprePopupSaveVO,auditScenario,hospitalId,patientId,patientName,codePrefix);
    }

    /**
     * 综合弹窗------确认保存（包含转自费操作）
     *
     * @return
     */
    @PostMapping("/saveForClinicalStatus")
    public MqsResult saveForClinicalStatus(@RequestParam(value = "batch",required = false) String batchNo,
                                        @RequestParam(value = "hospitalId")String hospitalId,
                                        @RequestParam(value = "admissionNo") String admissionNo,
                                        @RequestParam(value = "isIncrement") String isIncrement,
                                        @RequestParam(value = "auditScenario") String auditScenario,
                                        @RequestParam(value = "patientId",required = false) String patientId,
                                        @RequestParam(value = "patientName",required = false) String patientName,
                                        @RequestParam(value = "billState",required = false) String billState,
                                        @RequestParam(value = "mrStatus",required = false) String mrStatus,
                                        @RequestParam(value = "codePrefix",required = false) String codePrefix,
                                        @RequestBody List<ComprePopupDetailVO> comprePopupSaveVO) {
        return baseMedicalService.selfExpenseAndSave(batchNo,hospitalId,admissionNo,isIncrement,auditScenario,comprePopupSaveVO,patientId,patientName,billState,mrStatus,codePrefix);
    }

    /**
     * 综合弹窗------确认保存（包含转自费操作）
     *
     * @return
     */
    @PostMapping("/SaveForSelfExpense")
    public MqsResult selfExpenseAndSave(@RequestParam(value = "hospitalId")String hospitalId,
                                        @RequestParam(value = "admissionNo") String admissionNo,
                                        @RequestParam(value = "auditScenario") String auditScenario,
                                        @RequestParam(value = "patientId",required = false) String patientId,
                                        @RequestParam(value = "patientName",required = false) String patientName,
                                        @RequestParam(value = "billState",required = false) String billState,
                                        @RequestParam(value = "mrStatus",required = false) String mrStatus,
                                        @RequestParam(value = "codePrefix",required = false) String codePrefix,
                                        @RequestBody List<ComprePopupDetailVO> comprePopupSaveVO) {
        return baseMedicalService.selfExpenseAndSave(null,hospitalId,admissionNo,null,auditScenario,comprePopupSaveVO,patientId,patientName,billState,mrStatus,codePrefix);
    }

    /**
     * 获取违规级别配置列表(无需鉴权)
     * @param platform
     * @return
     */
    @GetMapping("/queryRuleLevelList")
    public MqsResult queryRuleLevelList(@RequestParam(value = "platform",required = false) String platform,
                                        @RequestParam(value = "levelConfig",required = false) String levelConfig){
        return MqsResult.OK(sysConfigService.queryRuleLevelList(SysRuleLevel.builder().platform(platform).levelConfig(levelConfig).hospitalId(LoginContext.getHospitalId()).build()));
    }

}
