package com.crhms.cloud.mqs.mqs_mr.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class MrDetailAggregation {
    //是否违规
    private String violationFlag;

    private String violationFlagCh;

    //是否自费
    private String selfExpense;

    private String selfExpenseCh;

    //审核渠道
    private String auditSource;

    private String auditSourceName;

    //临床处理
    private String clinicalStatus;

    //违规级别
    private String ruleType;

    //规则几级别名称
    private String ruleTypeName;

    //规则名称
    private String ruleNames;

    //违规原因
    private String ruleReasons;

    //数量
    private BigDecimal numbers;

    //金额
    private BigDecimal costs;

    //项目名称
    private String itemName;

    //项目类型名称
    private String itemTypeName;

    //甲乙类
    private String categoryName;

    //是否出院带药
    private String outpatientMedication;

    //用药途径
    private String routeAdministration;

    public MrDetailAggregation(String violationFlag,String violationFlagCh, String selfExpense,String selfExpenseCh,String auditSource,String auditSourceName,String clinicalStatus,String ruleType,String ruleTypeName,String ruleReasons,BigDecimal numbers,BigDecimal costs,String ruleNames,String categoryName,String outpatientMedication,String routeAdministration) {
        this.violationFlag = violationFlag;
        this.violationFlagCh = violationFlagCh;
        this.selfExpense = selfExpense;
        this.selfExpenseCh = selfExpenseCh;
        this.auditSource = auditSource;
        this.auditSourceName = auditSourceName;
        this.clinicalStatus = clinicalStatus;
        this.ruleType = ruleType;
        this.ruleTypeName = ruleTypeName;
        this.ruleReasons = ruleReasons;
        this.numbers = numbers;
        this.costs = costs;
        this.ruleNames = ruleNames;
        this.categoryName = categoryName;
        this.outpatientMedication = outpatientMedication;
        this.routeAdministration = routeAdministration;
    }


    public MrDetailAggregation( String selfExpense,String selfExpenseCh,String auditSource,String auditSourceName,String ruleType,String ruleTypeName,String ruleReasons,BigDecimal numbers,BigDecimal costs,String itemName,String itemTypeName,String categoryName,String outpatientMedication,String routeAdministration,String ruleNames) {
        this.selfExpense = selfExpense;
        this.selfExpenseCh = selfExpenseCh;
        this.auditSource = auditSource;
        this.auditSourceName = auditSourceName;
        this.ruleType = ruleType;
        this.ruleTypeName = ruleTypeName;
        this.ruleReasons = ruleReasons;
        this.numbers = numbers;
        this.costs = costs;
        this.itemName = itemName;
        this.itemTypeName = itemTypeName;
        this.categoryName = categoryName;
        this.outpatientMedication = outpatientMedication;
        this.routeAdministration = routeAdministration;
        this.ruleNames = ruleNames;
    }
}
