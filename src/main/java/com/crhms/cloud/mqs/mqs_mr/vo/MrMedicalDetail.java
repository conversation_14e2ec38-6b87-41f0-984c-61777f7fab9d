package com.crhms.cloud.mqs.mqs_mr.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 住院/门诊审核费用明细-人工审核类
 *
 * <AUTHOR>
 * @since 2023-02-08 17:07:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
public class MrMedicalDetail implements Serializable {

    /**
     * 明细流水号
     */

    private String detailNo;
    /**
     * 就诊流水号
     */
    private String admissionNo;
    /**
     * 单据号
     */
    private String no;
    /**
     * 结算日期
     */
    private Date billDate;
    /**
     * 项目编码
     */
    private String itemId;
    /**
     * 项目名称
     */
    private String itemName;
    /**
     * 项目类型名称
     */

    private String ptype;
    /**
     * 项目类型编码(A-Z大类)
     */
    private String itemTypeCode;
    /**
     * 项目类型名称
     */
    private String itemTypeName;
    /**
     * 项目日期
     */
    private Date itemDate;
    /**
     * 数量
     */
    private BigDecimal numbers;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 包装单位
     */
    private String usageUnit;
    /**
     * 总费用
     */
    private BigDecimal costs;
    /**
     * 计费数量
     */
    private BigDecimal costNumber;
    /**
     * 计费金额
     */
    private BigDecimal costCosts;
    /**
     * 医保内金额
     */
    private String bmiConveredAmount;
    /**
     * 医保统筹金额
     */
    private String bmiOverallAmount;
    /**
     * 规格
     */
    private String specification;
    /**
     * 每次用量
     */
    private String usage;
    /**
     * 用药天数
     */
    private String usageDays;
    /**
     * 开单医生编码
     */
    private String applyDoctorCode;
    /**
     * 开单医生名称
     */
    private String applyDoctorName;
    /**
     * 开单医生级别
     */
    private String applyDoctorLevel;
    /**
     * 开单科室编码
     */
    private String applyDeptCode;
    /**
     * 开单科室名称
     */
    private String applyDeptName;
    /**
     * 受单医生编码
     */
    private String execDoctorCode;
    /**
     * 受单医生名称
     */
    private String execDoctorName;
    /**
     * 受单科室编码
     */
    private String execDeptCode;
    /**
     * 受单科室名称
     */
    private String execDeptName;
    /**
     * 住院医师编码
     */
    private String rtDoctorCode;
    /**
     * 住院医师名称
     */
    private String rtDoctorName;
    /**
     * 责任护士编码
     */
    private String rpNurseCode;
    /**
     * 责任护士名称
     */
    private String rpNurseName;
    /**
     * 计费标记
     */
    @DictTranslate(dictCode = "LOCAL_YN_CODE")
    private String chargingFlag;
    /**
     * 是否自费
     */
    @DictTranslate(dictCode = "LOCLA_SELF_EXPENSE", dictText = "selfExpenseCh")
    private String selfExpense;
    private String selfExpenseCh;
    /**
     * 使用频次
     */
    @DictTranslate(dictCode = "DIC_FREQUENCY_INTERVAL")
    private String frequencyInterval;
    /**
     * 备案审批号
     */
    private String approvalNumber;
    /**
     * 医师行政职务
     */
    private String zPhysicianap;
    /**
     * 帖数
     */
    private String postsNumber;
    /**
     * 自付比例
     */
    private BigDecimal payRatio;
    /**
     * 出国带药标志
     */
    private String abroadDrugFlag;
    /**
     * 外院费用标志
     */
    private String outHospitalFlag;
    /**
     * 是否违规
     */
    @DictTranslate(dictCode = "LOCAL_VIOLATION_FLAG", dictText = "violationFlagCh")
    private String violationFlag;
    private String violationFlagCh;
    /**
     * 违规结果类型最高违规级别)
     */
    private String ruleType;

    /**
     * 违规名称
     */
    private String ruleTypeName;

    /**
     * 违规编码集
     */
    private String ruleCodes;
    /**
     * 违规名称集
     */
    private String ruleNames;
    /**
     * 违规原因集
     */
    private String ruleReasons;
    /**
     * 反馈类型集
     */
    private String reasonTypes;
    /**
     * 反馈原因集
     */
    private String reasonDess;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 院区id
     */
    private String hospitalId;
    /**
     * 备注字段
     */
    private String mrOpinion;
    /**
     * 备注字段
     */
    private String memo;

    /**
     * 审核渠道
     */
    @DictTranslate(dictCode = "LOCLA_AUDITSOURCE", dictText = "auditSourceName")
    private String auditSource;

    private String auditSourceName;
    /**
     * 临床处理状态
     */
    @DictTranslate(dictCode = "LOCLA_CLINICALSTATUS")
    private String clinicalStatus;

    /**
     * 明细个数
     */
    private String detailNum;

    /**
     * 甲乙类
     */
    private String categoryName;

    /**
     * 是否出院带药
     */
    @DictTranslate(dictCode = "LOCAL_YN_CODE")
    private String outpatientMedication;
    /**
     * 违规类型集
     */
    @DictTranslate(dictCode = "LOCAL_RULE_ORIGIN", dictSplit = ";")
    private String ruleOrigin;

    /**
     * 用药途径
     */
    private String routeAdministration;

    /*
    *  同一分组下的明细（人工审核-明细查询-分组）
    * */
    private List<MrMedicalDetail> child;

    private String relatedRecords;


}

