package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.ItemDrugTcms;
import com.crhms.cloud.mqs.mqs_kbm.mapper.ItemDrugTcmsMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 知识库管理-医保项目目录-国家医保药品分类与代码(中药饮片) service
 *
 * @author: wangxingang
 * @date: 2023/02/03
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class ItemDrugTcmsService extends ServiceImpl<ItemDrugTcmsMapper, ItemDrugTcms> {
    /**
     * 查询列表 - 国家医保药品分类与代码(中药饮片)
     *
     * @param pageInfo             分页信息
     * @param tcmsCode             中药饮片代码
     * @param tcmsName             中药饮片名称
     * @param medMaterialName      药材名称
     * @param efficacyClassifyList 功效分类
     * @param funcIndicate         功能与主治
     */
    public List<ItemDrugTcms> queryList(Page<ItemDrugTcms> pageInfo, String tcmsCode, String tcmsName, String medMaterialName, List<String> efficacyClassifyList, String funcIndicate) {
        return baseMapper.selectPage(pageInfo, new LambdaQueryWrapper<ItemDrugTcms>()
                .like(StrUtil.isNotEmpty(tcmsCode), ItemDrugTcms::getTcmsCode, tcmsCode)
                .like(StrUtil.isNotEmpty(tcmsName), ItemDrugTcms::getTcmsName, tcmsName)
                .like(StrUtil.isNotEmpty(medMaterialName), ItemDrugTcms::getMedMaterialName, medMaterialName)
                .in(CollUtil.isNotEmpty(efficacyClassifyList), ItemDrugTcms::getEfficacyClassify, efficacyClassifyList)
                .like(StrUtil.isNotEmpty(funcIndicate), ItemDrugTcms::getFuncIndicate, funcIndicate)
        ).getRecords();
    }

    /**
     * 查询功效分类下拉列表
     */
    public List<String> queryEfficacyClassifyList() {
        return baseMapper.selectEfficacyClassifyList();
    }
}
