package com.crhms.cloud.mqs.mqs_mr.controller;


import com.crhms.cloud.mqs.mqs_mr.service.MrPlanService;
import com.crhms.cloud.mqs.mqs_mr.vo.MrPlanVO;
import com.crhms.cloud.mqs.sys.dto.IppUser;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 人工审核流程配置表(MrPlan)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-06 14:35:12
 */
@RestController
@RequestMapping("api/mqs/mrPlan")
public class MrPlanController {
    /**
     * 服务对象
     */
    @Resource
    private MrPlanService mrPlanService;


    /**
     * 查询审核流程配置表
     *
     * @param
     * @return 查询结果
     */
    @GetMapping("/queryList")
    public ResponseEntity<MrPlanVO> queryList() {
        return ResponseEntity.ok(mrPlanService.queryList());
    }

    /**
     * 编辑/新增
     */
    @PostMapping("/edit")
    public ResponseEntity edit(@RequestBody MrPlanVO mrPlanVO) {
        mrPlanService.submit(mrPlanVO);
        return ResponseEntity.ok("操作成功！");
    }

    /**
     * 恢复默认
     */
    @GetMapping("/restDefulat")
    public ResponseEntity restoreDefault() {
        this.mrPlanService.restoreDefault();
        return ResponseEntity.ok("操作成功！");
    }


    /**
     * 查询拥有某个菜单权限的用户信息
     */
    @GetMapping("/queryAuditPerson")
    public ResponseEntity<List<IppUser>> queryAuditPerson(String menuCode) {
        return ResponseEntity.ok(mrPlanService.queryAuditPerson(menuCode));
    }


}

