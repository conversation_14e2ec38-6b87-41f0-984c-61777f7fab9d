package com.crhms.cloud.mqs.sys.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.*;

/**
 * 规则与规则大类对应关系实体类
 *
 * @author: wangxingang
 * @date: 2023.02.24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("mqs_sys_rule_broad_heading")
public class SysRuleBroadHeading extends BaseDomain {

    @TableId
    Long id;

    /**
     * 规则编码
     */
    @TableField
    private String ruleCode;

    /**
     * 规则大类
     */
    @DictTranslate(dictCode = "MQS_RULE_BROAD_HEADING")
    @TableField
    private String broadHeading;

    /**
     * 院区ID
     */
    @TableField
    private String hospitalId;
}
