package com.crhms.cloud.mqs.mqs_bmi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFb;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFbTemp;
import com.crhms.cloud.mqs.mqs_bmi.service.BmiFbService;
import com.crhms.cloud.mqs.mqs_bmi.vo.BmiImportVo;
import com.crhms.cloud.mqs.mqs_bmi.vo.BmiQueryVo;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 医保管理-导入分配控制层
 *
 * <AUTHOR>
 * @since 2023-02-10 10:40:08
 */
@RestController
@RequestMapping("api/mqs/bmiFbImp")
public class BmiImpAllotController {
    /**
     * 服务对象
     */
    @Resource
    private BmiFbService bmiFbService;


    /**
     * 医保导入分页查询接口
     *
     * @param bmiQueryVo
     * @return
     */
    @PostMapping("/queryByPage")
    public ResponseEntity<List<BmiFb>> queryByPage(@RequestBody BmiQueryVo bmiQueryVo) {

        Page myPage = PageUtil.getPage(bmiQueryVo.getPage(), bmiQueryVo.getPageSize());

        return new ResponseEntity(this.bmiFbService.queryByPage(bmiQueryVo, LoginContext.getHospitalId(), myPage), PageUtil.getTotalHeader(myPage), HttpStatus.OK);
    }

    /**
     * 批量分配到科室
     *
     * @param deptCode
     * @param deptName
     * @param nos
     * @return
     */
    @PostMapping("/allotToDept")
    public ResponseEntity allotToDept(@RequestParam(value = "deptCode") String deptCode,
                                      @RequestParam(value = "deptName") String deptName,
                                      @RequestBody List<String> nos) {
        this.bmiFbService.allotToDept(deptCode, deptName, nos, LoginContext.getHospitalId());
        return ResponseEntity.ok("分配科室成功！");
    }

    /**
     * 一键分配到科室
     *
     * @param billDateFrom
     * @param billDateTo
     * @param tag 1.入院 2 出院
     * @return
     */
    @GetMapping("/autoAllotToDept")
    public ResponseEntity<Map> autoAllotToDept(@RequestParam(value = "billDateFrom") String billDateFrom,
                                          @RequestParam(value = "billDateTo") String billDateTo,
                                          @RequestParam(value = "tag") String tag) {
        return ResponseEntity.ok(this.bmiFbService.autoAllotToDept(billDateFrom, billDateTo,tag, LoginContext.getHospitalId()));
    }


    @DeleteMapping("/delete")
    public ResponseEntity deleteBmiFbImp(@RequestBody List<Long> ids) {
        this.bmiFbService.deleteBmiFbImp(ids, LoginContext.getHospitalId());
        return ResponseEntity.ok("删除成功！");
    }

    /**
     * 医保数据导入Excel
     */
    @PostMapping("import")
    public ResponseEntity<Map> importFbExcel(BmiImportVo importVo) {
        return ResponseEntity.ok(this.bmiFbService.importFbExcel(importVo, LoginContext.getHospitalId()));
    }

    /**
     * 查询导入结果统计
     *
     * @param batchNo
     * @return
     */
    @GetMapping("import/queryTotal")
    public ResponseEntity<Map> queryImportTotal(@RequestParam(value = "batchNo") String batchNo) {
        return ResponseEntity.ok(this.bmiFbService.queryImportTotal(batchNo, LoginContext.getHospitalId()));
    }

    /**
     * 查询导入结果列表
     *
     * @param batchNo
     * @return
     */
    @GetMapping("import/queryByPage")
    public ResponseEntity<List<BmiFbTemp>> queryImportByPage(@RequestParam(value = "batchNo") String batchNo,
                                                             @RequestParam(value = "type", required = false) String type,
                                                             @RequestParam(value = "page") int page,
                                                             @RequestParam(value = "pageSize") int pageSize) {
        Page myPage = PageUtil.getPage(page, pageSize);

        return new ResponseEntity(this.bmiFbService.queryImportByPage(batchNo, type, LoginContext.getHospitalId(), myPage), PageUtil.getTotalHeader(myPage), HttpStatus.OK);
    }

    /**
     * 查询导入结果列表 - 不分页
     *
     * @param batchNo
     * @return
     */
    @GetMapping("import/queryNoPage")
    public ResponseEntity<List<BmiFbTemp>> queryImportNoPage(@RequestParam(value = "batchNo") String batchNo,
                                                             @RequestParam(value = "type", required = false) String type) {

        return ResponseEntity.ok(this.bmiFbService.queryImportNoPage(batchNo, type, LoginContext.getHospitalId()));
    }

    /**
     * 载入数据至 医保反馈
     *
     * @param batchNo
     * @return
     */
    @PostMapping("import/loadIn")
    public ResponseEntity loadIn(@RequestParam(value = "batchNo") String batchNo, @RequestBody List<String> ignoreNos) {
        this.bmiFbService.loadIn(batchNo, ignoreNos, LoginContext.getHospitalId());
        return ResponseEntity.ok("导入成功！");
    }


    /**
     * 医保数据导入模板导出
     */
    @GetMapping("/template/export")
    public ResponseEntity exportTemplate(HttpServletResponse response) {
        this.bmiFbService.exportExcel(response);
        return ResponseEntity.ok("已导出模板!");
    }



    /**
     * 导出"导入分发"
     *
     * @param response   响应
     * @param bmiQueryVo 查询条件
     */
    @PostMapping("/export/imp")
    public void exportImp(HttpServletResponse response, @RequestBody BmiQueryVo bmiQueryVo) {
        bmiFbService.exportImp(response, bmiQueryVo);
    }



    /**
     * 导出审核申诉
     *
     * @param response   响应
     * @param bmiQueryVo 查询条件
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody BmiQueryVo bmiQueryVo) {
        bmiFbService.export(response, bmiQueryVo);
    }

}

