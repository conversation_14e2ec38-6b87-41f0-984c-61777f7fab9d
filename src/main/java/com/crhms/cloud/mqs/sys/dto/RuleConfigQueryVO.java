package com.crhms.cloud.mqs.sys.dto;

import lombok.Data;

import java.util.List;

/**
 * 规则设置页面 分页查询参数
 *
 * @author: wangxingang
 * @date: 2023.02.22
 */
@Data
public class RuleConfigQueryVO {

    /**
     * 规则编码/名称模糊查询
     */
    private String rule;

    /**
     * 规则级别(多选)
     */
    private List<String> ruleLevelList;

    /**
     * 规则大类(多选)
     */
    private List<String> broadHeadingList;

    /**
     * 是否查询历史数据(多选)
     */
    private List<String> isHisList;

    /**
     * 是否作用在主单(多选)
     */
    private List<String> isMainList;

    /**
     * 规则来源
     * 1 自定义规则/系统
     * 2 审核引擎规则/统筹区
     * 3 省平台规则
     * 4 医院自定义
     */
    private String ruleType;

    /**
     * 规则一级分类
     */
    private String primaryCategory;
    /**
     * 规则二级分类
     */
    private String secondaryCategory;


    /**
     * 分页信息
     */
    private Integer page;
    private Integer pageSize;
}
