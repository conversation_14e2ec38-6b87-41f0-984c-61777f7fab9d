package com.crhms.cloud.mqs.basic.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.crhms.cloud.core.excel.dto.ExcelExport;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.basic.vo.BaseCaseQueryVO;
import com.crhms.cloud.mqs.basic.vo.BaseDetailQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 多场景审核 服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 09:48:16
 */
@Service("BaseMedicalServiceExport")
public class BaseMedicalServiceExport {
    @Autowired
    private BaseMedicalService baseMedicalService;

    /**
     * 导出excel
     *
     * @param response 响应
     * @param queryVO  筛选条件
     */
    public void exportExcel(HttpServletResponse response, BaseCaseQueryVO queryVO) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        // 查询数据
        List<BaseMedicalCase> list = baseMedicalService.queryCaseListByPage(queryVO, null);

        // 文件名称: 门诊审核-挂号审核(20230220_20230320)-20230320100744
        StringBuilder fileName = new StringBuilder();
        // 组装数据
        switch (queryVO.getAuditScenario()) {
            case "opPt":
                // 门诊缴费审核
                exportOpPt(heads, datas, list);
                fileName.append("门诊审核-缴费审核");
                break;
            case "opPct":
                // 门诊处方审核
                exportOpPct(heads, datas, list);
                fileName.append("门诊审核-处方审核");
                break;
            case "opRg":
                // 门诊挂号审核
                exportOpRg(heads, datas, list);
                fileName.append("门诊审核-挂号审核");
                break;
            case "hpRg":
                // 住院登记审核
                exportHpRg(heads, datas, list);
                fileName.append("住院审核-住院登记审核");
                break;
            case "hpDo":
                // 医嘱审核
                exportHpDo(heads, datas, list);
                fileName.append("住院审核-医嘱审核");
                break;
            case "hpBk":
                // 每日记账审核
                exportHpBk(heads, datas, list);
                fileName.append("住院审核-每日记账审核");
                break;
            case "hpTf":
                // 转科室审核
                exportHpTf(heads, datas, list);
                fileName.append("住院审核-转科室审核");
                break;
            case "hpPred":
                // 预出院审核
                exportHpPred(heads, datas, list);
                fileName.append("住院审核-预出院审核");
                break;
            case "hpOut":
                // 出院审核
                exportHpOut(heads, datas, list);
                fileName.append("住院审核-出院审核");
                break;
            case "hpSettle":
                // 结算审核
                exportSettle(heads, datas, list);
                fileName.append("住院审核-结算审核");
                break;
            default:
                throw new BaseException("不存在的审核场景！");
        }

        MqsUtils.buildExportFileNameSuffix(fileName, queryVO.getAdmissionDateFrom(), queryVO.getAdmissionDateTo());


        // 导出excel
        List<ExcelExport> exports = new ArrayList<>();
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);


    }


    /**
     * 门诊缴费审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportOpPt(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 表头
        heads.add(CollectionUtil.list(true, "VIOLATION_FLAG", "AUDIT_TIME", "NO", "NO", "BENEFIT_TYPE_ID", "PERSONNEL_TYPE",
                "CLAIM_TYPE_ID", "ITEM_DATE", "PATIENT_ID", "PATIENT_NAME", "PATIENT_GENDER", "PATIENT_BIRTHDAY", "OUT_DIAGNOSIS_CODE", "OUT_DIAGNOSIS_NAME", "SECONDARY_DISEASE_ID",
                "SECONDARY_DISEASE_ZH", "TOTAL_AMOUNT", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT", "DEPT_NAME", "DOC_NAME")
        );
        heads.add(CollectionUtil.list(true, "是否违规", "审核时间", "单据号", "门诊号", "参保类型", "人员类别",
                "医疗类别", "就诊日期", "参保人编码", "参保人姓名", "性别", "出生日期", "门诊主诊断编码", "门诊主诊断名称", "门诊次诊断编码",
                "门诊次诊断名称", "总金额", "医保内金额", "医保统筹金额", "开单科室", "开单医生"));

        // 数据
        for (BaseMedicalCase item : list) {
            datas.add(CollectionUtil.list(true, item.getViolationFlag(), DateUtil.format(item.getAuditTime(), "yyyy-MM-dd HH:mm:ss"), item.getNo(), item.getNo(), item.getBenefitTypeId(), item.getPersonnelType(),
                    item.getClaimTypeId(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"), item.getPatientId(), item.getPatientName(), item.getPatientGender(), item.getPatientBirthday(), item.getOutDiagnosisCode(), item.getOutDiagnosisName(), item.getSecondaryDiseaseId(),
                    item.getSecondaryDiseaseZh(), item.getTotalAmount(), item.getBmiConveredAmount(), item.getBmiOverallAmount(), item.getDeptName(), item.getDocName())
            );
        }
    }

    /**
     * 门诊处方审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportOpPct(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 表头
        heads.add(CollectionUtil.list(true, "VIOLATION_FLAG", "AUDIT_TIME", "NO", "NO", "AUDIT_NUMS", "BENEFIT_TYPE_ID", "PERSONNEL_TYPE",
                "CLAIM_TYPE_ID", "ITEM_DATE", "PATIENT_ID", "PATIENT_NAME", "PATIENT_GENDER", "PATIENT_BIRTHDAY", "OUT_DIAGNOSIS_CODE", "OUT_DIAGNOSIS_NAME", "SECONDARY_DISEASE_ID",
                "SECONDARY_DISEASE_ZH", "TOTAL_AMOUNT", "CHARGING_FLAG", "DEPT_NAME", "DOC_NAME")
        );
        heads.add(CollectionUtil.list(true, "是否违规", "审核时间", "单据号", "门诊号", "审核次数", "参保类型", "人员类别",
                "医疗类别", "就诊时间", "参保人编码", "参保人姓名", "性别", "出生日期", "门诊主诊断编码", "门诊主诊断名称", "门诊次诊断编码",
                "门诊次诊断名称", "总金额", "是否已计费", "开单科室", "开单医生"));


        // 数据
        for (BaseMedicalCase item : list) {
            datas.add(CollectionUtil.list(true, item.getViolationFlag(), DateUtil.format(item.getAuditTime(), "yyyy-MM-dd HH:mm:ss"), item.getNo(), item.getNo(), item.getAuditNums(), item.getBenefitTypeId(), item.getPersonnelType(),
                    item.getClaimTypeId(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"), item.getPatientId(), item.getPatientName(), item.getPatientGender(), item.getPatientBirthday(), item.getOutDiagnosisCode(), item.getOutDiagnosisName(), item.getSecondaryDiseaseId(),
                    item.getSecondaryDiseaseZh(), item.getTotalAmount(), item.getChargingFlag(), item.getDeptName(), item.getDocName())
            );
        }

    }

    /**
     * 门诊挂号审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportOpRg(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 表头
        heads.add(CollectionUtil.list(true,
                "VIOLATION_FLAG", "AUDIT_TIME", "NO", "ADMISSION_NO", "REGISTER_NUMS", "BENEFIT_TYPE_ID", "PERSONNEL_TYPE", "CLAIM_TYPE_ID", "ITEM_DATE",
                "PATIENT_ID", "PATIENT_NAME", "PATIENT_GENDER", "PATIENT_BIRTHDAY","TOTAL_AMOUNT", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT", "PAY_AMOUNT", "REGISTER_DOC_NAME"));
        heads.add(CollectionUtil.list(true,
                "是否违规", "审核时间", "单据号", "门诊号", "当日累计挂号次数", "参保类型", "人员类别", "医疗类别", "就诊日期",
                "参保人编码", "参保人姓名", "性别", "出生日期","总金额", "医保内金额", "医保统筹金额", "自付金额", "挂号医生"));

        // 数据
        for (BaseMedicalCase item : list) {
            datas.add(CollectionUtil.list(true,
                    item.getViolationFlag(), DateUtil.format(item.getAuditTime(), "yyyy-MM-dd HH:mm:ss"), item.getNo(), item.getAdmissionNo(), item.getRegisterNums(), item.getBenefitTypeId(), item.getPersonnelType(), item.getClaimTypeId(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"),
                    item.getPatientId(), item.getPatientName(), item.getPatientGender(), item.getPatientBirthday(), item.getTotalAmount(), item.getBmiConveredAmount(), item.getBmiOverallAmount(), item.getPayAmount(), item.getRegisterDocName()));
        }
    }

    /**
     * 住院登记审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpRg(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 表头
        heads.add(CollectionUtil.list(true, "VIOLATION_FLAG", "AUDIT_TIME", "NO", "ADMISSION_NO", "OUT_ZONE_NAME", "BENEFIT_TYPE_ID", "PERSONNEL_TYPE",
                "IS_DISCHARGE", "CLAIM_TYPE_ID", "PATIENT_ID", "PATIENT_NAME", "PATIENT_GENDER", "PATIENT_BIRTHDAY", "DOC_NAME", "DEPT_NAME", "IN_DIAGNOSIS_CODE", "IN_DIAGNOSIS_NAME",
                "RULE_NAMES", "RULE_REASONS", "ADMISSION_DATE"));
        heads.add(CollectionUtil.list(true, "是否违规", "审核时间", "单据号", "住院号", "病区", "参保类型", "人员类别",
                "住院状态", "医疗类别", "参保人编码", "参保人姓名", "性别", "出生日期", "医生", "科室", "入院诊断编码", "入院诊断名称",
                "规则名称", "违规原因", "入院日期"));

        // 数据
        for (BaseMedicalCase item : list) {
            datas.add(CollectionUtil.list(true, item.getViolationFlag(), DateUtil.format(item.getAuditTime(), "yyyy-MM-dd HH:mm:ss"), item.getNo(), item.getAdmissionNo(), item.getOutZoneName(), item.getBenefitTypeId(), item.getPersonnelType(),
                    item.getIsDischarge(), item.getClaimTypeId(), item.getPatientId(), item.getPatientName(), item.getPatientGender(), item.getPatientBirthday(), item.getDocName(), item.getDeptName(), item.getInDiagnosisCode(), item.getInDiagnosisName(),
                    item.getRuleNames(), item.getRuleReasons(), DateUtil.format(item.getAdmissionDate(), "yyyy-MM-dd HH:mm:ss")));
        }
    }

    /**
     * 医嘱审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpDo(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 表头
        heads.add(CollectionUtil.list(true, "VIOLATION_FLAG", "AUDIT_TIME", "NO", "ADMISSION_NO", "OUT_ZONE_NAME", "BENEFIT_TYPE_ID", "PERSONNEL_TYPE",
                "IS_DISCHARGE", "CLAIM_TYPE_ID", "PATIENT_ID", "PATIENT_NAME", "PATIENT_GENDER", "PATIENT_BIRTHDAY", "IN_DIAGNOSIS_CODE", "IN_DIAGNOSIS_NAME", "OUT_DIAGNOSIS_CODE", "OUT_DIAGNOSIS_NAME", "SECONDARY_DISEASE_ID", "SECONDARY_DISEASE_ZH",
                "ADMISSION_DATE", "TOTAL_AMOUNT", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT"));
        heads.add(CollectionUtil.list(true, "是否违规", "审核时间", "单据号", "住院号", "病区", "参保类型", "人员类别",
                "住院状态", "医疗类别", "参保人编码", "参保人姓名", "性别", "出生日期", "入院诊断编码", "入院诊断名称", "主诊断编码", "主诊断名称", "次诊断编码", "次诊断名称",
                "入院日期", "总金额", "医保内金额", "医保统筹金额"));

        // 数据
        for (BaseMedicalCase item : list) {
            datas.add(CollectionUtil.list(true, item.getViolationFlag(), DateUtil.format(item.getAuditTime(), "yyyy-MM-dd HH:mm:ss"), item.getNo(), item.getAdmissionNo(), item.getOutZoneName(), item.getBenefitTypeId(), item.getPersonnelType(),
                    item.getIsDischarge(), item.getClaimTypeId(), item.getPatientId(), item.getPatientName(), item.getPatientGender(), item.getPatientBirthday(), item.getInDiagnosisCode(), item.getInDiagnosisName(), item.getOutDiagnosisCode(), item.getOutDiagnosisName(), item.getSecondaryDiseaseId(), item.getSecondaryDiseaseZh(),
                    DateUtil.format(item.getAdmissionDate(), "yyyy-MM-dd HH:mm:ss"), item.getTotalAmount(), item.getBmiConveredAmount(), item.getBmiOverallAmount()));
        }

    }

    /**
     * 每日记账审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpBk(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 和医嘱审核一样
        exportHpDo(heads, datas, list);
    }

    /**
     * 转科室审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpTf(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 表头
        heads.add(CollectionUtil.list(true, "VIOLATION_FLAG", "AUDIT_TIME", "NO", "ADMISSION_NO", "OUT_ZONE_NAME", "BENEFIT_TYPE_ID", "PERSONNEL_TYPE",
                "IS_DISCHARGE", "CLAIM_TYPE_ID", "PATIENT_ID", "PATIENT_NAME", "PATIENT_GENDER", "PATIENT_BIRTHDAY", "IN_DIAGNOSIS_CODE", "IN_DIAGNOSIS_NAME", "OUT_DIAGNOSIS_CODE", "OUT_DIAGNOSIS_NAME", "SECONDARY_DISEASE_ID", "SECONDARY_DISEASE_ZH",
                "TF_FROM_DEPT_NAME", "TF_FROM_DOC_NAME", "TF_TO_DEPT_NAME", "TF_TO_DOC_NAME",
                "ADMISSION_DATE", "TF_DATE", "FEE_DATE", "TOTAL_AMOUNT", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT"));
        heads.add(CollectionUtil.list(true, "是否违规", "审核时间", "单据号", "住院号", "病区", "参保类型", "人员类别",
                "住院状态", "医疗类别", "参保人编码", "参保人姓名", "性别", "出生日期", "入院诊断编码", "入院诊断名称", "主诊断编码", "主诊断名称", "次诊断编码", "次诊断名称",
                "转出科室", "转出责任医生", "转入科室", "转入责任医生",
                "入院日期", "转科日期", "收费日期", "总金额", "医保内金额", "医保统筹金额"));

        // 数据
        for (BaseMedicalCase item : list) {
            datas.add(CollectionUtil.list(true, item.getViolationFlag(), DateUtil.format(item.getAuditTime(), "yyyy-MM-dd HH:mm:ss"), item.getNo(), item.getAdmissionNo(), item.getOutZoneName(), item.getBenefitTypeId(), item.getPersonnelType(),
                    item.getIsDischarge(), item.getClaimTypeId(), item.getPatientId(), item.getPatientName(), item.getPatientGender(), item.getPatientBirthday(), item.getInDiagnosisCode(), item.getInDiagnosisName(), item.getOutDiagnosisCode(), item.getOutDiagnosisName(), item.getSecondaryDiseaseId(), item.getSecondaryDiseaseZh(),
                    item.getTfFromDeptName(), item.getTfFromDocName(), item.getTfToDeptName(), item.getTfToDocName(),
                    DateUtil.format(item.getAdmissionDate(), "yyyy-MM-dd HH:mm:ss"), DateUtil.format(item.getTfDate(), "yyyy-MM-dd HH:mm:ss"), DateUtil.format(item.getFeeDate(), "yyyy-MM-dd HH:mm:ss"), item.getTotalAmount(), item.getBmiConveredAmount(), item.getBmiOverallAmount()));
        }

    }

    /**
     * 预出院审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpPred(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 表头
        heads.add(CollectionUtil.list(true, "VIOLATION_FLAG", "AUDIT_TIME", "NO", "ADMISSION_NO", "OUT_ZONE_NAME", "BENEFIT_TYPE_ID", "PERSONNEL_TYPE",
                "IS_DISCHARGE", "CLAIM_TYPE_ID", "PATIENT_ID", "PATIENT_NAME", "PATIENT_GENDER", "PATIENT_BIRTHDAY", "IN_DIAGNOSIS_CODE", "IN_DIAGNOSIS_NAME", "OUT_DIAGNOSIS_CODE", "OUT_DIAGNOSIS_NAME", "SECONDARY_DISEASE_ID", "SECONDARY_DISEASE_ZH", "OUT_DIAGNOSIS_CODE", "OUT_DIAGNOSIS_NAME",
                "ADMISSION_DATE", "TOTAL_AMOUNT", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT"));
        heads.add(CollectionUtil.list(true, "是否违规", "审核时间", "单据号", "住院号", "病区", "参保类型", "人员类别",
                "住院状态", "医疗类别", "参保人编码", "参保人姓名", "性别", "出生日期", "入院诊断编码", "入院诊断名称", "主诊断编码", "主诊断名称", "次诊断编码", "次诊断名称", "出院诊断编码", "出院诊断名称",
                "入院日期", "总金额", "医保内金额", "医保统筹金额"));

        // 数据
        for (BaseMedicalCase item : list) {
            datas.add(CollectionUtil.list(true, item.getViolationFlag(), DateUtil.format(item.getAuditTime(), "yyyy-MM-dd HH:mm:ss"), item.getNo(), item.getAdmissionNo(), item.getOutZoneName(), item.getBenefitTypeId(), item.getPersonnelType(),
                    item.getIsDischarge(), item.getClaimTypeId(), item.getPatientId(), item.getPatientName(), item.getPatientGender(), item.getPatientBirthday(), item.getInDiagnosisCode(), item.getInDiagnosisName(), item.getOutDiagnosisCode(), item.getOutDiagnosisName(), item.getSecondaryDiseaseId(), item.getSecondaryDiseaseZh(), item.getOutDiagnosisCode(), item.getOutDiagnosisName(),
                    DateUtil.format(item.getAdmissionDate(), "yyyy-MM-dd HH:mm:ss"), item.getTotalAmount(), item.getBmiConveredAmount(), item.getBmiOverallAmount()));
        }

    }

    /**
     * 出院审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportHpOut(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 表头
        heads.add(CollectionUtil.list(true, "VIOLATION_FLAG", "AUDIT_TIME", "NO", "ADMISSION_NO", "OUT_ZONE_NAME", "BENEFIT_TYPE_ID", "PERSONNEL_TYPE",
                "IS_DISCHARGE", "CLAIM_TYPE_ID", "PATIENT_ID", "PATIENT_NAME", "PATIENT_GENDER", "PATIENT_BIRTHDAY", "IN_DIAGNOSIS_CODE", "IN_DIAGNOSIS_NAME", "OUT_DIAGNOSIS_CODE", "OUT_DIAGNOSIS_NAME", "SECONDARY_DISEASE_ID", "SECONDARY_DISEASE_ZH", "OUT_DIAGNOSIS_CODE", "OUT_DIAGNOSIS_NAME",
                "ADMISSION_DATE", "DISCHARGE_DATE", "TOTAL_AMOUNT", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT"));
        heads.add(CollectionUtil.list(true, "是否违规", "审核时间", "单据号", "住院号", "病区", "参保类型", "人员类别",
                "住院状态", "医疗类别", "参保人编码", "参保人姓名", "性别", "出生日期", "入院诊断编码", "入院诊断名称", "主诊断编码", "主诊断名称", "次诊断编码", "次诊断名称", "出院诊断编码", "出院诊断名称",
                "入院日期", "出院日期", "总金额", "医保内金额", "医保统筹金额"));

        // 数据
        for (BaseMedicalCase item : list) {
            datas.add(CollectionUtil.list(true, item.getViolationFlag(), DateUtil.format(item.getAuditTime(), "yyyy-MM-dd HH:mm:ss"), item.getNo(), item.getAdmissionNo(), item.getOutZoneName(), item.getBenefitTypeId(), item.getPersonnelType(),
                    item.getIsDischarge(), item.getClaimTypeId(), item.getPatientId(), item.getPatientName(), item.getPatientGender(), item.getPatientBirthday(), item.getInDiagnosisCode(), item.getInDiagnosisName(), item.getOutDiagnosisCode(), item.getOutDiagnosisName(), item.getSecondaryDiseaseId(), item.getSecondaryDiseaseZh(), item.getOutDiagnosisCode(), item.getOutDiagnosisName(),
                    DateUtil.format(item.getAdmissionDate(), "yyyy-MM-dd HH:mm:ss"), DateUtil.format(item.getDischargeDate(), "yyyy-MM-dd HH:mm:ss"), item.getTotalAmount(), item.getBmiConveredAmount(), item.getBmiOverallAmount()));
        }

    }

    /**
     * 结算审核
     *
     * @param heads 表头
     * @param datas 数据
     * @param list  查询出的数据
     */
    private void exportSettle(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalCase> list) {
        // 表头
        heads.add(CollectionUtil.list(true, "VIOLATION_FLAG", "AUDIT_TIME", "NO", "ADMISSION_NO", "OUT_ZONE_NAME", "BENEFIT_TYPE_ID", "PERSONNEL_TYPE",
                "IS_DISCHARGE", "CLAIM_TYPE_ID", "PATIENT_ID", "PATIENT_NAME", "PATIENT_GENDER", "PATIENT_BIRTHDAY", "IN_DIAGNOSIS_CODE", "IN_DIAGNOSIS_NAME", "OUT_DIAGNOSIS_CODE", "OUT_DIAGNOSIS_NAME", "SECONDARY_DISEASE_ID", "SECONDARY_DISEASE_ZH", "OUT_DIAGNOSIS_CODE", "OUT_DIAGNOSIS_NAME",
                "ADMISSION_DATE", "DISCHARGE_DATE", "BILL_DATE", "TOTAL_AMOUNT", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT"));
        heads.add(CollectionUtil.list(true, "是否违规", "审核时间", "单据号", "住院号", "病区", "参保类型", "人员类别",
                "住院状态", "医疗类别", "参保人编码", "参保人姓名", "性别", "出生日期", "入院诊断编码", "入院诊断名称", "主诊断编码", "主诊断名称", "次诊断编码", "次诊断名称", "出院诊断编码", "出院诊断名称",
                "入院日期", "出院日期", "结算日期", "总金额", "医保内金额", "医保统筹金额"));

        // 数据
        for (BaseMedicalCase item : list) {
            datas.add(CollectionUtil.list(true, item.getViolationFlag(), DateUtil.format(item.getAuditTime(), "yyyy-MM-dd HH:mm:ss"), item.getNo(), item.getAdmissionNo(), item.getOutZoneName(), item.getBenefitTypeId(), item.getPersonnelType(),
                    item.getIsDischarge(), item.getClaimTypeId(), item.getPatientId(), item.getPatientName(), item.getPatientGender(), item.getPatientBirthday(), item.getInDiagnosisCode(), item.getInDiagnosisName(), item.getOutDiagnosisCode(), item.getOutDiagnosisName(), item.getSecondaryDiseaseId(), item.getSecondaryDiseaseZh(), item.getOutDiagnosisCode(), item.getOutDiagnosisName(),
                    DateUtil.format(item.getAdmissionDate(), "yyyy-MM-dd HH:mm:ss"), DateUtil.format(item.getDischargeDate(), "yyyy-MM-dd HH:mm:ss"), DateUtil.format(item.getBillDate(), "yyyy-MM-dd HH:mm:ss"), item.getTotalAmount(), item.getBmiConveredAmount(), item.getBmiOverallAmount()));
        }

    }


    /**
     * 导出费用明细
     *
     * @param response 响应
     * @param queryVO  筛选条件
     */
    public void exportDetail(HttpServletResponse response, BaseDetailQueryVO queryVO) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        // 查询数据
        List<BaseMedicalDetail> details = baseMedicalService.queryDetail(queryVO);

        // 文件名称: 门诊审核-挂号审核(20230220_20230320)-20230320100744
        StringBuilder fileName = new StringBuilder();

        // 组装数据
        switch (queryVO.getAuditScenario()) {
            case "opRg":
                // 门诊挂号审核明细
                exportOpRgDetail(heads, datas, details);
                fileName.append("门诊审核-挂号审核费用明细");
            case "opPt":
                // 门诊缴费审核明细
                exportOpPtDetail(heads, datas, details);
                fileName.append("门诊审核-缴费审核费用明细");
                break;
            case "opPct":
                // 门诊处方审核明细
                exportOpPctDetail(heads, datas, details);
                fileName.append("门诊审核-处方审核费用明细");
                break;
            case "hpDo":
                // 医嘱审核明细
                exportHpDoDetail(heads, datas, details);
                fileName.append("住院审核-医嘱审核费用明细");
                break;
            case "hpBk":
                // 每日记账审核明细
                exportHpBkDetail(heads, datas, details);
                fileName.append("住院审核-每日记账审核费用明细");
                break;
            case "hpTf":
                // 转科室审核明细
                exportHpTfDetail(heads, datas, details);
                fileName.append("住院审核-转科室审核费用明细");
                break;
            case "hpPred":
                // 预出院审核明细
                exportHpPredDetail(heads, datas, details);
                fileName.append("住院审核-预出院审核费用明细");
                break;
            case "hpOut":
                // 出院审核明细
                exportHpOutDetail(heads, datas, details);
                fileName.append("住院审核-出院审核费用明细");
                break;
            case "hpSettle":
                // 结算审核明细
                exportSettleDetail(heads, datas, details);
                fileName.append("住院审核-结算审核费用明细");
                break;
            default:
                throw new BaseException("不存在的审核场景！");
        }

        MqsUtils.buildExportFileNameSuffix(fileName, null, null);


        // 导出excel
        List<ExcelExport> exports = new ArrayList<>();
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);


    }

    private void exportOpRgDetail(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalDetail> details) {
        // 表头
        heads.add(CollectionUtil.list(true, "RULE_NAMES", "RULE_REASONS", "ITEM_ID", "ITEM_NAME", "ITEM_DATE", "APPLY_DEPT_NAME", "APPLY_DOCTOR_NAME", "REASON_TYPES", "REASON_DESS", "COSTS", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT", "PAY_AMOUNT"));
        heads.add(CollectionUtil.list(true, "规则名称", "违规原因", "项目编码", "项目名称", "挂号时间", "科室", "医生", "反馈类型", "反馈理由", "总金额", "医保内金额", "医保统筹金额", "自付金额"));

        // 数据
        for (BaseMedicalDetail item : details) {
            datas.add(CollectionUtil.list(true, item.getRuleNames(), item.getRuleReasons(), item.getItemId(), item.getItemName(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"), item.getApplyDeptName(), item.getApplyDoctorName(), item.getReasonTypes(), item.getReasonDess(), item.getCosts(), item.getBmiConveredAmount(), item.getBmiOverallAmount(), item.getPayAmount()));
        }
    }


    /**
     * 组装数据 - 结算审核明细
     *
     * @param heads   表头
     * @param datas   数据
     * @param details 查询出的数据
     */
    private void exportSettleDetail(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalDetail> details) {
        // 和出院审核明细一样
        exportHpOutDetail(heads, datas, details);
    }


    /**
     * 组装数据 - 出院审核明细
     *
     * @param heads   表头
     * @param datas   数据
     * @param details 查询出的数据
     */
    private void exportHpOutDetail(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalDetail> details) {
        // 表头
        heads.add(CollectionUtil.list(true, "RULE_NAMES", "RULE_REASONS", "ITEM_ID", "ITEM_NAME", "ITEM_TYPE_NAME", "ITEM_DATE",
                "APPLY_DOCTOR_NAME", "APPLY_DEPT_NAME", "EXEC_DOCTOR_NAME", "EXEC_DEPT_NAME", "RT_DOCTOR_NAME", "RP_NURSE_NAME",
                "SELF_EXPENSE", "REASON_TYPES", "REASON_DESS", "SPECIFICATION", "USAGE_UNIT", "USAGE", "PRICE", "NUMBERS", "COSTS", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT"));
        heads.add(CollectionUtil.list(true, "规则名称", "违规原因", "项目编码", "项目名称", "项目类型", "项目日期",
                "开单医生", "开单科室", "受单医生", "受单科室", "住院医师", "责任护士",
                "自费", "反馈类型", "反馈理由", "规格", "包装单位", "用量", "单价", "数量", "总金额", "医保内金额", "医保统筹金额"));

        // 数据
        for (BaseMedicalDetail item : details) {
            datas.add(CollectionUtil.list(true, item.getRuleNames(), item.getRuleReasons(), item.getItemId(), item.getItemName(), item.getItemTypeName(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"),
                    item.getApplyDoctorName(), item.getApplyDeptName(), item.getExecDoctorName(), item.getExecDeptName(), item.getRtDoctorName(), item.getRpNurseName(),
                    item.getSelfExpense(), item.getReasonTypes(), item.getReasonDess(), item.getSpecification(), item.getUsageUnit(), item.getUsage(), item.getPrice(), item.getNumbers(), item.getCosts(), item.getBmiConveredAmount(), item.getBmiOverallAmount()));
        }
    }


    /**
     * 组装数据 - 预出院审核明细
     *
     * @param heads   表头
     * @param datas   数据
     * @param details 查询出的数据
     */
    private void exportHpPredDetail(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalDetail> details) {
        // 和医嘱审核明细一样
        exportHpDoDetail(heads, datas, details);
    }


    /**
     * 组装数据 - 转科室审核明细
     *
     * @param heads   表头
     * @param datas   数据
     * @param details 查询出的数据
     */
    private void exportHpTfDetail(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalDetail> details) {
        // 和医嘱审核明细一样
        exportHpDoDetail(heads, datas, details);
    }


    /**
     * 组装数据 - 每日记账审核明细
     *
     * @param heads   表头
     * @param datas   数据
     * @param details 查询出的数据
     */
    private void exportHpBkDetail(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalDetail> details) {
        // 和医嘱审核明细一样
        exportHpDoDetail(heads, datas, details);
    }


    /**
     * 组装数据 - 医嘱审核明细
     *
     * @param heads   表头
     * @param datas   数据
     * @param details 查询出的数据
     */
    private void exportHpDoDetail(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalDetail> details) {
        // 表头
        heads.add(CollectionUtil.list(true, "RULE_NAMES", "RULE_REASONS", "ITEM_ID", "ITEM_NAME", "ITEM_TYPE_NAME", "ITEM_DATE",
                "APPLY_DOCTOR_NAME", "APPLY_DEPT_NAME", "EXEC_DOCTOR_NAME", "EXEC_DEPT_NAME", "RT_DOCTOR_NAME", "RP_NURSE_NAME",
                "SELF_EXPENSE", "REASON_TYPES", "REASON_DESS", "CHARGING_FLAG", "SPECIFICATION", "USAGE_UNIT", "USAGE", "PRICE", "NUMBERS", "COST_NUMBER", "COSTS", "COST_COSTS", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT"));
        heads.add(CollectionUtil.list(true, "规则名称", "违规原因", "项目编码", "项目名称", "项目类型", "项目日期",
                "开单医生", "开单科室", "受单医生", "受单科室", "住院医师", "责任护士",
                "自费", "反馈类型", "反馈理由", "是否已计费", "规格", "包装单位", "用量", "单价", "数量", "计费数量", "总金额", "计费金额", "医保内金额", "医保统筹金额"));

        // 数据
        for (BaseMedicalDetail item : details) {
            datas.add(CollectionUtil.list(true, item.getRuleNames(), item.getRuleReasons(), item.getItemId(), item.getItemName(), item.getItemTypeName(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"),
                    item.getApplyDoctorName(), item.getApplyDeptName(), item.getExecDoctorName(), item.getExecDeptName(), item.getRtDoctorName(), item.getRpNurseName(),
                    item.getSelfExpense(), item.getReasonTypes(), item.getReasonDess(), item.getChargingFlag(), item.getSpecification(), item.getUsageUnit(), item.getUsage(), item.getPrice(), item.getNumbers(), item.getCostNumber(), item.getCosts(), item.getCostCosts(), item.getBmiConveredAmount(), item.getBmiOverallAmount()));
        }
    }



    /**
     * 组装数据 - 门诊处方审核明细
     *
     * @param heads   表头
     * @param datas   数据
     * @param details 查询出的数据
     */
    private void exportOpPctDetail(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalDetail> details) {
        // 表头
        heads.add(CollectionUtil.list(true, "RULE_NAMES", "RULE_REASONS", "ITEM_ID", "ITEM_NAME", "ITEM_TYPE_NAME", "ITEM_DATE", "SELF_EXPENSE", "REASON_TYPES", "REASON_DESS", "SPECIFICATION", "USAGE_UNIT", "USAGE", "PRICE", "NUMBERS", "COSTS"));
        heads.add(CollectionUtil.list(true, "规则名称", "违规原因", "项目编码", "项目名称", "项目类型", "项目日期", "自费", "反馈类型", "反馈理由", "规格", "包装单位", "用量", "单价", "数量", "总金额"));

        // 数据
        for (BaseMedicalDetail item : details) {
            datas.add(CollectionUtil.list(true, item.getRuleNames(), item.getRuleReasons(), item.getItemId(), item.getItemName(), item.getItemTypeName(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"), item.getSelfExpense(), item.getReasonTypes(), item.getReasonDess(), item.getSpecification(), item.getUsageUnit(), item.getUsage(), item.getPrice(), item.getNumbers(), item.getCosts()));
        }

    }


    /**
     * 组装数据 - 门诊缴费审核明细
     *
     * @param heads   表头
     * @param datas   数据
     * @param details 查询出的数据
     */
    private void exportOpPtDetail(List<List<String>> heads, List<List<Object>> datas, List<BaseMedicalDetail> details) {
        // 表头
        heads.add(CollectionUtil.list(true, "RULE_NAMES", "RULE_REASONS", "ITEM_NO", "ITEM_NAME", "ITEM_TYPE_NAME", "ITEM_DATE", "SELF_EXPENSE", "REASON_TYPES", "REASON_DESS", "SPECIFICATION", "USAGE_UNIT", "USAGE", "PRICE", "NUMBERS", "COSTS", "BMI_CONVERED_AMOUNT", "BMI_OVERALL_AMOUNT"));
        heads.add(CollectionUtil.list(true, "规则名称", "违规原因", "项目编码", "项目名称", "项目类型", "项目日期", "自费", "反馈类型", "反馈理由", "规格", "包装单位", "用量", "单价", "数量", "总金额", "医保内金额", "医保统筹金额"));

        // 数据
        for (BaseMedicalDetail item : details) {
            datas.add(CollectionUtil.list(true, item.getRuleNames(), item.getRuleReasons(), item.getItemId(), item.getItemName(), item.getItemTypeName(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"), item.getSelfExpense(), item.getReasonTypes(), item.getReasonDess(), item.getSpecification(), item.getUsageUnit(), item.getUsage(), item.getPrice(), item.getNumbers(), item.getCosts(), item.getBmiConveredAmount(), item.getBmiOverallAmount()));
        }
    }
}
