<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_mr.mapper.MrPlanMapper">

    <resultMap type="com.crhms.cloud.mqs.mqs_mr.domain.MrPlan" id="MrPlanMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="planCode" column="plan_code" jdbcType="VARCHAR"/>
        <result property="planName" column="plan_name" jdbcType="VARCHAR"/>
        <result property="auditScenario" column="audit_scenario" jdbcType="VARCHAR"/>
        <result property="ruleType" column="rule_type" jdbcType="VARCHAR"/>
        <result property="ruleLevel" column="rule_level" jdbcType="VARCHAR"/>
        <result property="mrPlanUser" column="mr_plan_user" jdbcType="VARCHAR"/>
        <result property="enable" column="enable" jdbcType="VARCHAR"/>
        <result property="lastUpdatedBy" column="last_updated_by" jdbcType="INTEGER"/>
        <result property="lastUpdatedDate" column="last_updated_date" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="created_by" jdbcType="INTEGER"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="hospitalId" column="hospital_id" jdbcType="VARCHAR"/>
    </resultMap>


    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="MrPlanMap">
        select
          id, plan_code, plan_name, audit_scenario, rule_type, rule_level, mr_plan_user, enable, last_updated_by, last_updated_date, created_by, created_date, hospital_id
        from mqs_mr_plan
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="planCode != null and planCode != ''">
                and plan_code = #{planCode}
            </if>
            <if test="planName != null and planName != ''">
                and plan_name = #{planName}
            </if>
            <if test="auditScenario != null and auditScenario != ''">
                and audit_scenario = #{auditScenario}
            </if>
            <if test="ruleType != null and ruleType != ''">
                and rule_type = #{ruleType}
            </if>
            <if test="ruleLevel != null and ruleLevel != ''">
                and rule_level = #{ruleLevel}
            </if>
            <if test="mrPlanUser != null and mrPlanUser != ''">
                and mr_plan_user = #{mrPlanUser}
            </if>
            <if test="enable != null and enable != ''">
                and enable = #{enable}
            </if>
            <if test="lastUpdatedBy != null">
                and last_updated_by = #{lastUpdatedBy}
            </if>
            <if test="lastUpdatedDate != null">
                and last_updated_date = #{lastUpdatedDate}
            </if>
            <if test="createdBy != null">
                and created_by = #{createdBy}
            </if>
            <if test="createdDate != null">
                and created_date = #{createdDate}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and hospital_id = #{hospitalId}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>
    <select id="listAuditScenario" resultType="com.crhms.cloud.mqs.mqs_mr.domain.MrPlan">
        select
            mmp.id as id,
            mmp.plan_code as plan_code,
            mmp.plan_name as plan_name,
            mmp.audit_scenario as audit_scenario,
            mmp.rule_level1 as rule_level1,
            mmp.mr_plan_user as mr_plan_user,
            mmp.`enable` as `enable`
        from mqs_mr_plan mmp
                 left join mqs_sys_scene msc on msc.scene_code = mmp.audit_scenario and mmp.hospital_id = msc.hospital_id
        where mmp.`enable` = '1'
        and mmp.hospital_id = #{hospitalId}
        ORDER BY msc.id ASC
    </select>


</mapper>

