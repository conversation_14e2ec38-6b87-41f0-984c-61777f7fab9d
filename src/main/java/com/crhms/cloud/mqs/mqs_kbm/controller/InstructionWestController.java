package com.crhms.cloud.mqs.mqs_kbm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.mqs_kbm.service.InstructionWestService;
import com.crhms.cloud.mqs.mqs_kbm.vo.InstructionDrug;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 说明书-西药药品说明书 controller
 *
 * @author: wangxingang
 * @date: 2023.02.09
 */
@RestController
@RequestMapping("/api/mqs/kbm/instruction")
public class InstructionWestController {

    @Autowired
    private InstructionWestService service;

    /**
     * 通用接口: 查询剂型下拉列表
     *
     * @param type 0:西药 1:中药
     */
    @GetMapping("/dosageList")
    public ResponseEntity<List<String>> queryDosageList(@RequestParam(value = "type") String type) {
        return new ResponseEntity<>(service.queryDosageList(type), HttpStatus.OK);
    }


    /**
     * 通用接口: 查询药品列表
     *
     * @param type       0:西药 1:中药
     * @param drugCode   药品编码
     * @param drugName   药品名称
     * @param dosageList 剂型列表
     */
    @PostMapping("/page")
    public ResponseEntity<List<InstructionDrug>> queryDrugList(@RequestParam(value = "type") String type,
                                                               @RequestParam(value = "drugCode", required = false) String drugCode,
                                                               @RequestParam(value = "drugName", required = false) String drugName,
                                                               @RequestBody(required = false) List<String> dosageList,
                                                               @RequestParam(value = "page", defaultValue = "1") int page,
                                                               @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<InstructionDrug> pageInfo = PageUtil.getPage(page, pageSize);
        List<InstructionDrug> result = service.queryDrugList(pageInfo, type, drugCode, drugName, dosageList);
        return new ResponseEntity<>(result, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * 通用接口: 查询特殊人群用药
     *
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    @GetMapping("/specialGroup")
    public ResponseEntity<List<Map<String, String>>> querySpecialGroup(@RequestParam(value = "type") String type,
                                                                       @RequestParam(value = "drugCode") String drugCode,
                                                                       @RequestParam(value = "page", defaultValue = "1") int page,
                                                                       @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<Object> pageInfo = PageUtil.getPage(page, pageSize);
        List<Map<String, String>> result = service.querySpecialGroup(pageInfo, type, drugCode);
        return new ResponseEntity<>(result, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * 通用接口: 查询药品配伍禁忌
     *
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    @GetMapping("/incompatibility")
    public ResponseEntity<List<Map<String, String>>> queryIncompatibility(@RequestParam(value = "type") String type,
                                                                          @RequestParam(value = "drugCode") String drugCode,
                                                                          @RequestParam(value = "page", defaultValue = "1") int page,
                                                                          @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<Object> pageInfo = PageUtil.getPage(page, pageSize);
        List<Map<String, String>> result = service.queryIncompatibility(pageInfo, type, drugCode);
        return new ResponseEntity<>(result, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * 通用接口: 查询药品适应症关系
     *
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    @GetMapping("/indication")
    public ResponseEntity<List<Map<String, String>>> queryIndication(@RequestParam(value = "type") String type,
                                                                     @RequestParam(value = "drugCode") String drugCode,
                                                                     @RequestParam(value = "page", defaultValue = "1") int page,
                                                                     @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<Object> pageInfo = PageUtil.getPage(page, pageSize);
        List<Map<String, String>> result = service.queryIndication(pageInfo, type, drugCode);
        return new ResponseEntity<>(result, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * 通用接口: 查询药品禁忌症关系
     *
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    @GetMapping("/taboos")
    public ResponseEntity<List<Map<String, String>>> queryTaboos(@RequestParam(value = "type") String type,
                                                                 @RequestParam(value = "drugCode") String drugCode,
                                                                 @RequestParam(value = "page", defaultValue = "1") int page,
                                                                 @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<Object> pageInfo = PageUtil.getPage(page, pageSize);
        List<Map<String, String>> result = service.queryTaboos(pageInfo, type, drugCode);
        return new ResponseEntity<>(result, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * 西药接口: 查询药物相互作用
     *
     * @param drugCode 药品编码
     */
    @GetMapping("/interaction")
    public ResponseEntity<List<Map<String, String>>> queryInteraction(@RequestParam(value = "drugCode") String drugCode,
                                                                      @RequestParam(value = "page", defaultValue = "1") int page,
                                                                      @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<Object> pageInfo = PageUtil.getPage(page, pageSize);
        List<Map<String, String>> result = service.queryInteraction(pageInfo, drugCode);
        return new ResponseEntity<>(result, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * 西药接口: 查询过敏反应
     *
     * @param drugCode 药品编码
     */
    @GetMapping("/allergy")
    public ResponseEntity<List<Map<String, String>>> queryAllergy(@RequestParam(value = "drugCode") String drugCode,
                                                                  @RequestParam(value = "page", defaultValue = "1") int page,
                                                                  @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<Object> pageInfo = PageUtil.getPage(page, pageSize);
        List<Map<String, String>> result = service.queryAllergy(pageInfo, drugCode);
        return new ResponseEntity<>(result, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

}
