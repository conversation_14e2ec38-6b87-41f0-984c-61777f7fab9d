<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <!-- 规则增加审核结果类型 -->
    <changeSet id="20241107-1" author="zhaoyc (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_hp_rg_audit">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_rg_audit_his">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_audit">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_audit_his">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_audit">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_audit_his">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_audit">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_audit_his">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_audit">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_audit_his">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_audit">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_audit_his">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_audit">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_audit_his">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_audit">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_audit_his">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_audit">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_audit_his">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_audit">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_audit_his">
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规">
                <constraints nullable="true"/>
            </column>
        </addColumn>

    </changeSet>

    <changeSet id="20241107-2" author="zhaoyc (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_hp_rg_detail">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_rg_detail_his">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail_his">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail_his">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail_his">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail_his">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail_his">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail_his">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail_his">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail_his">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail_his">
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- 规则增加一级分类、二级分类 -->
    <changeSet id="20241111-1" author="zhaoyc (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_sys_rules">
            <column name="primary_category_code" type="varchar(20)" remarks="规则一级分类编码">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_sys_rules">
            <column name="primary_category_name" type="varchar(20)" remarks="规则一级分类名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_sys_rules">
            <column name="secondary_category_code" type="varchar(100)" remarks="规则二级分类编码">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_sys_rules">
            <column name="secondary_category_name" type="varchar(100)" remarks="规则二级分类名称">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- 所有明细新增操作、甲乙类、冲销编号字段 -->
    <changeSet id="20241112-1" author="lxj (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_hp_rg_detail">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_rg_detail_his">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail_his">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail_his">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail_his">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail_his">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail_his">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail_his">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail_his">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail_his">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail_his">
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量">
                <constraints nullable="true"/>
            </column>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额">
                <constraints nullable="true"/>
            </column>
            <column name="category_name" type="varchar(10)" remarks="甲乙类">
                <constraints nullable="true"/>
            </column>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）">
                <constraints nullable="true"/>
            </column>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号">
                <constraints nullable="true"/>
            </column>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否">
                <constraints nullable="true"/>
            </column>
        </addColumn>

    </changeSet>

    <changeSet id="20241118-1" author="zhaoyc (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_hp_rg_detail">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_rg_detail_his">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail_his">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail_his">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail_his">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail_his">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail_his">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail_his">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail_his">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail_his">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail_his">
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格">
                <constraints nullable="true"/>
            </column>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额">
                <constraints nullable="true"/>
            </column>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级">
                <constraints nullable="true"/>
            </column>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱">
                <constraints nullable="true"/>
            </column>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别">
                <constraints nullable="true"/>
            </column>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别">
                <constraints nullable="true"/>
            </column>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20241118-2" author="lxj (generated)" >
        <validCheckSum>1:any</validCheckSum>
        <sql>
            INSERT INTO `mqs_sys_config`(`id`, `sys_key`, `sys_value`, `remake`, `enable`, `last_updated_by`, `last_updated_date`, `created_by`, `created_date`, `hospital_id`) VALUES (20, 'MQS_AUDIT_UI_URL_ZH', 'http://10.17.130.111:22005/his/index_zh1.html', '审核综合弹窗Url地址', '1', 1, '2024-11-18 17:36:27', 1, '2024-11-18 17:36:31', '1');
        </sql>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241120-1">
        <createTable remarks="主场景表" tableName="mqs_sys_main_scene">
            <column name="id" remarks="主键" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="main_scene_name" remarks="审核场景名称" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="main_scene_code" remarks="审核场景编码" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="order_num" remarks="排序" type="int"/>
            <column name="last_updated_by" remarks="更新人" type="BIGINT"/>
            <column name="last_updated_date" remarks="更新时间" type="datetime"/>
            <column name="created_by" remarks="创建人" type="BIGINT"/>
            <column name="created_date" remarks="创建时间" type="datetime"/>
            <column name="hospital_id" remarks="院区id" type="VARCHAR(10)"/>
        </createTable>
    </changeSet>

    <changeSet id="20241121-1" author="lxj (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_sys_scene_function">
            <column name="mi_review_api" type="varchar(10)" remarks="医保审核接口">
                <constraints nullable="true"/>
            </column>
            <column name="manual_review" type="varchar(10)" remarks="人工审核">
                <constraints nullable="true"/>
            </column>
            <column name="show_format" type="varchar(10)" remarks="展示形式">
                <constraints nullable="true"/>
            </column>
            <column name="show_dimensions" type="varchar(10)" remarks="展示维度">
                <constraints nullable="true"/>
            </column>
            <column name="self_paid_condition" type="varchar(10)" remarks="转自费不可用条件">
                <constraints nullable="true"/>
            </column>
            <column name="mi_review_api_select" type="varchar(20)" remarks="医保审核接口下拉">
                <constraints nullable="true"/>
            </column>
            <column name="scene_id" type="bigint" remarks="子场景id" defaultValue="0">
                <constraints nullable="false" />
            </column>
            <column name="mi_review_api_back" type="varchar(10)" remarks="医保审核接口反馈服务（1-选中，0-不选）">
                <constraints nullable="true"/>
            </column>
            <column name="is_ignore" type="varchar(10)" remarks="是否忽略（1-是，0-否）">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20241121-2" author="lxj (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_sys_scene">
            <column name="main_scene_code" type="varchar(100)" remarks="主场景编码">
                <constraints nullable="true"/>
            </column>
            <column name="order_num" type="int" remarks="排序" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20241121-3" author="lxj (generated)">
        <addAutoIncrement tableName="mqs_sys_scene" columnName="id" columnDataType="BIGINT"/>
    </changeSet>

    <changeSet id="20241121-4" author="lxj (generated)">
        <addAutoIncrement tableName="mqs_sys_scene_function" columnName="id" columnDataType="BIGINT"/>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241125-1">
        <createTable remarks="人工审核-审核权限表" tableName="mqs_base_medical_stats">
            <column name="no" remarks="单据号（主键）" type="VARCHAR(100)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pre_discharge" remarks="预出院（1-是，0-否）" type="VARCHAR(10)"/>
            <column name="pre_discharge_date" remarks="预出院时间" type="datetime"/>
            <column name="discharge_order_date" remarks="开出院医嘱时间" type="datetime"/>
            <column name="discharged" remarks="已出院（1-是，0-否）" type="VARCHAR(10)"/>
            <column name="settled" remarks="已结算（1-是，0-否）" type="VARCHAR(10)"/>
            <column name="last_updated_by" remarks="更新人" type="BIGINT"/>
            <column name="last_updated_date" remarks="更新时间" type="datetime"/>
            <column name="created_by" remarks="创建人" type="BIGINT"/>
            <column name="created_date" remarks="创建时间" type="datetime"/>
            <column name="hospital_id" remarks="院区id" type="VARCHAR(10)"/>
        </createTable>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241125-2">
        <createTable remarks="人工审核-审核权限表" tableName="mqs_sys_logs">
            <column name="id" remarks="主键" type="bigint">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="operator_name" remarks="操作人姓名" type="VARCHAR(100)"/>
            <column name="operator_id" remarks="操作人id" type="VARCHAR(100)"/>
            <column name="operator_time" remarks="操作时间" type="datetime"/>
            <column name="operator_text" remarks="操作内容" type="VARCHAR(200)"/>
            <column name="admission_no" remarks="就诊流水号" type="VARCHAR(100)"/>
            <column name="patient_id" remarks="患者id" type="VARCHAR(100)"/>
            <column name="patient_name" remarks="患者姓名" type="VARCHAR(100)"/>
            <column name="detail_no" remarks="明细号" type="VARCHAR(100)"/>
            <column name="item_id" remarks="项目编码" type="VARCHAR(100)"/>
            <column name="item_name" remarks="项目名称" type="VARCHAR(100)"/>
            <column name="item_date" remarks="项目日期" type="datetime"/>
            <column name="operator_source" remarks="操作来源（sys-系统接口，popup-弹窗）" type="VARCHAR(10)"/>
            <column name="last_updated_by" remarks="更新人" type="BIGINT"/>
            <column name="last_updated_date" remarks="更新时间" type="datetime"/>
            <column name="created_by" remarks="创建人" type="BIGINT"/>
            <column name="created_date" remarks="创建时间" type="datetime"/>
            <column name="hospital_id" remarks="院区id" type="VARCHAR(10)"/>
        </createTable>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241125-3-1">
        <createTable remarks="人工审核-审核权限表" tableName="mqs_audit_ignore">
            <column name="id" remarks="主键" type="bigint">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="admission_no" remarks="就诊流水号" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="no" remarks="单据号" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="detail_no" remarks="明细号" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="item_id" remarks="项目编码" type="VARCHAR(100)"/>
            <column name="item_name" remarks="项目名称" type="VARCHAR(255)"/>
            <column name="rule_code" remarks="违规规则" type="VARCHAR(100)"/>
            <column name="hospital_id" remarks="院区id" type="VARCHAR(10)"/>
            <column name="enable" remarks="有效状态 1 有效 0 无效" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="20241125-3" author="lxj (generated)">
        <addAutoIncrement tableName="mqs_audit_ignore" columnName="id" columnDataType="BIGINT"/>
    </changeSet>

    <changeSet id="20241125-4" author="lxj (generated)">
        <dropNotNullConstraint tableName="mqs_sys_scene_function" columnName="function_code" columnDataType="varchar(100)"/>
        <dropNotNullConstraint tableName="mqs_sys_scene_function" columnName="is_mr" columnDataType="varchar(10)"/>
        <dropNotNullConstraint tableName="mqs_sys_scene_function" columnName="is_self" columnDataType="varchar(10)"/>
    </changeSet>

    <changeSet author="lxj" id="20241125-5-1">
        <dropTable tableName="mqs_mr_plan"/>
    </changeSet>

    <!-- 新增mr系列表-->
    <changeSet author="lxj (generated)" id="20241125-5">
        <createTable remarks="人工审核流程配置表" tableName="mqs_mr_plan">
            <column name="id" remarks="主键" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="enable" remarks="是否启用1.启用 0.禁用" type="VARCHAR(10)"/>
            <column name="audit_scenario" remarks="审核场景" type="VARCHAR(100)"/>
            <column name="business_process_node" remarks="业务流程节点" type="VARCHAR(10)"/>
            <column name="scene_classification" remarks="场景分类" type="VARCHAR(10)"/>
            <column name="data_conditions_type" remarks="数据条件类型" type="VARCHAR(10)"/>
            <column name="data_conditions" remarks="数据条件" type="VARCHAR(10)"/>
            <column name="rule_level1" remarks="系统规则级别(多级别,分开)" type="VARCHAR(100)"/>
            <column name="last_updated_by" remarks="更新人" type="BIGINT"/>
            <column name="last_updated_date" remarks="更新时间" type="datetime"/>
            <column name="created_by" remarks="创建人" type="BIGINT"/>
            <column name="created_date" remarks="创建时间" type="datetime"/>
            <column name="hospital_id" remarks="院区id" type="VARCHAR(10)"/>
        </createTable>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241125-6">
        <createTable remarks="人工审核-审核权限表" tableName="mqs_mr_plan_audit">
            <column name="id" remarks="主键" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="mr_plan_user" remarks="审核人员" type="VARCHAR(255)"/>
            <column name="mr_plan_department" remarks="审核科室" type="VARCHAR(255)"/>
            <column name="last_updated_by" remarks="更新人" type="BIGINT"/>
            <column name="last_updated_date" remarks="更新时间" type="datetime"/>
            <column name="created_by" remarks="创建人" type="BIGINT"/>
            <column name="created_date" remarks="创建时间" type="datetime"/>
            <column name="hospital_id" remarks="院区id" type="VARCHAR(10)"/>
        </createTable>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241125-7">
        <createTable remarks="人工审核表" tableName="mqs_mr_case">
            <column name="id" remarks="主键" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="admission_no" remarks="就诊流水号" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="case_no" remarks="单据号" type="VARCHAR(100)" >
                <constraints nullable="false"/>
            </column>
            <column name="mr_status" remarks="人工审核状态" type="VARCHAR(10)"/>
            <column name="mr_opinion" remarks="人工审核意见" type="VARCHAR(255)"/>
            <column name="mr_to" remarks="人工审核-分配质控人" type="VARCHAR(10)"/>
            <column name="mr_by" remarks="人工审核-实际质控人" type="VARCHAR(10)"/>
            <column name="mr_time" remarks="人工审核时间" type="datetime"/>
            <column name="hospital_id" remarks="院区id" type="VARCHAR(10)"/>
            <column name="lables" remarks="标签" type="VARCHAR(32)"/>
            <column name="audit_scenario" remarks="单据场景" type="VARCHAR(30)"/>
            <column name="clinical_status" remarks="临床处理状态 （0-未处理、1-处理中、2-处理完成、3-无需处理）" type="VARCHAR(10)"/>
        </createTable>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241125-8">
        <createTable remarks="人工审核-人工违规明细表" tableName="mqs_mr_audit">
            <column name="id" remarks="主键" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="detail_no" remarks="明细流水号" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="no" remarks="单据号" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="admission_no" remarks="就诊流水号" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="violation_flag" remarks="人工 违规标识 (1-转人工违规)" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="self_expense" remarks="自费标识 (2.人工转自费)" type="VARCHAR(10)"/>
            <column name="mr_opinion" remarks="人工审核原因" type="VARCHAR(255)"/>
            <column name="mr_self_opinion" remarks="人工转自非原因" type="VARCHAR(255)"/>
            <column name="rule_code" remarks="违规规则" type="VARCHAR(100)"/>
            <column name="rule_name" remarks="违规规则名称" type="VARCHAR(100)"/>
            <column name="rule_type" remarks="结果类型" type="VARCHAR(100)"/>
            <column name="rule_type_name" remarks="结果类型名称" type="VARCHAR(100)"/>
            <column name="rule_reason" remarks="违规原因" type="VARCHAR(100)"/>
            <column name="item_id" remarks="项目编码" type="VARCHAR(100)"/>
            <column name="item_name" remarks="项目名称" type="VARCHAR(100)"/>
            <column name="last_updated_by" remarks="更新人" type="BIGINT"/>
            <column name="last_updated_date" remarks="更新时间" type="datetime"/>
            <column name="created_by" remarks="创建人" type="BIGINT"/>
            <column name="created_date" remarks="创建时间" type="datetime"/>
            <column name="hospital_id" remarks="院区id" type="VARCHAR(10)"/>
        </createTable>
    </changeSet>

    <changeSet id="20241127-1" author="zhaoyac (generated)" >
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_hp_rg_audit">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_rg_audit_his">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_audit">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_audit_his">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_audit">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_audit_his">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_audit">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_audit_his">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_audit">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_audit_his">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_audit">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_audit_his">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_audit">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_audit_his">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_audit">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_audit_his">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_audit">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_audit_his">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_audit">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_audit_his">
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id">
                <constraints nullable="true"/>
            </column>
        </addColumn>

    </changeSet>

    <!-- 反馈类型反馈理由去掉必填 -->
    <changeSet id="20241127-2" author="zhaoyac (generated)">
        <validCheckSum>1:any</validCheckSum>

        <modifyDataType tableName="mqs_hp_rg_reason" columnName="reason_des" newDataType="varchar(200)"  />
        <modifyDataType tableName="mqs_hp_rg_reason" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_rg_reason_his" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_rg_reason_his" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_bk_reason" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_bk_reason" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_bk_reason_his" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_bk_reason_his" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_do_reason" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_do_reason" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_do_reason_his" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_do_reason_his" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_tf_reason" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_tf_reason" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_tf_reason_his" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_tf_reason_his" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_out_reason" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_out_reason" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_out_reason_his" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_out_reason_his" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_pred_reason" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_pred_reason" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_pred_reason_his" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_pred_reason_his" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_settle_reason" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_settle_reason" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_hp_settle_reason_his" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_hp_settle_reason_his" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_op_rg_reason" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_op_rg_reason" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_op_rg_reason_his" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_op_rg_reason_his" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_op_pt_reason" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_op_pt_reason" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_op_pt_reason_his" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_op_pt_reason_his" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_op_pct_reason" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_op_pct_reason" columnName="reason_type" newDataType="varchar(100)"/>

        <modifyDataType tableName="mqs_op_pct_reason_his" columnName="reason_des" newDataType="varchar(200)"/>
        <modifyDataType tableName="mqs_op_pct_reason_his" columnName="reason_type" newDataType="varchar(100)"/>

    </changeSet>

    <!-- 字段超长 -->
    <changeSet id="20241129-1" author="zhaoyac (generated)">
        <validCheckSum>1:any</validCheckSum>

        <modifyDataType tableName="mqs_hp_rg_detail" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_rg_detail" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_rg_detail" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_rg_detail_his" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_rg_detail_his" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_rg_detail_his" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_bk_detail" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_bk_detail" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_bk_detail" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_bk_detail_his" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_bk_detail_his" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_bk_detail_his" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_do_detail" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_do_detail" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_do_detail" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_do_detail_his" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_do_detail_his" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_do_detail_his" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_tf_detail" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_tf_detail" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_tf_detail" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_tf_detail_his" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_tf_detail_his" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_tf_detail_his" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_out_detail" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_out_detail" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_out_detail" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_out_detail_his" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_out_detail_his" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_out_detail_his" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_pred_detail" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_pred_detail" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_pred_detail" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_pred_detail_his" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_pred_detail_his" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_pred_detail_his" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_settle_detail" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_settle_detail" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_settle_detail" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_settle_detail_his" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_settle_detail_his" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_hp_settle_detail_his" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_rg_detail" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_rg_detail" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_rg_detail" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_rg_detail_his" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_rg_detail_his" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_rg_detail_his" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_pt_detail" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_pt_detail" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_pt_detail" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_pt_detail_his" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_pt_detail_his" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_pt_detail_his" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_pct_detail" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_pct_detail" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_pct_detail" columnName="rule_codes" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_pct_detail_his" columnName="rule_names" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_pct_detail_his" columnName="rule_reasons" newDataType="TEXT"/>
        <modifyDataType tableName="mqs_op_pct_detail_his" columnName="rule_codes" newDataType="TEXT"/>
    </changeSet>

    <!-- 人工质控表 -->
    <changeSet id="20241129-2" author="zhaoyac (generated)">
        <validCheckSum>1:any</validCheckSum>
        <createTable tableName="mqs_mr_base">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="no" type="varchar(100)" remarks="单据号">
                <constraints nullable="false"/>
            </column>
            <column name="admission_no" type="varchar(100)" remarks="就诊流水号">
                <constraints nullable="false"/>
            </column>
            <column name="bmi_number" type="varchar(100)" remarks="医保反馈编码"/>
            <column name="drg_code" type="varchar(100)" remarks="DRG编码"/>
            <column name="dip_code" type="varchar(100)" remarks="DIP编码"/>
            <column name="bill_date" type="datetime" remarks="单据结算日期"/>
            <column name="hospital_code" type="varchar(100)" remarks="医院编码"/>
            <column name="hospital_level" type="varchar(100)" remarks="医院级别"/>
            <column name="hospital_type" type="varchar(100)" remarks="定点机构类型"/>
            <column name="claim_type_id" type="varchar(100)" remarks="医疗类别（原就医方式编码）"/>
            <column name="benefit_type_id" type="varchar(100)" remarks="参保类型编码"/>
            <column name="self_expense" type="varchar(100)" remarks="自费标识"/>
            <column name="bmi_code" type="varchar(100)" remarks="医保报销统筹区编码"/>
            <column name="bmi_name" type="varchar(100)" remarks="医保报销统筹区名称"/>
            <column name="in_diagnosis_code" type="varchar(100)" remarks="入院诊断编码"/>
            <column name="in_diagnosis_name" type="varchar(100)" remarks="入院诊断名称"/>
            <column name="out_diagnosis_code" type="varchar(100)" remarks="出院诊断编码（在院时为主诊断）"/>
            <column name="out_diagnosis_name" type="varchar(100)" remarks="出院诊断名称（在院时为主诊断）"/>
            <column name="admission_date" type="datetime" remarks="入院时间"/>
            <column name="discharge_date" type="datetime" remarks="出院时间"/>
            <column name="patient_id" type="varchar(100)" remarks="参保人编码"/>
            <column name="patient_name" type="varchar(100)" remarks="参保人姓名"/>
            <column name="patient_gender" type="varchar(100)" remarks="性别"/>
            <column name="patient_birthday" type="varchar(100)" remarks="出生日期"/>
            <column name="is_bkl" type="varchar(100)" remarks="是否黑名单"/>
            <column name="is_pregnant" type="varchar(100)" remarks="是否孕期"/>
            <column name="is_lactating" type="varchar(100)" remarks="是否哺乳期"/>
            <column name="newborn_weight" type="decimal(18,4)" remarks="新生儿出生体重 (kg)"/>
            <column name="newborn_age" type="decimal(18,4)" remarks="新生儿年龄（小于一岁的天数） （天）"/>
            <column name="total_amount" type="decimal(18,4)" remarks="单据总金额"/>
            <column name="personnel_type" type="varchar(100)" remarks="人员类别编码"/>
            <column name="treatment_type" type="varchar(100)" remarks="待遇类型"/>
            <column name="bmi_convered_amount" type="decimal(18,4)" remarks="医保内金额"/>
            <column name="bmi_overall_amount" type="decimal(18,4)" remarks="医保统筹金额"/>
            <column name="unusual_flag" type="varchar(100)" remarks="参保人特殊保险类型组编码 1：生育 2：工伤 -1：其他"/>
            <column name="benefit_group_code" type="varchar(100)" remarks="是否异地就医"/>
            <column name="war_business_flag" type="varchar(100)" remarks="因战因公标志"/>
            <column name="single_disease_code" type="varchar(100)" remarks="单病种编码"/>
            <column name="single_disease_name" type="varchar(100)" remarks="单病种名称"/>
            <column name="cs_disease_code" type="varchar(100)" remarks="门诊慢特病病种编码"/>
            <column name="cs_disease_name" type="varchar(100)" remarks="门诊慢特病病种名称"/>
            <column name="out_zone_code" type="varchar(100)" remarks="出院病区编码"/>
            <column name="medical_record_id" type="varchar(100)" remarks="病案号（病人在医院看病的唯一登记号，只登记一次）"/>
            <column name="is_trans_hospital" type="varchar(100)" remarks="是否二次返医/转院 | 按指定病种定额结算0：正常  1：转院  2：二次返院  4:90天或180天结算"/>
            <column name="is_public_hosp" type="varchar(100)" remarks="是否公立医改医院"/>
            <column name="patient_idno" type="varchar(100)" remarks="证件号码"/>
            <column name="is_discharge" type="varchar(100)" remarks="出入院状态"/>
            <column name="item_date" type="datetime" remarks="就诊日期"/>
            <column name="dept_code" type="varchar(100)" remarks="入院科室编码"/>
            <column name="dept_name" type="varchar(100)" remarks="入院科室名称"/>
            <column name="discharge_dept_code" type="varchar(100)" remarks="出院科室编码"/>
            <column name="discharge_dept_name" type="varchar(100)" remarks="出院科室名称"/>
            <column name="doc_id" type="varchar(100)" remarks="开单医生编码"/>
            <column name="doc_name" type="varchar(100)" remarks="开单医生名称"/>
            <column name="doc_level" type="varchar(100)" remarks="开单医生级别"/>
            <column name="charging_flag" type="varchar(100)" remarks="是否计费"/>
            <column name="doc_group_code" type="varchar(100)" remarks="医师小组编码"/>
            <column name="doc_group_name" type="varchar(100)" remarks="医师小组名称"/>
            <column name="hosp_area_code" type="varchar(100)" remarks="院区编码"/>
            <column name="hosp_area_name" type="varchar(100)" remarks="院区名称"/>
            <column name="units_dept_code" type="varchar(100)" remarks="专科科室编码"/>
            <column name="units_dept_name" type="varchar(100)" remarks="专科科室名称"/>
            <column name="pay_ratio" type="decimal(18,4)" remarks="自付比例"/>
            <column name="pay_amount" type="decimal(18,4)" remarks="自付金额"/>
            <column name="register_doc_id" type="varchar(100)" remarks="挂号医生编码"/>
            <column name="register_doc_name" type="varchar(100)" remarks="挂号医生姓名"/>
            <column name="register_id" type="varchar(100)" remarks="挂号id"/>
            <column name="register_time" type="datetime" remarks="挂号时间"/>
            <column name="register_nums" type="int" remarks="挂号次数"/>
            <column name="registration_fee" type="decimal(18,4)" remarks="挂号费"/>
            <column name="tf_from_dept_code" type="varchar(100)" remarks="转出科室编码"/>
            <column name="tf_from_dept_name" type="varchar(100)" remarks="转出科室名称"/>
            <column name="tf_from_doc_code" type="varchar(100)" remarks="转出医生编码"/>
            <column name="tf_from_doc_name" type="varchar(100)" remarks="转出医生名称"/>
            <column name="tf_to_dept_code" type="varchar(100)" remarks="转入科室编码"/>
            <column name="tf_to_dept_name" type="varchar(100)" remarks="转入科室名称"/>
            <column name="tf_to_doc_code" type="varchar(100)" remarks="转入医生编码"/>
            <column name="tf_to_doc_name" type="varchar(100)" remarks="转入医生名称"/>
            <column name="tf_date" type="datetime" remarks="转科日期"/>
            <column name="fee_date" type="datetime" remarks="收费日期"/>
            <column name="diagnosis_code1" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code2" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code3" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code4" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code5" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code6" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code7" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code8" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code9" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code10" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code11" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code12" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code13" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code14" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code15" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_code16" type="varchar(100)" remarks="其它诊断编码"/>
            <column name="diagnosis_name1" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name2" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name3" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name4" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name5" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name6" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name7" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name8" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name9" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name10" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name11" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name12" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name13" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name14" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name15" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="diagnosis_name16" type="varchar(100)" remarks="其它诊断名称"/>
            <column name="expand_field1" type="varchar(100)" remarks="预留字段1"/>
            <column name="expand_field2" type="varchar(100)" remarks="预留字段2"/>
            <column name="expand_field3" type="varchar(100)" remarks="预留字段3"/>
            <column name="expand_field4" type="varchar(100)" remarks="预留字段4"/>
            <column name="expand_field5" type="varchar(100)" remarks="预留字段5"/>
            <column name="batch_no" type="varchar(100)" remarks="批次号"/>
            <column name="violation_flag" type="varchar(10)" remarks="是否违规"/>
            <column name="audit_nums" type="int" remarks="审核次数"/>
            <column name="audit_time" type="datetime" remarks="审核时间"/>
            <column name="mr_flag" type="varchar(10)" remarks="是否需要人工审核 1.是 0否"/>
            <column name="mr_status" type="varchar(10)" remarks="人工审核状态"/>
            <column name="mr_opinion" type="varchar(255)" remarks="人工审核意见"/>
            <column name="mr_time" type="datetime" remarks="人工审核时间"/>
            <column name="last_updated_by" type="bigint" remarks="更新人"/>
            <column name="last_updated_date" type="datetime" remarks="更新时间"/>
            <column name="created_by" type="bigint" remarks="创建人"/>
            <column name="created_date" type="datetime" remarks="创建时间"/>
            <column name="hospital_id" type="varchar(10)" remarks="医院id"/>
            <column name="present_dept_name" type="varchar(100)" remarks="当前科室名称"/>
            <column name="present_dept_code" type="varchar(100)" remarks="当前科室编码"/>
        </createTable>
        <createIndex indexName="mqs_mr_baseU1" tableName="mqs_mr_base">
            <column name="no"/>
            <column name="admission_no"/>
        </createIndex>
    </changeSet>

    <changeSet id="20241129-3" author="zhaoyac (generated)">
        <validCheckSum>1:any</validCheckSum>
        <createTable tableName="mqs_mr_base_audit">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="detail_no" type="varchar(100)" remarks="明细流水号">
                <constraints nullable="false"/>
            </column>
            <column name="no" type="varchar(100)" remarks="单据号">
                <constraints nullable="false"/>
            </column>
            <column name="admission_no" type="varchar(100)" remarks="流水号">
                <constraints nullable="false"/>
            </column>
            <column name="order_detail_no" type="varchar(100)" remarks="医嘱明细流水号"/>
            <column name="rule_code" type="varchar(100)" remarks="违规规则">
                <constraints nullable="false"/>
            </column>
            <column name="rule_name" type="varchar(100)" remarks="违规规则名称">
                <constraints nullable="false"/>
            </column>
            <column name="rule_type" type="varchar(100)" remarks="结果类型"/>
            <column name="rule_type_name" type="varchar(100)" remarks="结果类型名称"/>
            <column name="rule_reason" type="varchar(100)" remarks="违规原因"/>
            <column name="item_id" type="varchar(100)" remarks="项目编码"/>
            <column name="item_name" type="varchar(100)" remarks="项目名称"/>
            <column name="item_date" type="datetime" remarks="项目时间"/>
            <column name="item_type_code" type="varchar(100)" remarks="项目类型编码"/>
            <column name="item_type_name" type="varchar(100)" remarks="项目类型名称"/>
            <column name="numbers" type="decimal(18,4)" remarks="数量"/>
            <column name="price" type="decimal(18,4)" remarks="金额"/>
            <column name="costs" type="decimal(18,4)" remarks="单价"/>
            <column name="related" type="TEXT" remarks="违规相关明细"/>
            <column name="full_tip" type="varchar(255)" remarks="完整提示"/>
            <column name="group_code" type="varchar(100)" remarks="分组编码"/>
            <column name="tips_code4_hospital" type="varchar(255)" remarks="规则设置提示"/>
            <column name="violation_ratio" type="varchar(100)" remarks="违规比例"/>
            <column name="violation_amount" type="decimal(18,4)" remarks="违规金额"/>
            <column name="violation_type" type="varchar(10)" remarks="违规计费标记，0未违规 1违规"/>
            <column name="batch_no" type="varchar(100)" remarks="批次号"/>
            <column name="expand_field1" type="varchar(100)" remarks="预留字段1"/>
            <column name="expand_field2" type="varchar(100)" remarks="预留字段2"/>
            <column name="expand_field3" type="varchar(100)" remarks="预留字段3"/>
            <column name="expand_field4" type="varchar(100)" remarks="预留字段4"/>
            <column name="expand_field5" type="varchar(100)" remarks="预留字段5"/>
            <column name="last_updated_by" type="bigint" remarks="更新人"/>
            <column name="last_updated_date" type="datetime" remarks="更新时间"/>
            <column name="created_by" type="bigint" remarks="创建人"/>
            <column name="created_date" type="datetime" remarks="创建时间"/>
            <column name="hospital_id" type="varchar(10)" remarks="院区id"/>
            <column name="rule_origin" type="varchar(10)" remarks="1. 院内违规 2.医保违规"/>
            <column name="jr_id" type="varchar(100)" remarks="医保信息平台结果唯一id"/>
        </createTable>
        <createIndex indexName="mqs_mr_base_auditU1" tableName="mqs_mr_base_audit">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="detail_no"/>
        </createIndex>
    </changeSet>

    <changeSet id="20241129-4" author="zhaoyac (generated)">
        <validCheckSum>1:any</validCheckSum>
        <createTable tableName="mqs_mr_base_detail">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="detail_no" remarks="明细流水号" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="no" type="varchar(100)" remarks="单据号">
                <constraints nullable="false"/>
            </column>
            <column name="admission_no" type="varchar(100)" remarks="就诊流水号">
                <constraints nullable="false"/>
            </column>
            <column name="bill_date" type="datetime" remarks="结算日期"/>
            <column name="item_id" type="varchar(100)" remarks="项目编码"/>
            <column name="item_name" type="varchar(100)" remarks="项目名称"/>
            <column name="his_item_id" type="varchar(100)" remarks="院内项目编码"/>
            <column name="his_item_name" type="varchar(100)" remarks="院内项目名称"/>
            <column name="ptype" type="varchar(100)" remarks="项目类型名称"/>
            <column name="item_type_code" type="varchar(100)" remarks="项目类型编码(A-Z大类)"/>
            <column name="item_type_name" type="varchar(100)" remarks="项目类型名称"/>
            <column name="item_date" type="datetime" remarks="项目日期"/>
            <column name="numbers" type="decimal(18,4)" remarks="数量"/>
            <column name="price" type="decimal(18,4)" remarks="单价"/>
            <column name="usage_unit" type="varchar(10)" remarks="包装单位"/>
            <column name="usage_days" type="varchar(100)" remarks="用药天数"/>
            <column name="costs" type="decimal(18,4)" remarks="总费用"/>
            <column name="cost_number" type="decimal(18,4)" remarks="计费数量"/>
            <column name="cost_costs" type="decimal(18,4)" remarks="计费金额"/>
            <column name="bmi_convered_amount" type="varchar(100)" remarks="医保内金额"/>
            <column name="bmi_overall_amount" type="varchar(100)" remarks="医保统筹金额"/>
            <column name="specification" type="varchar(100)" remarks="规格"/>
            <column name="usage" type="varchar(100)" remarks="每次用量"/>
            <column name="apply_doctor_code" type="varchar(100)" remarks="开单医生编码"/>
            <column name="apply_doctor_name" type="varchar(100)" remarks="开单医生名称"/>
            <column name="apply_doctor_level" type="varchar(100)" remarks="开单医生级别"/>
            <column name="apply_dept_code" type="varchar(100)" remarks="开单科室编码"/>
            <column name="apply_dept_name" type="varchar(100)" remarks="开单科室名称"/>
            <column name="exec_doctor_code" type="varchar(100)" remarks="受单医生编码"/>
            <column name="exec_doctor_name" type="varchar(100)" remarks="受单医生名称"/>
            <column name="exec_dept_code" type="varchar(100)" remarks="受单科室编码"/>
            <column name="exec_dept_name" type="varchar(100)" remarks="受单科室名称"/>
            <column name="rt_doctor_code" type="varchar(100)" remarks="住院医师编码"/>
            <column name="rt_doctor_name" type="varchar(100)" remarks="住院医师名称"/>
            <column name="rp_nurse_code" type="varchar(100)" remarks="责任护士编码"/>
            <column name="rp_nurse_name" type="varchar(100)" remarks="责任护士名称"/>
            <column name="charging_flag" type="varchar(100)" remarks="计费标记"/>
            <column name="self_expense" type="varchar(100)" remarks="是否自费"/>
            <column name="self_original" type="varchar(100)" remarks="是否自费(原始值)"/>
            <column name="frequency_interval" type="varchar(100)" remarks="使用频次"/>
            <column name="approval_number" type="varchar(100)" remarks="备案审批号"/>
            <column name="z_physicianap" type="varchar(100)" remarks="医师行政职务"/>
            <column name="posts_number" type="varchar(100)" remarks="帖数"/>
            <column name="pay_ratio" type="decimal(18,4)" remarks="自付比例"/>
            <column name="abroad_drug_flag" type="varchar(100)" remarks="出国带药标志"/>
            <column name="out_hospital_flag" type="varchar(100)" remarks="外院费用标志"/>
            <column name="violation_flag" type="varchar(10)" remarks="违规标识"/>
            <column name="is_current" type="varchar(10)" remarks="当次明细标记，1.是 0.否"/>
            <column name="violation_type" type="varchar(10)" remarks="违规计费标记，0未违规 1违规"/>
            <column name="rule_type" type="varchar(100)" remarks="违规结果类型"/>
            <column name="rule_type_name" type="varchar(255)" remarks="违规结果类型名"/>
            <column name="rule_codes" type="TEXT" remarks="违规编码集"/>
            <column name="rule_names" type="TEXT" remarks="违规名称集"/>
            <column name="rule_reasons" type="TEXT" remarks="违规原因集"/>
            <column name="reason_types" type="varchar(255)" remarks="反馈类型集"/>
            <column name="reason_dess" type="varchar(255)" remarks="反馈原因集"/>
            <column name="batch_no" type="varchar(100)" remarks="批次号"/>
            <column name="mr_opinion" type="varchar(255)" remarks="人工审核原因"/>
            <column name="mr_self_opinion" type="varchar(255)" remarks="人工转自费原因"/>
            <column name="last_updated_by" type="bigint" remarks="更新人"/>
            <column name="last_updated_date" type="datetime" remarks="更新时间"/>
            <column name="created_by" type="bigint" remarks="创建人"/>
            <column name="created_date" type="datetime" remarks="创建时间"/>
            <column name="hospital_id" type="varchar(10)" remarks="院区id"/>
            <column name="rule_origin" type="varchar(100)" remarks="存在违规类型"/>
            <column name="violation_num" type="decimal(18,4)" remarks="违规数量"/>
            <column name="violation_amt" type="decimal(18,4)" remarks="违规金额"/>
            <column name="category_name" type="varchar(10)" remarks="甲乙类"/>
            <column name="operation_type" type="varchar(10)" remarks="是否增量（0-否,1-是）"/>
            <column name="refund_no" type="varchar(100)" remarks="冲销编号"/>
            <column name="reverse_flag" type="varchar(10)" remarks="删除标记 1 是 0 否"/>
            <column name="hilist_pric" type="decimal(18,4)" remarks="医保目录价格"/>
            <column name="ownpay_amt" type="decimal(18,4)" remarks="自费金额"/>
            <column name="selfpay_amt" type="decimal(18,4)" remarks="自付金额"/>
            <column name="hilist_lv" type="varchar(10)" remarks="医保目录等级"/>
            <column name="long_drord_flag" type="varchar(10)" remarks="是否长期医嘱"/>
            <column name="hilist_type" type="varchar(10)" remarks="目录类别"/>
            <column name="chrg_type" type="varchar(10)" remarks="收费类别"/>
            <column name="drord_bhvr" type="varchar(10)" remarks="医嘱行为"/>
        </createTable>
        <createIndex indexName="mqs_mr_base_detailU1" tableName="mqs_mr_base_detail">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="detail_no"/>
        </createIndex>
    </changeSet>

    <changeSet id="20241129-5" author="zhaoyac (generated)">
        <validCheckSum>1:any</validCheckSum>
        <createTable tableName="mqs_mr_base_orders">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="order_detail_no" type="varchar(100)" remarks="医嘱明细流水号">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="no" type="varchar(100)" remarks="单据号">
                <constraints nullable="false"/>
            </column>
            <column name="admission_no" type="varchar(100)" remarks="就诊流水号">
                <constraints nullable="false"/>
            </column>
            <column name="order_no" type="varchar(100)" remarks="医嘱序号">
                <constraints nullable="false"/>
            </column>
            <column name="order_sub_no" type="varchar(100)" remarks="医嘱子序号">
                <constraints nullable="false"/>
            </column>
            <column name="enter_date_time" type="datetime" remarks="下达医嘱时间"/>
            <column name="repeat_indicator" type="varchar(10)" remarks="长期医嘱标记  1.长期 0.临时"/>
            <column name="item_no" type="varchar(100)" remarks="项目序号"/>
            <column name="item_id" type="varchar(100)" remarks="项目编码"/>
            <column name="item_name" type="varchar(100)" remarks="项目名称"/>
            <column name="item_type_code" type="varchar(100)" remarks="项目类型编码"/>
            <column name="item_type_name" type="varchar(100)" remarks="项目类型名称"/>
            <column name="item_date" type="datetime" remarks="项目日期"/>
            <column name="broad_heading" type="varchar(10)" remarks="项目大类 1.医嘱项目 2.计价项目"/>
            <column name="apply_dept_code" type="varchar(100)" remarks="开医嘱科室编码"/>
            <column name="apply_dept_name" type="varchar(100)" remarks="开医嘱科室名称"/>
            <column name="apply_doctor_code" type="varchar(100)" remarks="开医嘱医生编码"/>
            <column name="apply_doctor_name" type="varchar(100)" remarks="开医嘱医生名称"/>
            <column name="numbers" type="decimal(18,4)" remarks="数量"/>
            <column name="specification" type="varchar(100)" remarks="规格"/>
            <column name="usage_unit" type="varchar(10)" remarks="包装/剂量/计价单位"/>
            <column name="usage" type="varchar(100)" remarks="每次用量"/>
            <column name="frequency_interval" type="varchar(100)" remarks="使用频次（值集）"/>
            <column name="freq_detail" type="varchar(255)" remarks="执行时间详细描述"/>
            <column name="freq_counter" type="decimal(18,4)" remarks="频率次数"/>
            <column name="freq_interval" type="decimal(18,4)" remarks="频率间隔"/>
            <column name="freq_interval_unit" type="varchar(100)" remarks="频率间隔单位"/>
            <column name="price" type="decimal(18,4)" remarks="单价"/>
            <column name="costs" type="decimal(18,4)" remarks="总费用"/>
            <column name="start_date_time" type="datetime" remarks="开始日期"/>
            <column name="stop_date_time" type="datetime" remarks="停止日期"/>
            <column name="batch_no" type="varchar(100)" remarks="批次号"/>
            <column name="last_updated_by" type="bigint" remarks="更新人"/>
            <column name="last_updated_date" type="datetime" remarks="更新时间"/>
            <column name="created_by" type="bigint" remarks="创建人"/>
            <column name="created_date" type="datetime" remarks="创建时间"/>
            <column name="hospital_id" type="varchar(10)" remarks="院区id"/>
        </createTable>
        <createIndex indexName="mqs_mr_base_ordersU1" tableName="mqs_mr_base_orders">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="order_detail_no"/>
        </createIndex>
    </changeSet>
    <changeSet id="20241129-6" author="zhaoyac (generated)">
        <validCheckSum>1:any</validCheckSum>
        <createTable tableName="mqs_mr_base_reason">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="detail_no" type="varchar(100)" remarks="明细流水号">
                <constraints nullable="false"/>
            </column>
            <column name="no" type="varchar(100)" remarks="单据号">
                <constraints nullable="false"/>
            </column>
            <column name="admission_no" type="varchar(100)" remarks="流水号">
                <constraints nullable="false"/>
            </column>
            <column name="item_id" type="varchar(100)" remarks="项目">
                <constraints nullable="false"/>
            </column>
            <column name="self_expense" type="varchar(10)" remarks="反馈自费标记1.是 0.否"/>
            <column name="rule_code" type="varchar(100)" remarks="违规编码"/>
            <column name="reason_type" type="varchar(100)" remarks="反馈类型"/>
            <column name="reason_des" type="varchar(200)" remarks="反馈理由"/>
            <column name="batch_no" type="varchar(100)" remarks="批次号"/>
            <column name="last_updated_by" type="bigint" remarks="更新人"/>
            <column name="last_updated_date" type="datetime" remarks="更新时间"/>
            <column name="created_by" type="bigint" remarks="创建人"/>
            <column name="created_date" type="datetime" remarks="创建时间"/>
            <column name="hospital_id" type="varchar(10)" remarks="院区id"/>
        </createTable>
        <createIndex indexName="mqs_mr_base_reasonU1" tableName="mqs_mr_base_reason">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="detail_no"/>
        </createIndex>
    </changeSet>


    <changeSet id="20241202-1" author="lxj (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_hp_rg_detail">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_rg_detail_his">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_bk_detail_his">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_do_detail_his">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_tf_detail_his">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_out_detail_his">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_pred_detail_his">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_hp_settle_detail_his">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_rg_detail_his">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pt_detail_his">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_op_pct_detail_his">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addColumn tableName="mqs_mr_base_detail">
            <column name="outpatient_medication" type="varchar(10)" remarks="是否出院带药">
                <constraints nullable="true"/>
            </column>
            <column name="route_administration" type="varchar(100)" remarks="用药途径">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20241203-1" author="lxj (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_mr_plan_audit">
            <column name="mr_plan_user_name" type="varchar(1000)" remarks="审核人员名字">
                <constraints nullable="true"/>
            </column>
        </addColumn>

    </changeSet>

    <changeSet id="20241203-2" author="lxj (generated)">
        <modifyDataType tableName="mqs_mr_plan_audit" columnName="mr_plan_user" newDataType="VARCHAR(1000)"/>
        <modifyDataType tableName="mqs_mr_case" columnName="mr_by" newDataType="VARCHAR(100)"/>
        <modifyDataType tableName="mqs_mr_case" columnName="mr_to" newDataType="VARCHAR(1000)"/>
    </changeSet>

    <changeSet id="20241204-1" author="lxj (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_mr_case">
            <column name="mr_by_name" type="VARCHAR(100)" remarks="审核人员名字">
                <constraints nullable="true"/>
            </column>
            <column name="mr_to_name" type="varchar(1000)" remarks="分配审核人员名字">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20241205-1" author="lxj (generated)">
        <dropNotNullConstraint tableName="mqs_sys_scene_function" columnName="is_self_agreement" columnDataType="varchar(10)"/>
    </changeSet>

    <!-- 初始化人工审核配置信息 -->
    <changeSet id="20241209-1" author="lxj (generated)">
        <sql>
            <![CDATA[
            INSERT INTO `mqs_mr_plan` VALUES (1847170633478692866, '1', 'opRg', NULL, 'op', '1', '1', '2,1,4,5,6,3', 396, '2024-10-18 14:59:32', 396, '2024-10-18 14:59:32', '1');
            INSERT INTO `mqs_mr_plan` VALUES (1847170633520635905, '1', 'opRg', NULL, 'op', '2', '1', NULL, 396, '2024-10-18 14:59:32', 396, '2024-10-18 14:59:32', '1');
            INSERT INTO `mqs_mr_plan` VALUES (1847170633529024513, '1', 'hpRg', '4', 'hp', '1', '1', '1,6,5,4,3,2', 396, '2024-10-18 14:59:32', 396, '2024-10-18 14:59:32', '1');
            INSERT INTO `mqs_mr_plan` VALUES (1847170633529024514, '1', 'hpRg', '1', 'hp', '2', '4', NULL, 396, '2024-10-18 14:59:32', 396, '2024-10-18 14:59:32', '1');
            INSERT INTO `mqs_mr_plan_audit` VALUES (1847170633180897282, '21', '219010', 396, '2024-10-18 14:59:32', 396, '2024-10-18 14:59:32', '1', NULL);
            ]]>
        </sql>
    </changeSet>

    <changeSet id="20241209-3" author="lxj (generated)">
        <sql>
            <![CDATA[
            INSERT INTO `mqs_sys_main_scene` VALUES (1866020508215021570, '门诊审核', '67569aa4e4b0951ae86334d6', 0, 1, '2024-12-09 15:22:37', 1, '2024-12-09 15:22:13', '1');
            INSERT INTO `mqs_sys_main_scene` VALUES (1866020508496039938, '住院审核', '67569aa4e4b0951ae86334d7', 1, 1, '2024-12-09 15:22:37', 1, '2024-12-09 15:22:13', '1');
            ]]>
        </sql>
    </changeSet>

    <changeSet id="20241209-4" author="lxj (generated)">
        <sql>
            <![CDATA[
            INSERT INTO `mqs_sys_scene` VALUES (1866017240438476801, 'opRg', '门诊挂号审核', '1', 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '67569aa4e4b0951ae86334d6', 0);
            INSERT INTO `mqs_sys_scene` VALUES (1866017240459448321, 'opPct', '门诊处方审核', '1', 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '67569aa4e4b0951ae86334d6', 1);
            INSERT INTO `mqs_sys_scene` VALUES (1866017240467836930, 'opPt', '门诊缴费审核', '1', 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '67569aa4e4b0951ae86334d6', 2);
            INSERT INTO `mqs_sys_scene` VALUES (1866017240472031234, 'hpRg', '住院登记审核', '1', 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '67569aa4e4b0951ae86334d7', 0);
            INSERT INTO `mqs_sys_scene` VALUES (1866017240472031235, 'hpDo', '医嘱审核', '1', 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '67569aa4e4b0951ae86334d7', 1);
            INSERT INTO `mqs_sys_scene` VALUES (1866017240476225538, 'hpBk', '每日记账审核', '1', 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '67569aa4e4b0951ae86334d7', 2);
            INSERT INTO `mqs_sys_scene` VALUES (1866017240480419842, 'hpTf', '转科室审核', '1', 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '67569aa4e4b0951ae86334d7', 3);
            INSERT INTO `mqs_sys_scene` VALUES (1866017240484614145, 'hpPred', '预出院审核', '1', 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '67569aa4e4b0951ae86334d7', 4);
            INSERT INTO `mqs_sys_scene` VALUES (1866017240488808450, 'hpOut', '出院审核', '1', 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '67569aa4e4b0951ae86334d7', 5);
            INSERT INTO `mqs_sys_scene` VALUES (1866017240488808451, 'hpSettle', '住院结算审核', '1', 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '67569aa4e4b0951ae86334d7', 6);
            INSERT INTO `mqs_sys_scene_function` VALUES (1866017240602054657, 'opRg', '0', '1', NULL, NULL, NULL, 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '2', '2', '0', '0', NULL, NULL, 1866017240438476801, NULL, NULL);
            INSERT INTO `mqs_sys_scene_function` VALUES (1866017240614637569, 'opPct', '0', '1', NULL, NULL, NULL, 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '0', '0', '0', '0', NULL, NULL, 1866017240459448321, NULL, NULL);
            INSERT INTO `mqs_sys_scene_function` VALUES (1866017240618831874, 'opPt', '0', '1', NULL, NULL, NULL, 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '1', '1', '0', '0', NULL, NULL, 1866017240467836930, NULL, NULL);
            INSERT INTO `mqs_sys_scene_function` VALUES (1866017240627220481, 'hpRg', '0', '1', NULL, NULL, NULL, 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '0', '2', '0', '0', NULL, NULL, 1866017240472031234, NULL, NULL);
            INSERT INTO `mqs_sys_scene_function` VALUES (1866017240631414786, 'hpDo', '0', '1', NULL, NULL, NULL, 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '0', '0', '0', '0', NULL, NULL, 1866017240472031235, NULL, NULL);
            INSERT INTO `mqs_sys_scene_function` VALUES (1866017240635609090, 'hpBk', '2', '1', NULL, NULL, NULL, 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '2', '0', '0', '0', NULL, NULL, 1866017240476225538, NULL, NULL);
            INSERT INTO `mqs_sys_scene_function` VALUES (1866017240639803393, 'hpTf', '1', '1', NULL, NULL, NULL, 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '1', '0', '0', '0', NULL, NULL, 1866017240480419842, NULL, NULL);
            INSERT INTO `mqs_sys_scene_function` VALUES (1866017240643997697, 'hpPred', '1', '1', NULL, NULL, NULL, 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '1', '1', '0', '0', NULL, NULL, 1866017240484614145, NULL, NULL);
            INSERT INTO `mqs_sys_scene_function` VALUES (1866017240648192002, 'hpOut', '1', '1', NULL, NULL, NULL, 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '2', '1', '0', '0', NULL, NULL, 1866017240488808450, NULL, NULL);
            INSERT INTO `mqs_sys_scene_function` VALUES (1866017240652386306, 'hpSettle', '1', '1', NULL, NULL, NULL, 1, '2024-12-09 15:09:14', 1, '2024-12-09 15:09:14', '1', '2', '1', '0', '0', NULL, NULL, 1866017240488808451, NULL, NULL);
            ]]>
        </sql>
    </changeSet>

    <changeSet id="20241211-2" author="lxj (generated)">
        <validCheckSum>1:any</validCheckSum>
        <addColumn tableName="mqs_mr_base">
            <column name="source_id" type="VARCHAR(10)" remarks="数据来源(场景编码)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20241213-1" author="lxj (generated)">
        <validCheckSum>1:any</validCheckSum>
        <createIndex indexName="mqs_mr_auditU1" tableName="mqs_mr_audit">
            <column name="no"/>
            <column name="admission_no"/>
            <column name="detail_no"/>
        </createIndex>
    </changeSet>

    <changeSet id="20241216-1" author="lxj (generated)">
        <addDefaultValue tableName="mqs_sys_scene_function" columnName="enable" defaultValue="0"></addDefaultValue>
    </changeSet>

    <changeSet author="lxj (generated)" id="20241230-1">
        <sql>
            INSERT INTO `mqs_sys_config`(`id`, `sys_key`, `sys_value`, `remake`, `enable`, `last_updated_by`, `last_updated_date`, `created_by`, `created_date`, `hospital_id`) VALUES (21, 'MQS_DIS_CHARGE', '[{"weight":"1","keyword":["今日出院","死亡"],"date":"0"},{"weight":"2","keyword":["明日出院"],"date":"1"}]', '预出院关键字', '1', 1, '2023-11-08 17:36:27', 1, '2023-11-08 17:36:31', '1');
        </sql>
    </changeSet>

    <changeSet id="20250109-1" author="zhaoyac(generated)">
        <sql>update mqs_sys_config set sys_value = '医院智能审核管理系统'  where sys_key = 'MQS_SYS_TITLE';</sql>
    </changeSet>

    <changeSet id="20250407-1" author="zhaoyac(generated)">
        <createIndex indexName="mqs_hp_diag_hisU4" tableName="mqs_hp_diag_his">
            <column name="batch_no"/>
        </createIndex>
    </changeSet>

    <changeSet id="20250417-1" author="zhaoyac (generated)" >
        <addColumn tableName="mqs_base_medical_stats">
            <column name="patient_id" type="varchar(100)" remarks="患者id">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="mqs_base_medical_stats">
            <column name="personnel_type" type="varchar(100)" remarks="人员类别">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="mqs_base_medical_stats">
            <column name="benefit_type_id" type="varchar(100)" remarks="参保类型">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>