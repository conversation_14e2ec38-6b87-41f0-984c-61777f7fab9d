package com.crhms.cloud.mqs.mqs_mr.domain;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 人工审核日志(MrLog)实体类
 *
 * <AUTHOR>
 * @since 2023-02-06 17:51:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_mr_log")
public class MrLog implements Serializable {
    private static final long serialVersionUID = -71953152191987852L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_NO="no";
    public static final String FIELD_ADMISSION_NO="admission_no";
    public static final String FIELD_MR_TIME="mr_time";
    public static final String FIELD_MR_STATUS="mr_status";
    public static final String FIELD_MR_OPINION="mr_opinion";
    public static final String FIELD_USER_ID="user_id";
    public static final String FIELD_USER_NAME="user_name";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * id
     */    
    @TableId
    private Long id;

    /**
     * 单据号
     */    
    @TableField
    private String no;
    /**
     * 住院号/门诊号
     */    
    @TableField
    private String admissionNo;
    /**
     * 审核时间
     */    
    @TableField
    private Date mrTime;
    /**
     * 审核结果
     */    
    @TableField
    private String mrStatus;
    /**
     * 审核意见
     */    
    @TableField
    private String mrOpinion;
    /**
     * 审核人编码
     */    
    @TableField
    private String userId;
    /**
     * 审核人姓名
     */    
    @TableField
    private String userName;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;


}

