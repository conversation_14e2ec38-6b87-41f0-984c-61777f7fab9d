package com.crhms.cloud.mqs.mqs_kbm.vo;

import lombok.Data;

import java.util.List;

/**
 * 封装知识库管理-分组合理性-编码有效性的查询条件
 *
 * @author: wangxingang
 * @date: 2023.02.07
 */

@Data
public class GroupCodeValidityVo {
    /**
     * 版本号
     */
    private List<String> versionNoList;

    /**
     * 类型
     */
    private List<String> typeList;

    /**
     * 诊断代码
     */
    private String diagCode;

    /**
     * 诊断名称
     */
    private String diagName;

    /**
     * 是否有效
     */
    private List<String> validFlagList;


}
