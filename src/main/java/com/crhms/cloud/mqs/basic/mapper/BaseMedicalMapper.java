package com.crhms.cloud.mqs.basic.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.mqs.basic.domain.*;
import com.crhms.cloud.mqs.basic.vo.*;
import com.crhms.cloud.mqs.mqs_ais.vo.DetailDto;
import com.crhms.cloud.mqs.mqs_ais.vo.DetailQueryVo;
import com.crhms.cloud.mqs.mqs_bmi.vo.FbMedicalCaseDto;
import com.crhms.cloud.mqs.mqs_bmi.vo.FbMedicalDetailDto;
import com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-01-18 15:40:47
 */

@Mapper
public interface BaseMedicalMapper {

    /**
     * 查询指定明细行数据
     *
     * @return 对象列表
     */
    List<BaseMedicalDetail> queryDetailAll(@Param("table") String table, @Param("queryVO") BaseDetailQueryVO queryVO,@Param("pageable") Page pageable);

    /**
     * 查询指定明细行历史数据
     *
     * @return 对象列表
     */
    List<BaseMedicalDetail> queryDetailAllHis(@Param("table") String table, @Param("queryVO") BaseDetailQueryVO queryVO,@Param("pageable") Page pageable);

    /**
     * 分页查询指定单据数据
     *
     * @param queryVO 查询条件
     * @return 对象列表
     */
    List<BaseMedicalCase> queryCaseListByPage(@Param("table") String table, @Param("queryVO") BaseCaseQueryVO queryVO,@Param("pageable") Page pageable);

    /**
     * 查询指定结算期间内的主单信息
     * @param table 表名
     * @param begin 开始时间
     * @param end   结束时间
     * @return
     */
    List<BaseMedicalCase> queryCaseListByBillDate(@Param("table") String table, @Param("begin") Date begin, @Param("end") Date end);

    /**
     * 查询指定结算期间内的费用明细信息
     * <AUTHOR>
     * @date 2023/4/3
     * @param table 表名
     * @param begin 开始时间
     * @param end   结束时间
     **/
    List<BaseMedicalDetail> queryFeeListByBillDate(@Param("table") String table, @Param("begin") Date begin, @Param("end") Date end);

    /**
     * 分页查询指定单据历史数据
     *
     * @return 对象列表
     */
    List<BaseMedicalCase> queryCaseHisList(@Param("table") String table,@Param("no") String no, @Param("batchNo") String batchNo, @Param("hospitalId") String hospitalId);

    /**
     * 查询医保反馈所需要的院内明细
     */
    List<FbMedicalDetailDto> queryBmiFbDetail(@Param("table") String table, @Param("no") String no, @Param("detailNo") String detailNo, @Param("item") String item, @Param("violationFlag") String violationFlag, @Param("hospitalId") String hospitalId, @Param("relatedRecords") String relatedRecords);

    /**
     * 查询医保反馈所需要的院内数据
     * @param table
     * @param no
     * @param hospitalId
     * @return
     */
    FbMedicalCaseDto queryBmiFbHead(@Param("table") String table, @Param("no") String no,@Param("hospitalId") String hospitalId);


    /**
     * 查询明细详情
     * @param table
     * @param detailQueryVo
     * @return
     */
    List<DetailDto> analyzeQueryDetail( @Param("table") String table,@Param("queryVO") DetailQueryVo detailQueryVo,@Param("pageable") Page pageable);

    /**
     * 批量保存单据历史数据
     * @param table
     * @param caseList
     */
    void batchInsertCaseHis(@Param("table") String table, @Param("entities") List<BaseMedicalCase> caseList);
    /**
     * 批量保存单据费用明细历史数据
     * @param table
     * @param detailList
     */
    void batchInsertDetailHis(@Param("table") String table, @Param("entities") List<BaseMedicalDetail> detailList);

    /**
     * 批量保存单据到正式表
     * @param table
     * @param caseList
     */
    void batchInsertCase(@Param("table") String table, @Param("entities") List<BaseMedicalCase> caseList);
    /**
     * 批量保存单据到明细表
     * @param table
     * @param detailList
     */
    void batchInsertDetail(@Param("table") String table, @Param("entities") List<BaseMedicalDetail> detailList);

    void deleteCaseByAdNo(@Param("table") String table, @Param("nos") List<String> nos,@Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    void deleteDetailByAdNo(@Param("table") String table, @Param("detailNos") List<String> detailNos,@Param("nos") List<String> nos,@Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);



    List<BaseMedicalCase> auditQueryMedicalCaseHis(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("batch") String batch, @Param("hospitalId") String hospitalId);

    List<BaseMedicalDetail> queryMedicalDetailCaseHis(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("batch") String batch, @Param("hospitalId") String hospitalId);

    List<BaseAudit> queryMedicalAuditRulesHis(@Param("detailNo") String detailNo, @Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("batch") String batch, @Param("ruleOrigin") String ruleOrigin, @Param("hospitalId") String hospitalId);

    List<BaseAudit> queryMedicalAuditRules(@Param("detailNo") String detailNo, @Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("batch") String batch, @Param("ruleOrigin") String ruleOrigin, @Param("hospitalId") String hospitalId);


    List<BaseMedicalDetail> queryMedicalDetailCaseHisByRule(@Param("table") String tableName, @Param("batch") String batch, @Param("hospitalId") String hospitalId, @Param("rules") List<String> rules);

    List<BaseMedicalDetail> queryMedicalDetailCaseByRule(@Param("table") String tableName, @Param("batch") String batch, @Param("hospitalId") String hospitalId, @Param("rules") List<String> rules);


    List<BaseMedicalCase> queryCaseHisByBatchNo(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("no") String no, @Param("batchNo") String batchNo, @Param("hospitalId") String hospitalId);

    List<BaseMedicalDetail> queryDetailHisByBatchNo(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("no") String no, @Param("batchNo") String batchNo, @Param("hospitalId") String hospitalId);

    void updateClaimDischargeFlag(@Param("table") String tableName, @Param("no") String no, @Param("admissionNo") String admissionNo, @Param("isDischarge") String isDischarge, @Param("hospitalId") String hospitalId);

    void updateClaimChargingFlag(@Param("table") String tableName, @Param("detailNo") String detailNo, @Param("no") String no, @Param("admissionNo") String admissionNo, @Param("chargingFlag") String chargingFlag, @Param("hospitalId") String hospitalId);

    List<Map> getHospitalSelfExpense(@Param("table") String tableName, @Param("detailNo") String detailNo, @Param("no") String no, @Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    List<BaseMedicalDetail> queryAuditDetail(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("nos") Collection<String> nos, @Param("detailNos") Collection<String> detailNos, @Param("hospitalId") String hospitalId);

    List<BaseMedicalDetail> queryAuditDetailByDetailNo(@Param("table") String tableName,@Param("detailNos") List<String> detailNos);

    List<BaseMedicalCase> queryAuditCase(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("nos") Collection<String> nos, @Param("hospitalId") String hospitalId);

    List<BaseMedicalDetail> queryMedicalCaseAuditHis(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("batch") String batch, @Param("hospitalId") String hospitalId);

    List<Map<String,Object>> selectAuditNums(@Param("table") String tableName, @Param("hospitalId") String hospitalId);

    int selectAuditNoNums(@Param("table") String tableName,@Param("no")String no, @Param("hospitalId") String hospitalId);

    /**
     * 更新自费标识
     * @param tableName
     * @param detailNo
     * @param no
     * @param admissionNo
     * @param selfExpense
     * @param mrOpinion
     * @param hospitalId
     */
    void updateDetailSelfExpense(@Param("table") String tableName, @Param("detailNo") String detailNo, @Param("no") String no, @Param("admissionNo") String admissionNo, @Param("selfExpense") String selfExpense, @Param("mrOpinion") String mrOpinion,@Param("hospitalId") String hospitalId,@Param("userId") Long userId);

    /**
     * 更新违规
     * @param tableName
     * @param detailNo
     * @param no
     * @param admissionNo
     * @param violationFlag
     * @param selfExpense
     * @param mrOpinion
     * @param hospitalId
     */
    void updateMrViolation(@Param("table") String tableName, @Param("detailNo") String detailNo, @Param("no") String no, @Param("admissionNo") String admissionNo, @Param("violationFlag") String violationFlag,@Param("selfExpense") String selfExpense,@Param("mrOpinion") String mrOpinion, @Param("hospitalId") String hospitalId,@Param("userId") Long userId);

    /**
     * 查询自费协议要导出的数据
     * @param tableName
     * @param no
     * @param hospitalId
     * @return
     */
    BaseMedicalCase querySelfExportCase(@Param("table") String tableName, @Param("no") String no, @Param("batch") String batch, @Param("hospitalId") String hospitalId);

    /**
     * 查询自费明细
     * @param tableName
     * @param no
     * @param hospitalId
     * @return
     */
    List<BaseMedicalDetail> querySelfExportCaseDetail(@Param("table") String tableName, @Param("no") String no, @Param("detailNos") List<String> detailNos, @Param("batch") String batch, @Param("hospitalId") String hospitalId);
    /**
     * 查询自费协议有效开单科室
     * @param tableName
     * @param no
     * @param hospitalId
     * @return
     */
    String querySelfExportApplyDept(@Param("table") String tableName, @Param("no") String no, @Param("detailNos") List<String> detailNos, @Param("batch") String batch, @Param("hospitalId") String hospitalId);

    /**
     * 查询融合平台的明细
     * @param admissionNo
     * @param selfExpense
     * @param item
     * @param itemTypes
     * @param hospitalId
     * @return
     */
    List<BaseMedicalDetail> queryClinicalDetail(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("violationFlag") String violationFlag, @Param("selfExpense") String selfExpense, @Param("item") String item, @Param("itemTypes") List<String> itemTypes, @Param("hospitalId") String hospitalId);

    /**
     * 查询融合平台需要的单据信息
     * @param tableName
     * @param admissionNo
     * @param hospitalId
     * @return
     */
    List<BaseMedicalCase> queryClinicalCase(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    /**
     * 查询丙类项目 (明细原始自费为1 的项目)
     * @param tableName
     * @param admissionNo
     * @param item
     * @param itemTypes
     * @param hospitalId
     * @return
     */
    List<BaseMedicalDetail> queryClinicalSelfItem(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("item") String item, @Param("itemTypes") List<String> itemTypes, @Param("hospitalId") String hospitalId);

    /**
     * 查询违规项为漏收费的项目
     * @param tableName
     * @param admissionNo
     * @param item
     * @param itemTypes
     * @param hospitalId
     * @return
     */
    List<BaseMedicalDetail> queryMissedItem(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("item") String item, @Param("itemTypes") List<String> itemTypes, @Param("hospitalId") String hospitalId);

    /**
     * 批量更新至自费
     * @param tableName
     * @param selfDetail
     * @param hospitalId
     */
    void updateDetailsToSelfExpense(@Param("table") String tableName, @Param("selfDetail") List<String> selfDetail, @Param("hospitalId") String hospitalId);

    /**
     * 批量更新至非自费
     * @param tableName
     * @param noSelfDetail
     * @param hospitalId
     */
    void updateDetailsToNoSelfExpense(@Param("table") String tableName, @Param("noSelfDetail") List<String> noSelfDetail, @Param("hospitalId") String hospitalId);

    /**
     * 查询费用明细信息
     * @param tableName
     * @param selfDetail
     * @param hospitalId
     * @return
     */
    List<BaseMedicalDetail> querydetailByDetailNo(@Param("table") String tableName, @Param("selfDetail") List<String> selfDetail, @Param("hospitalId") String hospitalId);

    void clearHisData(@Param("table")String tableName,@Param("clearDate") String clearDate);

    /**
     * 查询留存的明细违规原因
     * @param tableName
     * @param admissionNo
     * @param hospitalId
     * @return
     */
    List<BaseAuditReason> queryReasonByDetailNo(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    /**
     * 根据明细号查询当前忽略记录
     * @param nos
     * @param hospitalId
     * @return
     */
    List<BaseAuditIgnore> queryIgnoreByNos (@Param("nos") List<String> nos,@Param("hospitalId") String hospitalId);

    /**
     * 根据医院id查询主单
     * @param table
     * @param hospitalId
     * @param nos
     * @return
     */
    List<BaseMedicalCase> queryMedicalByHospitalId (@Param("table") String table , @Param("hospitalId") String hospitalId,@Param("nos") List<String> nos);


    List<AuditPatientVO> queryAuditPatient(@Param("table") String table, @Param("hospitalId") String hospitalId,@Param("patientName") String patientName,@Param("presentDeptCode") String presentDeptCode,@Param("docId") String docId);


    /**
     * 查询各场景明细信息
     * @param table
     * @param hospitalId
     * @param queryVO
     * @return
     */
    List<ComprePopupDetailVO> queryDetail(@Param("table") String table, @Param("queryVO") ComprePopupQueryVO queryVO,@Param("hospitalId") String hospitalId);


    /**
     * 查询各场景明细信息his
     * @param table
     * @param hospitalId
     * @param queryVO
     * @return
     */
    List<ComprePopupDetailVO> queryDetailHis(@Param("table") String table, @Param("queryVO") ComprePopupQueryVO queryVO,@Param("hospitalId") String hospitalId);

    /**
     * 根据单据号查询违规级别
     * @param nos
     * @param hospitalId
     */
    List<BaseMedicalDetail> queryMinRuleTypeByNo (@Param("table") String table,@Param("nos") List<String> nos, @Param("hospitalId") String hospitalId, @Param("detailNos") List<String> detailNos,@Param("batchNo") String batchNo);

    /**
     * 查询明细的审核渠道以及临床处理状态
     * @param detailNos
     * @param hospitalId
     * @return
     */
    List<MrMedicalDetail> queryDetailSourceAndReasons(@Param("detailTable") String detailTable,@Param("auditTable") String auditTable,@Param("detailNos") List<String> detailNos,@Param("hospitalId") String hospitalId,@Param("batchNo") String batchNo);


    /**
     * 查询明细信息
     * @param tableName
     * @param admissionNo
     * @param hospitalId
     * @return
     */
    List<BaseMedicalDetail> queryMedicalDetailByDetailNos(@Param("table") String tableName,@Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId, @Param("detailNos") List<String> detailNos,@Param("batchNo") String batchNo);


    /**
     * 批量新增忽略信息
     * @param ignores
     * @return
     */
    void batchInsertAuditIgnore(@Param("ignores") List<BaseAuditIgnore> ignores);

    /**
     * 查询忽略表数据
     * */
    List<BaseAuditIgnore> queryIgnoreByDetailNos(@Param("detailNos") List<String> detailNos,@Param("no") String no);

    void batchDeleteIgnoreByDetailNos (@Param("detailNos") List<String> detailNos,@Param("no") String no);

    /**
     * 修改明细表自费状态
     * @param tableName
     * @param userId
     * @param hospitalId
     * @param comprePopupSaveVOs
     * @return
     */
    void batchUpdateSelfExpense (@Param("table") String tableName,@Param("userId") String userId,@Param("hospitalId") String hospitalId,@Param("items") List<ComprePopupDetailVO> comprePopupSaveVOs);

    /**
     * 修改明细表（his）自费状态
     * @param tableName
     * @param userId
     * @param hospitalId
     * @param comprePopupSaveVOs
     * @return
     */
    void batchUpdateSelfExpenseHis (@Param("table") String tableName,@Param("userId") String userId,@Param("hospitalId") String hospitalId,@Param("items") List<ComprePopupDetailVO> comprePopupSaveVOs);


    /**
     * 查询主单违规记录
     * @param tableName
     * @param admissionNo
     * @param hospitalId
     * @param batchNo
     * @return
     */
    List<ComprePopupDetailVO> queryMedicalAudit(@Param("table") String tableName,@Param("auditTable") String auditTable,@Param("admissionNo") String admissionNo,@Param("hospitalId") String hospitalId,@Param("queryVO") ComprePopupQueryVO comprePopupQueryVO,@Param("batchNo") String batchNo);

    List<String> selectSurviveCase(@Param("table")String tableName, @Param("nos") List<String> nos);
}

