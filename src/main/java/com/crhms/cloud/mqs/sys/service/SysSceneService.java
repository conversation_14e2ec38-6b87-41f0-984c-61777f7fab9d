package com.crhms.cloud.mqs.sys.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.service.GlobalCache;
import com.crhms.cloud.mqs.sys.domain.*;
import com.crhms.cloud.mqs.sys.dto.SysSceneDTO;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.enums.SceneTemplate;
import com.crhms.cloud.mqs.sys.mapper.SysSceneMapper;
import com.crhms.cloud.mqs.sys.utils.DictUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 运营管理-审核场景(SysScene)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-21 13:37:48
 */
@Slf4j
@Service("sysSceneService")
public class SysSceneService extends ServiceImpl<SysSceneMapper, SysScene> {

    @Autowired
    SysSceneRuleService sysSceneRuleService;
    @Autowired
    SysSceneFunctionService sysSceneFunctionService;

    @Autowired
    GlobalCache globalCache;

    @Autowired
    SysMainSceneService sysMainSceneService;

    @Autowired
    RedisTemplate redisTemplate;

    /**
     * 查询 审核场景列表 （启用）
     *
     * @param hospitalId
     * @return
     */
    public List<SysScene> querySceneList(String scene, String enable, String hospitalId) {
        QueryWrapper<SysScene> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(SysScene.FIELD_SCENE_NAME, SysScene.FIELD_SCENE_CODE);
        queryWrapper.eq(SysScene.FIELD_HOSPITAL_ID, hospitalId);
        if (Strings.isNotBlank(scene)) {
            queryWrapper.and(i -> i.like(SysScene.FIELD_SCENE_NAME, scene).or(x -> x.like(SysScene.FIELD_SCENE_CODE, scene)));
        }
        if (Strings.isNotBlank(enable)) {
            queryWrapper.eq(SysScene.FIELD_ENABLE, enable);
        }
        return list(queryWrapper);
    }

    public List<SysScene> queryList(String hospitalId) {
        return this.baseMapper.queryList(hospitalId);
    }

    /**
     * 查询审核场景下的规则
     *
     * @param pageInfo
     * @param sceneCode
     * @param rule
     * @param hospitalId
     * @param ruleLevelList
     * @return
     */
    public List<SysRules> queryRulesList(Page<Object> pageInfo, String sceneCode, String rule, List<String> ruleLevelList, String hospitalId) {
        List<SysRules> rules = baseMapper.queryRulesByScene(pageInfo, rule, sceneCode, hospitalId, ruleLevelList);
        DictUtils.translateDict(rules);
        return rules;
    }


    /**
     * 更新审核场景规则
     *
     * @param sysScene
     * @return
     */
    @Transactional
    public void editData(SysScene sysScene) {
        String hospitalId = LoginContext.getHospitalId();
        //删除旧规则集
        sysSceneRuleService.getBaseMapper().delete(new QueryWrapper<SysSceneRule>().eq(SysSceneRule.FIELD_SCENE_CODE, sysScene.getSceneCode()).eq(SysSceneRule.FIELD_HOSPITAL_ID, hospitalId));
        //维护新规则
        if (CollectionUtil.isNotEmpty(sysScene.getRuleLists())) {
            for (SysSceneRule rule : sysScene.getRuleLists()) {
                rule.setSceneCode(sysScene.getSceneCode());
                rule.setHospitalId(hospitalId);
            }
            sysSceneRuleService.saveBatch(sysScene.getRuleLists());
        }
        //更新时间更新
        this.update(new SysScene(), new UpdateWrapper<SysScene>().set(SysScene.FIELD_LAST_UPDATED_DATE, new Date()).set(SysScene.FIELD_LAST_UPDATED_BY, LoginContext.getUserId()).eq(SysScene.FIELD_ID, sysScene.getId()).eq(SysScene.FIELD_HOSPITAL_ID, LoginContext.getHospitalId()));
        //刷新缓存
        globalCache.reflashSceneRuleCache(LoginContext.getHospitalId());
    }

    /**
     * 查询可添加到当前审核场景的规则
     *
     * @param sceneCode 场景编码
     * @param rule      规则名称或编码模糊查询
     */
    public List<SysRules> queryAddableRules(String sceneCode, String rule) {
        List<SysRules> rules = baseMapper.queryRulesNotInScene(sceneCode, rule, LoginContext.getHospitalId());
        DictUtils.translateDict(rules);
        return rules;
    }

    /**
     * 添加规则到场景中
     *
     * @param sceneCode    场景编码
     * @param ruleCodeList 规则编码集合
     */
    public void addRulesToScene(String sceneCode, List<String> ruleCodeList) {
        String hospitalId = LoginContext.getHospitalId();
        // 构造关系表数据
        List<SysSceneRule> ruleList = new ArrayList<>();
        for (String ruleCode : ruleCodeList) {
            ruleList.add(SysSceneRule.builder()
                    .sceneCode(sceneCode)
                    .ruleCode(ruleCode)
                    .hospitalId(hospitalId)
                    .build()
            );
        }

        // 保存到关系表
        sysSceneRuleService.saveBatch(ruleList);
        //刷新缓存
        globalCache.reflashSceneRuleCache(hospitalId);
    }

    /**
     * 根据id批量删除审核场景中的规则
     *
     * @param ids 规则id集合
     * <AUTHOR>
     * @date 2023.02.20
     */
    public void delete(List<Long> ids) {
        sysSceneRuleService.removeByIds(ids);
        //刷新缓存
        globalCache.reflashSceneRuleCache(LoginContext.getHospitalId());
    }

    /**
     * 查询审核场景 功能配置列表
     * @param hospitalId
     * @return
     */
    public List<SysSceneFunction> querySceneFunctionList(String hospitalId) {
        List<SysSceneFunction> list = sysSceneFunctionService.list(new QueryWrapper<SysSceneFunction>().eq(SysSceneFunction.FIELD_HOSPITAL_ID, hospitalId));
        for (SysSceneFunction sysSceneFunction : list) {
            sysSceneFunction.setSceneName(AuditScenarioEnum.valueOf(sysSceneFunction.getSceneCode()).getDesc());
        }
        return list;
    }

    /**
     * 查询功能配置
     * @param sceneCode
     * @param functionCode
     * @param hospitalId
     * @return
     */
    public SysSceneFunction querySceneFunctionOne(String sceneCode, String functionCode, String hospitalId) {
        return sysSceneFunctionService.getOne(new QueryWrapper<SysSceneFunction>().eq(SysSceneFunction.FIELD_HOSPITAL_ID,hospitalId).eq(SysSceneFunction.FIELD_SCENE_CODE,sceneCode).eq(SysSceneFunction.FIELD_FUNCTION_CODE,functionCode));
    }

    /**
     * 编辑
     * @param functionList
     * @param hospitalId
     */
    public void editSceneFunction(List<SysSceneFunction> functionList, String hospitalId) {
        for (SysSceneFunction sysSceneFunction : functionList) {
            sysSceneFunction.setHospitalId(hospitalId);
        }
        sysSceneFunctionService.saveOrUpdateBatch(functionList);
    }

    public List<Map<String, String>> queryEnableScene(String hospitalId) {
        Map<String,String> name = new HashMap<>();
        name.put("hp","住院");
        name.put("op","门诊");
        List<Map<String, String>> maps = this.baseMapper.queryEnableScene(hospitalId);
        if(CollUtil.isNotEmpty(maps)){
            for (Map<String, String> map : maps) {
                map.put("name",name.get(map.get("sceneType")));
            }
        }
        return maps;
    }


    /**
     * 全场景配置查询()
     * @param hospitalId
     * @return
     */
    public List<SysMainScene> queryAllScene (String hospitalId) {
        //查询主场景
        List<SysMainScene> mainScenes = sysMainSceneService.list(new LambdaQueryWrapper<SysMainScene>().eq(SysMainScene::getHospitalId,hospitalId));
        if(CollectionUtil.isEmpty(mainScenes)){
            return Collections.emptyList();
        }
        //主场景排序
        mainScenes.sort(Comparator.comparing(mainScene -> mainScene.getOrderNum()));
        //查询子场景及配置信息
        List<SysSceneDTO> scenes = getBaseMapper().querySceneByHospitalId(hospitalId);
        if(CollectionUtil.isNotEmpty(scenes)){
            //排序+分组
            Map<String,List<SysSceneDTO>> group = scenes.stream()
                    .sorted(Comparator.comparing(SysSceneDTO::getOrderNum))
                    .collect(Collectors.groupingBy(SysSceneDTO::getMainSceneCode));
            //组装
            for(SysMainScene mainScene : mainScenes){
                mainScene.setChildScene(group.get(mainScene.getMainSceneCode()));
            }
        }
        return mainScenes;
    }


    /**
     * 子场景弹窗功能配置查询()
     * @param hospitalId
     * @return
     */
    public List<SysSceneDTO> queryScene (String hospitalId) {
        //查询配置信息
        List<SysSceneDTO> sceneConfig = getBaseMapper().querySceneConfigByHospitalId(hospitalId,"1");
        //排序
        sceneConfig.sort(Comparator.comparing(dto -> dto.getOrderNum()));
        return sceneConfig;
    }

    /**
     * 全场景配置编辑
     * @param hospitalId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitAllScene (List<SysMainScene> list,String hospitalId) {
        //删除其余的主场景
        sysMainSceneService.removeOtherByIds(list,hospitalId);
        //首先执行删除操作
        remove(new LambdaQueryWrapper<SysScene>().eq(SysScene::getHospitalId,hospitalId));
        sysSceneFunctionService.delAll(hospitalId);
        //编辑子场景及其配置信息
        if(CollectionUtil.isNotEmpty(list)){
            List<SysMainScene> mainScenes = new ArrayList<>();
            List<SysSceneDTO> scenes = new ArrayList<>();
            int i = 0;
            for(SysMainScene mainScene :list){
                List<SysSceneDTO> sceneDtos = mainScene.getChildScene();
                String mainSceneCode = StringUtils.isEmpty(mainScene.getMainSceneCode())?IdUtil.objectId():mainScene.getMainSceneCode();
                IntStream.range(0, sceneDtos.size()).forEach(j -> {
                    SysSceneDTO sysSceneDTO = sceneDtos.get(j);
                    sysSceneDTO.setOrderNum(j);
                    sysSceneDTO.setMainSceneCode(mainSceneCode);
                    sysSceneDTO.setHospitalId(hospitalId);
                    sysSceneDTO.setId(null);
                });
                //设置主表 --- 如果传入的主场景有code则做修改，没有则新增
                mainScene.setHospitalId(hospitalId);
                mainScene.setOrderNum(i);
                mainScene.setMainSceneCode(mainSceneCode);
                mainScenes.add(mainScene);
                scenes.addAll(sceneDtos);
                i++;
            }
            //新增主场景
            sysMainSceneService.submitAll(mainScenes);
            //新增子场景以及配置信息
            saveSceneAndFunction(scenes);
        }
    }

    /**
     * 全场景配置编辑
     * @param scenes
     * @return
     */
    public void saveSceneAndFunction (List<SysSceneDTO> scenes){
        List<SysScene> newScenes = BeanUtil.copyToList(scenes,SysScene.class);
        List<SysSceneFunction> newSceneFunctions = BeanUtil.copyToList(scenes,SysSceneFunction.class);
        saveBatch(newScenes);
        for (int i = 0; i < scenes.size(); i++) {
            SysSceneFunction sceneFunction =  newSceneFunctions.get(i);
            sceneFunction.setSceneId(newScenes.get(i).getId());
        }
        sysSceneFunctionService.saveBatch(newSceneFunctions);

        //刷新场景缓存
        try{
            String hospitalId = LoginContext.getHospitalId();
            setSceneCache(hospitalId,newScenes);
            setSceneFunctionCache(hospitalId,newSceneFunctions);
        } catch (Exception e) {
            log.info("追加全场景配置缓存失败！！！");
        }

    }

    /**
     * 子场景弹窗功能配置新增修改
     * @param list   子场景及其配置集合
     * @return
     */
    public void submitScene (List<SysSceneDTO> list,String hospitalId) {
        //这里子场景不可能为空所以不做空值校验
        List<SysSceneFunction> sceneFunctions = new ArrayList<>(20);
        list.forEach(sceneFunction ->{
            SysSceneFunction sysSceneFunction = BeanUtil.copyProperties(sceneFunction,SysSceneFunction.class);
            sysSceneFunction.setId(sceneFunction.getSceneFunctionId());
            sysSceneFunction.setHospitalId(hospitalId);
            sceneFunctions.add(sysSceneFunction);
        });
        sysSceneFunctionService.saveOrUpdateBatch(sceneFunctions);
    }

    /**
     * 全场景配置默认恢复
     * @return
     */
    public void restDefulatAllScene () {
        List<SysMainScene> list = new ArrayList<>(20);
        List<SysSceneDTO> opChildScene = new ArrayList<>(20);
        List<SysSceneDTO> hpChildScene = new ArrayList<>(20);
        for (int i = 0; i < SceneTemplate.sceneNameArray.length; i++) {
            SysSceneDTO sysSceneDTO = SysSceneDTO.builder()
                    .sceneName(SceneTemplate.sceneNameArray[i])
                    .sceneCode(SceneTemplate.sceneCodeArray[i])
                    .enable(SceneTemplate.enableArray[i])
                    .functionCode(SceneTemplate.functionCodeArray[i])
                    .miReviewApi(SceneTemplate.miReviewApiArray[i])
                    .manualReview(SceneTemplate.manualReviewArray[i])
                    .showDimensions(SceneTemplate.showDimensionsArray[i])
                    .showFormat(SceneTemplate.showFormatArray[i]).build();
            if(i<=SceneTemplate.opSize){
                opChildScene.add(sysSceneDTO);
            } else {
                hpChildScene.add(sysSceneDTO);
            }
        }
        list.add(SysMainScene.builder().mainSceneName(SceneTemplate.opName).mainSceneCode("").childScene(opChildScene).build());
        list.add(SysMainScene.builder().mainSceneName(SceneTemplate.hpName).mainSceneCode("").childScene(hpChildScene).build());
        //删除现有的主场景
        String hospitalId = LoginContext.getHospitalId();
        sysMainSceneService.removeAll(hospitalId);
        submitAllScene(list,hospitalId);
    }


    /**
     * 子场景及配置默认恢复
     * @return
     */
    public void restDefaultScene () {
        String hospitalId = LoginContext.getHospitalId();
        List<SysSceneDTO> list = new ArrayList<>(20);
        for(int i = 0; i < SceneTemplate.sceneNameArray.length; i++){
            list.add(SysSceneDTO.builder()
                    .sceneName(SceneTemplate.sceneNameArray[i])
                    .sceneCode(SceneTemplate.sceneCodeArray[i])
                    .functionCode(SceneTemplate.functionCodeArray[i])
                    .isSelf(SceneTemplate.isSelfArray[i])
                    .selfPaidCondition(SceneTemplate.selfPaidConditionArray[i])
                    .isSelfAgreement(SceneTemplate.isSelfAgreementArray[i])
                    .isIgnore(SceneTemplate.isIgnoreArray[i])
                    .hospitalId(hospitalId).build());
        }
        sysSceneFunctionService.getBaseMapper().batchUpdateConfigByCode(list);
    }

    /**
     * 查询枚举类所有子场景
     * @return
     */
    public List<SysScene> getAllScenes () {
        return Arrays.stream(AuditScenarioEnum.values()).map(auditScenarioEnum->SysScene.builder().sceneName(auditScenarioEnum.getDesc()).sceneCode(auditScenarioEnum.getAuditScenario()).build()).collect(Collectors.toList());
    }

    /**
     * 查询子场景列表以及根据展示维度以及展示形式以及场景编码以及是否开启过滤
     * @return
     */
    public List<SysSceneDTO> getScenesList (String codePrefix ,String enable) {
        String hospitalId = LoginContext.getHospitalId();
        //查询子场景以及配置信息
        List<SysSceneDTO> list = getBaseMapper().getScenesList(hospitalId);
        if(CollectionUtil.isEmpty(list)){
            return Collections.emptyList();
        }
        return list.stream().filter(sceneDTO->!"2".equals(sceneDTO.getShowDimensions()) && !StringUtils.isEmpty(sceneDTO.getShowDimensions()) && sceneDTO.getSceneCode().startsWith(codePrefix) && enable.equals(sceneDTO.getEnable())).collect(Collectors.toList());
    }

    /*
    * 根据场景code查询所有order_num大于该场景的场景的编码集合
    * @param hospitalId 医院id
    * @param sceneCode 场景编码
    * */
    public List<String> getNextScenes (String hospitalId,String sceneCode) {
        List<SysScene> list = null;
        //首先从redis缓存中获取全部场景配置，如果能够获取到则继续，获取不到则重新查数据库并更新到缓存中
        Object value = getSceneCache(hospitalId);
        if(Objects.nonNull(value)){
            list = (List<SysScene>)value;
        } else {
            //查询所有场景
            list = list(new LambdaQueryWrapper<SysScene>().eq(SysScene::getHospitalId,hospitalId));
            if(CollectionUtil.isNotEmpty(list)){
                //重新加入缓存
                setSceneCache(hospitalId,list);
            }
        }
        //获取当前传入场景对象，拿到序号orderNum
        SysScene sysScene = list.stream()
                .filter(obj -> obj.getSceneCode().equals(sceneCode))
                .findFirst().get();
        //返回大于等于当前编号  且  同属于hp/op的所有场景的编码
        return list.stream()
                .filter(obj -> (Integer.valueOf(obj.getOrderNum()) >= Integer.valueOf(sysScene.getOrderNum()) && (obj.getSceneCode().startsWith(sceneCode.substring(0,2)))))
                .map(obj -> obj.getSceneCode())
                .collect(Collectors.toList());
    }

    /*
    *  查询所有主场景
    * @param hospitalId   医院id
    * */
    public List<SysMainScene>  queryAllMainSscenes () {
        String hospitalId = LoginContext.getHospitalId();
        return sysMainSceneService.list(new LambdaQueryWrapper<SysMainScene>().eq(SysMainScene::getHospitalId,hospitalId));
    }

    /*
     *  根据主场景code查询子场景
     * @param hospitalId    医院id
     * @param mainCode    主场景编码
     * */
    public List<SysSceneDTO> queryByMainSceneCode (String mainCode,String enable) {
        String hospitalId = LoginContext.getHospitalId();
        List<SysSceneDTO> sysSceneS = getBaseMapper().querySceneByHospitalId(hospitalId);
        if(CollectionUtil.isEmpty(sysSceneS)){
            return Collections.emptyList();
        }
        if(StringUtils.isEmpty(enable)){
            return sysSceneS.stream().filter(sysScene->mainCode.equals(sysScene.getMainSceneCode()) && !"2".equals(sysScene.getShowDimensions()) && !StringUtils.isEmpty(sysScene.getShowDimensions())).collect(Collectors.toList());
        } else {
            return sysSceneS.stream().filter(sysScene->mainCode.equals(sysScene.getMainSceneCode()) && !"2".equals(sysScene.getShowDimensions()) && !StringUtils.isEmpty(sysScene.getShowDimensions()) && enable.equals(sysScene.getEnable())).collect(Collectors.toList());
        }
    }

    /*
    * 根据场景编码前缀查询对应的表名称
    * */
    public List<String> getTableNameByCodePrefix (String codePrefix,String hospitalId){
        List<SysScene> list = list(new LambdaQueryWrapper<SysScene>().eq(SysScene::getHospitalId,hospitalId));
        List<String> codes = list.stream().filter(sysScene -> sysScene.getSceneCode().startsWith(codePrefix) && "1".equals(sysScene.getEnable())).map(sysScene -> sysScene.getSceneCode()).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(codes)){
            return Collections.emptyList();
        }
        List<AuditScenarioEnum> enums = AuditScenarioEnum.getAuditScenarioEnums(codes);
        return enums.stream().map(auditEnum -> auditEnum.getTableName()).collect(Collectors.toList());
    }

    /*
    * 编辑住院诊疗+门诊诊疗场景
    * */
    public void submitTr (List<SysSceneDTO> list) {
        if(CollectionUtil.isNotEmpty(list)){
            List<SysSceneFunction> sysSceneFunctions = BeanUtil.copyToList(list,SysSceneFunction.class);
            for (SysSceneFunction sysSceneFunction : sysSceneFunctions) {
                sysSceneFunction.setEnable("1");
                sysSceneFunction.setSceneId(1L);
                sysSceneFunction.setHospitalId(LoginContext.getHospitalId());
            }
            sysSceneFunctionService.saveOrUpdateBatch(sysSceneFunctions);
        }
    }

    /*
    * 查询住院诊疗+门诊诊疗配置信息
    * */
    public List<SysSceneFunction> queryTrList (){
        List<SysSceneFunction> functions = sysSceneFunctionService.list(new LambdaQueryWrapper<SysSceneFunction>().in(SysSceneFunction::getSceneCode,Arrays.asList("opTr","hpTr")).eq(SysSceneFunction::getHospitalId,LoginContext.getHospitalId()));
        if(CollectionUtil.isEmpty(functions)){
            return Arrays.asList(SysSceneFunction.builder().sceneName("门诊诊疗记录查看").sceneCode("opTr").build(),SysSceneFunction.builder().sceneName("住院诊疗记录查看").sceneCode("hpTr").build());
        }
        for (SysSceneFunction function : functions) {
            if("opTr".equals(function.getSceneCode())){
                function.setSceneName("门诊诊疗记录查看");
            } else {
                function.setSceneName("住院诊疗记录查看");
            }
        }
        return functions;
    }


    /**
     * 配置场景config缓存
     * */
    public void setSceneFunctionCache (String hospitalId, List<SysSceneFunction> newSceneFunctions) {
        String redisKey = StrUtil.format(GloablData.GLOBAL_SYS_SCENE_FUNCTION_KEY, hospitalId);
        redisTemplate.delete(redisKey);
        redisTemplate.opsForValue().set(redisKey,newSceneFunctions);
    }

    /**
     * 获取场景缓存
     **/
    public SysSceneFunction getSceneFunctionCache (String auditScenario, String hospitalId) {
        String redisKey = StrUtil.format(GloablData.GLOBAL_SYS_SCENE_FUNCTION_KEY, hospitalId);
        Object o = redisTemplate.opsForValue().get(redisKey);
        Optional<SysSceneFunction> first =  ((List<SysSceneFunction>) o).stream().filter(x -> auditScenario.equals(x.getSceneCode())).findFirst();
        if(!first.isPresent()){
            throw new BaseException("场景编码【" + auditScenario + "】未启用！");
        }
        return first.get();
    }



    /**
     * 配置场景缓存
     * */
    public void setSceneCache (String hospitalId, List<SysScene> scenes) {
        String redisKey = StrUtil.format(GloablData.GLOBAL_SYS_SCENE_KEY, hospitalId);
        redisTemplate.delete(redisKey);
        redisTemplate.opsForValue().set(redisKey,scenes);
    }

    /**
     * 获取场景缓存
     **/
    public Object getSceneCache (String hospitalId) {
        String redisKey = StrUtil.format(GloablData.GLOBAL_SYS_SCENE_KEY, hospitalId);
        return redisTemplate.opsForValue().get(redisKey);
    }

    public List<SysSceneDTO> getIgnoreAndSelfExpenseConfig (String hospitalId,String isSelf,String isSelfAgreement,String isIgnore,String enable,String auditScenario){
        List<SysSceneFunction> functionList = sysSceneFunctionService.getIgnoreAndSelfExpenseConfig(hospitalId,isSelf,isSelfAgreement,isIgnore,enable,auditScenario);
        if(CollectionUtil.isEmpty(functionList)){
            return Collections.emptyList();
        }
        List<SysSceneDTO> list = BeanUtil.copyToList(functionList,SysSceneDTO.class);
        return list;
    }

}
