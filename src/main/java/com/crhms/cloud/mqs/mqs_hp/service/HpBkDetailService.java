package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpBkDetail;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpBkDetailMapper;
import com.crhms.cloud.mqs.mqs_hp.service.HpBkDetailService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-每日记账明细(HpBkDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 09:48:16
 */
@Service("hpBkDetailService")
public class HpBkDetailService extends ServiceImpl< HpBkDetailMapper, HpBkDetail> {

}
