package com.crhms.cloud.mqs.sys.enums;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 审核场景枚举类
 */
@Getter
@AllArgsConstructor
public enum AuditScenarioEnum {

    opPt("opPt","mqs_op_pt","门诊缴费审核"),

    opPct("opPct","mqs_op_pct","门诊处方审核"),

    opRg("opRg","mqs_op_rg","门诊挂号审核"),

    hpRg("hpRg","mqs_hp_rg","住院登记审核"),

    hpDo("hpDo","mqs_hp_do","医嘱审核"),

    hpBk("hpBk","mqs_hp_bk","每日记账审核"),

    hpTf("hpTf","mqs_hp_tf","转科室审核"),

    hpPred("hpPred","mqs_hp_pred","预出院审核"),

    hpOut("hpOut","mqs_hp_out","出院审核"),

    hpSettle("hpSettle","mqs_hp_settle","结算审核");

    //opTr("opTr","mqs_op_tr","门诊诊疗记录查看"),

    //hpTr("hpTr","mqs_hp_tr","住院诊疗记录查看");

    private String auditScenario;
    private String tableName;
    private String desc;

    /*
    * 根据场景编码查询
    *
    * */
    public static List<AuditScenarioEnum> getAuditScenarioEnums (List<String> auditScenarios) {
        if(CollectionUtil.isEmpty(auditScenarios)){
            return Arrays.asList(AuditScenarioEnum.values());
        }
        return auditScenarios.stream().map(auditScenario->AuditScenarioEnum.valueOf(auditScenario)).collect(Collectors.toList());
    }

    /*
     * 根据表明查询
     *
     * */
    public static AuditScenarioEnum getAuditScenarioEnumByTableName (String tableName) {
        for(AuditScenarioEnum auditScenarioEnum : AuditScenarioEnum.values()) {
             if(tableName.equals(auditScenarioEnum.getTableName())) {
                 return auditScenarioEnum;
             }
        }
        return null;
    }
}
