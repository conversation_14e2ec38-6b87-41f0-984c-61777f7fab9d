package com.crhms.cloud.mqs.sys.domain;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 系统管理-反馈类型配置(SysReasonType)实体类
 *
 * <AUTHOR>
 * @since 2023-03-24 15:32:45
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_reason_type")
public class SysReasonType extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 530391603449776170L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_SCENE_CODE="scene_code";
    public static final String FIELD_REASON_TYPE="reason_type";
    public static final String FIELD_REASON_DES="reason_des";
    public static final String FIELD_ENABLE="enable";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 审核场景
     */    
    @TableField
    private String sceneCode;
    /**
     * 反馈类型编码
     */    
    @TableField
    private String reasonType;
    /**
     * 反馈原因
     */    
    @TableField
    private String reasonDes;
    /**
     * 停启用
     */    
    @TableField
    private String enable;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;


}

