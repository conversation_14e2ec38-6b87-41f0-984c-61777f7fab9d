package com.crhms.cloud.mqs.mqs_ais.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 运营管理-基金管理-个性化配置(SysMonitorPersonal)实体类
 *
 * <AUTHOR>
 * @since 2023-03-20 10:38:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_monitor_personal")
public class SysMonitorPersonal implements Serializable {
    private static final long serialVersionUID = 702468266680555540L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_USER_ID="user_id";
    public static final String FIELD_IS_CDS="is_cds";
    public static final String FIELD_IS_CI="is_ci";
    public static final String FIELD_IS_SD="is_sd";
    public static final String FIELD_IS_TD="is_td";
    public static final String FIELD_IS_SI="is_si";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 用户id
     */    
    @TableField
    private String userId;
    /**
     * 是否慢特病 1是 2否
     */
    @TableField
    private String isCdsCi;
    /**
     * 是否慢病 1是 2否
     */    
    @TableField
    private String isCds;
    /**
     * 是否特病 1是 2否
     */    
    @TableField
    private String isCi;
    /**
     * 是否单病种 1是 2否
     */    
    @TableField
    private String isSd;
    /**
     * 是否靶向药 1是 2否
     */    
    @TableField
    private String isTd;
    /**
     * 是否大病 1是 2否
     */    
    @TableField
    private String isSi;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;


}

