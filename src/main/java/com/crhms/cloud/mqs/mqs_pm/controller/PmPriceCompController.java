package com.crhms.cloud.mqs.mqs_pm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.mqs_pm.service.PmPriceCompService;
import com.crhms.cloud.mqs.mqs_pm.vo.PmPriceCompQueryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 物价管理-院内物价对比 controller
 *
 * @author: wangxingang
 * @date: 2023/02/02
 */

@RestController
@RequestMapping("/api/mqs/pmComp")
public class PmPriceCompController {

    @Autowired
    private PmPriceCompService service;


    /**
     * 分页查询
     *
     * @param queryVo  查询条件
     * @param page     页序号
     * @param pageSize 页大小
     */
    @PostMapping("/page")
    public ResponseEntity<List<Map<String, Object>>> queryPage(@RequestBody(required = false) PmPriceCompQueryVo queryVo,
                                                               @RequestParam(value = "page", defaultValue = "1") int page,
                                                               @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<Object> pageInfo = PageUtil.getPage(page, pageSize);
        return new ResponseEntity<>(service.queryPage(queryVo, pageInfo), PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }





    /**
     * 导出excel
     *
     * @param response 响应
     * @param queryVo  查询条件
     */
    @PostMapping("/export")
    public void exportExcel(HttpServletResponse response,
                            @RequestBody(required = false) PmPriceCompQueryVo queryVo) {
        service.exportExcel(response, queryVo);
    }
}
