package com.crhms.cloud.mqs.basic.vo;

import lombok.Data;

/**
 * 引擎审核返回结果类
 */
@Data
public class EngAuditResultVo {
    /**
     * 是否执行成功
     */
    private String is_success;
    /**
     * 总耗时
     */
    private Integer total_time;
    /**
     * 返回消息
     */
    private String message;
    /**
     * 本地规则执行结果
     */
    private EngPlReVo local;
    /**
     * 审核引擎执行结果
     */
    private EngPlReVo engine;
    /**
     * 省平台执行结果
     */
    private EngPlReVo platform;
}
