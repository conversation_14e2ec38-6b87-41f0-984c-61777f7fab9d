<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">


    <!-- 字段调整 -->
    <changeSet id="20250527-1" author="zhaoyac (generated)" >
        <sql>
            ALTER TABLE  mqs_hp_operation_record MODIFY COLUMN opera_start_date varchar(100) NULL COMMENT '手术开始时间';
            ALTER TABLE  mqs_hp_operation_record MODIFY COLUMN opera_end_date varchar(100)  NULL COMMENT '手术结束时间';
            ALTER TABLE  mqs_hp_operation_record MODIFY COLUMN opera_doctor_code varchar(100)  NULL COMMENT '手术医师编码';
            ALTER TABLE  mqs_hp_operation_record MODIFY COLUMN opera_doctor_name varchar(100)   NULL COMMENT '手术医师姓名';
            ALTER TABLE  mqs_hp_operation_record MODIFY COLUMN operation_type_code varchar(100)  NULL COMMENT '手术类别编码 1.择期手术　2.急诊手术';


            ALTER TABLE  mqs_hp_operation_record_his MODIFY COLUMN opera_start_date varchar(100) NULL COMMENT '手术开始时间';
            ALTER TABLE  mqs_hp_operation_record_his MODIFY COLUMN opera_end_date varchar(100)  NULL COMMENT '手术结束时间';
            ALTER TABLE  mqs_hp_operation_record_his MODIFY COLUMN opera_doctor_code varchar(100)  NULL COMMENT '手术医师编码';
            ALTER TABLE  mqs_hp_operation_record_his MODIFY COLUMN opera_doctor_name varchar(100)   NULL COMMENT '手术医师姓名';
            ALTER TABLE  mqs_hp_operation_record_his MODIFY COLUMN operation_type_code varchar(100)  NULL COMMENT '手术类别编码 1.择期手术　2.急诊手术';
        </sql>
    </changeSet>

    <changeSet id="20250527-2" author="zhaoyac (generated)" >
        <modifyDataType tableName="mqs_hp_rg_audit" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_rg_audit_his" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_bk_audit" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_bk_audit_his" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_do_audit" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_do_audit_his" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_tf_audit" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_tf_audit_his" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_out_audit" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_out_audit_his" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_pred_audit" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_pred_audit_his" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_settle_audit" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_hp_settle_audit_his" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_rg_audit" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_rg_audit_his" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_pt_audit" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_pt_audit_his" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_pct_audit" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_op_pct_audit_his" columnName="rule_reason" newDataType="TEXT"/>

        <modifyDataType tableName="mqs_mr_base_audit" columnName="rule_reason" newDataType="TEXT"/>

    </changeSet>


    <changeSet id="20250527-3" author="zhaoyac (generated)" >
        <sql>
            ALTER TABLE mqs_sys_audit_return_log MODIFY COLUMN id bigint auto_increment NOT NULL;
        </sql>
    </changeSet>

</databaseChangeLog>