package com.crhms.cloud.mqs.mqs_op.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.mapper.BaseAuditMapper;
import com.crhms.cloud.mqs.basic.service.BaseAuditService;
import com.crhms.cloud.mqs.basic.vo.BaseMedicalParam;
import com.crhms.cloud.mqs.basic.vo.HisBaseAuditDto;
import com.crhms.cloud.mqs.basic.vo.EngAuditResultVo;
import com.crhms.cloud.mqs.mqs_op.domain.OpPct;
import com.crhms.cloud.mqs.mqs_op.mapper.OpPctMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 门诊审核-门诊处方审核(OpPct)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-19 09:39:25
 */
@Log4j2
@Service("opPctService")
public class OpPctService extends ServiceImpl<OpPctMapper, OpPct> implements BaseAuditService {

    @Autowired
    private OpPctHisService opPctHisService;
    @Autowired
    private BaseAuditMapper baseAuditMapper;


    @Override
    public void beforeAudit(String batch, HisBaseAuditDto hisBaseAuditDto) {

    }

    @Override
    public void afterAudit(String batch, EngAuditResultVo result, HisBaseAuditDto hisBaseAuditDto, List<BaseMedicalParam> data) {

    }

}
