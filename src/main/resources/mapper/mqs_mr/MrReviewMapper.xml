<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_mr.mapper.MrReviewMapper">


    <sql id="BaseSql">
     head.no, head.admission_no, head.bmi_number, head.drg_code, head.dip_code, head.bill_date, head.hospital_level, head.hospital_type, head.claim_type_id, head.benefit_type_id, head.self_expense, head.bmi_code, head.bmi_name, head.in_diagnosis_code, head.in_diagnosis_name, head.out_diagnosis_code, head.out_diagnosis_name, head.admission_date, head.discharge_date, head.patient_id, head.patient_name, head.patient_gender, head.patient_birthday, head.is_bkl, head.is_pregnant, head.is_lactating, head.total_amount, head.personnel_type, head.treatment_type, head.bmi_convered_amount, head.bmi_overall_amount, head.unusual_flag, head.benefit_group_code, head.war_business_flag, head.single_disease_code, head.single_disease_name, head.cs_disease_code, head.cs_disease_name, head.out_zone_code, head.medical_record_id, head.is_trans_hospital, head.is_public_hosp, head.patient_idno, head.is_discharge, head.item_date, head.dept_code, head.dept_name, head.doc_id, head.doc_name, head.doc_level, head.charging_flag, head.pay_ratio, head.pay_amount, head.register_doc_id, head.register_doc_name, head.register_id, head.register_time, head.register_nums, head.registration_fee, head.tf_from_dept_code, head.tf_from_dept_name, head.tf_from_doc_code, head.tf_from_doc_name, head.tf_to_dept_code, head.tf_to_dept_name, head.tf_to_doc_code, head.tf_to_doc_name, head.tf_date, head.fee_date, CONCAT_WS(',',head.diagnosis_code1, head.diagnosis_code2, head.diagnosis_code3, head.diagnosis_code4, head.diagnosis_code5, head.diagnosis_code6, head.diagnosis_code7, head.diagnosis_code8, head.diagnosis_code9, head.diagnosis_code10, head.diagnosis_code11, head.diagnosis_code12, head.diagnosis_code13, head.diagnosis_code14, head.diagnosis_code15, head.diagnosis_code16) as secondaryDiseaseId, CONCAT_WS(',',head.diagnosis_name1, head.diagnosis_name2, head.diagnosis_name3, head.diagnosis_name4, head.diagnosis_name5, head.diagnosis_name6, head.diagnosis_name7, head.diagnosis_name8, head.diagnosis_name9, head.diagnosis_name10, head.diagnosis_name11, head.diagnosis_name12, head.diagnosis_name13, head.diagnosis_name14, head.diagnosis_name15, head.diagnosis_name16) as secondaryDiseaseZh, head.expand_field1, head.expand_field2, head.expand_field3, head.expand_field4, head.expand_field5, head.batch_no, head.violation_flag, head.audit_nums, head.audit_time, head.mr_status, head.mr_opinion, head.mr_time, head.last_updated_by, head.last_updated_date, head.created_by, head.created_date, head.hospital_id
    </sql>

    <sql id="BaseDetailSql">
        detail.detail_no, detail.admission_no, detail.no, detail.bill_date, detail.item_id, detail.item_name, detail.his_item_id, detail.his_item_name,  detail.ptype, detail.item_type_code, detail.item_type_name, detail.item_date, detail.numbers, detail.price, detail.costs, detail.cost_number, detail.cost_costs, detail.bmi_convered_amount, detail.bmi_overall_amount, detail.specification, detail.usage,detail.usage_unit,usage_days, detail.apply_doctor_code, detail.apply_doctor_name, detail.apply_doctor_level, detail.apply_dept_code, detail.apply_dept_name, detail.exec_doctor_code, detail.exec_doctor_name, detail.exec_dept_code, detail.exec_dept_name, detail.rt_doctor_code, detail.rt_doctor_name, detail.rp_nurse_code, detail.rp_nurse_name, detail.charging_flag, detail.self_expense, detail.self_original, detail.frequency_interval, detail.approval_number, detail.z_physicianap, detail.posts_number, detail.pay_ratio, detail.abroad_drug_flag, detail.out_hospital_flag, detail.violation_flag, detail.is_current,detail.rule_origin, detail.violation_type, detail.rule_type, detail.rule_type_name, detail.rule_codes, detail.rule_names, detail.rule_reasons, detail.reason_types, detail.reason_dess, detail.batch_no, detail.last_updated_by, detail.last_updated_date, detail.created_by, detail.created_date, detail.hospital_id, detail.category_name, detail.outpatient_medication, detail.route_administration
    </sql>

    <select id="queryListByPage" resultType="com.crhms.cloud.mqs.mqs_mr.domain.MrReview">
        select
        head.id,
        <include refid="BaseSql"/>
        from ${table} head
        <where>
            <if test="queryVO.no != null and queryVO.no != ''">
            and no like CONCAT('%',#{queryVO.no,jdbcType = VARCHAR},'%')
        </if>
            <if test="queryVO.admissionNo != null and queryVO.admissionNo != ''">
                and admission_no like CONCAT('%',#{queryVO.admissionNo,jdbcType = VARCHAR},'%')
            </if>
            <if test="queryVO.hsStatus != null and queryVO.hsStatus != ''">
                and hs_status = #{queryVO.hsStatus}
            </if>
            <if test="queryVO.mrStatus != null and queryVO.mrStatus != ''">
                and mr_status = #{queryVO.mrStatus}
            </if>
            <if test="queryVO.inpatientArea != null and queryVO.inpatientArea != ''">
                and inpatient_area = #{queryVO.inpatientArea}
            </if>
            <if test="queryVO.inpatientAreas != null and queryVO.inpatientAreas.size() > 0 ">
                and inpatient_area in
                <foreach collection="queryVO.inpatientAreas" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="queryVO.benefitType != null and queryVO.benefitType != ''">
                and benefit_type = #{queryVO.benefitType}
            </if>
            <if test="queryVO.personnelType != null and queryVO.personnelType != ''">
                and personnel_type = #{queryVO.personnelType}
            </if>
            <if test="queryVO.medicalType != null and queryVO.medicalType != ''">
                and medical_type = #{queryVO.medicalType}
            </if>
            <if test="queryVO.patient != null and queryVO.patient != ''">
                and ( patient like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%')
                or patient like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%'))
            </if>
            <if test="queryVO.ruleCodes != null and queryVO.ruleCodes.size() > 0">
                and no in (
                select no from ${table}_audit where hospital_id = #{queryVO.hospitalId} and rule_code in
                <foreach collection="queryVO.ruleCodes" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="queryVO.inDateFrom != null and queryVO.inDateFrom !='' ">
                and in_date &gt;= concat(#{queryVO.inDateFrom},' 00:00:00')
            </if>
            <if test="queryVO.inDateTo != null and queryVO.inDateTo !='' ">
                and in_date &lt;= concat(#{queryVO.inDateTo},' 23:59:59')
            </if>
            <if test="queryVO.violationFlag != null and queryVO.violationFlag != ''">
                and violation_flag = #{queryVO.violationFlag}
            </if>
            <if test="queryVO.batchNo != null and queryVO.batchNo != ''">
                and batch_no = #{queryVO.batchNo}
            </if>
            <if test="queryVO.hospitalId != null and queryVO.hospitalId != ''">
                and hospital_id = #{queryVO.hospitalId}
            </if>
        </where>
    </select>

    <select id="queryLogByPage" resultType="com.crhms.cloud.mqs.mqs_mr.domain.MrLog">

        SELECT * from mqs_mr_log
        where hospital_id = #{hospitalId}
        <if test="no != null and no !='' ">
            and no like CONCAT('%',#{no,jdbcType = VARCHAR},'%')
        </if>
        <if test="admissionNo != null and admissionNo !='' ">
            and admission_no like CONCAT('%',#{admissionNo,jdbcType = VARCHAR},'%')
        </if>
        <if test="userId != null and userId !='' ">
            and user_id like CONCAT('%',#{userId,jdbcType = VARCHAR},'%')
        </if>
        <if test="userName != null and userName !='' ">
            and user_name like CONCAT('%',#{userName,jdbcType = VARCHAR},'%')
        </if>
        order by mr_time desc
    </select>

    <update id="updateReviewData">
        UPDATE mqs_mr_case
        SET
        <if test="mrStatus != null and mrStatus != '' ">
            mr_status = #{mrStatus},
        </if>
        <if test="lables != null and lables != '' ">
            lables = #{lables},
        </if>
        <if test="mrOpinion != null and mrOpinion != '' ">
            mr_opinion = #{mrOpinion},
        </if>
        <if test="mrBy != null and mrBy != '' ">
            mr_by = #{mrBy},
        </if>
        <if test="mrByName != null and mrByName != '' ">
            mr_by_name = #{mrByName},
        </if>
        mr_time = now()
        WHERE case_no = #{no}
        AND admission_no = #{admissionNo}
        AND hospital_id = #{hospitalId}
    </update>

    <select id="queryDetailSourceAndClinical" resultType="com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail">
        select
            detail.id,
            detail.detail_no,
            CASE
                WHEN maudit.detail_no IS NOT NULL AND baudit.detail_no IS NOT NULL THEN '1,2'
                WHEN maudit.detail_no IS NOT NULL AND baudit.detail_no IS NULL THEN '2'
                WHEN maudit.detail_no IS NULL AND baudit.detail_no IS NOT NULL THEN '1'
                WHEN maudit.detail_no IS NULL AND baudit.detail_no IS NULL THEN '1'
                ELSE NULL
                END AS audit_source,
            CASE
                WHEN  baudit.detail_no IS NULL AND maudit.detail_no IS NULL THEN '3'   <!-- 明细未违规（没有机审以及人工违规） -->
                WHEN  (baudit.detail_no IS NOT NULL OR maudit.detail_no IS NOT NULL) AND (aignore.detail_no IS NULL AND detail.self_expense != '2') THEN '0'     <!-- 明细未处理（机审/人工违规 但是 没有进行忽略+转自费） -->
                WHEN  (baudit.detail_no IS NOT NULL OR maudit.detail_no IS NOT NULL) AND detail.self_expense = '2' THEN '5'  <!-- 明细处理完成-转自费（机审/人工违规 并且 转自费 且 有无忽略都可） -->
                WHEN  (baudit.detail_no IS NOT NULL OR maudit.detail_no IS NOT NULL) AND detail.self_expense != '2' and aignore.detail_no IS NOT NULL THEN '4'  <!-- 明细处理完成-忽略（机审/人工违规 并且 进行忽略 且  没转自费） -->
                ELSE '0'
                END AS clinical_status,
            CASE
                WHEN  maudit.detail_no IS NOT NULL THEN maudit.rule_reason
                ELSE  detail.rule_reasons
                END AS ruleReasons,
            CASE
                WHEN  maudit.rule_type_name IS NOT NULL THEN maudit.rule_type_name
                ELSE  detail.rule_type_name
                END AS ruleTypeName,
            CASE
                WHEN  maudit.rule_name IS NOT NULL THEN maudit.rule_name
                ELSE  detail.rule_names
                END AS ruleNames
        from mqs_mr_base_detail detail
                 left join (
                            select detail_no,hospital_id from mqs_mr_base_audit
                            where detail_no in
                            <foreach collection="detailNos" open="(" close=")" separator="," item="no">
                            #{no}
                            </foreach>
                            group by detail_no,hospital_id
                            ) baudit on detail.detail_no = baudit.detail_no and detail.hospital_id = baudit.hospital_id
                 left join mqs_mr_audit maudit on detail.detail_no = maudit.detail_no and detail.hospital_id = maudit.hospital_id
                 left join mqs_audit_ignore aignore on detail.detail_no = aignore.detail_no  and detail.hospital_id = aignore.hospital_id
        where detail.hospital_id = #{hospitalId}
        and detail.detail_no in
        <foreach collection="detailNos" open="(" close=")" separator="," item="no">
            #{no}
        </foreach>
    </select>

    <select id="queryMrCase" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        SELECT
            base.id,
            base.`no`,
            base.admission_no,
            base.patient_name,
            base.patient_gender,
            base.patient_birthday,
            base.benefit_type_id,
            base.personnel_type,
            base.claim_type_id,
            base.patient_id,
            base.hosp_area_name,
            base.present_dept_name,
            base.doc_name,
            base.total_amount,
            base.patient_idno,
            base.discharge_date,
            base.admission_date,
            mr.mr_status,
            base.out_diagnosis_code,
            base.out_diagnosis_name,
            CASE WHEN base.discharge_date IS NULL THEN 0 ELSE 1 END AS discharge_state,
            /*CASE
                WHEN audit.no IS NOT NULL AND ba.no IS NOT NULL THEN '1,2'
                WHEN audit.no IS NOT NULL AND ba.no IS NULL THEN '2'
                WHEN audit.no IS NULL AND ba.no IS NOT NULL THEN '1'
                WHEN audit.no IS NULL AND ba.no IS NULL THEN '1'
                ELSE NULL
                END AS audit_source,*/
            mr.lables
            /*stats.pre_discharge,
            stats.pre_discharge_date*/
        FROM mqs_mr_base base
        JOIN  mqs_mr_case mr on mr.case_no = base.`no` and base.hospital_id = mr.hospital_id
        /*JOIN  mqs_base_medical_stats stats on stats.`no` = base.no*/
        /*LEFT JOIN  mqs_mr_audit audit ON base.no = audit.no
        LEFT JOIN  mqs_mr_base_audit ba ON base.no = ba.no*/
        WHERE base.`no` = #{no} and base.hospital_id = #{hospitalId}
        GROUP BY base.id,mr.mr_status,mr.lables
    </select>

    <select id="queryMinRuleTypeByNo" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        WITH ranked_data AS (
        SELECT no, rule_type, rule_type_name,
        ROW_NUMBER() OVER (PARTITION BY no ORDER BY rule_type ASC) AS rn
        FROM (
        SELECT no, rule_type, rule_type_name FROM mqs_mr_base_audit WHERE hospital_id = #{hospitalId}
        <if test="nos != null and nos.size() > 0">
            AND no IN
            <foreach collection="nos" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        UNION ALL
        SELECT no, rule_type, rule_type_name FROM mqs_mr_audit WHERE hospital_id = #{hospitalId}
        <if test="nos != null and nos.size() > 0">
            AND no IN
            <foreach collection="nos" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        ) AS combined_results
        )
        SELECT no, rule_type, rule_type_name
        FROM ranked_data
        WHERE rn = 1;
    </select>

    <select id="queryMrAuditPatient" resultType="com.crhms.cloud.mqs.basic.vo.AuditPatientVO">
        SELECT
        audit.no,
        base.patient_id,
        base.patient_name
        FROM mqs_mr_audit audit
        JOIN mqs_mr_base base ON audit.no = base.no
        WHERE audit.hospital_id = '1'
        AND NOT EXISTS (
        SELECT 1 FROM mqs_audit_ignore aignore WHERE audit.detail_no = aignore.detail_no
        )
        AND EXISTS (
        SELECT 1 FROM mqs_mr_base_detail detail WHERE audit.detail_no = detail.detail_no AND detail.self_expense != 2
        )
        AND base.source_id in
        <foreach collection="sysSceneCodes" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        <if test="patientName != null and patientName != '' ">
            AND base.patient_name like CONCAT('%', #{patientName}, '%')
        </if>
        <if test="docId != null and docId != '' ">
            AND base.doc_id = #{docId}
        </if>
        <if test="presentDeptCode != null and presentDeptCode != '' ">
            AND base.present_dept_code = #{presentDeptCode}
        </if>
    </select>

    <select id="queryMrBaseAuditPatient" resultType="com.crhms.cloud.mqs.basic.vo.AuditPatientVO">
        SELECT
        audit.`no`,
        base.patient_id,
        base.patient_name
        FROM mqs_mr_base_audit audit
        JOIN mqs_mr_base base ON audit.no = base.no WHERE audit.hospital_id = '1'
        AND NOT EXISTS (
        SELECT 1 FROM mqs_audit_ignore aignore WHERE audit.detail_no = aignore.detail_no
        )
        AND EXISTS (
        SELECT 1 FROM mqs_mr_base_detail detail WHERE audit.detail_no = detail.detail_no AND detail.self_expense != 2
        )
        AND base.source_id in
        <foreach collection="sysSceneCodes" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        <if test="patientName != null and patientName != '' ">
            AND base.patient_name like CONCAT('%', #{patientName}, '%')
        </if>
        <if test="docId != null and docId != '' ">
            AND base.doc_id = #{docId}
        </if>
        <if test="presentDeptCode != null and presentDeptCode != '' ">
            AND base.present_dept_code = #{presentDeptCode}
        </if>
    </select>

    <select id="queryMrBaseMedicalCase" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        select
            head.patient_id,
            head.patient_name,
            head.patient_gender,
            head.patient_birthday,
            head.admission_date,
            head.mr_status,
            head.mr_time,
            head.violation_flag,
            head.audit_time,
            head.`no`,
            head.admission_no,
            head.claim_type_id,
            head.personnel_type,
            head.benefit_type_id,
            head.is_discharge,
            head.out_zone_code,
            a.name as outZoneName,
            head.dept_code,
            head.dept_name,
            head.doc_id,
            head.doc_name,
            head.bmi_convered_amount,
            head.bmi_overall_amount,
            head.total_amount,
            CONCAT_WS(',', head.in_diagnosis_name, head.out_diagnosis_name, head.diagnosis_name1, head.diagnosis_name2, head.diagnosis_name3, head.diagnosis_name4, head.diagnosis_name5, head.diagnosis_name6, head.diagnosis_name7, head.diagnosis_name8, head.diagnosis_name9, head.diagnosis_name10, head.diagnosis_name11, head.diagnosis_name12, head.diagnosis_name13, head.diagnosis_name14, head.diagnosis_name15, head.diagnosis_name16) as secondaryDiseaseZh
        from ${table} head
                 left join ipp_zone a on a.code = head.out_zone_code
        where head.hospital_id = #{hospitalId}
          and head.`no` = #{no}
    </select>

    <select id="queryMrDetailByPage" resultType="com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail">
        SELECT
        CASE
        WHEN EXISTS (
        SELECT 1
        FROM ${table}_audit d
        WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
        AND d.no != detail.no
        ) THEN '2'
        WHEN EXISTS (
        SELECT 1
        FROM ${table}_audit d
        WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
        AND d.no = detail.no
        ) THEN '1'
        ELSE '0'
        END AS relatedRecords,
        <include refid="BaseDetailSql"/>
        FROM ${table}_detail detail
        WHERE detail.hospital_id = #{hospitalId}
        <if test="queryVO.no != null and queryVO.no != ''">                                       <!-- 单据号-->
            and detail.no = #{queryVO.no}
        </if>
        <if test="queryVO.relatedRecords != null and queryVO.relatedRecords != ''">
            and CASE
            WHEN EXISTS (
            SELECT 1
            FROM ${table}_audit d
            WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
            AND d.no != detail.no
            ) THEN '2'
            WHEN EXISTS (
            SELECT 1
            FROM ${table}_audit d
            WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
            AND d.no = detail.no
            ) THEN '1'
            ELSE '0'
            END = #{queryVO.relatedRecords}
        </if>
        <if test="queryVO.detailNos != null and queryVO.detailNos.size() > 0">
            and detail.detail_no in
            <foreach collection="queryVO.detailNos" item="detailNo" open="(" separator="," close=")">
                #{detailNo}
            </foreach>
        </if>

        <if test="queryVO.violationFlag != null and  queryVO.violationFlag != '' ">               <!-- 是否违规-->
            <choose>
                <when test="queryVO.violationFlag == 0 ">
                    and detail.violation_flag = #{queryVO.violationFlag}
                    and not EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no)
                </when>
                <when test="queryVO.violationFlag == 1 ">
                    and (detail.violation_flag = #{queryVO.violationFlag} or EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no))
                </when>
            </choose>
        </if>

        <if test="queryVO.auditSource != null and  queryVO.auditSource != '' ">               <!-- 审核渠道-->
            <choose>
                <when test="queryVO.auditSource == 0 ">                                       <!-- 医保-->
                    and detail.rule_origin like CONCAT('%', '2', '%')
                </when>
                <when test="queryVO.auditSource == 1 ">                                       <!-- 院内-->
                    and not EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no)
                </when>
                <when test="queryVO.auditSource == 2 ">
                    and EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no)
                </when>
            </choose>
        </if>

        <if test="queryVO.ruleTypes != null and queryVO.ruleTypes.size() > 0">     <!--违规级别-->
            and (
            EXISTS (
            SELECT 1
            FROM mqs_mr_audit mrAudit
            WHERE mrAudit.no = detail.no and detail.detail_no = mrAudit.detail_no
            AND mrAudit.rule_type IN
            <foreach collection="queryVO.ruleTypes" item="ruleTypeItem" open="(" separator="," close=")">
                #{ruleTypeItem}
            </foreach>
            )
            OR detail.rule_type IN
            <foreach collection="queryVO.ruleTypes" item="ruleTypeItem" open="(" separator="," close=")">
                #{ruleTypeItem}
            </foreach>
            )
        </if>

        <if test="queryVO.selfExpense != null and  queryVO.selfExpense != '' ">    <!-- 自费状态 -->
            and detail.self_expense = #{queryVO.selfExpense}
        </if>

        <if test="queryVO.itemName != null and  queryVO.itemName != '' ">           <!-- 项目名称 -->
            and detail.item_name like concat('%' , #{queryVO.itemName} ,'%')
        </if>

        <if test="queryVO.itemTypeCode != null and queryVO.itemTypeCode != ''">     <!-- 项目类型编码 -->
            and detail.item_type_code = #{queryVO.itemTypeCode}
        </if>

        <if test="queryVO.itemIds != null and queryVO.itemIds.size() > 0">
            and detail.item_id in
            <foreach collection="queryVO.itemIds" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>

        <if test="queryVO.categoryName != null and  queryVO.categoryName != '' ">           <!-- 甲乙类 -->
            and detail.category_name like concat('%' , #{queryVO.categoryName} ,'%')
        </if>

        <if test="queryVO.outpatientMedication != null and  queryVO.outpatientMedication != '' ">    <!-- 是否出院带药 1-是，0-否 -->
            and detail.outpatient_medication = #{queryVO.outpatientMedication}
        </if>

    </select>

    <select id="queryMrDetailByItemId" resultType="com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail">
        SELECT
        MIN(detail.detail_no) AS detail_no,
        detail.item_id
        FROM ${table}_detail detail
        WHERE detail.hospital_id = #{hospitalId}
        <if test="queryVO.no != null and queryVO.no != ''">                                       <!-- 单据号-->
            and detail.no = #{queryVO.no}
        </if>
        <if test="queryVO.violationFlag != null and  queryVO.violationFlag != '' ">               <!-- 是否违规-->
            <choose>
                <when test="queryVO.violationFlag == 0 ">
                    and detail.violation_flag = #{queryVO.violationFlag}
                    and not EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no)
                </when>
                <when test="queryVO.violationFlag == 1 ">
                    and (detail.violation_flag = #{queryVO.violationFlag} or EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no))
                </when>
            </choose>
        </if>

        <if test="queryVO.auditSource != null and  queryVO.auditSource != '' ">               <!-- 审核渠道-->
            <choose>
                <when test="queryVO.auditSource == 0 ">
                    and detail.rule_origin like CONCAT('%', '2', '%')
                </when>
                <when test="queryVO.auditSource == 1 ">
                    and not EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no)
                </when>
                <when test="queryVO.auditSource == 2 ">
                    and EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no)
                </when>
            </choose>
        </if>

        <if test="queryVO.ruleTypes != null and queryVO.ruleTypes.size() > 0">     <!--违规级别-->
            and (
            EXISTS (
            SELECT 1
            FROM mqs_mr_audit mrAudit
            WHERE mrAudit.no = detail.no and detail.detail_no = mrAudit.detail_no
            AND mrAudit.rule_type IN
            <foreach collection="queryVO.ruleTypes" item="ruleTypeItem" open="(" separator="," close=")">
                #{ruleTypeItem}
            </foreach>
            )
            OR detail.rule_type IN
            <foreach collection="queryVO.ruleTypes" item="ruleTypeItem" open="(" separator="," close=")">
                #{ruleTypeItem}
            </foreach>
            )
        </if>

        <if test="queryVO.selfExpense != null and  queryVO.selfExpense != '' ">    <!-- 自费状态 -->
            and detail.self_expense = #{queryVO.selfExpense}
        </if>

        <if test="queryVO.itemName != null and  queryVO.itemName != '' ">           <!-- 项目名称 -->
            and detail.item_name like concat('%' , #{queryVO.itemName} ,'%')
        </if>

        <if test="queryVO.itemTypeCode != null and queryVO.itemTypeCode != ''">     <!-- 项目类型编码 -->
            and detail.item_type_code = #{queryVO.itemTypeCode}
        </if>

        <if test="queryVO.itemIds != null and queryVO.itemIds.size() > 0">
            and detail.item_id in
            <foreach collection="queryVO.itemIds" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>

        <if test="queryVO.categoryName != null and  queryVO.categoryName != '' ">           <!-- 甲乙类 -->
            and detail.category_name like concat('%' , #{queryVO.categoryName} ,'%')
        </if>

        <if test="queryVO.outpatientMedication != null and  queryVO.outpatientMedication != '' ">    <!-- 是否出院带药 1-是，0-否 -->
            and detail.outpatient_medication = #{queryVO.outpatientMedication}
        </if>
        GROUP BY
        detail.item_id
    </select>



    <select id="queryMrCaseListByPage" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        SELECT
        head.id,
        head.`no`,                                 <!-- 单据号 -->
        mr.lables,                                 <!-- 标记 -->
        head.patient_name,                         <!-- 患者名称 -->
        head.patient_id,                           <!-- 患者id -->
        head.patient_gender,                       <!-- 性别 -->
        head.admission_no,                         <!-- 就诊流水号 -->
        head.present_dept_name,                    <!-- 当前科室名称 -->
        head.present_dept_code,                    <!-- 当前科室 -->
        head.doc_name,                             <!-- 主诊医生 -->
        IFNULL(mr.mr_status,'0') mr_status,        <!-- 人工审核状态 -->
        mr.clinical_status,                         <!-- 临床处理状态 -->
        mr.mr_time,                                 <!-- 人工审核日期 -->
        mr.mr_by,                                   <!-- 最新质控人 -->
        mr.mr_by_name,                              <!-- 最新质控人姓名 -->
        mr.mr_to,                                   <!-- 分配审核人 -->
        mr.mr_to_name,                              <!-- 分配审核人姓名 -->
        head.violation_flag,                        <!-- 是否违规 -->
        head.source_id,                             <!-- 最新机审场景 -->
        head.audit_time,                            <!-- 最新机审日期 -->
        bms.discharge_order_date,                  <!-- 开出院医嘱日期 -->
        bms.pre_discharge,                          <!-- 预出院状态 -->
        bms.pre_discharge_date,                     <!-- 预计出院日期 -->
        head.benefit_type_id,                      <!-- 险种类型 -->
        head.personnel_type,                       <!-- 人员类别 -->
        head.claim_type_id,                        <!-- 医疗类别 -->
        CASE WHEN head.discharge_date IS NULL THEN 0 ELSE 1 END AS discharge_state,    <!-- 在院状态 -->
        CASE WHEN head.bill_date IS NULL THEN 0 ELSE 1 END AS billState,       <!-- 结算状态 -->
        head.bill_date,                             <!-- 结算日期 -->
        head.admission_date,                        <!-- 入院日期 -->
        head.discharge_date,                        <!-- 出院日期 -->
        head.patient_birthday,                       <!-- 出生日期 -->
        head.mr_flag
        FROM  mqs_mr_base head
        left join mqs_mr_case mr on mr.case_no = head.`no` and head.hospital_id = mr.hospital_id
        left join mqs_base_medical_stats bms on bms.no = head.no
        where head.hospital_id = #{hospitalId}

        <if test="queryVO.lables != null and queryVO.lables != ''">  <!--标记-->
            and mr.lables = #{queryVO.lables}
        </if>

        <if test="queryVO.auditTimeFrom != null and queryVO.auditTimeFrom != ''">  <!--机审日期-->
            and head.audit_time &gt;= concat(#{queryVO.auditTimeFrom},' 00:00:00')
        </if>

        <if test="queryVO.auditTimeTo != null and queryVO.auditTimeTo != ''">  <!--机审日期-->
            and head.audit_time &lt;= concat(#{queryVO.auditTimeFrom},' 23:59:59')
        </if>

        <if test="queryVO.mrTimeFrom != null and queryVO.mrTimeFrom != ''">  <!--人工审核日期-->
            and mr.mr_time &gt;= concat(#{queryVO.mrTimeFrom},' 00:00:00')
        </if>

        <if test="queryVO.mrTimeTo != null and queryVO.mrTimeTo != ''">  <!--人工审核日期-->
            and mr.mr_time &lt;= concat(#{queryVO.mrTimeTo},' 23:59:59')
        </if>

        <if test="queryVO.admissionNo != null and queryVO.admissionNo != ''">  <!--就诊流水号-->
            and head.admission_no = #{queryVO.admissionNo}
        </if>

        <if test="queryVO.startDischargeDate != null and queryVO.startDischargeDate != ''">  <!--出院日期-->
            and head.discharge_date &gt;= concat(#{queryVO.startDischargeDate},' 00:00:00')
        </if>

        <if test="queryVO.endDischargeDate != null and queryVO.endDischargeDate != ''">  <!--出院日期-->
            and head.discharge_date &lt;= concat(#{queryVO.endDischargeDate},' 23:59:59')
        </if>

        <if test="queryVO.startBillDate != null and queryVO.startBillDate != ''">  <!--结算日期-->
            and head.bill_date &gt;= concat(#{queryVO.startBillDate},' 00:00:00')
        </if>

        <if test="queryVO.endBillDate != null and queryVO.endBillDate != ''">  <!--结算日期-->
            and head.bill_date &lt;= concat(#{queryVO.endBillDate},' 23:59:59')
        </if>

        <if test="queryVO.auditScenarios != null and queryVO.auditScenarios.size() > 0">   <!--最新机审场景-->
            and head.source_id in
            <foreach collection="queryVO.auditScenarios" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="queryVO.patientId != null and queryVO.patientId != ''">  <!--患者-->
            and ( head.patient_name like concat('%' , #{queryVO.patientId} ,'%')
            or head.patient_id like concat('%' , #{queryVO.patientId} ,'%'))
        </if>

        <if test="queryVO.mrBys != null and queryVO.mrBys.size() > 0">   <!--最新质控人-->
            and mr.mr_by in
            <foreach collection="queryVO.mrBys" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="queryVO.mrTos != null and queryVO.mrTos.size() > 0">   <!--分配审核人-->
            and
            <foreach collection="queryVO.mrTos" open="(" close=")" item="item" separator="or">
                mr.mr_to like concat('%' , #{item} ,'%')
            </foreach>
        </if>

        <if test="queryVO.presentDeptCodes != null and queryVO.presentDeptCodes.size() > 0">  <!--当前科室编码-->
            and head.present_dept_code in
            <foreach collection="queryVO.presentDeptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="queryVO.docIds != null and queryVO.docIds.size() > 0">   <!--主诊医生-->
            and head.doc_id in
            <foreach collection="queryVO.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="queryVO.benefitTypeIds != null and queryVO.benefitTypeIds.size() > 0">   <!--险种类型-->
            and head.benefit_type_id in
            <foreach collection="queryVO.benefitTypeIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="queryVO.personnelTypes != null and queryVO.personnelTypes.size() > 0">   <!--人员类别-->
            and head.personnel_type in
            <foreach collection="queryVO.personnelTypes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="queryVO.claimTypeIds != null and queryVO.claimTypeIds.size() > 0">   <!--医疗类别-->
            and head.claim_type_id in
            <foreach collection="queryVO.claimTypeIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="queryVO.dischargeState != null and queryVO.dischargeState != '' and queryVO.dischargeState == 1">  <!--在院状态: 1-出院-->
            and head.discharge_date is not null
        </if>

        <if test="queryVO.dischargeState != null and queryVO.dischargeState != '' and queryVO.dischargeState == 0">  <!--在院状态：0-在院-->
            and head.discharge_date is null
        </if>

        <if test="queryVO.billState != null and queryVO.billState != '' and queryVO.billState == 1">  <!--结算状态: 1-已结算-->
            and head.bill_date is not null
        </if>

        <if test="queryVO.billState != null and queryVO.billState != '' and queryVO.billState == 0">  <!--结算状态：0-未结算-->
            and head.bill_date is null
        </if>

        <if test="queryVO.toDoAudit != null and queryVO.toDoAudit !=''">    <!--人工审核状态（2-审核完成，1-已审核，0-未审核）-->
            and mr.mr_status = #{queryVO.toDoAudit}
        </if>

        <if test="queryVO.clinicalStatus != null and queryVO.clinicalStatus !=''">    <!--临床处理状态 （0-未处理、1-处理中、2-处理完成、3-无需处理）-->
            and mr.clinical_status = #{queryVO.clinicalStatus}
        </if>

        <if test="queryVO.violationFlag != null and queryVO.violationFlag !=''">      <!--是否违规 （1-违规、0-不违规）-->
            <choose>
                <when test="queryVO.violationFlag == 0 ">
                    and head.violation_flag = #{queryVO.violationFlag}
                    and not EXISTS (select 1 from mqs_mr_audit audit where head.no = audit.no)
                </when>
                <when test="queryVO.violationFlag == 1 ">
                    and (head.violation_flag = #{queryVO.violationFlag} or EXISTS (select 1 from mqs_mr_audit audit where head.no = audit.no))
                </when>
            </choose>
        </if>

        <if test="queryVO.ruleType != null and queryVO.ruleType.size() > 0">     <!--违规级别-->
            and (
            EXISTS (
            SELECT 1
            FROM mqs_mr_audit mrAudit
            WHERE mrAudit.no = head.no
            AND mrAudit.rule_type IN
            <foreach collection="queryVO.ruleType" item="ruleTypeItem" open="(" separator="," close=")">
                #{ruleTypeItem}
            </foreach>
            )
            OR EXISTS (select 1 from mqs_mr_base_audit audit where audit.no = head.no and audit.rule_type IN
            <foreach collection="queryVO.ruleType" item="ruleTypeItem" open="(" separator="," close=")">
                #{ruleTypeItem}
            </foreach>)
            )
        </if>

        <if test="queryVO.preDischarge != null and queryVO.preDischarge != ''">  <!--预出院状态-->
            and bms.pre_discharge  = #{queryVO.preDischarge}
        </if>

        <if test="queryVO.preDischargeDateFrom != null and queryVO.preDischargeDateFrom != ''">  <!--预出院时间-->
            and bms.pre_discharge_date &gt;= concat(#{queryVO.preDischargeDateFrom},' 00:00:00')
        </if>

        <if test="queryVO.preDischargeDateTo != null and queryVO.preDischargeDateTo != ''">  <!--预出院时间-->
            and bms.pre_discharge_date &lt;= concat(#{queryVO.preDischargeDateTo},' 23:59:59')
        </if>

        <if test="queryVO.dischargeOrderDateFrom != null and queryVO.dischargeOrderDateFrom != ''">  <!--开出院医嘱时间-->
            and bms.discharge_order_date &gt;= concat(#{queryVO.dischargeOrderDateFrom},' 00:00:00')
        </if>

        <if test="queryVO.dischargeOrderDateTo != null and queryVO.dischargeOrderDateTo != ''">  <!--开出院医嘱时间-->
            and bms.discharge_order_date &lt;= concat(#{queryVO.dischargeOrderDateTo},' 23:59:59')
        </if>

        <if test="queryVO.dischargeToday != null and  '1'.toString() == queryVO.dischargeToday.toString() "> <!--是否今日出院-->
            and ((head.discharge_date is not null and DATE(head.discharge_date) =  CURDATE()) or left(bms.pre_discharge_date,10) =  CURDATE())
        </if>
        <if test="queryVO.dischargeToday != null and '0'.toString() ==  queryVO.dischargeToday.toString() "> <!--是否今日出院-->
            and ((head.discharge_date is not null and DATE(head.discharge_date) !=  CURDATE()) or left(bms.pre_discharge_date,10) !=  CURDATE())
        </if>
    </select>

    <select id="queryMrDetailByDetailNos" resultType="com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail">
        select
        <include refid="BaseDetailSql"/>
        FROM ${table}_detail detail
        WHERE detail.hospital_id = #{hospitalId}
        AND detail.detail_no in
        <foreach collection="detailNos" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <update id="updateSelfExpenseByDetailNo">
        <foreach collection="items" item="item" separator=";">
        UPDATE mqs_mr_base_detail
        SET
            self_expense = #{item.selfExpense}
        WHERE detail_no = #{item.detailNo}
        AND hospital_id = #{hospitalId}
        </foreach>
    </update>

    <select id="queryMrBaseAudit" resultType="com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail">
        select
            audit.`no`,
            audit.detail_no,
            detail.self_expense
        from ${table}_audit audit
        join ${table}_detail detail on audit.detail_no = detail.detail_no
        where audit.hospital_id = #{hospitalId}
        and audit.no in
        <foreach collection="nos" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        group by audit.detail_no, detail.self_expense,audit.`no`
    </select>

    <select id="queryCaseAudit" resultType="com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail">
        select mmba.`no`,
               mmba.admission_no,
               mmba.rule_code as ruleCodes,
               mmba.rule_name as ruleNames,
               mmba.rule_type,
               mmba.rule_type_name,
               mmba.rule_reason as ruleReasons,
               mmba.rule_origin,
               mmbr.reason_type as reasonTypes,
               mmbr.reason_des as reasonDess
        from mqs_mr_base_audit mmba
        left join mqs_mr_base_reason mmbr on mmba.detail_no = mmbr.detail_no
                and mmba.`no` = mmbr.`no`
        where mmba.detail_no = '0'
          and mmba.no = #{no}
          and mmba.admission_no = #{admissionNo}
          and mmba.hospital_id = #{hospitalId}
    </select>

    <select id="queryMrBaseByNo" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        SELECT
        <include refid="BaseSql"/>,
        head.source_id
        FROM mqs_mr_base head
        where head.hospital_id = #{hospitalId}
        <if test="no != null and no != ''">                                       <!-- 单据号-->
            and head.no = #{no}
        </if>
    </select>

    <select id="queryMrBaseAuditByNo" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        SELECT
            audit.no
        FROM mqs_mr_base_audit audit
        WHERE audit.no = #{no}
        and audit.hospital_id = #{hospitalId}
    </select>
    <select id="queryMrCaseDetail" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">

        SELECT
            detail_no,
            `no`,
            admission_no,
            item_id,
            item_name,
            his_item_id,
            his_item_name,
            item_date,
            numbers,
            price,
            bmi_convered_amount,
            costs,
            specification,
            bill_date
        FROM mqs_mr_base_detail detail
        where detail.hospital_id = #{hospitalId}
        <if test="no != null and no != ''">
            and detail.no = #{no}
        </if>

    </select>

</mapper>

