package com.crhms.cloud.mqs.sys.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.sys.domain.SysRuleLevel;
import com.crhms.cloud.mqs.sys.domain.SysRuleLevelThird;
import com.crhms.cloud.mqs.sys.service.SysRuleLevelService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 规则级别配置表(SysRuleLevel)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-21 17:58:43
 */
@RestController
@RequestMapping("api/mqs/sysRuleLevel")
public class SysRuleLevelController {
    /**
     * 服务对象
     */
    @Resource
    private SysRuleLevelService sysRuleLevelService;


    /**
     * 获取系统违规级别配置
     * @return
     */
    @GetMapping("/queryLevelList")
    public ResponseEntity<List<SysRuleLevel>> queryRuleLevelList(){

        return ResponseEntity.ok(sysRuleLevelService.queryRuleLevelList(LoginContext.getHospitalId()));
    }

    /**
     * 获取第三方违规级别配置
     * @param platform 1自定义2内置3医保平台
     * @return
     */
    @GetMapping("/queryThirdLevelList")
    public ResponseEntity<List<SysRuleLevelThird>> queryThirdLevelList(@RequestParam(value = "platform",required = false) String platform){

        return ResponseEntity.ok(sysRuleLevelService.queryThirdLevelList(platform, LoginContext.getHospitalId()));
    }

    /**
     * 获取违规级别 - 页面
     * @return
     */
    @GetMapping("/queryAllLevelList")
    public ResponseEntity<Map> queryAllLevelList(){

        return ResponseEntity.ok(sysRuleLevelService.queryAllLevelList(LoginContext.getHospitalId()));
    }

    /**
     * 更新规则级别配置
     * @param data
     * @return
     */
    @PostMapping("/editConnfig")
    public ResponseEntity editConnfig(@RequestBody Object data){
        Map<String,JSONArray> dataMap = JSONUtil.toBean(JSONUtil.toJsonStr(data), Map.class);

        List<SysRuleLevel> collect1 = JSONUtil.toList( dataMap.get("collect1"),SysRuleLevel.class);
        List<SysRuleLevelThird> collect2 = JSONUtil.toList( dataMap.get("collect2"),SysRuleLevelThird.class);
        List<SysRuleLevelThird> collect3 = JSONUtil.toList( dataMap.get("collect3"),SysRuleLevelThird.class);
        sysRuleLevelService.editConnfig(collect1, collect2, collect3);
        return ResponseEntity.ok("更新成功！");

    }

}

