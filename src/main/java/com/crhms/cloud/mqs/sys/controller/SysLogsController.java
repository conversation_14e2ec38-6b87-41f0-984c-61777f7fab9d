package com.crhms.cloud.mqs.sys.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.sys.domain.SysLogs;
import com.crhms.cloud.mqs.sys.dto.SysLogsVO;
import com.crhms.cloud.mqs.sys.service.SysLogsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统日志控制层
 *
 * <AUTHOR>
 * @since 2024-12-21 13:37:48
 */
@RestController
@RequestMapping("api/mqs/sysLogs")
public class SysLogsController {

    @Autowired
    private SysLogsService sysLogsService;

    /**
     * 分页查询日志
     * @param
     * @return
     */
    @PostMapping("/queryRulesList")
    public ResponseEntity<List<SysLogs>> queryRulesList(@RequestBody SysLogsVO queryVO) {
        Page<Object> pageInfo = PageUtil.getPage(queryVO.getPage(), queryVO.getPageSize());
        return new ResponseEntity<>(this.sysLogsService.queryByPage(pageInfo,queryVO), PageUtil.getTotalHeader(pageInfo),HttpStatus.OK);
    }






}
