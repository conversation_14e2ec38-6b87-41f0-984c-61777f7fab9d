package com.crhms.cloud.mqs.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.mqs.sys.domain.SysRules;
import com.crhms.cloud.mqs.sys.domain.SysScene;
import com.crhms.cloud.mqs.sys.dto.SysSceneDTO;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 运营管理-审核场景(SysScene)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-12-21 13:37:48
 */

@Mapper
public interface SysSceneMapper extends BaseMapper<SysScene> {

    /**
     * 审核管理查询
     *
     * @return 对象列表
     */
    List<SysScene> queryList(@Param("hospitalId") String hospitalId);


    /**
     * 查询审核场景匹配的规则
     *
     * @param pageInfo
     * @param rule
     * @param sceneCode
     * @param hospitalId
     * @param ruleLevelList
     * @return
     */
    List<SysRules> queryRulesByScene(@Param("pageInfo") Page<Object> pageInfo,
                                     @Param("rule") String rule,
                                     @Param("sceneCode") String sceneCode,
                                     @Param("hospitalId") String hospitalId,
                                     @Param("ruleLevelList") List<String> ruleLevelList);

    List<SysScene> querySceneConfig(@Param("hospitalId") String hospitalId);

    List<SysRules> queryRulesNotInScene(@Param("sceneCode") String sceneCode,
                                        @Param("rule") String rule,
                                        @Param("hospitalId") String hospitalId);

    /**
     * 查询启用的业务场景
     * @param hospitalId
     * @return
     */
    List<Map<String, String>> queryEnableScene(@Param("hospitalId") String hospitalId);

    /**
     * 全场景配置查询
     * @param hospitalId
     * @return
     */
    @Select("select \n" +
            "    b.id as sceneId,\n" +
            "    b.scene_name as sceneName,\n" +
            "    b.scene_code as sceneCode,\n" +
            "    b.main_scene_code as mainSceneCode,\n" +
            "    b.order_num as orderNum,\n" +
            "    b.`enable`,\n" +
            "    c.id as sceneFunctionId,\n" +
            "    c.function_code as functionCode,\n" +
            "    c.mi_review_api as miReviewApi,\n" +
            "    c.mi_review_api_select as miReviewApiSelect,\n" +
            "    c.mi_review_api_back as miReviewApiBack,\n" +
            "    c.manual_review as manualReview,\n" +
            "    c.show_format as showFormat,\n" +
            "    c.is_ignore as isIgnore,\n" +
            "    c.show_dimensions as showDimensions from mqs_sys_scene b\n" +
            "    left join mqs_sys_scene_function c on b.id = c.scene_id and c.hospital_id = #{hospitalId}\n" +
            "    where b.hospital_id = #{hospitalId}")
    List<SysSceneDTO> querySceneByHospitalId(String hospitalId);

    /**
     * 子场景弹窗功能配置查询
     * @param hospitalId
     * @return
     */
    @Select("select \n" +
            "b.id as sceneId,\n" +
            "b.scene_name as sceneName,\n" +
            "b.scene_code as sceneCode,\n" +
            "b.order_num as orderNum,\n" +
            "c.id as sceneFunctionId,\n" +
            "c.function_code as functionCode,\n" +
            "c.is_self as isSelf,\n" +
            "c.self_paid_condition as selfPaidCondition,\n" +
            "c.is_ignore as isIgnore,\n" +
            "c.is_self_agreement as isSelfAgreement from mqs_sys_scene b\n" +
            "left join mqs_sys_scene_function c on b.id = c.scene_id and c.hospital_id = #{hospitalId} and c.enable = #{enable}\n" +
            "where b.hospital_id = #{hospitalId} and b.enable = #{enable} ")
    List<SysSceneDTO> querySceneConfigByHospitalId (String hospitalId,String enable);





    /**
     * 查询子场景列表以及根据展示维度以及展示形式以及场景编码以及是否开启过滤
     * @param hospitalId
     * @return
     */
    @Select("select \n" +
            "  b.id as sceneId,\n" +
            "  b.scene_name as sceneName,\n" +
            "  b.scene_code as sceneCode,\n" +
            "  b.order_num as orderNum,\n" +
            "  b.`enable`,\n" +
            "  c.id as sceneFunctionId,\n" +
            "  c.show_dimensions as showDimensions,\n" +
            "  c.show_format as showFormat\n" +
            "  from mqs_sys_scene b\n" +
            "  left join mqs_sys_scene_function c on b.id = c.scene_id and c.hospital_id = #{hospitalId}\n" +
            "  where b.hospital_id = #{hospitalId}")
    List<SysSceneDTO> getScenesList (String hospitalId);

}

