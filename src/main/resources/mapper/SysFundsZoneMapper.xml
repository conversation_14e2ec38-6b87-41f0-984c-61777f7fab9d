<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.sys.mapper.SysFundsZoneMapper">


    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update mqs_sys_funds_zone
            <set>
                <if test="item.fundsId != null">funds_id = #{item.fundsId,jdbcType=INTEGER},</if>
                <if test="item.code != null">`code` = #{item.code,jdbcType=VARCHAR},</if>
                <if test="item.name != null">`name` = #{item.name,jdbcType=VARCHAR},</if>
                <if test="item.yearsAmount != null">years_amount = #{item.yearsAmount},</if>
                <if test="item.yearsWarn != null">years_warn = #{item.yearsWarn},</if>
                <if test="item.usedAmount != null">used_amount = #{item.usedAmount},</if>
                <if test="item.usedRatio != null">used_ratio = #{item.usedRatio},</if>
                <if test="item.amount1 != null">amount1 = #{item.amount1},</if>
                <if test="item.warn1 != null">warn1 = #{item.warn1},</if>
                <if test="item.amount2 != null">amount2 = #{item.amount2},</if>
                <if test="item.warn2 != null">warn2 = #{item.warn2},</if>
                <if test="item.amount3 != null">amount3 = #{item.amount3},</if>
                <if test="item.warn3 != null">warn3 = #{item.warn3},</if>
                <if test="item.amount4 != null">amount4 = #{item.amount4},</if>
                <if test="item.warn4 != null">warn4 = #{item.warn4},</if>
                <if test="item.amount5 != null">amount5 = #{item.amount5},</if>
                <if test="item.warn5 != null">warn5 = #{item.warn5},</if>
                <if test="item.amount6 != null">amount6 = #{item.amount6},</if>
                <if test="item.warn6 != null">warn6 = #{item.warn6},</if>
                <if test="item.amount7 != null">amount7 = #{item.amount7},</if>
                <if test="item.warn7 != null">warn7 = #{item.warn7},</if>
                <if test="item.amount8 != null">amount8 = #{item.amount8},</if>
                <if test="item.warn8 != null">warn8 = #{item.warn8},</if>
                <if test="item.amount9 != null">amount9 = #{item.amount9},</if>
                <if test="item.warn9 != null">warn9 = #{item.warn9},</if>
                <if test="item.amount10 != null">amount10 = #{item.amount10},</if>
                <if test="item.warn10 != null">warn10 = #{item.warn10},</if>
                <if test="item.amount11 != null">amount11 = #{item.amount11},</if>
                <if test="item.warn11 != null">warn11 = #{item.warn11},</if>
                <if test="item.amount12 != null">amount12 = #{item.amount12},</if>
                <if test="item.warn12 != null">warn12 = #{item.warn12},</if>
                <if test="item.usedAmountMonth1 != null">used_amount_month1 = #{item.usedAmountMonth1},</if>
                <if test="item.usedAmountMonth2 != null">used_amount_month2 = #{item.usedAmountMonth2},</if>
                <if test="item.usedAmountMonth3 != null">used_amount_month3 = #{item.usedAmountMonth3},</if>
                <if test="item.usedAmountMonth4 != null">used_amount_month4 = #{item.usedAmountMonth4},</if>
                <if test="item.usedAmountMonth5 != null">used_amount_month5 = #{item.usedAmountMonth5},</if>
                <if test="item.usedAmountMonth6 != null">used_amount_month6 = #{item.usedAmountMonth6},</if>
                <if test="item.usedAmountMonth7 != null">used_amount_month7 = #{item.usedAmountMonth7},</if>
                <if test="item.usedAmountMonth8 != null">used_amount_month8 = #{item.usedAmountMonth8},</if>
                <if test="item.usedAmountMonth9 != null">used_amount_month9 = #{item.usedAmountMonth9},</if>
                <if test="item.usedAmountMonth10 != null">used_amount_month10 = #{item.usedAmountMonth10},</if>
                <if test="item.usedAmountMonth11 != null">used_amount_month11 = #{item.usedAmountMonth11},</if>
                <if test="item.usedAmountMonth12 != null">used_amount_month12 = #{item.usedAmountMonth12},</if>
                last_updated_by = #{item.lastUpdatedBy},
                last_updated_date = now()
            </set>
            where id = #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>

