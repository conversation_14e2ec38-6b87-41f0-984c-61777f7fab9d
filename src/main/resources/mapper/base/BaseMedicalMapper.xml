<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper">

    <sql id="BaseSql">
        head.no, head.admission_no, head.bmi_number, head.drg_code, head.dip_code, head.bill_date, head.hospital_code, head.hospital_level, head.hospital_type, head.claim_type_id, head.benefit_type_id, head.self_expense, head.bmi_code, head.bmi_name, head.in_diagnosis_code, head.in_diagnosis_name, head.out_diagnosis_code, head.out_diagnosis_name, head.admission_date, head.discharge_date, head.patient_id, head.patient_name, head.patient_gender, head.patient_birthday, head.is_bkl, head.is_pregnant, head.is_lactating, head.newborn_weight,head.newborn_age,head.total_amount, head.personnel_type, head.treatment_type, head.bmi_convered_amount, head.bmi_overall_amount, head.unusual_flag, head.benefit_group_code, head.war_business_flag, head.single_disease_code, head.single_disease_name, head.cs_disease_code, head.cs_disease_name, head.out_zone_code, head.medical_record_id, head.is_trans_hospital, head.is_public_hosp, head.patient_idno, head.is_discharge, head.item_date, head.dept_code, head.dept_name,head.discharge_dept_code,head.discharge_dept_name, head.doc_id, head.doc_name, head.doc_level, head.charging_flag, head.pay_ratio, head.pay_amount, head.register_doc_id, head.register_doc_name, head.register_id, head.register_time, head.register_nums, head.registration_fee, head.tf_from_dept_code, head.tf_from_dept_name, head.tf_from_doc_code, head.tf_from_doc_name, head.tf_to_dept_code, head.tf_to_dept_name, head.tf_to_doc_code, head.tf_to_doc_name, head.tf_date, head.fee_date, head.secondaryDiseaseId, head.secondaryDiseaseZh, head.expand_field1, head.expand_field2, head.expand_field3, head.expand_field4, head.expand_field5, head.batch_no, head.violation_flag, head.audit_nums, head.audit_time, head.mr_flag, head.mr_status, head.mr_opinion, head.mr_time, head.last_updated_by, head.last_updated_date,
             head.created_by, head.created_date, head.hospital_id, present_dept_name, present_dept_code, doc_group_code, doc_group_name, hosp_area_code, hosp_area_name, units_dept_code, units_dept_name
    </sql>

    <sql id="BaseHeadSql">
      head.no, head.admission_no, head.bmi_number, head.drg_code, head.dip_code, head.bill_date, head.hospital_code, head.hospital_level, head.hospital_type, head.claim_type_id, head.benefit_type_id, head.self_expense, head.bmi_code, head.bmi_name, head.in_diagnosis_code, head.in_diagnosis_name, head.out_diagnosis_code, head.out_diagnosis_name, head.admission_date, head.discharge_date, head.patient_id, head.patient_name, head.patient_gender, head.patient_birthday, head.is_bkl, head.is_pregnant, head.is_lactating, head.newborn_weight,head.newborn_age,head.total_amount, head.personnel_type, head.treatment_type, head.bmi_convered_amount, head.bmi_overall_amount, head.unusual_flag, head.benefit_group_code, head.war_business_flag, head.single_disease_code, head.single_disease_name, head.cs_disease_code, head.cs_disease_name, head.out_zone_code, head.medical_record_id, head.is_trans_hospital, head.is_public_hosp, head.patient_idno, head.is_discharge, head.item_date, head.dept_code, head.dept_name,head.discharge_dept_code,head.discharge_dept_name, head.doc_id, head.doc_name, head.doc_level, head.charging_flag, head.pay_ratio, head.pay_amount, head.register_doc_id, head.register_doc_name, head.register_id, head.register_time, head.register_nums, head.registration_fee, head.tf_from_dept_code, head.tf_from_dept_name, head.tf_from_doc_code, head.tf_from_doc_name, head.tf_to_dept_code, head.tf_to_dept_name, head.tf_to_doc_code, head.tf_to_doc_name, head.tf_date, head.fee_date, CONCAT_WS(',',head.diagnosis_code1, head.diagnosis_code2, head.diagnosis_code3, head.diagnosis_code4, head.diagnosis_code5, head.diagnosis_code6, head.diagnosis_code7, head.diagnosis_code8, head.diagnosis_code9, head.diagnosis_code10, head.diagnosis_code11, head.diagnosis_code12, head.diagnosis_code13, head.diagnosis_code14, head.diagnosis_code15, head.diagnosis_code16) as secondaryDiseaseId, CONCAT_WS(',',head.diagnosis_name1, head.diagnosis_name2, head.diagnosis_name3, head.diagnosis_name4, head.diagnosis_name5, head.diagnosis_name6, head.diagnosis_name7, head.diagnosis_name8, head.diagnosis_name9, head.diagnosis_name10, head.diagnosis_name11, head.diagnosis_name12, head.diagnosis_name13, head.diagnosis_name14, head.diagnosis_name15, head.diagnosis_name16) as secondaryDiseaseZh, head.expand_field1, head.expand_field2, head.expand_field3, head.expand_field4, head.expand_field5, head.batch_no, head.violation_flag, head.audit_nums, head.audit_time, head.mr_flag, head.mr_status, head.mr_opinion, head.mr_time, head.last_updated_by, head.last_updated_date,
             head.created_by, head.created_date, head.hospital_id, present_dept_name, present_dept_code, doc_group_code, doc_group_name, hosp_area_code, hosp_area_name, units_dept_code, units_dept_name
    </sql>

    <sql id="BaseDetailSql">
      detail.detail_no, detail.admission_no, detail.no, detail.bill_date, detail.item_id, detail.item_name, detail.his_item_id, detail.his_item_name,  detail.ptype, detail.item_type_code, detail.item_type_name, detail.item_date, detail.numbers, detail.price, detail.costs, detail.cost_number, detail.cost_costs, detail.bmi_convered_amount, detail.bmi_overall_amount, violation_num, violation_amt, detail.specification, detail.usage,detail.usage_unit,usage_days, detail.apply_doctor_code, detail.apply_doctor_name, detail.apply_doctor_level, detail.apply_dept_code, detail.apply_dept_name, detail.exec_doctor_code, detail.exec_doctor_name, detail.exec_dept_code, detail.exec_dept_name, detail.rt_doctor_code, detail.rt_doctor_name, detail.rp_nurse_code, detail.rp_nurse_name, detail.charging_flag, detail.self_expense, detail.self_original, detail.frequency_interval, detail.approval_number, detail.z_physicianap,
            detail.posts_number, detail.pay_ratio, detail.abroad_drug_flag, detail.out_hospital_flag, detail.violation_flag, detail.is_current,detail.rule_origin, detail.violation_type, detail.rule_type, detail.rule_type_name, detail.rule_codes, detail.rule_names, detail.rule_reasons, detail.reason_types, detail.reason_dess, detail.batch_no,category_name,operation_type,refund_no,reverse_flag, detail.last_updated_by, detail.last_updated_date, detail.created_by, detail.created_date, detail.hospital_id,detail.hilist_pric,detail.ownpay_amt,detail.selfpay_amt,detail.hilist_lv,detail.long_drord_flag,detail.hilist_type,detail.chrg_type,detail.drord_bhvr,detail.outpatient_medication,detail.route_administration
    </sql>

    <sql id="popDetailSql">
        detail.detail_no, detail.admission_no, detail.no, detail.bill_date, detail.item_id, detail.item_name, detail.his_item_id, detail.his_item_name,  detail.ptype, detail.item_type_code, detail.item_type_name, detail.item_date, detail.numbers, detail.price, detail.costs, detail.cost_number, detail.cost_costs, detail.bmi_convered_amount, detail.bmi_overall_amount, violation_num, violation_amt, detail.specification, detail.usage,detail.usage_unit,usage_days, detail.apply_doctor_code, detail.apply_doctor_name, detail.apply_doctor_level, detail.apply_dept_code, detail.apply_dept_name, detail.exec_doctor_code, detail.exec_doctor_name, detail.exec_dept_code, detail.exec_dept_name, detail.rt_doctor_code, detail.rt_doctor_name, detail.rp_nurse_code, detail.rp_nurse_name, detail.charging_flag, detail.self_expense, detail.self_original, detail.frequency_interval, detail.approval_number, detail.z_physicianap, detail.posts_number, detail.pay_ratio, detail.abroad_drug_flag, detail.out_hospital_flag, detail.violation_flag, detail.is_current,detail.rule_origin, detail.violation_type, detail.rule_type, detail.rule_type_name, detail.rule_codes, detail.rule_names, detail.rule_reasons, detail.batch_no,category_name,refund_no,reverse_flag, detail.last_updated_by, detail.last_updated_date, detail.created_by, detail.created_date, detail.hospital_id,detail.outpatient_medication,detail.route_administration
    </sql>

    <sql id="popDetailHisSql">
        detail.detail_no, detail.admission_no, detail.no, detail.bill_date, detail.item_id, detail.item_name, detail.his_item_id, detail.his_item_name,  detail.ptype, detail.item_type_code, detail.item_type_name, detail.item_date, detail.numbers, detail.price, detail.costs, detail.cost_number, detail.cost_costs, detail.bmi_convered_amount, detail.bmi_overall_amount, violation_num, violation_amt, detail.specification, detail.usage,detail.usage_unit,usage_days, detail.apply_doctor_code, detail.apply_doctor_name, detail.apply_doctor_level, detail.apply_dept_code, detail.apply_dept_name, detail.exec_doctor_code, detail.exec_doctor_name, detail.exec_dept_code, detail.exec_dept_name, detail.rt_doctor_code, detail.rt_doctor_name, detail.rp_nurse_code, detail.rp_nurse_name, detail.charging_flag, detail.self_expense, detail.self_original, detail.frequency_interval, detail.approval_number, detail.z_physicianap,
            detail.posts_number, detail.pay_ratio, detail.abroad_drug_flag, detail.out_hospital_flag, detail.violation_flag, detail.is_current,detail.rule_origin, detail.violation_type, detail.rule_type, detail.rule_type_name, detail.rule_codes, detail.rule_names, detail.rule_reasons, detail.batch_no,category_name,operation_type,refund_no,reverse_flag, detail.last_updated_by, detail.last_updated_date, detail.created_by, detail.created_date, detail.hospital_id,detail.hilist_pric,detail.ownpay_amt,detail.selfpay_amt,detail.hilist_lv,detail.long_drord_flag,detail.hilist_type,detail.chrg_type,detail.drord_bhvr,detail.outpatient_medication,detail.route_administration
    </sql>

    <insert id="batchInsertCaseHis">

        insert into ${table}_his(batch_no, no, personnel_type, item_date, patient_name, violation_flag,
        bmi_convered_amount, audit_time, last_updated_by, last_updated_date, created_by, created_date, hospital_id,
        admission_no, bmi_number, drg_code, dip_code, bill_date, hospital_code, hospital_level, hospital_type, claim_type_id,
        benefit_type_id, self_expense, bmi_code, bmi_name, in_diagnosis_code, in_diagnosis_name, out_diagnosis_code,
        out_diagnosis_name, admission_date, discharge_date, patient_id, patient_gender, patient_birthday, is_bkl,
        is_pregnant, is_lactating, newborn_weight, newborn_age,total_amount, treatment_type, bmi_overall_amount, unusual_flag, benefit_group_code,
        war_business_flag, single_disease_code, single_disease_name, cs_disease_code, cs_disease_name, out_zone_code,
        medical_record_id, is_trans_hospital, is_public_hosp, patient_idno, is_discharge, dept_code, dept_name,
        discharge_dept_code, discharge_dept_name, doc_id, doc_name, doc_level, charging_flag, pay_ratio, pay_amount,
        register_doc_id, register_doc_name, register_id, register_time, register_nums, registration_fee,
        tf_from_dept_code, tf_from_dept_name, tf_from_doc_code, tf_from_doc_name, tf_to_dept_code, tf_to_dept_name,
        tf_to_doc_code, tf_to_doc_name, tf_date, fee_date, diagnosis_code1, diagnosis_code2, diagnosis_code3,
        diagnosis_code4, diagnosis_code5, diagnosis_code6, diagnosis_code7, diagnosis_code8, diagnosis_code9,
        diagnosis_code10, diagnosis_code11, diagnosis_code12, diagnosis_code13, diagnosis_code14, diagnosis_code15,
        diagnosis_code16, diagnosis_name1, diagnosis_name2, diagnosis_name3, diagnosis_name4, diagnosis_name5,
        diagnosis_name6, diagnosis_name7, diagnosis_name8, diagnosis_name9, diagnosis_name10, diagnosis_name11,
        diagnosis_name12, diagnosis_name13, diagnosis_name14, diagnosis_name15, diagnosis_name16, expand_field1,
        expand_field2, expand_field3, expand_field4, expand_field5,is_increment, audit_nums, mr_flag, mr_status,
        mr_opinion, mr_time<if test="table == 'mqs_mr_base'">, source_id</if>, present_dept_name, present_dept_code, doc_group_code, doc_group_name, hosp_area_code,
        hosp_area_name, units_dept_code, units_dept_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.batchNo}, #{entity.no}, #{entity.personnelType}, #{entity.itemDate}, #{entity.patientName},
            #{entity.violationFlag}, #{entity.bmiConveredAmount}, #{entity.auditTime}, #{entity.lastUpdatedBy},
            now(), #{entity.createdBy},now(), #{entity.hospitalId},
            #{entity.admissionNo}, #{entity.bmiNumber}, #{entity.drgCode}, #{entity.dipCode}, #{entity.billDate},
            #{entity.hospitalCode}, #{entity.hospitalLevel}, #{entity.hospitalType}, #{entity.claimTypeId}, #{entity.benefitTypeId},
            #{entity.selfExpense}, #{entity.bmiCode}, #{entity.bmiName}, #{entity.inDiagnosisCode},
            #{entity.inDiagnosisName}, #{entity.outDiagnosisCode}, #{entity.outDiagnosisName}, #{entity.admissionDate},
            #{entity.dischargeDate}, #{entity.patientId}, #{entity.patientGender}, #{entity.patientBirthday},
            #{entity.isBkl}, #{entity.isPregnant}, #{entity.isLactating},#{entity.newbornWeight}, #{entity.newbornAge}, #{entity.totalAmount},
            #{entity.treatmentType}, #{entity.bmiOverallAmount}, #{entity.unusualFlag}, #{entity.benefitGroupCode},
            #{entity.warBusinessFlag}, #{entity.singleDiseaseCode}, #{entity.singleDiseaseName},
            #{entity.csDiseaseCode}, #{entity.csDiseaseName}, #{entity.outZoneCode}, #{entity.medicalRecordId},
            #{entity.isTransHospital}, #{entity.isPublicHosp}, #{entity.patientIdno}, #{entity.isDischarge},
            #{entity.deptCode}, #{entity.deptName}, #{entity.dischargeDeptCode}, #{entity.dischargeDeptName},
            #{entity.docId}, #{entity.docName}, #{entity.docLevel}, #{entity.chargingFlag}, #{entity.payRatio},
            #{entity.payAmount}, #{entity.registerDocId}, #{entity.registerDocName}, #{entity.registerId},
            #{entity.registerTime}, #{entity.registerNums}, #{entity.registrationFee}, #{entity.tfFromDeptCode},
            #{entity.tfFromDeptName}, #{entity.tfFromDocCode}, #{entity.tfFromDocName}, #{entity.tfToDeptCode},
            #{entity.tfToDeptName}, #{entity.tfToDocCode}, #{entity.tfToDocName}, #{entity.tfDate}, #{entity.feeDate},
            #{entity.diagnosisCode1}, #{entity.diagnosisCode2}, #{entity.diagnosisCode3}, #{entity.diagnosisCode4},
            #{entity.diagnosisCode5}, #{entity.diagnosisCode6}, #{entity.diagnosisCode7}, #{entity.diagnosisCode8},
            #{entity.diagnosisCode9}, #{entity.diagnosisCode10}, #{entity.diagnosisCode11}, #{entity.diagnosisCode12},
            #{entity.diagnosisCode13}, #{entity.diagnosisCode14}, #{entity.diagnosisCode15}, #{entity.diagnosisCode16},
            #{entity.diagnosisName1}, #{entity.diagnosisName2}, #{entity.diagnosisName3}, #{entity.diagnosisName4},
            #{entity.diagnosisName5}, #{entity.diagnosisName6}, #{entity.diagnosisName7}, #{entity.diagnosisName8},
            #{entity.diagnosisName9}, #{entity.diagnosisName10}, #{entity.diagnosisName11}, #{entity.diagnosisName12},
            #{entity.diagnosisName13}, #{entity.diagnosisName14}, #{entity.diagnosisName15}, #{entity.diagnosisName16},
            #{entity.expandField1}, #{entity.expandField2}, #{entity.expandField3}, #{entity.expandField4},
            #{entity.expandField5},#{entity.isIncrement}, #{entity.auditNums}, #{entity.mrFlag}, #{entity.mrStatus},
            #{entity.mrOpinion}, #{entity.mrTime}<if test="table == 'mqs_mr_base'">, #{entity.sourceId}</if>, #{entity.presentDeptName}, #{entity.presentDeptCode},
            #{entity.docGroupCode}, #{entity.docGroupName}, #{entity.hospAreaCode}, #{entity.hospAreaName},
            #{entity.unitsDeptCode},#{entity.unitsDeptName}
            )
        </foreach>

    </insert>
    <insert id="batchInsertDetailHis">
        insert into ${table}_detail_his(batch_no, detail_no, no, item_id, item_name, his_item_id, his_item_name, item_type_name, item_date,
        self_expense, self_original, specification, usage_unit, usage_days, `usage`, price, cost_number, costs, cost_costs, bmi_convered_amount,
        bmi_overall_amount, rule_codes, rule_names, rule_reasons, reason_types, reason_dess, last_updated_by,
        last_updated_date, created_by, created_date, hospital_id, admission_no, bill_date, ptype, item_type_code,
        numbers, violation_num, violation_amt, apply_doctor_code, apply_doctor_name, apply_doctor_level, apply_dept_code, apply_dept_name,
        exec_doctor_code, exec_doctor_name, exec_dept_code, exec_dept_name, rt_doctor_code, rt_doctor_name,
        rp_nurse_code, rp_nurse_name, charging_flag, frequency_interval, approval_number, z_physicianap, posts_number,
        pay_ratio, abroad_drug_flag, out_hospital_flag, violation_flag, is_current,rule_origin, violation_type, rule_type, rule_type_name,category_name,operation_type,refund_no,reverse_flag,
        hilist_pric,ownpay_amt,selfpay_amt,hilist_lv,long_drord_flag,hilist_type,chrg_type,drord_bhvr,outpatient_medication,route_administration)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.batchNo}, #{entity.detailNo}, #{entity.no}, #{entity.itemId}, #{entity.itemName}, #{entity.hisItemId}, #{entity.hisItemName},
            #{entity.itemTypeName}, #{entity.itemDate}, #{entity.selfExpense}, #{entity.selfOriginal}, #{entity.specification},
            #{entity.usageUnit}, #{entity.usageDays}, #{entity.usage}, #{entity.price}, #{entity.costNumber}, #{entity.costs},
            #{entity.costCosts}, #{entity.bmiConveredAmount}, #{entity.bmiOverallAmount}, #{entity.ruleCodes},
            #{entity.ruleNames}, #{entity.ruleReasons}, #{entity.reasonTypes}, #{entity.reasonDess},
            #{entity.lastUpdatedBy}, now(), #{entity.createdBy},now(),
            #{entity.hospitalId}, #{entity.admissionNo}, #{entity.billDate}, #{entity.ptype}, #{entity.itemTypeCode},
            #{entity.numbers}, #{entity.violationNum} , #{entity.violationAmt}, #{entity.applyDoctorCode}, #{entity.applyDoctorName}, #{entity.applyDoctorLevel},
            #{entity.applyDeptCode}, #{entity.applyDeptName}, #{entity.execDoctorCode}, #{entity.execDoctorName},
            #{entity.execDeptCode}, #{entity.execDeptName}, #{entity.rtDoctorCode}, #{entity.rtDoctorName},
            #{entity.rpNurseCode}, #{entity.rpNurseName}, #{entity.chargingFlag}, #{entity.frequencyInterval},
            #{entity.approvalNumber}, #{entity.zPhysicianap}, #{entity.postsNumber}, #{entity.payRatio},
            #{entity.abroadDrugFlag}, #{entity.outHospitalFlag}, #{entity.violationFlag}, #{entity.isCurrent},
            #{entity.ruleOrigin}, #{entity.violationType},
            #{entity.ruleType},#{entity.ruleTypeName},#{entity.categoryName},#{entity.operationType},#{entity.refundNo},#{entity.reverseFlag},
            #{entity.hilistPric},#{entity.ownpayAmt},#{entity.selfpayAmt},#{entity.hilistLv},#{entity.longDrordFlag},#{entity.hilistType},#{entity.chrgType},#{entity.drordBhvr},
            #{entity.outpatientMedication},#{entity.routeAdministration}
            )
        </foreach>
    </insert>
    <insert id="batchInsertCase">
        insert into ${table}(batch_no, no, personnel_type, item_date, patient_name, violation_flag, bmi_convered_amount,
        audit_time, last_updated_by, last_updated_date, created_by, created_date, hospital_id, admission_no, bmi_number,
        drg_code, dip_code, bill_date, hospital_code, hospital_level, hospital_type, claim_type_id, benefit_type_id, self_expense,
        bmi_code, bmi_name, in_diagnosis_code, in_diagnosis_name, out_diagnosis_code, out_diagnosis_name,
        admission_date, discharge_date, patient_id, patient_gender, patient_birthday, is_bkl, is_pregnant, is_lactating, newborn_weight, newborn_age,
        total_amount, treatment_type, bmi_overall_amount, unusual_flag, benefit_group_code, war_business_flag,
        single_disease_code, single_disease_name, cs_disease_code, cs_disease_name, out_zone_code, medical_record_id,
        is_trans_hospital, is_public_hosp, patient_idno, is_discharge, dept_code, dept_name, discharge_dept_code,
        discharge_dept_name, doc_id, doc_name, doc_level, charging_flag, pay_ratio, pay_amount, register_doc_id,
        register_doc_name, register_id, register_time, register_nums, registration_fee, tf_from_dept_code,
        tf_from_dept_name, tf_from_doc_code, tf_from_doc_name, tf_to_dept_code, tf_to_dept_name, tf_to_doc_code,
        tf_to_doc_name, tf_date, fee_date, diagnosis_code1, diagnosis_code2, diagnosis_code3, diagnosis_code4,
        diagnosis_code5, diagnosis_code6, diagnosis_code7, diagnosis_code8, diagnosis_code9, diagnosis_code10,
        diagnosis_code11, diagnosis_code12, diagnosis_code13, diagnosis_code14, diagnosis_code15, diagnosis_code16,
        diagnosis_name1, diagnosis_name2, diagnosis_name3, diagnosis_name4, diagnosis_name5, diagnosis_name6,
        diagnosis_name7, diagnosis_name8, diagnosis_name9, diagnosis_name10, diagnosis_name11, diagnosis_name12,
        diagnosis_name13, diagnosis_name14, diagnosis_name15, diagnosis_name16, expand_field1, expand_field2,
        expand_field3, expand_field4, expand_field5, audit_nums, mr_flag,mr_status, mr_opinion, mr_time
        <if test="table == 'mqs_mr_base'">, source_id</if>, present_dept_name, present_dept_code,
        doc_group_code, doc_group_name, hosp_area_code,hosp_area_name,
        units_dept_code, units_dept_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.batchNo}, #{entity.no}, #{entity.personnelType}, #{entity.itemDate}, #{entity.patientName},
            #{entity.violationFlag}, #{entity.bmiConveredAmount}, #{entity.auditTime}, #{entity.lastUpdatedBy},
            now(), #{entity.createdBy},now(), #{entity.hospitalId},
            #{entity.admissionNo}, #{entity.bmiNumber}, #{entity.drgCode}, #{entity.dipCode}, #{entity.billDate},
            #{entity.hospitalCode}, #{entity.hospitalLevel}, #{entity.hospitalType}, #{entity.claimTypeId}, #{entity.benefitTypeId},
            #{entity.selfExpense}, #{entity.bmiCode}, #{entity.bmiName}, #{entity.inDiagnosisCode},
            #{entity.inDiagnosisName}, #{entity.outDiagnosisCode}, #{entity.outDiagnosisName}, #{entity.admissionDate},
            #{entity.dischargeDate}, #{entity.patientId}, #{entity.patientGender}, #{entity.patientBirthday},
            #{entity.isBkl}, #{entity.isPregnant}, #{entity.isLactating}, #{entity.newbornWeight}, #{entity.newbornAge},#{entity.totalAmount},
            #{entity.treatmentType}, #{entity.bmiOverallAmount}, #{entity.unusualFlag}, #{entity.benefitGroupCode},
            #{entity.warBusinessFlag}, #{entity.singleDiseaseCode}, #{entity.singleDiseaseName},
            #{entity.csDiseaseCode}, #{entity.csDiseaseName}, #{entity.outZoneCode}, #{entity.medicalRecordId},
            #{entity.isTransHospital}, #{entity.isPublicHosp}, #{entity.patientIdno}, #{entity.isDischarge},
            #{entity.deptCode}, #{entity.deptName}, #{entity.dischargeDeptCode}, #{entity.dischargeDeptName},
            #{entity.docId}, #{entity.docName}, #{entity.docLevel}, #{entity.chargingFlag}, #{entity.payRatio},
            #{entity.payAmount}, #{entity.registerDocId}, #{entity.registerDocName}, #{entity.registerId},
            #{entity.registerTime}, #{entity.registerNums}, #{entity.registrationFee}, #{entity.tfFromDeptCode},
            #{entity.tfFromDeptName}, #{entity.tfFromDocCode}, #{entity.tfFromDocName}, #{entity.tfToDeptCode},
            #{entity.tfToDeptName}, #{entity.tfToDocCode}, #{entity.tfToDocName}, #{entity.tfDate}, #{entity.feeDate},
            #{entity.diagnosisCode1}, #{entity.diagnosisCode2}, #{entity.diagnosisCode3}, #{entity.diagnosisCode4},
            #{entity.diagnosisCode5}, #{entity.diagnosisCode6}, #{entity.diagnosisCode7}, #{entity.diagnosisCode8},
            #{entity.diagnosisCode9}, #{entity.diagnosisCode10}, #{entity.diagnosisCode11}, #{entity.diagnosisCode12},
            #{entity.diagnosisCode13}, #{entity.diagnosisCode14}, #{entity.diagnosisCode15}, #{entity.diagnosisCode16},
            #{entity.diagnosisName1}, #{entity.diagnosisName2}, #{entity.diagnosisName3}, #{entity.diagnosisName4},
            #{entity.diagnosisName5}, #{entity.diagnosisName6}, #{entity.diagnosisName7}, #{entity.diagnosisName8},
            #{entity.diagnosisName9}, #{entity.diagnosisName10}, #{entity.diagnosisName11}, #{entity.diagnosisName12},
            #{entity.diagnosisName13}, #{entity.diagnosisName14}, #{entity.diagnosisName15}, #{entity.diagnosisName16},
            #{entity.expandField1}, #{entity.expandField2}, #{entity.expandField3}, #{entity.expandField4},
            #{entity.expandField5}, #{entity.auditNums}, #{entity.mrFlag}, #{entity.mrStatus}, #{entity.mrOpinion},
            #{entity.mrTime}<if test="table == 'mqs_mr_base'">, #{entity.sourceId}</if>, #{entity.presentDeptName}, #{entity.presentDeptCode},
            #{entity.docGroupCode}, #{entity.docGroupName}, #{entity.hospAreaCode}, #{entity.hospAreaName},
            #{entity.unitsDeptCode},#{entity.unitsDeptName})
        </foreach>
    </insert>
    <insert id="batchInsertDetail">
        insert into ${table}_detail(batch_no, detail_no, no, item_id, item_name, his_item_id, his_item_name, item_type_name, item_date,
        self_expense, self_original, specification, usage_unit,usage_days, `usage`, price, cost_number, costs, cost_costs, bmi_convered_amount,
        bmi_overall_amount, rule_codes, rule_names, rule_reasons, reason_types, reason_dess, last_updated_by,
        last_updated_date, created_by, created_date, hospital_id, admission_no, bill_date, ptype, item_type_code,
        numbers, violation_num, violation_amt, apply_doctor_code, apply_doctor_name, apply_doctor_level, apply_dept_code, apply_dept_name,
        exec_doctor_code, exec_doctor_name, exec_dept_code, exec_dept_name, rt_doctor_code, rt_doctor_name,
        rp_nurse_code, rp_nurse_name, charging_flag, frequency_interval, approval_number, z_physicianap, posts_number,
        pay_ratio, abroad_drug_flag, out_hospital_flag, violation_flag, is_current, rule_origin, violation_type, rule_type, rule_type_name,category_name,operation_type,refund_no,reverse_flag,
        hilist_pric,ownpay_amt,selfpay_amt,hilist_lv,long_drord_flag,hilist_type,chrg_type,drord_bhvr,outpatient_medication,route_administration)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.batchNo}, #{entity.detailNo}, #{entity.no}, #{entity.itemId}, #{entity.itemName}, #{entity.hisItemId}, #{entity.hisItemName},
            #{entity.itemTypeName}, #{entity.itemDate}, #{entity.selfExpense}, #{entity.selfOriginal}, #{entity.specification},
            #{entity.usageUnit}, #{entity.usageDays}, #{entity.usage}, #{entity.price}, #{entity.costNumber}, #{entity.costs},
            #{entity.costCosts}, #{entity.bmiConveredAmount}, #{entity.bmiOverallAmount}, #{entity.ruleCodes},
            #{entity.ruleNames}, #{entity.ruleReasons}, #{entity.reasonTypes}, #{entity.reasonDess},
            #{entity.lastUpdatedBy}, now(), #{entity.createdBy},now(),
            #{entity.hospitalId}, #{entity.admissionNo}, #{entity.billDate}, #{entity.ptype}, #{entity.itemTypeCode},
            #{entity.numbers}, #{entity.violationNum}, #{entity.violationAmt}, #{entity.applyDoctorCode}, #{entity.applyDoctorName}, #{entity.applyDoctorLevel},
            #{entity.applyDeptCode}, #{entity.applyDeptName}, #{entity.execDoctorCode}, #{entity.execDoctorName},
            #{entity.execDeptCode}, #{entity.execDeptName}, #{entity.rtDoctorCode}, #{entity.rtDoctorName},
            #{entity.rpNurseCode}, #{entity.rpNurseName}, #{entity.chargingFlag}, #{entity.frequencyInterval},
            #{entity.approvalNumber}, #{entity.zPhysicianap}, #{entity.postsNumber}, #{entity.payRatio},
            #{entity.abroadDrugFlag}, #{entity.outHospitalFlag}, #{entity.violationFlag}, #{entity.isCurrent},
            #{entity.ruleOrigin}, #{entity.violationType},
            #{entity.ruleType},#{entity.ruleTypeName},#{entity.categoryName},#{entity.operationType},#{entity.refundNo},#{entity.reverseFlag},
            #{entity.hilistPric},#{entity.ownpayAmt},#{entity.selfpayAmt},#{entity.hilistLv},#{entity.longDrordFlag},#{entity.hilistType},#{entity.chrgType},#{entity.drordBhvr},
            #{entity.outpatientMedication},#{entity.routeAdministration}
            )
        </foreach>

    </insert>
    <update id="updateClaimDischargeFlag">
        update ${table}
        set is_discharge = #{isDischarge},
        last_updated_date = now()
        where
        hospital_id = #{hospitalId} and admission_no = #{admissionNo} and `no` = #{no}

    </update>
    <update id="updateClaimChargingFlag">
        update ${table}_detail
        set charging_flag = #{chargingFlag},
        last_updated_date = now()
        where
        hospital_id = #{hospitalId}
        and admission_no = #{admissionNo}
        and `no` = #{no}
        and detail_no = #{detailNo}

    </update>
    <update id="updateDetailSelfExpense">
        update ${table}_detail
        set mr_self_opinion = #{mrOpinion},
        self_expense = #{selfExpense},
        last_updated_date = now(),
        last_updated_by = #{userId}
        where
        hospital_id = #{hospitalId}
        and admission_no = #{admissionNo}
        and `no` = #{no}
        and detail_no = #{detailNo}

    </update>
    <update id="updateMrViolation">
        update ${table}_detail
        set mr_opinion = #{mrOpinion},
        self_expense = #{selfExpense},
        violation_flag = #{violationFlag},
        last_updated_date = now(),
        last_updated_by = #{userId}
        where
        hospital_id = #{hospitalId}
        and admission_no = #{admissionNo}
        and `no` = #{no}
        and detail_no = #{detailNo}


    </update>
    <update id="updateDetailsToSelfExpense">

        update ${table}_detail
        set
        self_expense = '1',
        last_updated_date = now()
        where
        hospital_id = #{hospitalId}
        and detail_no in
         <foreach collection="selfDetail" separator="," open="(" close=")" item="item">
             #{item}
         </foreach>

    </update>
    <update id="updateDetailsToNoSelfExpense">

        update ${table}_detail
        set
        self_expense = '0',
        last_updated_date = now()
        where
        hospital_id = #{hospitalId}
        and detail_no in
        <foreach collection="noSelfDetail" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>

    </update>
    <delete id="deleteCaseByAdNo">
        DELETE FROM ${table}
        WHERE
        hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and admission_no = #{admissionNo}
        </if>
        <if test="nos != null and nos.size() >0 ">
            and `no` in
            <foreach collection="nos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </delete>
    <delete id="deleteDetailByAdNo">
        DELETE FROM ${table}_detail
        WHERE
        hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and admission_no = #{admissionNo}
        </if>
        <if test="nos != null and nos.size() >0 ">
            and `no` in
            <foreach collection="nos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="detailNos != null and detailNos.size() >0 ">
            and detail_no in
            <foreach collection="detailNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </delete>
    <delete id="clearHisData">
        DELETE FROM ${table}_his
        WHERE last_updated_date &lt;= concat(#{clearDate},' 23:59:59');
        DELETE FROM ${table}_audit_his
        WHERE last_updated_date &lt;= concat(#{clearDate},' 23:59:59');
        DELETE FROM ${table}_orders_his
        WHERE last_updated_date &lt;= concat(#{clearDate},' 23:59:59');
        DELETE FROM ${table}_reason_his
        WHERE last_updated_date &lt;= concat(#{clearDate},' 23:59:59');
        DELETE FROM ${table}_detail_his
        WHERE last_updated_date &lt;= concat(#{clearDate},' 23:59:59');
    </delete>

    <!--<select id="queryCaseListByPage" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        select
        head.id,
        audit.ruleType,
        <include refid="BaseHeadSql"/>
        from ${table} head
        left join
        (select no,MAX(CAST(rule_type AS SIGNED)) AS ruleType from ${table}_audit GROUP BY no) audit
        on head.no = audit.no
        where head.hospital_id = #{queryVO.hospitalId}
        <if test="queryVO.no != null and queryVO.no != ''">
            and head.no like CONCAT('%',#{queryVO.no,jdbcType = VARCHAR},'%')
        </if>
        <if test="queryVO.admissionNo != null and queryVO.admissionNo != ''">
            and head.admission_no like CONCAT('%',#{queryVO.admissionNo,jdbcType = VARCHAR},'%')
        </if>
        <if test="queryVO.registerId != null and queryVO.registerId != ''">
            and head.register_id like CONCAT('%',#{queryVO.registerId,jdbcType = VARCHAR},'%')
        </if>
        <if test="queryVO.mrFlag != null and queryVO.mrFlag != ''">
            and head.mr_flag = #{queryVO.mrFlag}
        </if>
        <if test="queryVO.mrStatus != null and queryVO.mrStatus != ''">
            and head.mr_status = #{queryVO.mrStatus}
        </if>
        <if test="queryVO.isDischarge != null and queryVO.isDischarge != ''">
            and head.is_discharge = #{queryVO.isDischarge}
        </if>
        <if test="queryVO.inpatientArea != null and queryVO.inpatientArea != ''">
            and out_zone_code = #{queryVO.inpatientArea}
        </if>
        <if test="queryVO.inpatientAreas != null and queryVO.inpatientAreas.size() > 0 ">
            and out_zone_code in
            <foreach collection="queryVO.inpatientAreas" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVO.benefitTypeId != null and queryVO.benefitTypeId != ''">
            and benefit_type_id = #{queryVO.benefitTypeId}
        </if>
        <if test="queryVO.personnelType != null and queryVO.personnelType != ''">
            and personnel_type = #{queryVO.personnelType}
        </if>
        <if test="queryVO.claimTypeId != null and queryVO.claimTypeId != ''">
            and claim_type_id = #{queryVO.claimTypeId}
        </if>
        <if test="queryVO.tfFromDeptCode != null and queryVO.tfFromDeptCode != ''">
            and tf_from_dept_code = #{queryVO.tfFromDeptCode}
        </if>

        <if test="queryVO.patient != null and queryVO.patient != ''">
            and ( patient_id like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%')
            or patient_name like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%'))
        </if>
        <if test="queryVO.deptCodes != null and queryVO.deptCodes.size() > 0">
            and dept_code in
            <foreach collection="queryVO.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVO.docIds != null and queryVO.docIds.size() > 0">
            and doc_id in
            <foreach collection="queryVO.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="queryVO.ruleCodes != null and queryVO.ruleCodes.size() > 0">
            and no in (
            select DISTINCT no from ${table}_audit where hospital_id = #{queryVO.hospitalId} and rule_code in
            <foreach collection="queryVO.ruleCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="queryVO.admissionDateFrom != null and queryVO.admissionDateFrom !='' ">
            and admission_date &gt;= concat(#{queryVO.admissionDateFrom},' 00:00:00')
        </if>
        <if test="queryVO.admissionDateTo != null and queryVO.admissionDateTo !='' ">
            and admission_date &lt;= concat(#{queryVO.admissionDateTo},' 23:59:59')
        </if>
        <if test="queryVO.auditTimeFrom != null and queryVO.auditTimeFrom !='' ">
            and audit_time &gt;= concat(#{queryVO.auditTimeFrom},' 00:00:00')
        </if>
        <if test="queryVO.auditTimeTo != null and queryVO.auditTimeTo !='' ">
            and audit_time &lt;= concat(#{queryVO.auditTimeTo},' 23:59:59')
        </if>
        <if test="queryVO.itemDateFrom != null and queryVO.itemDateFrom !='' ">
            and item_date &gt;= concat(#{queryVO.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVO.itemDateTo != null and queryVO.itemDateTo !='' ">
            and item_date &lt;= concat(#{queryVO.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVO.registerTimeFrom != null and queryVO.registerTimeFrom !='' ">
            and register_time &gt;= CONCAT(#{queryVO.registerTimeFrom},' 00:00:00')
        </if>
        <if test="queryVO.registerTimeTo != null and queryVO.registerTimeTo !='' ">
            and register_time &lt;= CONCAT(#{queryVO.registerTimeTo},' 23:59:59')
        </if>

        <if test="queryVO.violationFlag != null and queryVO.violationFlag != ''">
            and violation_flag = #{queryVO.violationFlag}
        </if>
        <if test="queryVO.batchNo != null and queryVO.batchNo != ''">
            and batch_no = #{queryVO.batchNo}
        </if>
        <if test="queryVO.patientId != null and queryVO.patientId != ''">
            and patient_id = #{queryVO.patientId}
        </if>
        <if test="queryVO.startBillDate != null and queryVO.startBillDate !='' ">
            and bill_date &gt;= CONCAT(#{queryVO.startBillDate},' 00:00:00')
        </if>
        <if test="queryVO.endBillDate != null and queryVO.endBillDate !='' ">
            and bill_date &lt;= CONCAT(#{queryVO.endBillDate},' 23:59:59')
        </if>
        <if test="queryVO.startDischargeDate != null and queryVO.startDischargeDate !='' ">
            and discharge_date &gt;= CONCAT(#{queryVO.startDischargeDate},' 00:00:00')
        </if>
        <if test="queryVO.endDischargeDate != null and queryVO.endDischargeDate !='' ">
            and discharge_date &lt;= CONCAT(#{queryVO.endDischargeDate},' 23:59:59')
        </if>
        <if test="queryVO.mrTimeFrom != null and queryVO.mrTimeFrom !='' ">
            and mr_time &gt;= CONCAT(#{queryVO.mrTimeFrom},' 00:00:00')
        </if>
        <if test="queryVO.mrTimeTo != null and queryVO.mrTimeTo !='' ">
            and mr_time &lt;= CONCAT(#{queryVO.mrTimeTo},' 23:59:59')
        </if>
        <if test="queryVO.presentDeptCodes != null and queryVO.presentDeptCodes.size() > 0 ">
            and present_dept_code in
            <foreach collection="queryVO.presentDeptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="queryVO.ruleType != null and queryVO.ruleType.size() > 0">
            and audit.ruleType in
            <foreach collection="queryVO.ruleType" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        order by head.audit_time desc
    </select>-->
    <select id="queryCaseListByPage" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        <!--select
            head.id,
            head.ruleType,
            head.sourceTable,
            <include refid="BaseSql"/>
        from(
        <foreach collection="tables" item="table" separator="UNION ALL"> -->
            select
            head.id,
            audit.ruleType,
            audit.ruleTypeName,
            '${table}' AS sourceTable,
            <include refid="BaseHeadSql"/>
            from ${table} head
            left join
        (SELECT `no`,rule_type AS ruletype,rule_type_name AS ruleTypeName FROM ${table}_audit WHERE CAST(rule_type AS SIGNED) = (SELECT MIN(CAST(rule_type AS SIGNED)) FROM ${table}_audit AS sub WHERE sub.`no` = ${table}_audit.`no`) GROUP BY `no`,rule_type,rule_type_name)  audit
        on head.`no` = audit.`no`
            where head.hospital_id = #{queryVO.hospitalId}
            <if test="queryVO.no != null and queryVO.no != ''">
                and head.`no` like CONCAT('%',#{queryVO.no,jdbcType = VARCHAR},'%')
            </if>
            <if test="queryVO.admissionNo != null and queryVO.admissionNo != ''">
                and head.admission_no like CONCAT('%',#{queryVO.admissionNo,jdbcType = VARCHAR},'%')
            </if>
            <if test="queryVO.registerId != null and queryVO.registerId != ''">
                and head.register_id like CONCAT('%',#{queryVO.registerId,jdbcType = VARCHAR},'%')
            </if>
            <if test="queryVO.mrFlag != null and queryVO.mrFlag != ''">
                and head.mr_flag = #{queryVO.mrFlag}
            </if>
            <if test="queryVO.mrStatus != null and queryVO.mrStatus != ''">
                and head.mr_status = #{queryVO.mrStatus}
            </if>
            <if test="queryVO.isDischarge != null and queryVO.isDischarge != ''">
                and head.is_discharge = #{queryVO.isDischarge}
            </if>
            <if test="queryVO.inpatientArea != null and queryVO.inpatientArea != ''">
                and out_zone_code = #{queryVO.inpatientArea}
            </if>
            <if test="queryVO.inpatientAreas != null and queryVO.inpatientAreas.size() > 0 ">
                and out_zone_code in
                <foreach collection="queryVO.inpatientAreas" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="queryVO.benefitTypeId != null and queryVO.benefitTypeId != ''">
                and benefit_type_id = #{queryVO.benefitTypeId}
            </if>
            <if test="queryVO.personnelType != null and queryVO.personnelType != ''">
                and personnel_type = #{queryVO.personnelType}
            </if>
            <if test="queryVO.claimTypeId != null and queryVO.claimTypeId != ''">
                and claim_type_id = #{queryVO.claimTypeId}
            </if>
            <if test="queryVO.tfFromDeptCode != null and queryVO.tfFromDeptCode != ''">
                and tf_from_dept_code = #{queryVO.tfFromDeptCode}
            </if>

            <if test="queryVO.patient != null and queryVO.patient != ''">
                and ( patient_id like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%')
                or patient_name like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%'))
            </if>
            <if test="queryVO.deptCodes != null and queryVO.deptCodes.size() > 0">
                and dept_code in
                <foreach collection="queryVO.deptCodes" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="queryVO.docIds != null and queryVO.docIds.size() > 0">
                and doc_id in
                <foreach collection="queryVO.docIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="queryVO.ruleCodes != null and queryVO.ruleCodes.size() > 0">
                and head.`no` in (
                select DISTINCT `no` from ${table}_audit where hospital_id = #{queryVO.hospitalId} and rule_code in
                <foreach collection="queryVO.ruleCodes" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="queryVO.admissionDateFrom != null and queryVO.admissionDateFrom !='' ">
                and admission_date &gt;= concat(#{queryVO.admissionDateFrom},' 00:00:00')
            </if>
            <if test="queryVO.admissionDateTo != null and queryVO.admissionDateTo !='' ">
                and admission_date &lt;= concat(#{queryVO.admissionDateTo},' 23:59:59')
            </if>
            <if test="queryVO.startAuditTime != null and queryVO.startAuditTime !='' ">
                and audit_time &gt;= concat(#{queryVO.startAuditTime},' 00:00:00')
            </if>
            <if test="queryVO.endAuditTime != null and queryVO.endAuditTime !='' ">
                and audit_time &lt;= concat(#{queryVO.endAuditTime},' 23:59:59')
            </if>
            <if test="queryVO.itemDateFrom != null and queryVO.itemDateFrom !='' ">
                and item_date &gt;= concat(#{queryVO.itemDateFrom},' 00:00:00')
            </if>
            <if test="queryVO.itemDateTo != null and queryVO.itemDateTo !='' ">
                and item_date &lt;= concat(#{queryVO.itemDateTo},' 23:59:59')
            </if>
            <if test="queryVO.registerTimeFrom != null and queryVO.registerTimeFrom !='' ">
                and register_time &gt;= CONCAT(#{queryVO.registerTimeFrom},' 00:00:00')
            </if>
            <if test="queryVO.registerTimeTo != null and queryVO.registerTimeTo !='' ">
                and register_time &lt;= CONCAT(#{queryVO.registerTimeTo},' 23:59:59')
            </if>

            <if test="queryVO.violationFlag != null and queryVO.violationFlag != ''">
                and violation_flag = #{queryVO.violationFlag}
            </if>
            <if test="queryVO.batchNo != null and queryVO.batchNo != ''">
                and batch_no = #{queryVO.batchNo}
            </if>
            <if test="queryVO.patientId != null and queryVO.patientId != ''">
                and patient_id = #{queryVO.patientId}
            </if>
            <if test="queryVO.startBillDate != null and queryVO.startBillDate !='' ">
                and bill_date &gt;= CONCAT(#{queryVO.startBillDate},' 00:00:00')
            </if>
            <if test="queryVO.endBillDate != null and queryVO.endBillDate !='' ">
                and bill_date &lt;= CONCAT(#{queryVO.endBillDate},' 23:59:59')
            </if>
            <if test="queryVO.startDischargeDate != null and queryVO.startDischargeDate !='' ">
                and discharge_date &gt;= CONCAT(#{queryVO.startDischargeDate},' 00:00:00')
            </if>
            <if test="queryVO.endDischargeDate != null and queryVO.endDischargeDate !='' ">
                and discharge_date &lt;= CONCAT(#{queryVO.endDischargeDate},' 23:59:59')
            </if>
            <if test="queryVO.mrTimeFrom != null and queryVO.mrTimeFrom !='' ">
                and mr_time &gt;= CONCAT(#{queryVO.mrTimeFrom},' 00:00:00')
            </if>
            <if test="queryVO.mrTimeTo != null and queryVO.mrTimeTo !='' ">
                and mr_time &lt;= CONCAT(#{queryVO.mrTimeTo},' 23:59:59')
            </if>
            <if test="queryVO.presentDeptCodes != null and queryVO.presentDeptCodes.size() > 0 ">
                and present_dept_code in
                <foreach collection="queryVO.presentDeptCodes" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="queryVO.ruleType != null and queryVO.ruleType.size() > 0">
                and audit.ruleType in
                <foreach collection="queryVO.ruleType" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            order by head.audit_time desc
        <!-- </foreach>
        ) as head -->
    </select>


    <!--查询指定结算期间内的主单信息-->
    <select id="queryCaseListByBillDate" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        select
        head.id,
        <include refid="BaseHeadSql"/>
        from ${table} head
        where head.bill_date between #{begin} and #{end}
    </select>

    <!--查询指定结算期间内的费用明细信息-->
    <select id="queryFeeListByBillDate" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        select
        detail.id,
        <include refid="BaseDetailSql"/>
        from ${table} detail
        where detail.bill_date between #{begin} and #{end}
    </select>

    <select id="queryCaseHisList" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        select
        <include refid="BaseHeadSql"/>
        from ${table}_his head
        where head.hospital_id = #{hospitalId}
        and head.no = #{no}
        and head.batch_no != #{batchNo}
        order by head.audit_time desc
    </select>


    <select id="queryDetailAll" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        select
        detail.id,
        head.benefit_type_id as benefitTypeId,
        head.discharge_date as dischargeDate,
        head.mr_time as mrTime,
        head.audit_time as auditTime,
        head.patient_id as patientId,
        head.patient_name as patientName,
        head.claim_type_id as claimTypeId,
        head.patient_gender as patientGender,
        head.patient_birthday as patientBirthday,
        head.in_diagnosis_code as inDiagnosisCode,
        head.in_diagnosis_name as inDiagnosisName,
        head.out_diagnosis_code as outDiagnosisCode,
        head.out_diagnosis_name as outDiagnosisName,
        head.admission_date as admissionDate,
        head.out_zone_code as outZoneCode,
        CASE
        WHEN EXISTS (
        SELECT 1
        FROM ${table}_audit d
        WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
        AND d.no != detail.no
        ) THEN '2'
        WHEN EXISTS (
        SELECT 1
        FROM ${table}_audit d
        WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
        AND d.no = detail.no
        ) THEN '1'
        ELSE '0'
        END AS relatedRecords,
        CONCAT_WS(',',head.diagnosis_code1, head.diagnosis_code2, head.diagnosis_code3, head.diagnosis_code4, head.diagnosis_code5, head.diagnosis_code6, head.diagnosis_code7, head.diagnosis_code8, head.diagnosis_code9, head.diagnosis_code10, head.diagnosis_code11, head.diagnosis_code12, head.diagnosis_code13, head.diagnosis_code14, head.diagnosis_code15, head.diagnosis_code16) as secondaryDiseaseId,
        CONCAT_WS(',',head.diagnosis_name1, head.diagnosis_name2, head.diagnosis_name3, head.diagnosis_name4, head.diagnosis_name5, head.diagnosis_name6, head.diagnosis_name7, head.diagnosis_name8, head.diagnosis_name9, head.diagnosis_name10, head.diagnosis_name11, head.diagnosis_name12, head.diagnosis_name13, head.diagnosis_name14, head.diagnosis_name15, head.diagnosis_name16) as secondaryDiseaseZh,
        <include refid="BaseDetailSql"/>,
        detail.costs * detail.pay_ratio as pay_amount
        from ${table}_detail detail
        join ${table} head
        on detail.no = head.no
        where detail.hospital_id = #{queryVO.hospitalId}
        <if test="queryVO.no != null and queryVO.no != ''">
            and detail.no = #{queryVO.no}
        </if>
        <if test="queryVO.inpatientAreas != null and queryVO.inpatientAreas.size() > 0 ">
            and head.out_zone_code in
            <foreach collection="queryVO.inpatientAreas" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVO.patientId != null and queryVO.patientId != ''">
            and head.patient_id = #{queryVO.patientId}
        </if>
        <if test="queryVO.relatedRecords != null and queryVO.relatedRecords != ''">
            and CASE
            WHEN EXISTS (
            SELECT 1
            FROM ${table}_audit d
            WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
            AND d.no != detail.no
            ) THEN '2'
            WHEN EXISTS (
            SELECT 1
            FROM ${table}_audit d
            WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
            AND d.no = detail.no
            ) THEN '1'
            ELSE '0'
            END = #{queryVO.relatedRecords}
        </if>
        <if test="queryVO.benefitTypeId != null and queryVO.benefitTypeId != ''">
            and head.benefit_type_id = #{queryVO.benefitTypeId}
        </if>
        <if test="queryVO.startDischargeDate != null and queryVO.startDischargeDate !='' ">
            and head.discharge_date &gt;= CONCAT(#{queryVO.startDischargeDate},' 00:00:00')
        </if>
        <if test="queryVO.endDischargeDate != null and queryVO.endDischargeDate !='' ">
            and head.discharge_date &lt;= CONCAT(#{queryVO.endDischargeDate},' 23:59:59')
        </if>
        <if test="queryVO.startBillDate != null and queryVO.startBillDate !='' ">
            and head.bill_date &gt;= CONCAT(#{queryVO.startBillDate},' 00:00:00')
        </if>
        <if test="queryVO.endBillDate != null and queryVO.endBillDate !='' ">
            and head.bill_date &lt;= CONCAT(#{queryVO.endBillDate},' 23:59:59')
        </if>
        <if test="queryVO.admissionNo != null and queryVO.admissionNo != ''">
            and head.admission_no like CONCAT('%',#{queryVO.admissionNo,jdbcType = VARCHAR},'%')
        </if>
        <if test="queryVO.patient != null and queryVO.patient != ''">
            and ( head.patient_id like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%')
            or head.patient_name like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%'))
        </if>
        <if test="queryVO.claimTypeId != null and queryVO.claimTypeId != ''">
            and head.claim_type_id = #{queryVO.claimTypeId}
        </if>
        <include refid="detailWhere"/>
    </select>

    <select id="queryDetailAllHis" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        select
        head.benefit_type_id as benefitTypeId,
        head.discharge_date as dischargeDate,
        head.mr_time as mrTime,
        head.audit_time as auditTime,
        head.patient_id as patientId,
        head.patient_name as patientName,
        head.claim_type_id as claimTypeId,
        head.patient_gender as patientGender,
        head.patient_birthday as patientBirthday,
        head.in_diagnosis_code as inDiagnosisCode,
        head.in_diagnosis_name as inDiagnosisName,
        head.out_diagnosis_code as outDiagnosisCode,
        head.out_diagnosis_name as outDiagnosisName,
        head.admission_date as admissionDate,
        head.out_zone_code as outZoneCode,
        CASE
        WHEN EXISTS (
        SELECT 1
        FROM ${table}_audit_his d
        WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
        AND d.no != detail.no
        ) THEN '2'
        WHEN EXISTS (
        SELECT 1
        FROM ${table}_audit_his d
        WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
        AND d.no = detail.no
        ) THEN '1'
        ELSE '0'
        END AS relatedRecords,
        CONCAT_WS(',',head.diagnosis_code1, head.diagnosis_code2, head.diagnosis_code3, head.diagnosis_code4, head.diagnosis_code5, head.diagnosis_code6, head.diagnosis_code7, head.diagnosis_code8, head.diagnosis_code9, head.diagnosis_code10, head.diagnosis_code11, head.diagnosis_code12, head.diagnosis_code13, head.diagnosis_code14, head.diagnosis_code15, head.diagnosis_code16) as secondaryDiseaseId,
        CONCAT_WS(',',head.diagnosis_name1, head.diagnosis_name2, head.diagnosis_name3, head.diagnosis_name4, head.diagnosis_name5, head.diagnosis_name6, head.diagnosis_name7, head.diagnosis_name8, head.diagnosis_name9, head.diagnosis_name10, head.diagnosis_name11, head.diagnosis_name12, head.diagnosis_name13, head.diagnosis_name14, head.diagnosis_name15, head.diagnosis_name16) as secondaryDiseaseZh,
        <include refid="BaseDetailSql"/>,
        detail.costs * detail.pay_ratio as pay_amount
        from ${table}_detail_his detail
        join ${table}_his head
        on detail.no = head.no and detail.batch_no = head.batch_no
        where detail.no = #{queryVO.no}
        and detail.batch_no = #{queryVO.batchNo}
        and detail.hospital_id = #{queryVO.hospitalId}
        <if test="queryVO.inpatientAreas != null and queryVO.inpatientAreas.size() > 0 ">
            and head.out_zone_code in
            <foreach collection="queryVO.inpatientAreas" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVO.patientId != null and queryVO.patientId != ''">
            and head.patient_id = #{queryVO.patientId}
        </if>
        <if test="queryVO.relatedRecords != null and queryVO.relatedRecords != ''">
            and CASE
            WHEN EXISTS (
            SELECT 1
            FROM ${table}_audit_his d
            WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
            AND d.no != detail.no
            ) THEN '2'
            WHEN EXISTS (
            SELECT 1
            FROM ${table}_audit_his d
            WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
            AND d.no = detail.no
            ) THEN '1'
            ELSE '0'
            END = #{queryVO.relatedRecords}
        </if>
        <if test="queryVO.benefitTypeId != null and queryVO.benefitTypeId != ''">
            and head.benefit_type_id = #{queryVO.benefitTypeId}
        </if>
        <if test="queryVO.startDischargeDate != null and queryVO.startDischargeDate !='' ">
            and head.discharge_date &gt;= CONCAT(#{queryVO.startDischargeDate},' 00:00:00')
        </if>
        <if test="queryVO.endDischargeDate != null and queryVO.endDischargeDate !='' ">
            and head.discharge_date &lt;= CONCAT(#{queryVO.endDischargeDate},' 23:59:59')
        </if>
        <if test="queryVO.startBillDate != null and queryVO.startBillDate !='' ">
            and head.bill_date &gt;= CONCAT(#{queryVO.startBillDate},' 00:00:00')
        </if>
        <if test="queryVO.endBillDate != null and queryVO.endBillDate !='' ">
            and head.bill_date &lt;= CONCAT(#{queryVO.endBillDate},' 23:59:59')
        </if>
        <if test="queryVO.admissionNo != null and queryVO.admissionNo != ''">
            and head.admission_no like CONCAT('%',#{queryVO.admissionNo,jdbcType = VARCHAR},'%')
        </if>
        <if test="queryVO.patient != null and queryVO.patient != ''">
            and ( head.patient_id like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%')
            or head.patient_name like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%'))
        </if>
        <if test="queryVO.claimTypeId != null and queryVO.claimTypeId != ''">
            and head.claim_type_id = #{queryVO.claimTypeId}
        </if>
        <include refid="detailWhere"/>
    </select>
    <select id="queryBmiFbDetail" resultType="com.crhms.cloud.mqs.mqs_bmi.vo.FbMedicalDetailDto">
        SELECT
        detail.detail_no,
        detail.no,
        detail.item_date,
        detail.ptype,
        detail.item_type_code,
        detail.item_type_name,
        detail.apply_doctor_name as doctorName,
        detail.apply_dept_name as deptName,
        detail.item_id,
        detail.item_name,
        detail.numbers,
        detail.price,
        detail.costs,
        detail.bmi_convered_amount,
        detail.violation_flag,
        detail.rule_names,
        detail.batch_no,
        CASE
        WHEN EXISTS (
        SELECT 1
        FROM ${table}_audit d
        WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
        AND d.no != detail.no
        ) THEN '2'
        WHEN EXISTS (
        SELECT 1
        FROM ${table}_audit d
        WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
        AND d.no = detail.no
        ) THEN '1'
        ELSE '0'
        END AS relatedRecords
        FROM
        ${table}_detail detail
        LEFT JOIN ${table} head ON detail.no = head.no
        where detail.hospital_id = #{hospitalId} and head.hospital_id = #{hospitalId}
        and detail.no = #{no}
        <if test="detailNo != null and  detailNo != '' ">
            and detail.detail_no = #{detailNo}
        </if>
        <if test="violationFlag != null and  violationFlag != '' ">
            and detail.violation_flag = #{violationFlag}
        </if>
        <if test="item != null and item != '' ">
            and ( detail.item_id like concat('%' , #{item} ,'%')
            or detail.item_name like concat('%' , #{item} ,'%'))
        </if>
        <if test="relatedRecords != null and relatedRecords != ''">
            and CASE
            WHEN EXISTS (
            SELECT 1
            FROM ${table}_audit d
            WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
            AND d.no != detail.no
            ) THEN '2'
            WHEN EXISTS (
            SELECT 1
            FROM ${table}_audit d
            WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
            AND d.no = detail.no
            ) THEN '1'
            ELSE '0'
            END = #{relatedRecords}
        </if>
    </select>
    <select id="queryBmiFbHead" resultType="com.crhms.cloud.mqs.mqs_bmi.vo.FbMedicalCaseDto">
        select
        head.no,
        head.admission_no,
        head.patient_id,
        head.patient_name,
        head.patient_gender,
        head.patient_birthday,
        head.claim_type_id as claimType,
        head.personnel_type,
        head.benefit_type_id as benefitType,
        head.out_diagnosis_name,
        CONCAT_WS(',',head.diagnosis_name1, head.diagnosis_name2, head.diagnosis_name3, head.diagnosis_name4, head.diagnosis_name5, head.diagnosis_name6, head.diagnosis_name7, head.diagnosis_name8, head.diagnosis_name9, head.diagnosis_name10, head.diagnosis_name11, head.diagnosis_name12, head.diagnosis_name13, head.diagnosis_name14, head.diagnosis_name15, head.diagnosis_name16) as diagnosis,
        head.bmi_convered_amount,
        head.total_amount
        from ${table} head
        where head.hospital_id = #{hospitalId}
        and head.no = #{no}
    </select>

    <sql id="detailWhere">
        <if test="queryVO.itemTypeCode != null and queryVO.itemTypeCode != ''">
            and detail.item_type_code = #{queryVO.itemTypeCode}
        </if>
        <if test="queryVO.rule != null and  queryVO.rule != '' ">
            and ( detail.rule_codes like concat('%' , #{queryVO.rule} ,'%')
            or detail.rule_names like concat('%' , #{queryVO.rule} ,'%'))
        </if>
        <if test="queryVO.item != null and  queryVO.item != '' ">
            and ( detail.item_id like concat('%' , #{queryVO.item} ,'%')
            or detail.item_name like concat('%' , #{queryVO.item} ,'%'))
        </if>
        <if test="queryVO.deptCodes != null and queryVO.deptCodes.size() > 0">
            and detail.apply_dept_code in
            <foreach collection="queryVO.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVO.docIds != null and queryVO.docIds.size() > 0">
            and detail.apply_doctor_code in
            <foreach collection="queryVO.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVO.rtDoctorCodes != null and queryVO.rtDoctorCodes.size() > 0">
            and detail.rt_doctor_code in
            <foreach collection="queryVO.rtDoctorCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVO.outpatientMedication != null and  queryVO.outpatientMedication != '' ">
            and detail.outpatient_medication = #{queryVO.outpatientMedication}
        </if>
        <if test="queryVO.ruleType != null and queryVO.ruleType.size() > 0">
            and detail.rule_type in
            <foreach collection="queryVO.ruleType" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVO.startAuditTime != null and queryVO.startAuditTime !='' ">
            and detail.last_updated_date &gt;= concat(#{queryVO.startAuditTime},' 00:00:00')
        </if>
        <if test="queryVO.auditTimeTo != null and queryVO.auditTimeTo !='' ">
            and detail.last_updated_date &lt;= concat(#{queryVO.auditTimeTo},' 23:59:59')
        </if>
        <if test="(queryVO.mrTimeFrom != null and queryVO.mrTimeFrom !='') or (queryVO.mrTimeTo != null and queryVO.mrTimeTo !='') ">
            and EXISTS (
            SELECT 1
            FROM mqs_mr_audit mrAudit
            WHERE mrAudit.detail_no = detail.detail_no
            <if test="queryVO.mrTimeFrom != null and queryVO.mrTimeFrom !=''">
                AND mrAudit.last_updated_date &gt;= CONCAT(#{queryVO.mrTimeFrom}, ' 00:00:00')
            </if>
            <if test="queryVO.mrTimeTo != null and queryVO.mrTimeTo !=''">
                AND mrAudit.last_updated_date &lt;= CONCAT(#{queryVO.mrTimeTo}, ' 23:59:59')
            </if>
            )
        </if>
        <if test="queryVO.itemDateFrom != null and queryVO.itemDateFrom !='' ">
            and detail.item_date &gt;= concat(#{queryVO.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVO.itemDateTo != null and queryVO.itemDateTo !='' ">
            and detail.item_date &lt;= concat(#{queryVO.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVO.violationFlag != null and queryVO.violationFlag != ''">
            and detail.violation_flag = #{queryVO.violationFlag}
        </if>

        <choose>
            <when test="queryVO.ruleCode != null and  queryVO.ruleCode != '' ">
                ORDER BY CASE WHEN detail.rule_codes like concat('%' , #{queryVO.ruleCode} ,'%') THEN 0 ELSE 1 END
            </when>
            <otherwise>
                ORDER BY detail.rule_names desc
            </otherwise>
        </choose>
    </sql>


    <select id="analyzeQueryDetail" resultType="com.crhms.cloud.mqs.mqs_ais.vo.DetailDto">

        SELECT
        head.no,
        head.admission_no,
        head.patient_id,
        head.patient_name,
        head.claim_type_id,
        head.benefit_type_id,
        head.patient_gender,
        head.patient_birthday,
        head.out_diagnosis_code,
        head.out_diagnosis_name,
        head.in_diagnosis_code,
        head.in_diagnosis_name,
        CONCAT_WS(',',head.diagnosis_code1, head.diagnosis_code2, head.diagnosis_code3, head.diagnosis_code4,
        head.diagnosis_code5, head.diagnosis_code6, head.diagnosis_code7, head.diagnosis_code8, head.diagnosis_code9,
        head.diagnosis_code10, head.diagnosis_code11, head.diagnosis_code12, head.diagnosis_code13,
        head.diagnosis_code14, head.diagnosis_code15, head.diagnosis_code16) as secondary_disease_id,
        CONCAT_WS(',',head.diagnosis_name1, head.diagnosis_name2, head.diagnosis_name3, head.diagnosis_name4,
        head.diagnosis_name5, head.diagnosis_name6, head.diagnosis_name7, head.diagnosis_name8, head.diagnosis_name9,
        head.diagnosis_name10, head.diagnosis_name11, head.diagnosis_name12, head.diagnosis_name13,
        head.diagnosis_name14, head.diagnosis_name15, head.diagnosis_name16) as secondary_disease_zh,
        head.admission_date,
        head.discharge_date,
        head.bill_date,
        detail.apply_doctor_code,
        detail.apply_doctor_name,
        detail.apply_dept_code,
        detail.apply_dept_name,
        detail.rule_names,
        detail.rule_reasons,
        detail.reason_types,
        detail.reason_dess,
        detail.item_type_code,
        detail.item_type_name,
        detail.item_date,
        detail.item_id,
        detail.item_name,
        detail.specification,
        detail.`usage`,
        detail.numbers,
        detail.price,
        detail.usage_unit,
        detail.costs,
        detail.bmi_convered_amount,
        detail.bmi_overall_amount,
        detail.batch_no batchNo,
        detail.detail_no detailNo,
        head.hospital_id hospitalId,
        CASE
        WHEN detail.violation_type = '1' THEN '1'
        ELSE '0'
        END AS relatedRecords
        from
        ${table} head
        LEFT JOIN ${table}_detail detail on detail.no = head.no
        WHERE head.hospital_id = #{queryVO.hospitalId}
        <if test="queryVO.no != null and queryVO.no != ''">
            and head.no like CONCAT('%',#{queryVO.no,jdbcType = VARCHAR},'%')
        </if>
        <if test="queryVO.admissionNo != null and queryVO.admissionNo != ''">
            and head.admission_no like CONCAT('%',#{queryVO.admissionNo,jdbcType = VARCHAR},'%')
        </if>
        <if test="queryVO.relatedRecords != null and queryVO.relatedRecords != ''">
            and CASE
            WHEN EXISTS (
            SELECT 1
            FROM ${table}_audit d
            WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
            AND d.no != detail.no
            ) THEN '2'
            WHEN EXISTS (
            SELECT 1
            FROM ${table}_audit d
            WHERE FIND_IN_SET(detail.detail_no, d.related) > 0
            AND d.no = detail.no
            ) THEN '1'
            ELSE '0'
            END = #{queryVO.relatedRecords}
        </if>
        <if test="queryVO.rule != null and  queryVO.rule != '' ">
            and ( detail.rule_codes like concat('%' , #{queryVO.rule} ,'%')
            or detail.rule_names like concat('%' , #{queryVO.rule} ,'%'))
        </if>
        <if test="queryVO.item != null and  queryVO.item != '' ">
            and ( detail.item_id like concat('%' , #{queryVO.item} ,'%')
            or detail.item_name like concat('%' , #{queryVO.item} ,'%'))
        </if>
        <if test="queryVO.deptCodes != null and queryVO.deptCodes.size() > 0">
            and detail.apply_dept_code in
            <foreach collection="queryVO.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVO.docIds != null and queryVO.docIds.size() > 0">
            and detail.apply_doctor_code in
            <foreach collection="queryVO.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVO.claimTypeId != null and queryVO.claimTypeId != ''">
            and head.claim_type_id = #{queryVO.claimTypeId}
        </if>
        <if test="queryVO.violationFlag != null and queryVO.violationFlag != ''">
            and detail.violation_flag = #{queryVO.violationFlag}
            <if test="queryVO.violationFlag != null and queryVO.violationFlag == 1">
                and detail.violation_type = '1'
            </if>
        </if>
        <if test="queryVO.mrStatus != null and queryVO.mrStatus != ''">
            and head.mr_status = #{queryVO.mrStatus}
        </if>
        <if test="queryVO.itemTypeCode != null and queryVO.itemTypeCode != ''">
            and detail.item_type_code = #{queryVO.itemTypeCode}
        </if>

        <if test="queryVO.patient != null and queryVO.patient != ''">
            and ( head.patient_id like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%')
            or head.patient_name like CONCAT('%',#{queryVO.patient,jdbcType = VARCHAR},'%'))
        </if>

        <if test="queryVO.itemDateFrom != null and queryVO.itemDateFrom !='' ">
            and detail.item_date &gt;= concat(#{queryVO.itemDateFrom},' 00:00:00')
        </if>
        <if test="queryVO.itemDateTo != null and queryVO.itemDateTo !='' ">
            and detail.item_date &lt;= concat(#{queryVO.itemDateTo},' 23:59:59')
        </if>
        <if test="queryVO.billDateFrom != null and queryVO.billDateFrom !='' ">
            and head.bill_date &gt;= concat(#{queryVO.billDateFrom},' 00:00:00')
        </if>
        <if test="queryVO.billDateTo != null and queryVO.billDateTo !='' ">
            and head.bill_date &lt;= concat(#{queryVO.billDateTo},' 23:59:59')
        </if>

    </select>
    <select id="auditQueryMedicalCaseHis" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
       SELECT
            batch_no,
            `no`,
            out_zone_code,
            admission_no,
            bill_date,
            in_diagnosis_code,
            in_diagnosis_name,
            out_diagnosis_code,
            out_diagnosis_name,
            admission_date,
            discharge_date,
            patient_id,
            patient_name,
            patient_gender,
            patient_birthday,
            hospital_id,
            claim_type_id,
            benefit_type_id,
            personnel_type,
            personnel_type as personnelTypeId
        FROM
            ${table}_his
        WHERE
            hospital_id = #{hospitalId}
            AND admission_no = #{admissionNo}
            AND batch_no = #{batch}

    </select>
    <select id="queryMedicalDetailCaseHis" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        SELECT
            detail.`no`,
            detail.detail_no,
            audit.rule_type,
            audit.rule_type_name,
            detail.item_id,
            detail.item_name,
            detail.item_date,
            detail.costs,
            detail.apply_dept_code,
            detail.apply_dept_name,
            detail.numbers,
            detail.outpatient_medication,
            detail.route_administration,
            detail.self_expense,
            audit.rule_origin
        from
            ${table}_audit_his audit left join
                ${table}_detail_his detail on audit.detail_no = detail.detail_no and detail.batch_no = audit.batch_no
            WHERE detail.violation_flag = '1'
            AND detail.violation_type = '1'
            AND detail.is_current = '1'
            AND detail.admission_no = #{admissionNo}
            AND detail.batch_no = #{batch}
            AND detail.hospital_id = #{hospitalId}

    </select>
    <select id="queryMedicalAuditRulesHis" resultType="com.crhms.cloud.mqs.basic.domain.BaseAudit">
        SELECT
        audit.rule_code,
        audit.rule_name,
        audit.rule_type,
        audit.rule_type_name,
        audit.rule_reason,
        audit.rule_origin,
        audit.related,
        audit.detail_no
        FROM
            ${table}_audit_his audit
        WHERE audit.admission_no = #{admissionNo}
        <if test="detailNo != null and detailNo != ''">
            and audit.detail_no = #{detailNo}
        </if>
	    AND audit.hospital_id = #{hospitalId}
	    AND audit.batch_no = #{batch}
        <if test="ruleOrigin != null and ruleOrigin != ''">
            and audit.rule_origin = #{ruleOrigin}
        </if>


    </select>

    <select id="queryMedicalAuditRules" resultType="com.crhms.cloud.mqs.basic.domain.BaseAudit">
        SELECT
        audit.rule_code,
        audit.rule_name,
        audit.rule_type,
        audit.rule_type_name,
        audit.rule_reason,
        audit.rule_origin,
        audit.related,
        audit.detail_no
        FROM
        ${table}_audit audit
        <where>
        <if test="admissionNo != null and admissionNo != ''">
            audit.admission_no = #{admissionNo}
        </if>
        <if test="detailNo != null and detailNo != ''">
            and audit.detail_no = #{detailNo}
        </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND audit.hospital_id = #{hospitalId}
            </if>
        <if test="batch != null and batch != ''">
             AND audit.batch_no = #{batch}
        </if>
        <if test="ruleOrigin != null and ruleOrigin != ''">
            and audit.rule_origin = #{ruleOrigin}
        </if>
        </where>

    </select>


    <select id="queryMedicalDetailCaseHisByRule"
            resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        SELECT
            detail.admission_no,
            detail.detail_no,
            audit.rule_type,
            audit.rule_type_name,
            detail.detail_no,
            detail.item_id,
            detail.item_name,
            detail.item_date,
            detail.item_date,
            detail.specification,
            detail.price,
            detail.numbers,
            detail.apply_dept_code,
            detail.apply_dept_name,
            detail.outpatient_medication,
            detail.route_administration,
            detail.usage_days,
            detail.`usage`,
            detail.frequency_interval,
            detail.item_type_code,
            detail.item_type_name,
            detail.costs,
            detail.self_expense,
            audit.rule_code AS rule_codes
        FROM
        ${table}_audit_his audit
			LEFT JOIN ${table}_detail_his detail ON detail.detail_no = audit.detail_no
			AND detail.admission_no = audit.admission_no AND detail.batch_no = audit.batch_no
            WHERE detail.violation_flag = '1'
            AND detail.batch_no = #{batch}
            AND detail.hospital_id = #{hospitalId}
            AND audit.rule_code in
            <foreach collection="rules" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>

    </select>


    <select id="queryMedicalDetailCaseByRule"
            resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        SELECT
        detail.admission_no,
        detail.detail_no,
        audit.rule_type,
        audit.rule_type_name,
        detail.detail_no,
        detail.item_id,
        detail.item_name,
        detail.item_date,
        detail.item_date,
        detail.specification,
        detail.price,
        detail.numbers,
        detail.apply_dept_code,
        detail.apply_dept_name,
        detail.outpatient_medication,
        detail.route_administration,
        detail.usage_days,
        detail.`usage`,
        detail.frequency_interval,
        detail.item_type_code,
        detail.item_type_name,
        detail.costs,
        detail.self_expense,
        audit.rule_code AS rule_codes
        FROM
        ${table}_audit audit
        LEFT JOIN ${table}_detail detail ON detail.detail_no = audit.detail_no
        AND detail.admission_no = audit.admission_no AND detail.batch_no = audit.batch_no
        WHERE detail.violation_flag = '1'
        <if test="batch != null and batch != ''">
        AND detail.batch_no = #{batch}
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            AND detail.hospital_id = #{hospitalId}
        </if>
        AND audit.rule_code in
        <foreach collection="rules" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </select>


    <select id="queryCaseHisByBatchNo" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        SELECT
        <include refid="BaseHeadSql"/>,
        is_increment,
        head.diagnosis_code1, head.diagnosis_code2, head.diagnosis_code3, head.diagnosis_code4, head.diagnosis_code5, head.diagnosis_code6, head.diagnosis_code7, head.diagnosis_code8, head.diagnosis_code9, head.diagnosis_code10, head.diagnosis_code11, head.diagnosis_code12, head.diagnosis_code13, head.diagnosis_code14, head.diagnosis_code15, head.diagnosis_code16,
        head.diagnosis_name1, head.diagnosis_name2, head.diagnosis_name3, head.diagnosis_name4, head.diagnosis_name5, head.diagnosis_name6, head.diagnosis_name7, head.diagnosis_name8, head.diagnosis_name9, head.diagnosis_name10, head.diagnosis_name11, head.diagnosis_name12, head.diagnosis_name13, head.diagnosis_name14, head.diagnosis_name15, head.diagnosis_name16
        FROM ${table}_his head
        WHERE  hospital_id = #{hospitalId}
        AND batch_no = #{batchNo}
        <if test="admissionNo != null and admissionNo != ''">
            AND admission_no = #{admissionNo}
        </if>
        <if test="no != null and no != ''">
            and `no` = #{no}
        </if>
    </select>
    <select id="queryDetailHisByBatchNo" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        SELECT
        <include refid="BaseDetailSql"/>
        FROM ${table}_detail_his detail
        WHERE  hospital_id = #{hospitalId}
        <if test="batchNo != null and batchNo != ''">
        AND batch_no = #{batchNo}
        </if>
        <if test="admissionNo != null and admissionNo != ''" >
          AND admission_no = #{admissionNo}
        </if>
        <if test="no != null and no != ''">
            and `no` = #{no}
        </if>

    </select>

    <select id="getHospitalSelfExpense" resultType="java.util.Map">
        select
        admission_no,
        `no`,
        detail_no,
        self_expense
        from
        ${table}_detail
        where
        hospital_id = #{hospitalId} and admission_no = #{admissionNo}
        <if test="no != null and no != ''" >
           and `no` = #{no}
        </if>
        <if test="detailNo != null and detailNo != ''" >
           and detail_no = #{detailNo}
        </if>

    </select>
    <select id="queryAuditDetail" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail" >
    select
        <include refid="BaseDetailSql"/>
        from ${table}_detail detail
        where detail.hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and detail.admission_no = #{admissionNo}
        </if>
        <if test="nos != null and nos.size() > 0" >
            and `no` in
            <foreach collection="nos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="detailNos != null and detailNos.size() > 0" >
            and detail_no in
            <foreach collection="detailNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="queryAuditDetailByDetailNo" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail" >
        select
        <include refid="BaseDetailSql"/>
        from ${table}_detail detail
        where detail.detail_no in
        <foreach collection="detailNos" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>

    </select>
    <select id="queryAuditCase" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        select
        <include refid="BaseHeadSql"/>,
        head.diagnosis_code1, head.diagnosis_code2, head.diagnosis_code3, head.diagnosis_code4, head.diagnosis_code5, head.diagnosis_code6, head.diagnosis_code7, head.diagnosis_code8, head.diagnosis_code9, head.diagnosis_code10, head.diagnosis_code11, head.diagnosis_code12, head.diagnosis_code13, head.diagnosis_code14, head.diagnosis_code15, head.diagnosis_code16,
        head.diagnosis_name1, head.diagnosis_name2, head.diagnosis_name3, head.diagnosis_name4, head.diagnosis_name5, head.diagnosis_name6, head.diagnosis_name7, head.diagnosis_name8, head.diagnosis_name9, head.diagnosis_name10, head.diagnosis_name11, head.diagnosis_name12, head.diagnosis_name13, head.diagnosis_name14, head.diagnosis_name15, head.diagnosis_name16
        from ${table} head
        where head.hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and head.admission_no = #{admissionNo}
        </if>
        <if test="nos != null and nos.size() > 0" >
            and `no` in
            <foreach collection="nos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="queryMedicalCaseAuditHis" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">

        SELECT
            '0' AS detail_no,
            audit.`no`,
            audit.`no` AS item_id,
            '主单违规' AS item_name,
            audit.rule_type,
            audit.rule_type_name,
            audit.rule_code,
            audit.rule_origin,
            audit.rule_reason
        FROM
            ${table}_audit_his audit
        WHERE
            audit.admission_no = #{admissionNo}
            AND audit.batch_no = #{batch}
            AND audit.hospital_id = #{hospitalId}
            AND audit.detail_no = '0'

    </select>
    <select id="selectAuditNums" resultType="java.util.Map">
        SELECT
            `no`,
            hospital_id,
            max(audit_nums) as nums
        FROM
        (
            select
            `no`,
            hospital_id,
            audit_nums
            from
            ${table}_his
            <if test="hospitalId != null and hospitalId != ''">
                and hospital_id = #{hospitalId}
            </if>
            order by
            last_updated_date desc
            limit 10000
        ) t
        GROUP BY
            `no`,
            hospital_id
    </select>

    <select id="querySelfExportCaseDetail" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        select
        detail.detail_no,
        detail.admission_no,
        detail.item_id,
        detail.item_name,
        detail.item_date,
        SUM(detail.numbers) numbers,
        AVG(detail.price) price,
        SUM(detail.costs) costs
        from ${table} detail
        where hospital_id = #{hospitalId}
        <if test="no != null and no != '' and detailNos == null">
            and detail.no = #{no}
        </if>
        <if test="batch != null and batch != ''">
            and detail.batch_no = #{batch}
        </if>
        <choose>
            <when test="detailNos != null and detailNos.size() > 0 ">
                and detail.detail_no in
                <foreach collection="detailNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and detail.self_expense = '1'
            </otherwise>
        </choose>
        group by
        detail.detail_no,
        detail.admission_no,
        detail.item_id,
        detail.item_name,
        detail.item_date

    </select>
    <select id="querySelfExportCase" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">

        select
            head.no,
            head.dept_code,
            head.dept_name,
            head.admission_no,
            head.patient_name,
            head.patient_id,
            admission_date,
            patient_idno,
            medical_record_id,
            patient_birthday,
            patient_gender,
            CONCAT_WS(',',in_diagnosis_name,out_diagnosis_name,head.diagnosis_code1, head.diagnosis_code2, head.diagnosis_code3, head.diagnosis_code4, head.diagnosis_code5, head.diagnosis_code6, head.diagnosis_code7, head.diagnosis_code8, head.diagnosis_code9, head.diagnosis_code10, head.diagnosis_code11, head.diagnosis_code12, head.diagnosis_code13, head.diagnosis_code14, head.diagnosis_code15, head.diagnosis_code16) as secondaryDiseaseId, CONCAT_WS(',',head.diagnosis_name1, head.diagnosis_name2, head.diagnosis_name3, head.diagnosis_name4, head.diagnosis_name5, head.diagnosis_name6, head.diagnosis_name7, head.diagnosis_name8, head.diagnosis_name9, head.diagnosis_name10, head.diagnosis_name11, head.diagnosis_name12, head.diagnosis_name13, head.diagnosis_name14, head.diagnosis_name15, head.diagnosis_name16)
            as secondaryDiseaseZh,
            head.out_zone_code,
            head.hospital_id
        from ${table} head
        where hospital_id = #{hospitalId}
        and head.no = #{no}
        <if test="batch != null and batch != ''">
            and head.batch_no = #{batch}
        </if>



    </select>
    <select id="querySelfExportApplyDept" resultType="java.lang.String">
        select
        apply_dept_name
        from ${table} detail
        where hospital_id = #{hospitalId}
        <if test="no != null and no != '' and detailNos == null">
            and detail.no = #{no}
        </if>
        <if test="batch != null and batch != ''">
            and detail.batch_no = #{batch}
        </if>
        <choose>
            <when test="detailNos != null and detailNos.size() > 0 ">
                and detail.detail_no in
                <foreach collection="detailNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and detail.self_expense = '1'
            </otherwise>
        </choose>
        ORDER BY item_date DESC
        limit 1
    </select>
    <select id="queryClinicalDetail" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">

        SELECT
        detail.`no`,
        detail.detail_no,
        detail.item_id,
        detail.item_name,
        detail.violation_flag,
        detail.self_expense,
        detail.item_type_code,
        detail.item_type_name,
        detail.item_date,
        detail.numbers,
        detail.price,
        detail.costs,
        detail.mr_opinion,
        memo
        FROM
        ${table}_detail detail
        LEFT JOIN mqs_sys_item_memo memo ON memo.item_id = detail.item_id and memo.hospital_id = detail.hospital_id
        WHERE
        detail.hospital_id = #{hospitalId}
        AND detail.violation_flag != '0'
        AND detail.self_original = '0'
        AND detail.admission_no = #{admissionNo}
        <if test="violationFlag != null and violationFlag != '' ">
            and detail.violation_flag = #{violationFlag}
        </if>
        <if test="selfExpense != null and selfExpense != '' ">
            and detail.self_expense = #{selfExpense}
        </if>
        <if test="item != null and item != '' ">
            and ( detail.item_id like concat('%' , #{item} ,'%')
            or detail.item_name like concat('%' , #{item} ,'%'))
        </if>
        <if test="itemTypes != null and itemTypes.size() > 0 ">
            and detail.item_type_code in
            <foreach collection="itemTypes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="queryClinicalCase" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        SELECT
        `no`,
        admission_no,
        MAX(audit_time) as audit_time,
        GROUP_CONCAT(DISTINCT patient_name) as patient_name
        FROM
        ${table}
        WHERE hospital_id = #{hospitalId}
        AND admission_no = #{admissionNo}
        GROUP BY
        `no`,
        admission_no

    </select>
    <select id="queryClinicalSelfItem" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        SELECT
        detail.`no`,
        detail.detail_no,
        detail.item_id,
        detail.item_name,
        detail.violation_flag,
        detail.self_expense,
        detail.item_type_code,
        detail.item_type_name,
        detail.item_date,
        detail.numbers,
        detail.price,
        detail.costs
        FROM ${table}_detail detail
        WHERE detail.hospital_id = #{hospitalId}
        AND detail.self_original = '1'
        AND detail.admission_no = #{admissionNo}
        <if test="item != null and item != '' ">
            and ( detail.item_id like concat('%' , #{item} ,'%')
            or detail.item_name like concat('%' , #{item} ,'%'))
        </if>
        <if test="itemTypes != null and itemTypes.size() > 0 ">
            and detail.item_type_code in
            <foreach collection="itemTypes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="queryMissedItem" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        SELECT
        audit.`no`,
        audit.item_id,
        audit.item_name,
        audit.item_type_code,
        audit.item_type_name,
        audit.item_date,
        audit.numbers,
        audit.price,
        audit.costs
        FROM ${table}_audit audit
        WHERE audit.hospital_id = #{hospitalId}
        AND audit.rule_code IN ('C990106','C990107')
        AND audit.admission_no = #{admissionNo}
        <if test="item != null and item != '' ">
            and ( audit.item_id like concat('%' , #{item} ,'%')
            or audit.item_name like concat('%' , #{item} ,'%'))
        </if>
        <if test="itemTypes != null and itemTypes.size() > 0 ">
            and audit.item_type_code in
            <foreach collection="itemTypes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="querydetailByDetailNo" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">

        select
        detail_no,
        item_id,
        item_name
        from ${table}_detail
        where
        hospital_id = #{hospitalId}
        and detail_no in
        <foreach collection="selfDetail" separator="," open="(" close=")" item="item">
        #{item}
        </foreach>

    </select>
    <select id="queryReasonByDetailNo" resultType="com.crhms.cloud.mqs.basic.domain.BaseAuditReason">
        SELECT id, detail_no, `no`, admission_no, item_id, rule_code, self_expense,reason_type, reason_des, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id
        FROM ${table}_reason
        WHERE  hospital_id = #{hospitalId}
        and admission_no = #{admissionNo}
    </select>

    <select id="queryIgnoreByNos" resultType="com.crhms.cloud.mqs.basic.domain.BaseAuditIgnore">
        SELECT
        `no`,
        detail_no
        FROM mqs_audit_ignore
        WHERE `no` IN
            <foreach collection="nos" separator="," open="(" close=")" item="item">
            #{item}
            </foreach>
        AND hospital_id = #{hospitalId}
    </select>

    <select id="queryMedicalByHospitalId" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        select
            head.mr_flag,
        <include refid="BaseHeadSql"/>
        FROM ${table} head
        WHERE  head.hospital_id = #{hospitalId}
        <if test="nos != null and nos.size() > 0 ">
            and head.no in
            <foreach collection="nos" open="(" close=")" separator="," item="no">
                #{no}
            </foreach>
        </if>
    </select>


    <select id="queryAuditPatient" resultType="com.crhms.cloud.mqs.basic.vo.AuditPatientVO">
        SELECT
            audit.no,
            base.patient_id,
            base.patient_name
        FROM ${table}_audit audit
        INNER JOIN ${table} base ON audit.no = base.no
        WHERE audit.hospital_id = '1'
            AND EXISTS (
                  SELECT 1 FROM ${table}_detail detail WHERE detail.detail_no = audit.detail_no AND detail.self_expense != 2
            )
            AND NOT EXISTS (
                  SELECT 1 FROM mqs_audit_ignore aignore WHERE audit.detail_no = aignore.detail_no
            )
            AND NOT EXISTS (
                  SELECT 1 FROM mqs_mr_audit mraudit WHERE audit.detail_no = mraudit.detail_no
            )
        <if test="patientName != null and patientName != '' ">
            AND base.patient_name like CONCAT('%', #{patientName}, '%')
        </if>
        <if test="docId != null and docId != '' ">
            AND base.doc_id = #{docId}
        </if>
        <if test="presentDeptCode != null and presentDeptCode != '' ">
            AND base.present_dept_code = #{presentDeptCode}
        </if>
    </select>



    <select id="queryDetail" resultType="com.crhms.cloud.mqs.basic.vo.ComprePopupDetailVO">
        SELECT
        <include refid="popDetailSql"/>,
        '0' as operation_type,
        memo.memo,
        reason.reason_type as reason_types,
        reason.reason_des as reason_dess
        FROM ${table}_detail detail
        LEFT JOIN (SELECT
                     detail_no,
                     reason_type,
                     reason_des,
                   ROW_NUMBER() OVER (PARTITION BY r.detail_no ORDER BY r.last_updated_date DESC) AS rn
                   FROM ${table}_reason r) reason ON detail.detail_no = reason.detail_no AND reason.rn = 1
        LEFT JOIN mqs_sys_item_memo memo on detail.item_id = memo.item_id
        <where>
            <if test="hospitalId != null and hospitalId != ''">
                and detail.hospital_id = #{hospitalId}
            </if>
        <if test="queryVO.no != null and queryVO.no != ''">                                       <!-- 单据号-->
            and detail.no = #{queryVO.no}
        </if>
        <if test="queryVO.admissionNo != null and queryVO.admissionNo != ''">                                       <!-- 就诊流水号-->
            and detail.admission_no = #{queryVO.admissionNo}
        </if>
        <if test="queryVO.detailNos != null and queryVO.detailNos.size() > 0" >
            and detail.detail_no in
            <foreach collection="queryVO.detailNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVO.batchNo != null and queryVO.batchNo != ''">                             <!-- 批次号-->
            and detail.batch_no = #{queryVO.batchNo}
        </if>
        <if test="queryVO.violationFlag != null and  queryVO.violationFlag != '' ">               <!-- 是否违规-->
            <choose>
                <when test="queryVO.violationFlag == 0 ">
                    and detail.violation_flag = #{queryVO.violationFlag}
                    and not EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no)
                </when>
                <when test="queryVO.violationFlag == 1 ">
                    and (detail.violation_flag = #{queryVO.violationFlag} or EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no))
                </when>
            </choose>
        </if>
        <if test="queryVO.clinicalStatus != null and  queryVO.clinicalStatus != '' ">               <!-- 临床处理状态-->
            <choose>
                <when test="queryVO.clinicalStatus == 0 ">
                    <!-- and not EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no) -->
                    and not EXISTS (select 1 from mqs_audit_ignore maIgnore where detail.detail_no = maIgnore.detail_no and detail.no = maIgnore.no)
                    and detail.self_expense != '2'
                </when>
                <when test="queryVO.clinicalStatus == 2 ">
                    and (
                    <!--EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no)
                    or -->  EXISTS (select 1 from mqs_audit_ignore maIgnore where detail.detail_no = maIgnore.detail_no and detail.no = maIgnore.no)
                    or detail.self_expense = '2'
                    )
                </when>
            </choose>
        </if>
        <if test="queryVO.selfExpense != null and  queryVO.selfExpense != '' ">    <!-- 自费状态 -->
            and detail.self_expense = #{queryVO.selfExpense}
        </if>
        <if test="queryVO.auditSource != null and  queryVO.auditSource != '' ">               <!-- 审核渠道-->
            <choose>
                <when test="queryVO.auditSource == 0 ">
                    and detail.rule_origin like CONCAT('%', '2', '%')
                </when>
                <when test="queryVO.auditSource == 1 ">
                    and not EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no)
                </when>
                <when test="queryVO.auditSource == 2 ">
                    and EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no)
                </when>
            </choose>
        </if>
        <if test="queryVO.ruleTypes != null and queryVO.ruleTypes.size() > 0">     <!--违规级别-->
            and (
            EXISTS (
                 SELECT 1
                 FROM mqs_mr_audit mrAudit
                 WHERE mrAudit.no = detail.no and detail.detail_no = mrAudit.detail_no
                 AND mrAudit.rule_type IN
                 <foreach collection="queryVO.ruleTypes" item="ruleTypeItem" open="(" separator="," close=")">
                     #{ruleTypeItem}
                 </foreach>
                   )
            OR detail.rule_type IN
            <foreach collection="queryVO.ruleTypes" item="ruleTypeItem" open="(" separator="," close=")">
                #{ruleTypeItem}
            </foreach>
            )
        </if>
        <if test="queryVO.itemName != null and  queryVO.itemName != '' ">           <!-- 项目名称 -->
            and detail.item_name like concat('%' , #{queryVO.itemName} ,'%')
        </if>

        <if test="queryVO.itemTypeCode != null and queryVO.itemTypeCode != ''">     <!-- 项目类型编码 -->
            and detail.item_type_code = #{queryVO.itemTypeCode}
        </if>
        <if test="queryVO.categoryName != null and  queryVO.categoryName != '' ">           <!-- 甲乙类 -->
            and detail.category_name like concat('%' , #{queryVO.categoryName} ,'%')
        </if>
        <if test="queryVO.outpatientMedication != null and  queryVO.outpatientMedication != '' ">           <!-- 是否出院带药 -->
            and detail.outpatient_medication = #{queryVO.outpatientMedication}
        </if>
        </where>
    </select>


    <select id="queryDetailHis" resultType="com.crhms.cloud.mqs.basic.vo.ComprePopupDetailVO">
        SELECT
        <include refid="popDetailHisSql"/>,
        memo.memo,
        reason.reason_type as reason_types,
        reason.reason_des as reason_dess
        FROM ${table}_detail_his detail
        LEFT JOIN (SELECT
                     detail_no,
                     reason_type,
                     reason_des,
                     ROW_NUMBER() OVER (PARTITION BY r.detail_no ORDER BY r.last_updated_date DESC) AS rn
                   FROM ${table}_reason r) reason ON detail.detail_no = reason.detail_no AND reason.rn = 1
        LEFT JOIN mqs_sys_item_memo memo on detail.item_id = memo.item_id
        WHERE detail.hospital_id = #{hospitalId}
        and detail.reverse_flag != '1'                                                             <!-- 冲销标志-->
        <if test="queryVO.no != null and queryVO.no != ''">                                       <!-- 单据号-->
            and detail.no = #{queryVO.no}
        </if>
        <if test="queryVO.batchNo != null and queryVO.batchNo != ''">                             <!-- 批次号-->
            and detail.batch_no = #{queryVO.batchNo}
        </if>
        <if test="queryVO.violationFlag != null and  queryVO.violationFlag != '' ">               <!-- 是否违规-->
            <choose>
                <when test="queryVO.violationFlag == 0 ">
                    and detail.violation_flag = #{queryVO.violationFlag}
                    and not EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no)
                </when>
                <when test="queryVO.violationFlag == 1 ">
                    and (detail.violation_flag = #{queryVO.violationFlag} or EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no))
                </when>
            </choose>
        </if>
        <if test="queryVO.clinicalStatus != null and  queryVO.clinicalStatus != '' ">               <!-- 临床处理状态-->
            <choose>
                <when test="queryVO.clinicalStatus == 0 ">
                    <!-- and not EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no)-->
                    and not EXISTS (select 1 from mqs_audit_ignore maIgnore where detail.detail_no = maIgnore.detail_no and detail.no = maIgnore.no)
                    and detail.self_expense != '2'
                </when>
                <when test="queryVO.clinicalStatus == 2 ">
                    and (
                    <!-- EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no and detail.no = mrAudit.no)
                    or-->  EXISTS (select 1 from mqs_audit_ignore maIgnore where detail.detail_no = maIgnore.detail_no and detail.no = maIgnore.no)
                    or detail.self_expense = '2'
                    )
                </when>
            </choose>
        </if>
        <if test="queryVO.selfExpense != null and  queryVO.selfExpense != '' ">    <!-- 自费状态 -->
            and detail.self_expense = #{queryVO.selfExpense}
        </if>
        <if test="queryVO.auditSource != null and  queryVO.auditSource != '' ">               <!-- 审核渠道-->
            <choose>
                <when test="queryVO.auditSource == 0 ">
                    and detail.rule_origin like CONCAT('%', '2', '%')
                </when>
                <when test="queryVO.auditSource == 1 ">
                    and not EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no)
                </when>
                <when test="queryVO.auditSource == 2 ">
                    and EXISTS (select 1 from mqs_mr_audit mrAudit where detail.detail_no = mrAudit.detail_no)
                </when>
            </choose>
        </if>
        <if test="queryVO.ruleTypes != null and queryVO.ruleTypes.size() > 0">     <!--违规级别-->
            and (
            EXISTS (
            SELECT 1
            FROM mqs_mr_audit mrAudit
            WHERE mrAudit.no = detail.no and detail.detail_no = mrAudit.detail_no
            AND mrAudit.rule_type IN
            <foreach collection="queryVO.ruleTypes" item="ruleTypeItem" open="(" separator="," close=")">
                #{ruleTypeItem}
            </foreach>
            )
            OR detail.rule_type IN
            <foreach collection="queryVO.ruleTypes" item="ruleTypeItem" open="(" separator="," close=")">
                #{ruleTypeItem}
            </foreach>
            )
        </if>
        <if test="queryVO.itemName != null and  queryVO.itemName != '' ">           <!-- 项目名称 -->
            and detail.item_name like concat('%' , #{queryVO.itemName} ,'%')
        </if>

        <if test="queryVO.itemTypeCode != null and queryVO.itemTypeCode != ''">     <!-- 项目类型编码 -->
            and detail.item_type_code = #{queryVO.itemTypeCode}
        </if>
        <if test="queryVO.categoryName != null and  queryVO.categoryName != '' ">           <!-- 甲乙类 -->
            and detail.category_name like concat('%' , #{queryVO.categoryName} ,'%')
        </if>
        <if test="queryVO.outpatientMedication != null and  queryVO.outpatientMedication != '' ">           <!-- 是否出院带药 -->
            and detail.outpatient_medication = #{queryVO.outpatientMedication}
        </if>
    </select>


    <select id="queryMinRuleTypeByNo" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        SELECT no, detail_no, rule_type, rule_type_name , rule_name as rule_names, rule_reason as rule_reasons
        FROM ${table}
        WHERE hospital_id = #{hospitalId}
        <if test="detailNos != null and detailNos.size() > 0">
            AND detail_no IN
            <foreach collection="detailNos" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="nos != null and nos.size() > 0">
            AND no IN
            <foreach collection="nos" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="batchNo != null and batchNo != ''">
            AND batch_no = #{batchNo}
        </if>
        UNION ALL
        SELECT no, detail_no, rule_type, rule_type_name , rule_name as rule_names, rule_reason as rule_reasons
        FROM mqs_mr_audit
        WHERE hospital_id = #{hospitalId}
        <if test="detailNos != null and detailNos.size() > 0">
            AND detail_no IN
            <foreach collection="detailNos" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="nos != null and nos.size() > 0">
            AND no IN
            <foreach collection="nos" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>

    </select>


    <select id="queryDetailSourceAndReasons" resultType="com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail">
        select
        detail.id,
        detail.detail_no,
        CASE
        WHEN maudit.detail_no IS NOT NULL AND baudit.detail_no IS NOT NULL THEN '1,2'
        WHEN maudit.detail_no IS NOT NULL AND baudit.detail_no IS NULL THEN '2'
        WHEN maudit.detail_no IS NULL AND baudit.detail_no IS NOT NULL THEN '1'
        WHEN maudit.detail_no IS NULL AND baudit.detail_no IS NULL THEN '1'
        ELSE NULL
        END AS audit_source
        from ${detailTable} detail
        left join ${auditTable} baudit on detail.detail_no = baudit.detail_no and detail.hospital_id = baudit.hospital_id
        <if test="batchNo != null and  batchNo != '' "> and detail.batch_no = baudit.batch_no</if>
        left join mqs_mr_audit maudit on detail.detail_no = maudit.detail_no and detail.hospital_id = maudit.hospital_id
        where detail.hospital_id = #{hospitalId}
        <if test="batchNo != null and  batchNo != '' ">
            and detail.batch_no = #{batchNo}
        </if>
        and detail.detail_no in
        <foreach collection="detailNos" open="(" close=")" separator="," item="no">
            #{no}
        </foreach>
    </select>

    <select id="queryMedicalDetailByDetailNos" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail">
        SELECT
        <include refid="BaseDetailSql"/>
        FROM ${table} detail
        WHERE detail.hospital_id = #{hospitalId}
        and detail.admission_no = #{admissionNo}
        and detail.detail_no in
        <foreach collection="detailNos" open="(" close=")" separator="," item="no">
            #{no}
        </foreach>
        <if test="batchNo != null and  batchNo != '' ">
            and detail.batch_no = #{batchNo}
        </if>
    </select>

    <insert id="batchInsertAuditIgnore" parameterType="java.util.List">
        INSERT INTO mqs_audit_ignore (admission_no, no, detail_no,item_id,item_name,hospital_id,enable)
        VALUES
        <foreach collection="ignores" item="ignore" separator=",">
            (#{ignore.admissionNo}, #{ignore.no}, #{ignore.detailNo}, #{ignore.itemId}, #{ignore.itemName}, #{ignore.hospitalId}, #{ignore.enable})
        </foreach>
    </insert>

    <select id="queryIgnoreByDetailNos" resultType = "com.crhms.cloud.mqs.basic.domain.BaseAuditIgnore">
        select admission_no,no,detail_no,item_id,item_name,hospital_id,enable
        from mqs_audit_ignore
        where detail_no in
        <foreach collection="detailNos" open="(" close=")" separator="," item="no">
            #{no}
        </foreach>
        <if test="no != null and  no != '' ">
            and no = #{no}
        </if>
    </select>

    <delete id="batchDeleteIgnoreByDetailNos">
        delete from mqs_audit_ignore
        where detail_no in
        <foreach collection="detailNos" open="(" close=")" separator="," item="no">
            #{no}
        </foreach>
        <if test="no != null and  no != '' ">
            and no = #{no}
        </if>
    </delete>

    <update id="batchUpdateSelfExpense">
        <foreach collection="items" item="item" separator=";">
            update ${table}_detail
            set
            self_expense = #{item.selfExpense},
            last_updated_date = now(),
            last_updated_by = #{userId}
            where
            hospital_id = #{hospitalId}
            and detail_no = #{item.detailNo}
        </foreach>
    </update>

    <update id="batchUpdateSelfExpenseHis">
        <foreach collection="items" item="item" separator=";">
            update ${table}_detail_his
            set
            self_expense = #{item.selfExpense},
            last_updated_date = now(),
            last_updated_by = #{userId}
            where
            hospital_id = #{hospitalId}
            and detail_no = #{item.detailNo}
        </foreach>
    </update>

    <select id = "queryMedicalAudit" resultType="com.crhms.cloud.mqs.basic.vo.ComprePopupDetailVO">
        SELECT
            audit.detail_no,
            audit.`no`,
            audit.`no` AS item_id,
            '主单违规' AS item_name,
            GROUP_CONCAT(DISTINCT audit.rule_type ORDER BY audit.rule_type SEPARATOR ',') AS rule_type,
            GROUP_CONCAT(DISTINCT audit.rule_type_name ORDER BY audit.rule_type_name SEPARATOR ',') AS rule_type_name,
            GROUP_CONCAT(DISTINCT audit.rule_name ORDER BY audit.rule_name SEPARATOR ',') AS rule_names,
            GROUP_CONCAT(DISTINCT audit.rule_reason ORDER BY audit.rule_reason SEPARATOR ',') AS rule_reasons,
            reason.reason_type as reason_types,
            reason.reason_des as reason_dess,
            '1' AS operation_type,
            case
              WHEN audit.rule_origin = '1' then '1'
              WHEN audit.rule_origin = '2' then '0'
            ELSE '1'
            END AS audit_source
        FROM
            ${auditTable} audit
            LEFT JOIN ${table}_reason reason ON audit.detail_no = reason.detail_no AND audit.admission_no = reason.admission_no
        WHERE
            audit.admission_no = #{admissionNo}
        AND audit.hospital_id = #{hospitalId}
        AND audit.detail_no = '0'
        <if test="batchNo != null and  batchNo != '' ">
            AND audit.batch_no = #{batchNo}
        </if>

        <if test="queryVO.auditSource != null and  queryVO.auditSource != '' ">               <!-- 审核渠道-->
            <choose>
                <when test="queryVO.auditSource == 0 ">
                    AND audit.rule_origin = '2'
                </when>
                <when test="queryVO.auditSource == 1 ">
                    AND audit.rule_origin = '1'
                </when>
                <when test="queryVO.auditSource == 2 ">
                    AND 1 = 0
                </when>
            </choose>
        </if>
        <if test="queryVO.selfExpense != null and  queryVO.selfExpense != '' ">               <!-- 自费状态-->
            AND 1 = 0
        </if>
        <if test="queryVO.selfExpense != null and  queryVO.selfExpense != '' ">               <!-- 自费状态-->
            AND 1 = 0
        </if>
        <if test="queryVO.ruleTypes != null and queryVO.ruleTypes.size() > 0">                <!--违规级别-->
            AND audit.rule_type in
            <foreach collection="queryVO.ruleTypes" item="ruleTypeItem" open="(" separator="," close=")">
                #{ruleTypeItem}
            </foreach>
        </if>
        <if test="queryVO.itemName != null and queryVO.itemName != ''">
            AND '主单违规' LIKE CONCAT('%', #{queryVO.itemName}, '%')
        </if>
        <if test="queryVO.itemTypeCode != null and queryVO.itemTypeCode != ''">
            and 1 = 0
        </if>
        <if test="queryVO.categoryName != null and queryVO.categoryName != ''">
            and 1 = 0
        </if>
        <if test="queryVO.outpatientMedication != null and queryVO.outpatientMedication != ''">
            and 1 = 0
        </if>
        <if test="queryVO.clinicalStatus != null and  queryVO.clinicalStatus != '' and table == 'mqs_mr_base'">               <!-- 临床处理状态-->
            <choose>
                <when test="queryVO.clinicalStatus == 0 ">
                    and not EXISTS (select 1 from mqs_audit_ignore maIgnore where audit.detail_no = maIgnore.detail_no and audit.no = maIgnore.no)
                </when>
                <when test="queryVO.clinicalStatus == 2 ">
                    and EXISTS (select 1 from mqs_audit_ignore maIgnore where audit.detail_no = maIgnore.detail_no and audit.no = maIgnore.no)
                </when>
            </choose>
        </if>
        GROUP BY
        audit.detail_no,audit.`no`, reason.reason_type, reason.reason_des,audit.rule_origin;
    </select>
    <select id="selectSurviveCase" resultType="java.lang.String">
        SELECT `no`
        FROM ${table}
        where `no` IN
        <foreach collection="nos" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>

    </select>
    <select id="selectAuditNoNums" resultType="java.lang.Integer">
        select
            IFNULL(max(audit_nums),0)
        from (
             SELECT
                 audit_nums
             FROM
                 ${table}
             where
                 `no` = #{no}
             union all
             select
                 max(audit_nums)
             FROM
                 ${table}_his
             where
                 `no` = #{no}
         ) t
    </select>

</mapper>

