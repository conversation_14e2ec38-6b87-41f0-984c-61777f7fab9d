<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_kbm.mapper.GroupCodeValidityMapper">

    <select id="selectVersionNoList" resultType="java.lang.String">
        SELECT
            version_no
        FROM
            mqs_kbm_group_code_validity
        GROUP BY
            version_no
    </select>
    <select id="selectTypeList" resultType="java.lang.String">
        SELECT
            type
        FROM
            mqs_kbm_group_code_validity
        GROUP BY
            type
    </select>
    <select id="selectValidFlagList" resultType="java.lang.String">
        SELECT
            valid_flag
        FROM
            mqs_kbm_group_code_validity
        GROUP BY
            valid_flag
    </select>
</mapper>