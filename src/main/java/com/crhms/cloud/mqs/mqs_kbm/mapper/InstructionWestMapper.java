package com.crhms.cloud.mqs.mqs_kbm.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.mqs.mqs_kbm.vo.InstructionDrug;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 明书-西药药品说明书 mapper
 *
 * @author: wangxingang
 * @date: 2023.02.09
 */

@Mapper
public interface InstructionWestMapper {
    /**
     * 查询剂型下拉列表
     */
    List<String> queryDosageList(@Param("type") String type);

    /**
     * 查询西药药品列表
     *
     * @param pageInfo   分页信息
     * @param type       0:西药 1:中药
     * @param drugCode   药品编码
     * @param drugName   药品名称
     * @param dosageList 剂型列表
     */
    List<InstructionDrug> queryWestDrugList(@Param("pageInfo") Page<InstructionDrug> pageInfo,
                                            @Param("type") String type,
                                            @Param("drugCode") String drugCode,
                                            @Param("drugName") String drugName,
                                            @Param("dosageList") List<String> dosageList);

    /**
     * 查询中药药品列表
     *
     * @param pageInfo   分页信息
     * @param type       0:西药 1:中药
     * @param drugCode   药品编码
     * @param drugName   药品名称
     * @param dosageList 剂型列表
     */
    List<InstructionDrug> queryChineseDrugList(@Param("pageInfo") Page<InstructionDrug> pageInfo,
                                               @Param("type") String type,
                                               @Param("drugCode") String drugCode,
                                               @Param("drugName") String drugName,
                                               @Param("dosageList") List<String> dosageList);

    /**
     * 通用接口: 查询特殊人群用药
     *
     * @param pageInfo 分页信息
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    @MapKey("")
    List<Map<String, String>> querySpecialGroup(@Param("pageInfo") Page<Object> pageInfo,
                                                @Param("type") String type,
                                                @Param("drugCode") String drugCode);

    /**
     * 通用接口: 查询药品配伍禁忌
     *
     * @param pageInfo 分页信息
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    @MapKey("")
    List<Map<String, String>> queryIncompatibility(@Param("pageInfo") Page<Object> pageInfo,
                                                   @Param("type") String type,
                                                   @Param("drugCode") String drugCode);

    /**
     * 通用接口: 查询药品适应症关系
     *
     * @param pageInfo 分页信息
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    @MapKey("")
    List<Map<String, String>> queryIndication(@Param("pageInfo") Page<Object> pageInfo,
                                              @Param("type") String type,
                                              @Param("drugCode") String drugCode);

    /**
     * 通用接口: 查询药品禁忌症关系
     *
     * @param pageInfo 分页信息
     * @param type     0:西药 1:中药
     * @param drugCode 药品编码
     */
    @MapKey("")
    List<Map<String, String>> queryTaboos(@Param("pageInfo") Page<Object> pageInfo,
                                          @Param("type") String type,
                                          @Param("drugCode") String drugCode);

    /**
     * 西药接口: 查询药物相互作用
     *
     * @param pageInfo 分页信息
     * @param drugCode 药品编码
     */
    @MapKey("")
    List<Map<String, String>> queryInteraction(@Param("pageInfo") Page<Object> pageInfo,
                                               @Param("drugCode") String drugCode);

    /**
     * 西药接口: 查询过敏反应
     *
     * @param pageInfo 分页信息
     * @param drugCode 药品编码
     */
    @MapKey("")
    List<Map<String, String>> queryAllergy(@Param("pageInfo") Page<Object> pageInfo,
                                           @Param("drugCode") String drugCode);
}
