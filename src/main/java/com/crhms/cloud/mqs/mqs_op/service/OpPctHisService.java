package com.crhms.cloud.mqs.mqs_op.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_op.domain.OpPctHis;
import com.crhms.cloud.mqs.mqs_op.mapper.OpPctHisMapper;
import com.crhms.cloud.mqs.mqs_op.service.OpPctHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 门诊审核-门诊处方审核(OpPctHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-19 14:40:10
 */
@Service("opPctHisService")
public class OpPctHisService extends ServiceImpl< OpPctHisMapper, OpPctHis> {

}
