package com.crhms.cloud.mqs.mqs_mr.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.service.BaseMedicalService;
import com.crhms.cloud.mqs.basic.vo.BaseCaseQueryVO;
import com.crhms.cloud.mqs.mqs_mr.domain.MrLog;
import com.crhms.cloud.mqs.mqs_mr.service.MrReviewService;
import com.crhms.cloud.mqs.mqs_mr.vo.MrChangeDTO;
import com.crhms.cloud.mqs.mqs_mr.vo.MrDetailQueryVO;
import com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail;
import com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetailVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 人工审核模块 控制层
 *
 * <AUTHOR>
 * @since 2023-02-06 10:48:16
 */
@RestController
@RequestMapping("api/mqs/mrReview")
public class MrReviewController {
    /**
     * 服务对象
     */
    @Autowired
    private MrReviewService mrReviewService;

    @Autowired
    private BaseMedicalService baseMedicalService;

    /**
     * 分页分场景查询人工审核数据
     *
     * @param queryVO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryByPage")
    public ResponseEntity<List<BaseMedicalCase>> queryByPage(@RequestBody BaseCaseQueryVO queryVO) {
        Page page = PageUtil.getPage(queryVO.getPage(), queryVO.getPageSize());
        return new ResponseEntity(this.mrReviewService.queryByPage(queryVO, page, LoginContext.getHospitalId()), PageUtil.getTotalHeader(page), HttpStatus.OK);
    }


    /**
     * 查询主单违规数据
     *
     * @return 查询结果
     */
    @GetMapping("/queryCaseAudit")
    public ResponseEntity<MrMedicalDetail> queryCaseAudit(@RequestParam(value = "no") String no, @RequestParam(value = "admissionNo") String admissionNo) {
         return new ResponseEntity(this.mrReviewService.queryCaseAudit(no,admissionNo,LoginContext.getHospitalId()), HttpStatus.OK);
    }

    /**
     * 查询人工审核详情 - 费用明细
     * @param baseDetailQueryVO
     * @return
     */
    @PostMapping("/queryBaseMedicalDetail")
    public ResponseEntity<MrMedicalDetailVO> queryBaseMedicalDetail(@RequestBody MrDetailQueryVO baseDetailQueryVO){
        Page page = PageUtil.getPage(baseDetailQueryVO.getPage(), baseDetailQueryVO.getPageSize());
        return new ResponseEntity(this.mrReviewService.queryBaseMedicalDetail(baseDetailQueryVO, page), PageUtil.getTotalHeader(page), HttpStatus.OK);
    }

    /**
     * 查询人工审核详情 - 费用明细-分组
     * @param baseDetailQueryVO
     * @return
     */
    @PostMapping("/queryBaseMedicalDetail/byItem")
    public ResponseEntity<MrMedicalDetailVO> queryBaseMedicalDetailByItemIds(@RequestBody MrDetailQueryVO baseDetailQueryVO){
        Page page = PageUtil.getPage(baseDetailQueryVO.getPage(), baseDetailQueryVO.getPageSize());
        return new ResponseEntity(this.mrReviewService.queryBaseMedicalDetailByItemIds(baseDetailQueryVO, page), PageUtil.getTotalHeader(page), HttpStatus.OK);
    }


    /**
     * 转人工违规
     *
     * @return
     */
    @PostMapping("/change/violationFlag")
    public ResponseEntity changeViolationFlag(@RequestBody MrChangeDTO mrChangeDTO) {
        mrReviewService.changeViolationFlag(mrChangeDTO, LoginContext.getHospitalId());
        return ResponseEntity.ok("转违规完成！");
    }

    /**
     * 撤销转人工违规
     *
     * @return
     */
    @PostMapping("/unDoChange/violationFlag")
    public ResponseEntity unDoChangeViolationFlag(@RequestBody List<String> detailNos) {
        mrReviewService.unDoChangeViolationFlag(detailNos, LoginContext.getHospitalId());
        return ResponseEntity.ok("撤销转违规完成！");
    }

    /**
     * 审核
     *
     * @return
     */
    @PostMapping("/reviewPass")
    public ResponseEntity reviewPass(@RequestBody List<MrChangeDTO> mrChangeDTOs) {
        mrReviewService.reviewPass(mrChangeDTOs,LoginContext.getHospitalId(),true);
        return ResponseEntity.ok("审核已通过！！！");
    }

    /**
     * 撤销审核
     *
     * @return
     */
    @GetMapping("/unDoReviewPass")
    public ResponseEntity unDoReviewPass(@RequestParam(value = "no") String no,@RequestParam(value = "admissionNo") String admissionNo) {
        mrReviewService.unDoReviewPass(no,admissionNo,LoginContext.getHospitalId(),true);
        return ResponseEntity.ok("已撤销审核！！！");
    }























    /**
     * 查询人工审核详情
     * @param no
     * @return
     */
    @GetMapping("/queryBaseMedicalCase")
    public ResponseEntity<BaseMedicalCase> queryBaseMedicalCase(@RequestParam(value = "no") String no, @RequestParam(value = "auditScenario") String auditScenario){
        return ResponseEntity.ok(mrReviewService.queryBaseMedicalCase(no,auditScenario,LoginContext.getHospitalId()));
    }


    /**
     * 人工审核接口
     *
     * @return
     */
    @PostMapping("/review")
    public ResponseEntity review(@RequestBody Map<String, Object> requestBody) {
        List<Map> mrList = (List<Map>) requestBody.get("list");
        mrReviewService.review((String) requestBody.get("auditScenario"),
                (String) requestBody.get("mrStatus"),
                (String) requestBody.get("mrOpinion"),
                mrList);
        return ResponseEntity.ok("人工审核完成！");
    }

    /**
     * 转自费
     *
     * @return
     */
    @PostMapping("/change/selfExpense")
    public ResponseEntity changeSelfExpense(@RequestBody MrChangeDTO mrChangeDTO) {
        mrReviewService.changeSelfExpense(mrChangeDTO, LoginContext.getHospitalId());
        return ResponseEntity.ok("转自费成功！");
    }


    /**
     * 自费协议导出
     * @param no
     * @return
     */
    @GetMapping("/exportSelfAgreement")
    public ResponseEntity exportSelfAgreement(@RequestParam(value = "no") String no, @RequestParam(value = "auditScenario") String auditScenario,HttpServletResponse response){

        baseMedicalService.exportSelfAgreement(response, no, null,auditScenario,null, LoginContext.getHospitalId(),null);
        return ResponseEntity.ok("success");
    }


    /**
     * 查询人工审核日志
     *
     * @param no
     * @param admissionNo
     * @param userId
     * @param userName
     * @return
     */
    @GetMapping("/queryLog")
    public ResponseEntity<List<MrLog>> queryLog(@RequestParam(value = "no", required = false) String no,
                                                @RequestParam(value = "admissionNo", required = false) String admissionNo,
                                                @RequestParam(value = "userId", required = false) String userId,
                                                @RequestParam(value = "userName", required = false) String userName,
                                                @RequestParam(value = "page") int page,
                                                @RequestParam(value = "pageSize") int pageSize) {

        Page Page = PageUtil.getPage(page, pageSize);

        return new ResponseEntity(this.mrReviewService.queryLog(no, admissionNo, userId, userName,Page), PageUtil.getTotalHeader(Page), HttpStatus.OK);
    }


    /**
     * 导出excel
     *
     * @param response 响应
     * @param queryVO  筛选条件
     */
    @PostMapping("/export")
    public void exportExcel(HttpServletResponse response, @RequestBody BaseCaseQueryVO queryVO) {
        mrReviewService.exportExcel(response, queryVO);
    }





}

