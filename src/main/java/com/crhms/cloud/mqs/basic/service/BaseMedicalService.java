package com.crhms.cloud.mqs.basic.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.user.dto.CustomUser;
import com.crhms.cloud.core.utils.ApplicationContextUtil;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.common.DownloadWordDocument;
import com.crhms.cloud.mqs.basic.common.MqsResult;
import com.crhms.cloud.mqs.basic.domain.*;
import com.crhms.cloud.mqs.basic.mapper.*;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.basic.vo.*;
import com.crhms.cloud.mqs.core.service.CdmpServiceInterface;
import com.crhms.cloud.mqs.core.service.EngineService;
import com.crhms.cloud.mqs.mqs_mr.domain.MrAudit;
import com.crhms.cloud.mqs.mqs_mr.domain.MrCase;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlan;
import com.crhms.cloud.mqs.mqs_mr.manager.MrPlanFilterImpl;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrAuditMapper;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrReviewMapper;
import com.crhms.cloud.mqs.mqs_mr.service.MrCaseService;
import com.crhms.cloud.mqs.mqs_mr.service.MrPlanService;
import com.crhms.cloud.mqs.mqs_mr.vo.MrDetailAggregation;
import com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail;
import com.crhms.cloud.mqs.sys.disruptor.DisruptorQueue;
import com.crhms.cloud.mqs.sys.domain.*;
import com.crhms.cloud.mqs.sys.dto.SysSceneDTO;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.mapper.SysRulesMapper;
import com.crhms.cloud.mqs.sys.service.*;
import com.crhms.cloud.mqs.sys.utils.BigDecimalUtils;
import com.crhms.cloud.mqs.sys.utils.DictUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Comparator.comparing;

/**
 * 多场景审核 服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 09:48:16
 */
@Log4j2
@Service("BaseMedicalService")
public class BaseMedicalService {
    @Autowired
    private BaseMedicalMapper baseMedicalMapper;
    @Autowired
    private BaseAuditMapper baseAuditMapper;
    @Autowired
    private BaseMedicalOrdersMapper baseMedicalOrdersMapper;
    @Autowired
    private HpOperationMapper hpOperationMapper;

    @Autowired
    private HpDiagMapper hpDiagMapper;

    @Autowired
    private SysRuleLevelService sysRuleLevelService;
    @Autowired
    private MrPlanService mrPlanService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private IppService ippService;
    @Autowired
    private SysRulesMapper sysRulesMapper;
    @Autowired
    private EngineService engineService;
    @Autowired
    private MrCaseService mrCaseService;
    @Lazy
    @Autowired
    private BaseMedicalService baseMedicalService;

    @Autowired
    private ExportWordService exportWordService;
    @Autowired
    private SysConfigService sysConfigService;
    //自费协议模板文件名
    private static final String MQS_SELFAGREEMENT_TEMP = "MQS_SELFAGREEMENT_TEMP";
    //自费协议文件标题内容
    private static final String MQS_SELFAGREEMENT_TITLE = "MQS_SELFAGREEMENT_TITLE";

    //批量保存 批次大小
    private static int MQS_BATCH_INSERT_SIZE= 5000;
    //in 条件大小
    private static int MQS_BATCH_UPDATE_SIZE= 1000;

    @Autowired
    private MrPlanFilterImpl mrPlanFilterImpl;

    @Autowired
    private SysSceneService sysSceneService;

    @Autowired
    private BaseMedicalStatsService baseMedicalStatsService;

    @Autowired
    private MrReviewMapper mrReviewMapper;

    @Autowired
    private MrAuditMapper mrAuditMapper;

    @Autowired
    private SysLogsService sysLogsService;

    @Autowired
    private CdmpServiceInterface cdmpServiceInterface;

    public List<BaseMedicalCase> queryCaseListByPage(BaseCaseQueryVO queryVO,Page page) {
        List<BaseMedicalCase> baseMedicalCases = new ArrayList<>(20);
        try {
            //主单查询包含（分TAB 和 合并展示）
            String hospitalId = LoginContext.getHospitalId();
            queryVO.setHospitalId(hospitalId);
            //获取表
            AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(queryVO.getAuditScenario());
            String tableName = auditScenarioEnum.getTableName();
            //分页查询
            List<BaseMedicalCase> pageList = baseMedicalMapper.queryCaseListByPage(tableName,queryVO,page);
            if(CollectionUtil.isEmpty(pageList)) {
                return Collections.emptyList();
            }
            //查询病区字段
            Map<String,IppZone> collect = ippService.selectZoneMap(null, hospitalId);
            //查询人工审核日期
            List<MrCase> cases = mrCaseService.queryMrCase(null,hospitalId,queryVO.getAuditScenario());
            //按照场景进行分组
            Map<String,List<BaseMedicalCase>> groupMap = pageList.stream().collect(Collectors.groupingBy(BaseMedicalCase::getSourceTable));
            //遍历
            groupMap.forEach((k,v) -> {
                Map<String,MrCase> caseMap = null;
                if(CollectionUtil.isNotEmpty(cases)){
                    caseMap = cases.stream().collect(Collectors.toMap(MrCase::getCaseNo,mrCase -> mrCase));
                }
                Map<String, List<BaseAudit>> auditCollect = new HashMap<>();
                //判断是否是特殊场景 门诊挂号审核以及住院登记审核
                boolean flag = AuditScenarioEnum.opRg.getTableName().equals(k) || AuditScenarioEnum.hpRg.getTableName().equals(k);
                if(flag){
                    //查询特殊场景的审核结果信息
                    List<String> nos = v.stream().map(BaseMedicalCase::getNo).collect(Collectors.toList());
                    List<BaseAudit> baseAudits = baseAuditMapper.selectCaseAudit(k,nos,Arrays.asList("0"),hospitalId);
                    auditCollect = baseAudits.stream().collect(Collectors.groupingBy(BaseAudit::getNo));
                }
                //对结果进行修改组装
                for(BaseMedicalCase medicalCase : v) {
                    //获取场景标识
                    String auditScenario = AuditScenarioEnum.getAuditScenarioEnumByTableName(k).getAuditScenario();
                    //设置出入院状态
                    if(medicalCase.getIsDischarge() == null){
                        medicalCase.setIsDischarge(auditScenario.contains("op") ? "2" : medicalCase.getDischargeDate() == null ? "0" : "1");
                    }
                    //设置病区名称
                    IppZone ippZone = collect.get(medicalCase.getOutZoneCode());
                    medicalCase.setOutZoneName(ippZone ==null ? null : ippZone.getName());
                    medicalCase.setAuditScenario(auditScenario);
                    //特殊场景拼装规则名称、原因
                    if(flag){
                        List<BaseAudit> baseAudits = auditCollect.get(medicalCase.getNo());
                        if(CollectionUtil.isNotEmpty(baseAudits)){
                            medicalCase.setRuleNames(baseAudits.stream().map(BaseAudit::getRuleName).collect(Collectors.joining("|")));
                            medicalCase.setRuleReasons(baseAudits.stream().map(BaseAudit::getRuleReason).collect(Collectors.joining("|")));
                        }
                    }
                    //设置人工审核时间
                    medicalCase.setMrTime(Objects.nonNull(caseMap) && Objects.nonNull(caseMap.get(medicalCase.getNo())) ? caseMap.get(medicalCase.getNo()).getMrTime() : null);
                    baseMedicalCases.add(medicalCase);
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            log.error("主单查询失败！！！异常：{}，请求参数：{}",e,JSONUtil.parseObj(queryVO));
        } finally {
            //翻译字典
            DictUtils.translateDict(baseMedicalCases);
            return baseMedicalCases;
        }
    }

    /**
     * 查询历史单据
     *
     * @param no
     * @param batchNo
     * @return
     */
    public List<BaseMedicalCase> queryCaseHisList(String auditScenario, String no, String batchNo, String hospitalId) {
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(auditScenario);
        String tableName = auditScenarioEnum.getTableName();
        List<BaseMedicalCase> hisList = baseMedicalMapper.queryCaseHisList(tableName, no, batchNo, hospitalId);

        if(CollectionUtil.isNotEmpty(hisList)){
            //挂号和住院登记单独查询违规信息
            Map<String, List<BaseAudit>> auditCollect = new HashMap<>();
            if(AuditScenarioEnum.opRg.equals(auditScenarioEnum) || AuditScenarioEnum.hpRg.equals(auditScenarioEnum)){

                List<BaseAudit> baseAudits = baseAuditMapper.selectCaseAuditHis(tableName,no,hospitalId);
                auditCollect = baseAudits.stream().collect(Collectors.groupingBy(BaseAudit::getBatchNo));

            }
            List<IppZone> ippZones = ippService.selectZoneList(null, hospitalId);
            Map<String, String> collect = ippZones.stream().collect(Collectors.toMap(IppZone::getCode, IppZone::getName, (key1, key2) -> key2));
            for (BaseMedicalCase x : hisList) {
                if(x.getIsDischarge() == null){
                    x.setIsDischarge(auditScenarioEnum.getAuditScenario().contains("op") ? "2" : x.getDischargeDate() == null ? "0" : "1");
                }
                x.setOutZoneName(collect.get(x.getOutZoneCode()));
                List<BaseAudit> baseAudits = auditCollect.get(x.getBatchNo());
                if(CollectionUtil.isNotEmpty(baseAudits)){
                    StringBuffer ruleNames = new StringBuffer();
                    StringBuffer ruleReasons = new StringBuffer();
                    Iterator<BaseAudit> iterator = baseAudits.iterator();
                    while (iterator.hasNext()){
                        BaseAudit next = iterator.next();
                        ruleNames.append(next.getRuleName());
                        ruleReasons.append(next.getRuleReason());
                        if(iterator.hasNext()){
                            ruleNames.append("|");
                            ruleReasons.append("|");
                        }
                    }
                    x.setRuleNames(ruleNames.toString());
                    x.setRuleReasons(ruleReasons.toString());
                }
            }

            DictUtils.translateDict(hisList);

        }
        return hisList;
    }

    /**
     * 查询费用明细信息
     *
     * @return
     */
    public List<BaseMedicalDetail> queryDetail(BaseDetailQueryVO queryVO) {
        String hospitalId = LoginContext.getHospitalId();
        queryVO.setHospitalId(hospitalId);
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), queryVO.getAuditScenario())) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(queryVO.getAuditScenario()).getTableName();
        List<BaseMedicalDetail> details = new ArrayList<>();
        if ("1".equals(queryVO.getIsHis())) {
            details = baseMedicalMapper.queryDetailAllHis(tableName, queryVO,null);
        } else {
            details = baseMedicalMapper.queryDetailAll(tableName, queryVO,null);
        }
        details.forEach(e->{
            if(StringUtils.isEmpty(e.getRelatedRecords()))
                e.setRelatedRecords("0");
        });

//        if(CollectionUtils.isNotEmpty(details)){
//            String admissionNo = queryVO.getAdmissionNo();
//            List<BaseMedicalDetail> inner_detail = details.stream().filter(e-> StringUtils.isNotBlank(e.getRuleCodes())).collect(Collectors.toList());
//            if(CollectionUtils.isNotEmpty(inner_detail)){
//                inner_detail.forEach(e->{
//                    if (e.getAdmissionNo().equals(admissionNo)){
//                        e.setRelatedRecords("1");
//                    }else{
//                        e.setRelatedRecords("2");
//                    }
//                });
//            }
//        }

        //增加高亮显示标识
        if (StringUtils.isNotEmpty(queryVO.getRuleCode())) {
            for (BaseMedicalDetail detail : details) {
                detail.setHighLight(StringUtils.isEmpty(detail.getRuleCodes()) ? false : detail.getRuleCodes().contains(queryVO.getRuleCode()));
            }
        }
        DictUtils.translateDict(details);
        return details;
    }

    public List<BaseMedicalDetail> queryAllDetail (BaseDetailQueryVO queryVO,Page page){
        List<BaseMedicalDetail> details = new ArrayList<>(20);
        try{
            String hospitalId = LoginContext.getHospitalId();
            queryVO.setHospitalId(hospitalId);
            //子场景不管是分TAB 还是 合并都只允许一个场景
            String auditScenarios = queryVO.getAuditScenario();
            AuditScenarioEnum auditScenarioEnum = AuditScenarioEnum.valueOf(auditScenarios);
            String tableName = auditScenarioEnum.getTableName();
            //是否查询his数据 还是正式数据
            if ("1".equals(queryVO.getIsHis())){
                details = baseMedicalMapper.queryDetailAllHis(tableName, queryVO,page);
            } else {
                details = baseMedicalMapper.queryDetailAll(tableName, queryVO,page);
            }
            if(CollectionUtil.isEmpty(details)){
                return Collections.emptyList();
            }
            details.forEach(e->{
                if(StringUtils.isEmpty(e.getRelatedRecords()))
                    e.setRelatedRecords("0");
            });
            //查询人工审核日期
            Map<String,MrAudit> auditMap = null;
            List<String> detailNos = details.stream().map(detail -> detail.getDetailNo()).collect(Collectors.toList());
            List<MrAudit> mrAudits = mrAuditMapper.selectList(new LambdaQueryWrapper<MrAudit>().eq(MrAudit::getHospitalId,hospitalId).in(MrAudit::getDetailNo,detailNos));
            if(CollectionUtil.isNotEmpty(mrAudits)) {
                auditMap = mrAudits.stream().collect(Collectors.toMap(MrAudit::getDetailNo,mrAudit -> mrAudit));
            }
            //查询病区信息
            List<IppZone> ippZones = ippService.selectZoneList(null, hospitalId);
            Map<String, String> collect = ippZones.stream().collect(Collectors.toMap(IppZone::getCode, IppZone::getName, (key1, key2) -> key2));
            //高亮显示
            for (BaseMedicalDetail detail : details) {
                detail.setMrTime(Objects.nonNull(auditMap) && Objects.nonNull(auditMap.get(detail.getDetailNo())) ? auditMap.get(detail.getDetailNo()).getLastUpdatedDate() : null);
                detail.setOutZoneName(collect.get(detail.getOutZoneCode()));
                detail.setHighLight(StringUtils.isEmpty(detail.getRuleCodes()) || StringUtils.isEmpty(queryVO.getRuleCode()) ? false : detail.getRuleCodes().contains(queryVO.getRuleCode()));

            }
        } catch (Exception e) {
            log.info("明细查询失败！！！异常信息：{}，参数：{}",e,JSONUtil.parseObj(queryVO));
        } finally {
            //翻译字典项
            DictUtils.translateDict(details);
            return details;
        }
    }


    /**
     * 用于诊间审核后数据记录
     *
     * @param batch            审核批次
     * @param auditScenario    场景
     * @param engAuditResultVo 引擎结果
     * @param medicalParamList  单据数据
     * @param isPretrial       是否预审
     * @param isSave           是否直接入表
     */
    @Async
    @Transactional
    public void auditSave(String batch, String auditScenario, EngAuditVo engAuditVo, EngAuditResultVo engAuditResultVo, List<BaseMedicalParam> medicalParamList, String isPretrial, boolean isSave, String hospitalId) {

        if (CollectionUtil.isEmpty(medicalParamList)) {
            return;
        }
        List<String> nos = medicalParamList.stream().map(medicalParam -> medicalParam.getMedical_case().getNo()).collect(Collectors.toList());
        //增量单据no集合
        List<String> incrementNos = new ArrayList<>();
        //审核结果集
        List<BaseAudit> baseAuditList = new ArrayList<>();
        //跨单结果集
        List<BaseAudit> crossBaseAuditList = new ArrayList<>();
        //明细集
        List<BaseMedicalDetail> detailList = new ArrayList<>();
        //医嘱明细
        List<BaseMedicalOrders> ordersList = new ArrayList<>();
        //诊断明细
        List<BaseMedicalDiag> diagList = new ArrayList<>();

        //手术记录集
        List<HisOperationMainVo> operation_list = new ArrayList<>();
        //手术明细记录集
        List<HisOperationDetailVo> operation_detail_list = new ArrayList<>();

        // 引擎返回结果标记(是否有违规行)
        boolean violation = false;

        //1.违规数据组装
        Map<String, EngPlReVo> engPlReVoMap = new HashMap<>();
        engPlReVoMap.put("local", engAuditResultVo.getLocal());
        engPlReVoMap.put("engine", engAuditResultVo.getEngine());
        engPlReVoMap.put("platform", engAuditResultVo.getPlatform());

        for (Map.Entry<String, EngPlReVo> entry : engPlReVoMap.entrySet()) {
            String key = entry.getKey();
            if(ObjectUtil.isNotNull(entry.getValue())){
                if (ObjectUtil.isNotNull(entry.getValue().getResult())) {
                    for (EngResultVo engResultVo : entry.getValue().getResult()) {
                        if (ObjectUtil.isNotNull(engResultVo.getViolation_result())) {
                            for (EngViolationResultVo resultVo : engResultVo.getViolation_result()) {
                                String detailNo = ObjectUtil.isEmpty(resultVo.getDetail_id())? "0":resultVo.getDetail_id();
                                String no = StrUtil.isNotEmpty(resultVo.getAdmission_no()) ? resultVo.getAdmission_no() : engResultVo.getClaim_id();
                                BigDecimal violationAmount = BigDecimal.ZERO;
                                DateTime itemDate = null;
                                if(resultVo.getItem_time() != null){
                                    itemDate = DateUtil.parse(resultVo.getItem_time(), "yyyy-MM-dd HH:mm:ss");
                                }
                                if("1".equals(resultVo.getViolation_type())){
                                    violation = true;
                                }
                                //引擎返回的违规为明细id，需要补全主单号
                                BaseAudit build = BaseAudit.builder().no(no)
                                        .detailNo(detailNo)
                                        .orderDetailNo(resultVo.getOrder_id())
                                        .batchNo(batch)
                                        .ruleType(resultVo.getResult_type())
                                        .ruleTypeName(resultVo.getResult_type_name())
                                        .ruleOrigin("local".equals(key) ? "1" : "2")
                                        .ruleCode(resultVo.getRule_no())
                                        .ruleName(resultVo.getRule_name())
                                        .ruleReason(resultVo.getReason())
                                        .related(resultVo.getRelated())
                                        .fullTip(resultVo.getFull_tip())
                                        .groupCode(resultVo.getGroup_code())
                                        .violationRatio(resultVo.getViolationRatio())
                                        .violationAmount(violationAmount)
                                        .violationType(resultVo.getViolation_type())
                                        .itemId(resultVo.getItem_id())
                                        .itemName(resultVo.getItem_name())
                                        .itemTypeCode(resultVo.getItem_type_code())
                                        .itemTypeName(resultVo.getItem_type_name())
                                        .itemDate(itemDate)
                                        .jrId(resultVo.getJr_id())
                                        .numbers(MqsUtils.getBigDecimal(resultVo.getNumbers()))
                                        .costs(MqsUtils.getBigDecimal(resultVo.getCosts()))
                                        .hospitalId(hospitalId).build();

                                if(!nos.contains(no)){
                                    //跨单据结果集 用作后续的单据补全 作为展示依据
                                    crossBaseAuditList.add(build);
                                }
                                baseAuditList.add(build);

                            }
                        }
                    }
                }
            }
        }

        //2.批量存储至历史表
        String table = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        if(CollectionUtil.isNotEmpty(crossBaseAuditList)){
            log.info("===" + batch + "=== 跨单据审核结果补全===【" + crossBaseAuditList.size() + "】===start===");

            Set<String> nolist = crossBaseAuditList.stream().map(BaseAudit::getNo).collect(Collectors.toSet());
            Set<String> details = crossBaseAuditList.stream().map(BaseAudit::getDetailNo).collect(Collectors.toSet());

            // 跨单据业务数据补全
            List<BaseMedicalParam> baseMedicalParams = this.genExitAuditDto(batch, nolist, details, auditScenario, hospitalId);
            medicalParamList.addAll(baseMedicalParams);

            // 跨单据审核结果补全
            List<BaseAudit> otherBaseAuditList = this.genCrossAuditDto(batch, nolist, details, auditScenario, hospitalId);
            // 比较去重
            otherBaseAuditList.stream().filter(x -> !baseAuditList.stream().anyMatch(y -> x.getDetailNo().equals(y.getDetailNo())
                    && x.getNo().equals(y.getNo())
                    && x.getRuleCode().equals(y.getRuleCode()))).forEach(baseAuditList::add);

            log.info("===" + batch + "=== 跨单据审核结果补全...===end===");
        }


        List<BaseMedicalCase> caseList = new ArrayList<>();
        List<String> sceneCodes = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(medicalParamList)) {

            for (BaseMedicalParam medicalParam : medicalParamList) {
                //统计审核次数
                BaseMedicalCase medicalCase = medicalParam.getMedical_case();
                caseList.add(medicalCase);
                List<BaseAudit> baseAudits = baseAuditList.stream().filter(x -> ObjectUtil.isNotEmpty(x.getNo()) && x.getNo().equals(medicalCase.getNo())).collect(Collectors.toList());
                baseAudits.stream().forEach(
                        x -> x.setAdmissionNo(medicalCase.getAdmissionNo())
                );
                medicalCase.setViolationFlag(CollectionUtil.isNotEmpty(baseAudits) ? "1" : "0");

                medicalCase.setAuditTime(new Date());
                medicalCase.setAuditNums(genAuditNums(table,hospitalId,medicalCase.getNo()));
                //增量no集合
                if(medicalParam.getIs_increment()){
                    incrementNos.add(medicalCase.getNo());
                }
                if(CollectionUtil.isNotEmpty(medicalParam.getFee_list())){
                    detailList.addAll(medicalParam.getFee_list());
                }
                if(CollectionUtil.isNotEmpty(medicalParam.getOrders_list())){
                    ordersList.addAll(medicalParam.getOrders_list());
                }
                if (CollectionUtil.isNotEmpty(medicalParam.getOperation_list())) {
                    operation_list.addAll(medicalParam.getOperation_list());
                    for (HisOperationMainVo hisOperationMainVo : medicalParam.getOperation_list()) {
                        if (CollectionUtil.isNotEmpty(hisOperationMainVo.getOperation_detail_list())) {
                            operation_detail_list.addAll(hisOperationMainVo.getOperation_detail_list());
                        }
                    }
                }
                if(CollectionUtil.isNotEmpty(medicalParam.getDiagnosis_list())){
                    diagList.addAll(medicalParam.getDiagnosis_list());
                }
            }

            // 获取人工审核标记 更新单据状态 保存业务流程结果
            baseMedicalStatsService.invokeMrFlagMap(baseAuditList, auditScenario, medicalParamList, sceneCodes, hospitalId);

            for (List<BaseMedicalCase> baseMedicalCases : CollectionUtil.split(caseList, MQS_BATCH_UPDATE_SIZE)) {
                baseMedicalMapper.batchInsertCaseHis(table, baseMedicalCases);
            }
        }

        if (CollectionUtil.isNotEmpty(detailList)) {

            // 增量场景 且本次涉及违规,提取历史数据合并处理
            if(CollUtil.isNotEmpty(incrementNos)){
                Set<String> allcollect = detailList.stream()
                        .map(BaseMedicalDetail::getDetailNo)
                        .collect(Collectors.toSet());
                //查询涉及历史明细
                Set<String> collect = detailList.stream()
                        .filter(x -> incrementNos.contains(x.getNo()))
                        .map(BaseMedicalDetail::getDetailNo)
                        .collect(Collectors.toSet());

                Set<String> oldDetails = baseAuditList.stream()
                        .filter(baseAudit -> collect.contains(baseAudit.getDetailNo()))
                        .flatMap(baseAudit -> Arrays.stream(baseAudit.getRelated().split(",")))
                        .filter(s -> !allcollect.contains(s))
                        .collect(Collectors.toSet());

                if(CollectionUtil.isNotEmpty(oldDetails)){
                    //查询历史明细
                    List<BaseMedicalDetail> oldDetailList = this.baseMedicalMapper.queryAuditDetailByDetailNo(table, new ArrayList<>(oldDetails));
                    //补全的历史明细
                    // OperationType:0 不属于当次明细标记
                    // IsCurrent:0 不属于增量数据
                    // LastUpdatedBy:99区分接口传入还是逻辑补全
                    // BatchNo 赋值当次待确认批次
                    // reverseFlag 赋值为0
                    oldDetailList.forEach(x -> { x.setOperationType("0");x.setIsCurrent("0"); x.setLastUpdatedBy(99L); x.setBatchNo(batch);});
                    detailList.addAll(oldDetailList);
                }

            }
            //拼装违规信息到明细
            splitAuditToDetail(auditScenario, detailList, baseAuditList);

            for (List<BaseMedicalDetail> medicalDetails : CollectionUtil.split(detailList, MQS_BATCH_INSERT_SIZE)) {
                baseMedicalMapper.batchInsertDetailHis(table, medicalDetails);
            }
        }
        if(CollectionUtil.isNotEmpty(ordersList)){
            //医嘱明细处理
            for (List<BaseMedicalOrders> baseMedicalOrders : CollectionUtil.split(ordersList, MQS_BATCH_INSERT_SIZE)) {
                baseMedicalOrdersMapper.batchInsertOrdersHis(table, baseMedicalOrders);
            }
        }

        if (CollectionUtil.isNotEmpty(baseAuditList)) {
            for (List<BaseAudit> baseAudits : CollectionUtil.split(baseAuditList, MQS_BATCH_INSERT_SIZE)) {
                baseAuditMapper.batchInsertAuditHis(table, baseAudits);
            }
        }
        if (CollectionUtil.isNotEmpty(operation_list)) {
            for (List<HisOperationMainVo> hisOperationMainVos : CollectionUtil.split(operation_list, MQS_BATCH_INSERT_SIZE)) {
                hpOperationMapper.batchSaveOperationsHis(hisOperationMainVos);
            }
        }
        if (CollectionUtil.isNotEmpty(operation_detail_list)) {
            for (List<HisOperationDetailVo> hisOperationDetailVos : CollectionUtil.split(operation_detail_list, MQS_BATCH_INSERT_SIZE)) {
                hpOperationMapper.batchSaveListHis(hisOperationDetailVos);
            }
        }
        if(CollectionUtil.isNotEmpty(diagList)){
            for (List<BaseMedicalDiag> medicalDiagList : CollectionUtil.split(diagList, MQS_BATCH_INSERT_SIZE)) {
                hpDiagMapper.batchSaveDiagHis(medicalDiagList);
            }
        }
        //本次审核的 isCurrent = 1 （当次明细） 且 reverseFlag = 0 （未被冲销删除的明细) 且 是否违规 =1   ==》 （存在相关违规）
        boolean noViolation = !detailList.stream().anyMatch(x -> "1".equals(x.getIsCurrent()) && "0".equals(x.getReverseFlag()) && "1".equals(x.getViolationFlag()));
        //3.（ 引擎执行成功 && 明细无违规 && 非预审 ） || 强制入表
        if (("true".equals(engAuditResultVo.getIs_success()) && noViolation && "0".equals(isPretrial)) || isSave) {
            //同步到正式表数据
            baseMedicalService.saveToCase(auditScenario, hospitalId, caseList, detailList, ordersList, baseAuditList, null, operation_list, operation_detail_list, diagList,sceneCodes);
            log.info("===" + batch + "=== 异步储存至正式表...======");
            //引擎有返回违规,不会自动保存，业务逻辑判断的保存要额外提醒引擎补全
            if(violation){
                EngAuditResultVo result = engineService.saveRecord(engAuditVo, hospitalId);
                log.debug("引擎返回：" + JSONUtil.toJsonStr(result));
                log.debug("======调用引擎保存单据 End======" + new java.sql.Timestamp(System.currentTimeMillis()).toString());
            }
        }
        log.info("===" + batch + "=== 线程保存======" + auditScenario + "======END======");
    }

    private void splitAuditToDetail(String auditScenario,List<BaseMedicalDetail> detailList,List<BaseAudit> baseAuditList){

        //拼装违规信息到明细
        for (BaseMedicalDetail baseMedicalDetail : detailList) {
            Set<String> ruleCodesSet = new HashSet();
            Set<String> ruleNamesSet = new HashSet<>();
            Set<String> ruleReasonsSet = new HashSet();
            Set<String> ruleOriginSet = new HashSet();
            Iterator<BaseAudit> iterator = baseAuditList.stream().filter(x -> x.getDetailNo().equals(baseMedicalDetail.getDetailNo())).iterator();
            String violationFlag = "0"; // 违规标记，存在相关违规则 置为 1
            String violationType = "0"; // 违规计费标记，存在相关违规且为扣款项 置为 1
            String maxRuleType = ""; // 违规计费标记，存在相关违规且为扣款项 置为 1
            String maxRuleTypeName = ""; // 违规计费标记，存在相关违规且为扣款项 置为 1
            BigDecimal vnumbers = BigDecimal.ZERO; // 违规数量取引擎返回值，最大不超过本身数量
            BigDecimal vcosts = BigDecimal.ZERO; // 违规金额取引擎返回值，最大不超过本身金额
            if( baseMedicalDetail.getNumbers() == null){
                baseMedicalDetail.setNumbers(BigDecimal.ZERO);
            }
            if( baseMedicalDetail.getCosts() == null){
                baseMedicalDetail.setCosts(BigDecimal.ZERO);
            }
            while (iterator.hasNext()){
                BaseAudit next = iterator.next();
                violationFlag = "1"; // 存在相关违规
                if("1".equals(next.getViolationType())){
                    //只处理 violation_type 为 1的结果数据用于直接展示
                    if(StrUtil.isEmpty(maxRuleType) || maxRuleType.compareTo(next.getRuleType()) > 0){
                        maxRuleType = next.getRuleType();
                        maxRuleTypeName = next.getRuleTypeName();
                    }
                    violationType= "1";
                    next.setViolationAmount(baseMedicalDetail.getCosts());
                    next.getRuleType();
                    ruleCodesSet.add(next.getRuleCode());
                    ruleNamesSet.add(next.getRuleName());
                    ruleReasonsSet.add(next.getRuleReason());
                    ruleOriginSet.add(next.getRuleOrigin());
                    //累计违规金额
                    vnumbers = BigDecimalUtils.add(vnumbers, next.getNumbers());
                    vcosts = BigDecimalUtils.add(vcosts, next.getCosts());
                }
            }
            String ruleNames = String.join("|", ruleNamesSet);
            String ruleCodes = String.join("|", ruleCodesSet);
            String ruleReasons = String.join("|", ruleReasonsSet);
            String ruleOrigins = String.join("|", ruleOriginSet);
            baseMedicalDetail.setViolationFlag(violationFlag);
            baseMedicalDetail.setViolationType(violationType);
            baseMedicalDetail.setRuleCodes(ruleCodes.length() > 0 ? ruleCodes.toString() : null);
            baseMedicalDetail.setRuleNames(ruleNames.length() > 0 ? ruleNames.toString() : null);
            baseMedicalDetail.setRuleReasons(ruleReasons.length() > 0 ? ruleReasons.toString() : null);
            baseMedicalDetail.setRuleOrigin(ruleOrigins.length() > 0 ? ruleOrigins.toString() : null);
            baseMedicalDetail.setRuleType(StrUtil.isEmpty(maxRuleType) ? null : maxRuleType);
            baseMedicalDetail.setRuleTypeName(StrUtil.isEmpty(maxRuleTypeName) ? null : maxRuleTypeName);
            baseMedicalDetail.setViolationNum(vnumbers.compareTo(baseMedicalDetail.getNumbers()) > 0 ?
                    baseMedicalDetail.getNumbers() : vnumbers );
            baseMedicalDetail.setViolationAmt(vcosts.compareTo(baseMedicalDetail.getCosts()) > 0 ?
                    baseMedicalDetail.getCosts() : vcosts );
        }
    }

    /**
     * 查询弹窗信息
     *
     * @param admissionNo
     * @param batch
     * @param auditScenario
     * @param hospitalId
     * @return
     */
    public BaseMedicalCase auditQueryMedicalCase(String admissionNo, String batch, String auditScenario, String hospitalId) {

        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        List<BaseMedicalCase> baseMedicalCases = this.baseMedicalMapper.auditQueryMedicalCaseHis(tableName, admissionNo, batch, hospitalId);
        if (CollectionUtil.isEmpty(baseMedicalCases)) {
            return null;
        }
        BaseMedicalCase medicalCase = baseMedicalCases.get(0);
        List<BaseAudit> baseAudits = baseAuditMapper.queryAuditHisByBatchNo(tableName, admissionNo, batch, hospitalId);

        List<String> rules = baseAudits.stream().map(BaseAudit::getRuleCode).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(rules)) {
            //查询是否有阻断级别的违规
            List<String> rule = sysRulesMapper.selectBlockRules("1", rules, hospitalId);
            medicalCase.setIsBlock(CollectionUtil.isNotEmpty(rule) ? "1" : "0");
        }

        //在院状态  (1-出院，0-在院) （根据出院时间判断，有出院时间则为出院，无则为在院）
        boolean dischargeState = Objects.nonNull(medicalCase.getDischargeDate());
        //入院天数
        Date admissionDate = Objects.nonNull(medicalCase.getAdmissionDate()) ? medicalCase.getAdmissionDate() : new Date();
        long days = 0;
        if(dischargeState){
            //已出院
            days = DateUtil.betweenDay(medicalCase.getDischargeDate(),admissionDate,true) + 1;
        } else {
            // 在院
            days = DateUtil.betweenDay(new Date(),admissionDate,true) + 1;
        }
        medicalCase.setInhospitalDays(String.valueOf(days));

        DictUtils.translateDict(medicalCase);
        return medicalCase;
    }

    public Map< String ,List<BaseMedicalDetail>> queryMedicalDetailCase(String admissionNo, String batch, String auditScenario, String hospitalId) {
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        //查询返回 三个列表 医嘱/院内 院内  医嘱
        Map<String ,List<BaseMedicalDetail>> result = new HashMap<>();

        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        List<BaseMedicalDetail> medicalDetails = new ArrayList<>();
        //查询头部违规
        List<BaseMedicalDetail> baseMedicalDetail = this.baseMedicalMapper.queryMedicalCaseAuditHis(tableName, admissionNo, batch, hospitalId);
        if(ObjectUtil.isNotNull(baseMedicalDetail)){
            medicalDetails.addAll(baseMedicalDetail);
        }

        //查询明细违规
        List<BaseMedicalDetail> medicalDetails1 = this.baseMedicalMapper.queryMedicalDetailCaseHis(tableName, admissionNo, batch, hospitalId);
        if(CollectionUtil.isNotEmpty(medicalDetails1)){
            medicalDetails.addAll(medicalDetails1);
        }
        DictUtils.translateDict(medicalDetails);
        //数据处理，分组
        Set<Map.Entry<String, List<BaseMedicalDetail>>> entries = medicalDetails.stream().collect(Collectors.groupingBy(BaseMedicalDetail::getDetailNo)).entrySet();
        List<BaseMedicalDetail> DetailsList = new ArrayList<>();
        List<BaseMedicalDetail> YNDetailsList = new ArrayList<>();
        List<BaseMedicalDetail> YBDtailsList = new ArrayList<>();

        for (Map.Entry<String, List<BaseMedicalDetail>> entry : entries) {
            String detailNo = entry.getKey();
            List<BaseMedicalDetail> details = entry.getValue();
            List<BaseMedicalDetail> YNDetails = details.stream().filter(x -> "1".equals(x.getRuleOrigin())).collect(Collectors.toList());
            List<BaseMedicalDetail> YBDetails = details.stream().filter(x -> "2".equals(x.getRuleOrigin())).collect(Collectors.toList());
            //只处理 violation_type 为 1的结果数据用于直接展示
            String maxRuleType = "";
            Set<String> ruleTypeNameSet = new HashSet<>();
            Set<String> ruleOriginSet = new HashSet<>();

            String YNmaxRuleType = "";
            Set<String> YNruleTypeNameSet = new HashSet<>();
            String YBmaxRuleType = "";
            Set<String> YBruleTypeNameSet = new HashSet<>();

            for (BaseMedicalDetail detail : details) {
                //取列表中ruleType 的最小值 和 ruleTypeName 合并内容
                if(StrUtil.isEmpty(maxRuleType) || maxRuleType.compareTo(detail.getRuleType()) > 0){
                    maxRuleType = detail.getRuleType();
                }
                ruleTypeNameSet.add(detail.getRuleTypeName());
                ruleOriginSet.add(detail.getRuleOrigin());
            }
            for (BaseMedicalDetail ynDetail : YNDetails) {
                //取列表中ruleType 的最小值 和 ruleTypeName 合并内容
                if(StrUtil.isEmpty(YNmaxRuleType) || YNmaxRuleType.compareTo(ynDetail.getRuleType()) > 0){
                    YNmaxRuleType = ynDetail.getRuleType();
                }
                YNruleTypeNameSet.add(ynDetail.getRuleTypeName());
            }
            for (BaseMedicalDetail ybDetail : YBDetails) {
                //取列表中ruleType 的最小值 和 ruleTypeName 合并内容
                if(StrUtil.isEmpty(YBmaxRuleType) || YBmaxRuleType.compareTo(ybDetail.getRuleType()) > 0){
                    YBmaxRuleType = ybDetail.getRuleType();
                }
                YBruleTypeNameSet.add(ybDetail.getRuleTypeName());
            }

            DetailsList.add(BaseMedicalDetail.builder()
                    .detailNo(detailNo)
                    .no(details.get(0).getNo())
                    .ruleType(maxRuleType)
                    .ruleTypeName(StrUtil.join(",",ruleTypeNameSet))
                    .itemId(details.get(0).getItemId())
                    .itemName(details.get(0).getItemName())
                    .itemDate(details.get(0).getItemDate())
                    .routeAdministration(details.get(0).getRouteAdministration())
                    .outpatientMedication(details.get(0).getOutpatientMedication())
                    .selfExpense(details.get(0).getSelfExpense())
                    .costs(details.get(0).getCosts())
                    .ruleOrigin(StrUtil.join(",",ruleOriginSet))
                    .build());
            YNDetailsList.add(BaseMedicalDetail.builder()
                    .detailNo(detailNo)
                    .no(details.get(0).getNo())
                    .ruleType(YNmaxRuleType)
                    .ruleTypeName(StrUtil.join(",",YNruleTypeNameSet))
                    .itemId(details.get(0).getItemId())
                    .itemName(details.get(0).getItemName())
                    .itemDate(details.get(0).getItemDate())
                    .routeAdministration(details.get(0).getRouteAdministration())
                    .outpatientMedication(details.get(0).getOutpatientMedication())
                    .selfExpense(details.get(0).getSelfExpense())
                    .costs(details.get(0).getCosts())
                    .ruleOrigin(GloablData.LOCAL_DICT.LOCAL_RULE_ORIGIN.dicts.get("1"))
                    .build());
            YBDtailsList.add(BaseMedicalDetail.builder()
                    .detailNo(detailNo)
                    .no(details.get(0).getNo())
                    .ruleType(YBmaxRuleType)
                    .ruleTypeName(StrUtil.join(",",YBruleTypeNameSet))
                    .itemId(details.get(0).getItemId())
                    .itemName(details.get(0).getItemName())
                    .itemDate(details.get(0).getItemDate())
                    .routeAdministration(details.get(0).getRouteAdministration())
                    .outpatientMedication(details.get(0).getOutpatientMedication())
                    .selfExpense(details.get(0).getSelfExpense())
                    .costs(details.get(0).getCosts())
                    .ruleOrigin((GloablData.LOCAL_DICT.LOCAL_RULE_ORIGIN.dicts.get("2")))
                    .build());
        }
        result.put("ALLDetailsList", DetailsList);
        result.put("YNDetailsList", YNDetailsList);
        result.put("YBDtailsList", YBDtailsList);
        return result;

    }

    public List<Map> queryMedicalAuditRules(String detailNo, String admissionNo, String batch, String auditScenario,String ruleOrigin, String hospitalId) {
        List<Map> result = new ArrayList<>();
        String tableName = "";
        if(StringUtils.isNotBlank(auditScenario)){
            if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
                throw new BaseException("不存在的审核场景！");
            }
            tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        }else{
            tableName = MrCase.BASE_TABLE;
        }

        log.info("场景标识 auditScenario："+auditScenario+"; 获取相关明细读取表名："+ tableName);
        //查询明细下违规
        List<BaseAudit> baseAuditList  = new ArrayList<>();
        if (tableName.equals(MrCase.BASE_TABLE)){
            baseAuditList = this.baseMedicalMapper.queryMedicalAuditRules(detailNo, tableName, admissionNo, batch, ruleOrigin, hospitalId);
        }else{
            baseAuditList = this.baseMedicalMapper.queryMedicalAuditRulesHis(detailNo, tableName, admissionNo, batch, ruleOrigin, hospitalId);

        }
        DictUtils.translateDict(baseAuditList);
        List<String> rules = baseAuditList.stream().map(BaseAudit::getRuleCode).collect(Collectors.toList());
        List<BaseMedicalDetail> medicalDetails = new ArrayList<>();
        List<String> blocks = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(rules)) {
            //查询阻断规则
            blocks = sysRulesMapper.selectBlockRules("1", rules, hospitalId);
            //查询规则下明细
            if (tableName.equals(MrCase.BASE_TABLE)){
                medicalDetails = this.baseMedicalMapper.queryMedicalDetailCaseByRule(tableName, batch, hospitalId, rules);
            }else{
                medicalDetails = this.baseMedicalMapper.queryMedicalDetailCaseHisByRule(tableName, batch, hospitalId, rules);
            }
            DictUtils.translateDict(medicalDetails);
        }

        for (BaseAudit baseAudit : baseAuditList) {
            List<String> abouts = new ArrayList<>();
            abouts.add(detailNo);
            if(baseAudit.getRelated() != null){
                abouts.addAll(Arrays.asList(baseAudit.getRelated().split(",")));
            }
            List<BaseMedicalDetail> collect = medicalDetails.stream().filter(x -> {
                        if(admissionNo.equals(x.getAdmissionNo())){
                            x.setAdmissionNo("否");
                        }
                        return x.getRuleCodes().equals(baseAudit.getRuleCode()) && abouts.contains(x.getDetailNo());
                    }
            ).collect(Collectors.toList());
            baseAudit.setIsBlock(blocks.contains(baseAudit.getRuleCode()) ? "1" : "0");
            Map<String, Object> resultDetail = new HashMap<>();
            resultDetail.put("rule", baseAudit);
            resultDetail.put("details", collect);
            result.add(resultDetail);
        }

        return result;
    }


    public List<Map> queryMedicalAuditRules4(String detailNo, String admissionNo, String isHis, String batch, String auditScenario,String ruleOrigin, String hospitalId) {
        List<Map> result = new ArrayList<>();
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        if(StringUtils.isBlank(isHis)){
            tableName = MrCase.BASE_TABLE;
        }
        log.info("场景标识 auditScenario："+auditScenario+"；isHis："+ isHis+"; 获取相关明细读取表名："+ tableName);
        //查询明细下违规
        List<BaseAudit> baseAuditList = new ArrayList<>();
        if(  StringUtils.equals("1",isHis)){
            baseAuditList = this.baseMedicalMapper.queryMedicalAuditRulesHis(detailNo, tableName, admissionNo, batch, ruleOrigin, hospitalId);
        }else{
            baseAuditList = this.baseMedicalMapper.queryMedicalAuditRules(detailNo, tableName, admissionNo, batch, ruleOrigin, hospitalId);
        }

        DictUtils.translateDict(baseAuditList);
        List<String> rules = baseAuditList.stream().map(BaseAudit::getRuleCode).collect(Collectors.toList());
        List<BaseMedicalDetail> medicalDetails = new ArrayList<>();
        List<String> blocks = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(rules)) {
            //查询阻断规则
            blocks = sysRulesMapper.selectBlockRules("1", rules, hospitalId);
            //查询规则下明细
            medicalDetails =  StringUtils.equals("1",isHis)
                    ?
                    this.baseMedicalMapper.queryMedicalDetailCaseHisByRule(tableName, batch, hospitalId, rules)
                    :
                    this.baseMedicalMapper.queryMedicalDetailCaseByRule(tableName, batch, hospitalId, rules);
            DictUtils.translateDict(medicalDetails);
        }

        for (BaseAudit baseAudit : baseAuditList) {
            List<String> abouts = new ArrayList<>();
            abouts.add(detailNo);
            if(baseAudit.getRelated() != null){
                abouts.addAll(Arrays.asList(baseAudit.getRelated().split(",")));
            }
            List<BaseMedicalDetail> collect = medicalDetails.stream().filter(x -> {
                        if(admissionNo.equals(x.getAdmissionNo())){
                            x.setAdmissionNo("否");
                        }
                        return x.getRuleCodes().equals(baseAudit.getRuleCode()) && abouts.contains(x.getDetailNo());
                    }
            ).collect(Collectors.toList());
            baseAudit.setIsBlock(blocks.contains(baseAudit.getRuleCode()) ? "1" : "0");
            Map<String, Object> resultDetail = new HashMap<>();
            resultDetail.put("rule", baseAudit);
            resultDetail.put("details", collect);
            result.add(resultDetail);
        }

        return result;
    }

    /**
     * 强制保存
     *
     * @param baseAuditResonDto
     */

    public MqsResult saveClaimAuditHospital(BaseAuditResonDto baseAuditResonDto) {
        String hospitalId = baseAuditResonDto.getHospital_id();
        String batchNo = baseAuditResonDto.getBatch_no();
        String auditScenario = baseAuditResonDto.getAudit_scenario();
        String admissionNo = baseAuditResonDto.getAdmission_no();
        String is_increment = baseAuditResonDto.getIs_increment();
        List<SaveResonVo> saveResonVos = baseAuditResonDto.getData();

        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        if (CollectionUtil.isEmpty(saveResonVos)) {
            return MqsResult.OK(BaseAuditResultDto.builder().result_code("200100").msg("无有效数据，保存成功").build());
        }
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();

        List<BaseAudit> baseAudits = baseAuditMapper.queryAuditHisByBatchNo(tableName, null, batchNo, hospitalId);

        List<String> rules = baseAudits.stream().filter(x -> admissionNo.equals(x.getAdmissionNo())).map(BaseAudit::getRuleCode).collect(Collectors.toList());
        List<String> isReminds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(rules)) {
            //查询是否有阻断级别的违规
            List<String> isBlocks = sysRulesMapper.selectBlockRules("1", rules, hospitalId);
            if (CollectionUtil.isNotEmpty(isBlocks)) {
                return MqsResult.ERROR("该单据存在阻断违规项!.请返回修改后重新审核！");
            }
            //查询需要填写违规原因的规则(需说明和阻断的)
            isReminds = sysRulesMapper.selectBlockRules("2", rules, hospitalId);
        }

        //查询审核记录数据
        List<BaseMedicalDetail> medicalDetails = baseMedicalMapper.queryDetailHisByBatchNo(tableName, null,null, batchNo, hospitalId);
        List<BaseAuditReason> baseAuditReasons = new ArrayList<>();
        //拼接违规信息
        for (SaveResonVo saveResonVo : saveResonVos) {
            baseAuditReasons.add(BaseAuditReason.builder().itemId(saveResonVo.getItem_id()).no(saveResonVo.getNo()).selfExpense(saveResonVo.getSelf_expense()).reasonType(saveResonVo.getReason_type()).reasonDes(saveResonVo.getReason_des()).detailNo(saveResonVo.getDetail_no()).batchNo(batchNo).admissionNo(admissionNo).hospitalId(hospitalId).build());
        }
        //查询非本次的历史违规原因 保证历史违规原因明细保留
        List<BaseAuditReason> reasonHisList = baseMedicalMapper.queryReasonByDetailNo(tableName, admissionNo, hospitalId);
        if (CollectionUtil.isNotEmpty(reasonHisList)) {
            Set<String> existingDetailNos = baseAuditReasons.stream()
                    .map(BaseAuditReason::getDetailNo)
                    .collect(Collectors.toSet());
            for (BaseAuditReason reason : reasonHisList) {
                if (!existingDetailNos.contains(reason.getDetailNo())) {
                    baseAuditReasons.add(reason);
                    existingDetailNos.add(reason.getDetailNo());
                }
            }
        }

        String msg = "";
        for (BaseMedicalDetail medicalDetail : medicalDetails) {
            Optional<BaseAuditReason> first = baseAuditReasons.stream().filter(saveResonVo -> medicalDetail.getDetailNo().equals(saveResonVo.getDetailNo()) && medicalDetail.getNo().equals(saveResonVo.getNo())).findFirst();
            if (first.isPresent()) {
                BaseAuditReason saveResonVo = first.get();
                medicalDetail.setSelfExpense(StrUtil.isNotEmpty(saveResonVo.getSelfExpense()) ? saveResonVo.getSelfExpense() : medicalDetail.getSelfExpense());
                medicalDetail.setReasonDess(saveResonVo.getReasonDes());
                medicalDetail.setReasonTypes(saveResonVo.getReasonType());
            }
            //存在违规 && 是逻辑当此明细 && 是当次操作明细
            if (CollectionUtil.isNotEmpty(isReminds) && "1".equals(medicalDetail.getIsCurrent()) && "1".equals(medicalDetail.getOperationType())) {
                List<String> collect = baseAudits.stream().filter(x -> x.getDetailNo().equals(medicalDetail.getDetailNo())).map(BaseAudit::getRuleCode).collect(Collectors.toList());
                for (String s : collect) {
                    // 本次明细存在阻断级别的违规,未填写违规原因
                    if (isReminds.contains(s) &&( StrUtil.isBlank(medicalDetail.getReasonTypes()) )) {
                        msg += "【" + medicalDetail.getItemName() + "】";
                        break;
                    }
                }
            }
        }
        if (!msg.equals("")) {
            return MqsResult.ERROR("以下项目违规，请维护违规原因！\r\n" + msg);
        }

        List<BaseMedicalCase> baseMedicalCases = baseMedicalMapper.queryCaseHisByBatchNo(tableName, null,null, batchNo, hospitalId);
        if (CollectionUtil.isEmpty(baseMedicalCases)) {
            return MqsResult.ERROR("单据已失效！");
        }
        for (BaseMedicalCase baseMedicalCase : baseMedicalCases) {
            if(StrUtil.isEmpty(baseMedicalCase.getIsIncrement())){
                baseMedicalCase.setIsIncrement(is_increment);
            }
        }
        //查询医嘱明细
        List<BaseMedicalOrders> medicalOrders = baseMedicalOrdersMapper.queryOrdersHisByBatchNo(tableName, admissionNo, batchNo, hospitalId);
        List<HisOperationMainVo> hisOperationMainVos = hpOperationMapper.queryOperationListHis(admissionNo, null, batchNo, hospitalId);
        List<HisOperationDetailVo> hisOperationDetailVos = hpOperationMapper.queryOperationDetailListHis(admissionNo, null, batchNo, hospitalId);
        List<BaseMedicalDiag> medicalDiagList = hpDiagMapper.queryDiagListHis(admissionNo, null, batchNo, hospitalId);

        //获取场景实现工厂
        BaseAuditService auditService = BaseAuditFactory.createAuditService(auditScenario);
        //单据保存至引擎
        BaseAuditResultDto baseAuditResultDto = auditService.auditSave(auditScenario, admissionNo, hospitalId, baseMedicalCases, medicalDetails, hisOperationMainVos, hisOperationDetailVos,medicalDiagList);
        //异步反馈医保端
        baseMedicalService.BmiCallBack(auditScenario, baseAudits, baseAuditReasons, hospitalId,"1");
        //异步保存数据至正式表
        if("200100".equals(baseAuditResultDto.getResult_code())){
            //异步保存数据至正式表
            baseMedicalService.saveToCase(auditScenario, hospitalId, baseMedicalCases, medicalDetails, medicalOrders, baseAudits, baseAuditReasons, hisOperationMainVos, hisOperationDetailVos,medicalDiagList,null);
        }

        return MqsResult.OK(baseAuditResultDto);
    }

    /**
     * 反馈值医保端
     * @param baseAudits
     * @param baseAuditReasons
     */
    @Async
    public void BmiCallBack(String auditScenario,List<BaseAudit> baseAudits, List<BaseAuditReason> baseAuditReasons,String hospitalId,String dspo_way){

        //当前场景配置获取
        SysSceneFunction sysSceneFunction = SpringUtil.getBean(SysSceneService.class).getSceneFunctionCache(auditScenario, hospitalId);
        BmiSaveResonVo bmiSaveResonVo = new BmiSaveResonVo();
        if("2".equals(sysSceneFunction.getMiReviewApi())){
            return;
        }
        List<BmiSaveWarnVo> bmiSaveWarnVos = new ArrayList<>();
        bmiSaveResonVo.setWarn_type("0".equals(sysSceneFunction.getMiReviewApi())? "1":"2");
        bmiSaveResonVo.setWarns(bmiSaveWarnVos);
        //根据医保反馈唯一id分组
        Map<String, List<BaseAudit>> collect = baseAudits.stream().filter(x -> "2".equals(x.getRuleOrigin())).collect(Collectors.groupingBy(BaseAudit::getJrId));
        for (Map.Entry<String, List<BaseAudit>> e : collect.entrySet()) {
            String jrId = e.getKey();
            List<BaseAudit> baseAuditList = e.getValue();
            Set<String> wayReasSet = new HashSet<>();
            for (BaseAudit baseAudit : baseAuditList) {
                for (BaseAuditReason baseAuditReason : baseAuditReasons) {
                    if(baseAudit.getDetailNo().equals(baseAuditReason.getDetailNo())){
                        if(StrUtil.isNotBlank(baseAuditReason.getReasonDes())){
                            wayReasSet.add(baseAuditReason.getReasonDes());
                        }
                    }
                }
            }
            String dspo_way_rea = StrUtil.join(",", wayReasSet);
            bmiSaveWarnVos.add(BmiSaveWarnVo.builder()
                    .warn_rslt_id(jrId)
                    .dspo_way(dspo_way)
                    .dspo_way_rea(StrUtil.sub(dspo_way_rea,0,200))
                    .build());
        }
        try {
            cdmpServiceInterface.sendToBmiCallBack(bmiSaveResonVo);
        }catch (Exception e){
            e.printStackTrace();
            log.error("3103医保平台反馈失败！"+ e.getMessage());
        }
    }

    /**
     * 保存入正式表
     *      (全局redis锁 admissionNo级别，查询此类数据注意异步线程)
     * @param auditScenario             场景
     * @param hospitalId            医院id
     * @param baseMedicalCases      处方集
     * @param medicalDetails        处方下费用集
     * @param baseAudits            审核结果集
     * @param baseAuditReasons      反馈原因集
     * @param operation_list        手术记录
     * @param operation_detail_list 手术明细记录
     * @param sceneCodes  场景集合
     */
    @Async
    @Transactional
    public void saveToCase(String auditScenario, String hospitalId,
                           List<BaseMedicalCase> baseMedicalCases,
                           List<BaseMedicalDetail> medicalDetails,
                           List<BaseMedicalOrders> medicalOrders,
                           List<BaseAudit> baseAudits,
                           List<BaseAuditReason> baseAuditReasons,
                           List<HisOperationMainVo> operation_list,
                           List<HisOperationDetailVo> operation_detail_list,
                           List<BaseMedicalDiag> medicalDiagList,
                           List<String> sceneCodes) {

        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();

        //处方增量标识为true,不删除旧的费用明细，只覆盖重复的费用
        List<String> allNo = baseMedicalCases.stream().map(BaseMedicalCase::getNo).collect(Collectors.toList());

        // 防止 高并发下GAP锁 导致死锁问题 ，当前事务内 查询已存在的单据号，删除操作仅适用于实际存在的单据
        List<String> sAllNo = baseMedicalMapper.selectSurviveCase(tableName, allNo);
        List<String> incrementNo = baseMedicalCases.stream().filter(x -> "true".equals(x.getIsIncrement())).map(BaseMedicalCase::getNo).collect(Collectors.toList());
        List<String> unIncrementNo = baseMedicalCases.stream().filter(x -> !"true".equals(x.getIsIncrement())).map(BaseMedicalCase::getNo).collect(Collectors.toList());
        //增量场景下，处理增量的费用明细 （删除同detailNo数据，插入未打删除标记的数据）
        List<String> incrementDeatilNo = medicalDetails.stream().filter(x -> incrementNo.contains(x.getNo())).map(BaseMedicalDetail::getDetailNo).collect(Collectors.toList());
        List<String> incrementOperationId = operation_list.stream().filter(x -> incrementNo.contains(x.getNo())).map(HisOperationMainVo::getOperation_id).collect(Collectors.toList());
        List<String> incrementOrdersNo = medicalOrders.stream().filter(x -> incrementNo.contains(x.getNo())).map(BaseMedicalOrders::getOrderDetailNo).collect(Collectors.toList());
        List<String> incrementDiagNo = medicalDiagList.stream().filter(x -> incrementNo.contains(x.getNo())).map(BaseMedicalDiag::getDiagnosisNo).collect(Collectors.toList());

        //删除已存在正式表数据
        if(CollectionUtil.isNotEmpty(sAllNo)){
            baseMedicalMapper.deleteCaseByAdNo(tableName, sAllNo, null, hospitalId);
        }

        //不是增量的单据下明细全部删除
        if (CollectionUtil.isNotEmpty(unIncrementNo) && CollectionUtil.isNotEmpty(sAllNo)) {
            //取交集
            List<String> delnos = (List<String>) CollectionUtil.intersection(sAllNo, unIncrementNo);
            if(CollectionUtil.isNotEmpty(delnos)){
                baseMedicalMapper.deleteDetailByAdNo(tableName, null, delnos, null, hospitalId);
                baseMedicalOrdersMapper.deleteOrdersByAdNo(tableName, null, delnos, null, hospitalId);
                baseAuditMapper.deleteAuditByAdNo(tableName, null, delnos, null, hospitalId);
                baseAuditMapper.deleteReasonByAdNo(tableName, null, delnos, null, hospitalId);
                hpOperationMapper.deleteOperationByAdNo(null, delnos, null, hospitalId);
                hpOperationMapper.deleteOperationListByAdNo(null, delnos, null, hospitalId);
                hpDiagMapper.deleteDiagByAdNo(null, delnos, null, hospitalId);
            }
        }
        //增量的单据下明细只删除重复的
        if (CollectionUtil.isNotEmpty(incrementDeatilNo)) {
            //单独删除主单违规记录
            baseAuditMapper.deleteAuditByAdNo(tableName, Arrays.asList("0"), incrementNo, null, hospitalId);
            baseAuditMapper.deleteReasonByAdNo(tableName,  Arrays.asList("0"), incrementNo, null, hospitalId);
            for (List<String> detailNos : CollectionUtil.split(incrementDeatilNo, MQS_BATCH_UPDATE_SIZE)) {
                baseMedicalMapper.deleteDetailByAdNo(tableName, detailNos, incrementNo, null, hospitalId);
                baseAuditMapper.deleteAuditByAdNo(tableName, detailNos, incrementNo, null, hospitalId);
                baseAuditMapper.deleteReasonByAdNo(tableName, detailNos, incrementNo, null, hospitalId);
            }
            for (List<String> operationIds : CollectionUtil.split(incrementOperationId, MQS_BATCH_UPDATE_SIZE)) {
                hpOperationMapper.deleteOperationByAdNo(operationIds, incrementNo, null, hospitalId);
                hpOperationMapper.deleteOperationListByAdNo(operationIds, incrementNo, null, hospitalId);
            }
        }
        if(CollectionUtil.isNotEmpty(incrementOrdersNo)){
            for (List<String> detailNos : CollectionUtil.split(incrementOrdersNo, MQS_BATCH_UPDATE_SIZE)) {
                baseMedicalOrdersMapper.deleteOrdersByAdNo(tableName, detailNos, incrementNo, null, hospitalId);
            }
        }
        if(CollectionUtil.isNotEmpty(incrementDiagNo)){
            for (List<String> diagNos : CollectionUtil.split(incrementDiagNo, MQS_BATCH_UPDATE_SIZE)) {
                hpDiagMapper.deleteDiagByAdNo(diagNos, incrementNo, null, hospitalId);
            }
        }

        //保存入表
        if (CollectionUtil.isNotEmpty(baseMedicalCases)) {
            for (List<BaseMedicalCase> caseList : CollectionUtil.split(baseMedicalCases, MQS_BATCH_INSERT_SIZE)) {
                baseMedicalMapper.batchInsertCase(tableName, caseList);
            }
        }
        //排除打删除标记的数据
        List<BaseMedicalDetail> inserts = medicalDetails.stream().filter(x -> !"1".equals(x.getReverseFlag())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(inserts)) {
            for (List<BaseMedicalDetail> detailList : CollectionUtil.split(inserts, MQS_BATCH_INSERT_SIZE)) {
                baseMedicalMapper.batchInsertDetail(tableName, detailList);
            }
        }
        if (CollectionUtil.isNotEmpty(medicalOrders)) {
            for (List<BaseMedicalOrders> ordersList : CollectionUtil.split(medicalOrders, MQS_BATCH_INSERT_SIZE)) {
                baseMedicalOrdersMapper.batchInsertOrders(tableName, ordersList);
            }
        }
        if (CollectionUtil.isNotEmpty(baseAudits)) {
            for (List<BaseAudit> baseAuditList : CollectionUtil.split(baseAudits, MQS_BATCH_INSERT_SIZE)) {
                baseAuditMapper.batchInsertAudit(tableName, baseAuditList);
            }
        }
        if (CollectionUtil.isNotEmpty(baseAuditReasons)) {
            for (List<BaseAuditReason> auditReasons : CollectionUtil.split(baseAuditReasons, MQS_BATCH_INSERT_SIZE)) {
                baseAuditMapper.batchInsertAuditReason(tableName, auditReasons);
            }
        }
        if (CollectionUtil.isNotEmpty(operation_list)) {
            for (List<HisOperationMainVo> hisOperationMainVos : CollectionUtil.split(operation_list, MQS_BATCH_INSERT_SIZE)) {
                hpOperationMapper.batchSaveOperations(hisOperationMainVos);
            }
        }
        if (CollectionUtil.isNotEmpty(operation_detail_list)) {
            for (List<HisOperationDetailVo> hisOperationDetailVos : CollectionUtil.split(operation_detail_list, MQS_BATCH_INSERT_SIZE)) {
                hpOperationMapper.batchSaveList(hisOperationDetailVos);
            }
        }
        if(CollectionUtil.isNotEmpty(medicalDiagList)){
            for (List<BaseMedicalDiag> baseMedicalDiags : CollectionUtil.split(medicalDiagList, MQS_BATCH_INSERT_SIZE)) {
                hpDiagMapper.batchSaveDiag(baseMedicalDiags);
            }
        }

        /**
         * 质控数据数据 - 载入
         */
        //质控单据集
        List<String> mrNos = baseMedicalCases.stream().filter(x -> !"0".equals(x.getMrFlag())).map(BaseMedicalCase::getNo).collect(Collectors.toList());

        List<String> mrincrementNo = baseMedicalCases.stream().filter(x -> !"0".equals(x.getMrFlag()) && "true".equals(x.getIsIncrement())).map(BaseMedicalCase::getNo).collect(Collectors.toList());
        List<String> mrunIncrementNo = baseMedicalCases.stream().filter(x -> !"0".equals(x.getMrFlag()) && !"true".equals(x.getIsIncrement())).map(BaseMedicalCase::getNo).collect(Collectors.toList());
        List<String> mrincrementDeatilNo = medicalDetails.stream().filter(x -> mrincrementNo.contains(x.getNo())).map(BaseMedicalDetail::getDetailNo).collect(Collectors.toList());
        List<String> mrincrementOrdersNo = medicalOrders.stream().filter(x -> mrincrementNo.contains(x.getNo())).map(BaseMedicalOrders::getOrderDetailNo).collect(Collectors.toList());

        //删除正式表数据
        if(CollectionUtil.isNotEmpty(mrNos)){
            baseMedicalMapper.deleteCaseByAdNo(MrCase.BASE_TABLE, mrNos, null, hospitalId);
        }
        //不是增量的单据下明细全部删除
        if (CollectionUtil.isNotEmpty(mrunIncrementNo)) {
            baseMedicalMapper.deleteDetailByAdNo(MrCase.BASE_TABLE, null, mrunIncrementNo, null, hospitalId);
            baseMedicalOrdersMapper.deleteOrdersByAdNo(MrCase.BASE_TABLE, null, mrunIncrementNo, null, hospitalId);
            baseAuditMapper.deleteAuditByAdNo(MrCase.BASE_TABLE, null, mrunIncrementNo, null, hospitalId);
            baseAuditMapper.deleteReasonByAdNo(MrCase.BASE_TABLE, null, mrunIncrementNo, null, hospitalId);
        }
        //增量的单据下明细只删除重复的
        if (CollectionUtil.isNotEmpty(mrincrementDeatilNo)) {
            baseAuditMapper.deleteAuditByAdNo(MrCase.BASE_TABLE, Arrays.asList("0"), mrincrementNo, null, hospitalId);
            baseAuditMapper.deleteReasonByAdNo(MrCase.BASE_TABLE,  Arrays.asList("0"), mrincrementNo, null, hospitalId);
            for (List<String> detailNos : CollectionUtil.split(mrincrementDeatilNo, MQS_BATCH_UPDATE_SIZE)) {
                baseMedicalMapper.deleteDetailByAdNo(MrCase.BASE_TABLE, detailNos, mrincrementNo, null, hospitalId);
                baseAuditMapper.deleteAuditByAdNo(MrCase.BASE_TABLE, detailNos, mrincrementNo, null, hospitalId);
                baseAuditMapper.deleteReasonByAdNo(MrCase.BASE_TABLE, detailNos, mrincrementNo, null, hospitalId);
            }
        }
        if(CollectionUtil.isNotEmpty(mrincrementOrdersNo)){
            for (List<String> detailNos : CollectionUtil.split(mrincrementOrdersNo, MQS_BATCH_UPDATE_SIZE)) {
                baseMedicalOrdersMapper.deleteOrdersByAdNo(MrCase.BASE_TABLE, detailNos, mrincrementNo, null, hospitalId);
            }
        }

        if (CollectionUtil.isNotEmpty(baseMedicalCases)) {
            baseMedicalCases = baseMedicalCases.stream().filter( x -> mrNos.contains(x.getNo())).collect(Collectors.toList());
            for (List<BaseMedicalCase> caseList : CollectionUtil.split(baseMedicalCases, MQS_BATCH_INSERT_SIZE)) {
                //质控单据新增
                mrCaseService.insertMrCase(auditScenario, caseList, hospitalId);
                //主单表插入
                baseMedicalMapper.batchInsertCase(MrCase.BASE_TABLE, caseList);
            }
        }
        //质控明细新增
        if (CollectionUtil.isNotEmpty(medicalDetails)) {
            //需审核且没有删除标记的明细入表
            List <BaseMedicalDetail> mrinserts = medicalDetails.stream().filter( x -> mrNos.contains(x.getNo()) && !"1".equals(x.getReverseFlag())).collect(Collectors.toList());
            for (List<BaseMedicalDetail> detailList : CollectionUtil.split(mrinserts, MQS_BATCH_INSERT_SIZE)) {
                baseMedicalMapper.batchInsertDetail(MrCase.BASE_TABLE, detailList);
            }
        }
        //质控医嘱明细
        if (CollectionUtil.isNotEmpty(medicalOrders)) {
            medicalOrders = medicalOrders.stream().filter( x -> mrNos.contains(x.getNo())).collect(Collectors.toList());
            for (List<BaseMedicalOrders> ordersList : CollectionUtil.split(medicalOrders, MQS_BATCH_INSERT_SIZE)) {
                baseMedicalOrdersMapper.batchInsertOrders(MrCase.BASE_TABLE, ordersList);
            }
        }
        //违规单据
        if (CollectionUtil.isNotEmpty(baseAudits)) {
            baseAudits = baseAudits.stream().filter( x -> mrNos.contains(x.getNo())).collect(Collectors.toList());
            for (List<BaseAudit> baseAuditList : CollectionUtil.split(baseAudits, MQS_BATCH_INSERT_SIZE)) {
                baseAuditMapper.batchInsertAudit(MrCase.BASE_TABLE, baseAuditList);
            }
        }
        if (CollectionUtil.isNotEmpty(baseAuditReasons)) {
            baseAuditReasons = baseAuditReasons.stream().filter( x -> mrNos.contains(x.getNo())).collect(Collectors.toList());
            for (List<BaseAuditReason> auditReasons : CollectionUtil.split(baseAuditReasons, MQS_BATCH_INSERT_SIZE)) {
                baseAuditMapper.batchInsertAuditReason(MrCase.BASE_TABLE, auditReasons);
            }
        }
        //批量更新临床处理状态
        mrCaseService.batchUpdateCaseClinicalStatus (auditScenario,baseMedicalCases,hospitalId,sceneCodes);
    }

    public List<BaseMedicalCase> getHospitalSaveStatus(String admissionNo, String batch, String auditScenario, String hospitalId) {

        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();

        return baseAuditMapper.querySavedCase(tableName, admissionNo, batch, hospitalId);
    }

    @Transactional
    public void delClaimAuditHospital(String patientId, List<String> detailNos, String no, String admissionNo, String auditScenario, String hospitalId) {

        List<String> tableList = new ArrayList<>();
        if(StrUtil.isEmpty(auditScenario)){
            for (AuditScenarioEnum value : AuditScenarioEnum.values()) {
                tableList.add(value.getTableName());
            }
        }else{
            if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
                throw new BaseException("不存在的审核场景！");
            }
            tableList.add(AuditScenarioEnum.valueOf(auditScenario).getTableName());
        }

        ThreadPoolTaskExecutor threadPoolTaskExecutor = ApplicationContextUtil.getBean(ThreadPoolTaskExecutor.class);

        if(CollectionUtil.isNotEmpty(detailNos)){
            detailNos.forEach(detailNo -> {
                threadPoolTaskExecutor.submit(() -> {
                    Map resultMap = engineService.deleteRecord(patientId, no, detailNo, hospitalId);
                    if (!(boolean) resultMap.get("is_success")) {
                        log.info("删除动作 引擎返回异常：" + JSONUtil.toJsonStr(resultMap));
                    }
                });
            });
        }else {
            threadPoolTaskExecutor.submit(() -> {
                Map resultMap = engineService.deleteRecord(patientId, no, null, hospitalId);
                if (!(boolean) resultMap.get("is_success")) {
                    log.info("删除动作 引擎返回异常：" + JSONUtil.toJsonStr(resultMap));
                }
            });
        }
        //删除场景下数据
        for (String tableName : tableList) {
            //删除
            if (CollectionUtil.isEmpty(detailNos)) {
                baseMedicalMapper.deleteCaseByAdNo(tableName, Arrays.asList(no), admissionNo, hospitalId);
            }
            baseMedicalMapper.deleteDetailByAdNo(tableName, detailNos, Arrays.asList(no), admissionNo, hospitalId);
            baseAuditMapper.delAudit(tableName, detailNos, no, admissionNo, hospitalId);
            baseAuditMapper.delAuditReason(tableName, detailNos, no, admissionNo, hospitalId);
        }
    }

    public void updateClaimDischargeFlag(String no, String admissionNo, String isDischarge, String auditScenario, String hospitalId) {
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        baseMedicalMapper.updateClaimDischargeFlag(tableName, no, admissionNo, isDischarge, hospitalId);
    }

    public void updateClaimChargingFlag(String detailNo, String no, String admissionNo, String chargingFlag, String auditScenario, String hospitalId) {
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        baseMedicalMapper.updateClaimChargingFlag(tableName, detailNo, no, admissionNo, chargingFlag, hospitalId);
    }

    public List<Map> getHospitalSelfExpense(String detailNo, String no, String admissionNo, String auditScenario, String hospitalId) {

        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();

        return baseMedicalMapper.getHospitalSelfExpense(tableName, detailNo, no, admissionNo, hospitalId);
    }

    public Map getClaimRes(String no, String admissionNo, String auditScenario, String hospitalId) {
        Map result = new HashMap();

        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();

        List<Map> claimRes = baseAuditMapper.getClaimRes(tableName, no, admissionNo, hospitalId);
        if (CollectionUtil.isEmpty(claimRes)) {
            result.put("result_code", "200100");
            result.put("msg", "无违规记录");
        } else {
            result.put("result_code", "200200");
            result.put("violation_result", claimRes);
            result.put("msg", "单据违规");
        }
        return result;
    }

    /**
     * 根据已有单据组装审核全部对象
     *
     * @param admissionNo
     * @param auditScenario
     * @param hospitalId
     * @return
     */
    public List<BaseMedicalParam> genExitAuditDto(String batch, String admissionNo, String no, String auditScenario, String hospitalId, String oldBatchNo) {
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();

        List<BaseMedicalParam> baseMedicalParamList = new ArrayList<>();
        List<BaseMedicalCase> caseList ;
        List<BaseMedicalDetail> fees ;
        List<HisOperationMainVo> operation_list ;
        List<HisOperationDetailVo> operation_detail_list ;
        List<BaseMedicalDiag> medicalDiagList ;
        if(StrUtil.isEmpty(oldBatchNo)){
            //无批次号 查询已保存单据作为重审标准数据
            //查询已有单据
            caseList = this.baseMedicalMapper.queryAuditCase(tableName, admissionNo, StrUtil.isNotEmpty(no) ? Arrays.asList(no) : null, hospitalId);
            //费用记录
            fees = this.baseMedicalMapper.queryAuditDetail(tableName, admissionNo, StrUtil.isNotEmpty(no) ? Arrays.asList(no) : null, null, hospitalId);
            //手术记录表
            operation_list = this.hpOperationMapper.queryOperationList(admissionNo, no, hospitalId);
            operation_detail_list = this.hpOperationMapper.queryOperationDetailList(admissionNo, no, hospitalId);
            medicalDiagList = this.hpDiagMapper.queryDiagList(admissionNo, no, hospitalId);

        }else {
            //有批次号 查询历史批次单据作为重审标准数据
            //查询已有单据
            caseList = this.baseMedicalMapper.queryCaseHisByBatchNo(tableName, admissionNo, no, oldBatchNo, hospitalId);
            //费用记录
            fees = this.baseMedicalMapper.queryDetailHisByBatchNo(tableName, admissionNo, no, oldBatchNo, hospitalId);
            //手术记录表
            operation_list = this.hpOperationMapper.queryOperationListHis(admissionNo, no, oldBatchNo, hospitalId);
            operation_detail_list = this.hpOperationMapper.queryOperationDetailListHis(admissionNo, no, oldBatchNo,hospitalId);
            medicalDiagList = this.hpDiagMapper.queryDiagListHis(admissionNo, no, oldBatchNo, hospitalId);
        }

        if (CollectionUtil.isEmpty(caseList)) {
            throw new BaseException(admissionNo + "单据不存在！");
        }

        Map<String, List<BaseMedicalDetail>> feeMap = fees.stream().collect(Collectors.groupingBy(x -> x.getNo()));
        Map<String, List<HisOperationMainVo>> operationMap = operation_list.stream().collect(Collectors.groupingBy(x -> x.getNo()));
        Map<String, List<HisOperationDetailVo>> operationDetailMap = operation_detail_list.stream().collect(Collectors.groupingBy(x -> x.getOperation_id()));
        Map<String, List<BaseMedicalDiag>> diagMap = medicalDiagList.stream().collect(Collectors.groupingBy(x -> x.getNo()));
        for (HisOperationMainVo hisOperationMainVo : operation_list) {
            hisOperationMainVo.setBatch_no(batch);
            hisOperationMainVo.setOperation_detail_list(operationDetailMap.get(hisOperationMainVo.getOperation_id()));
        }
        for (BaseMedicalDetail fee : fees) {
            fee.setBatchNo(batch);
        }
        for (HisOperationDetailVo hisOperationDetailVo : operation_detail_list) {
            hisOperationDetailVo.setBatch_no(batch);
        }
        for (BaseMedicalDiag baseMedicalDiag : medicalDiagList) {
            baseMedicalDiag.setBatchNo(batch);
        }
        for (BaseMedicalCase medicalCase : caseList) {
            BaseMedicalParam baseMedicalParam = new BaseMedicalParam();
            baseMedicalParamList.add(baseMedicalParam);
            String NO = medicalCase.getNo();
            medicalCase.setBatchNo(batch);
            baseMedicalParam.setMedical_case(medicalCase);
            baseMedicalParam.setIs_increment("true".equals(medicalCase.getIsIncrement()));
            baseMedicalParam.setFee_list(feeMap.get(NO));
            baseMedicalParam.setOperation_list(operationMap.get(NO));
            baseMedicalParam.setDiagnosis_list(diagMap.get(NO));
            List<UnpaidRecordsVo> unpaidRecords = new ArrayList<>();
            List<UnVisitVo> unVisitVos = new ArrayList<>();
            baseMedicalParam.setUnpaid_list(unpaidRecords);
            baseMedicalParam.setUnvisit_list(unVisitVos);
        }

        return baseMedicalParamList;
    }

    /**
     * 根据指定明细，获取增量的审核明细对象
     *
     * @param nos
     * @param detailNos
     * @param hospitalId
     * @return
     */
    public List<BaseMedicalParam> genExitAuditDto(String batch, Collection<String> nos, Collection<String> detailNos, String auditScenario, String hospitalId) {
        if (!MqsUtils.isScenarioExist(AuditScenarioEnum.values(), auditScenario)) {
            throw new BaseException("不存在的审核场景！");
        }
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();

        List<BaseMedicalParam> baseMedicalParamList = new ArrayList<>();
        List<BaseMedicalCase> caseList ;
        List<BaseMedicalDetail> fees ;
        //查询已有单据
        caseList = this.baseMedicalMapper.queryAuditCase(tableName, null, nos, hospitalId);
        //费用记录
        fees = this.baseMedicalMapper.queryAuditDetail(tableName, null,  nos, detailNos, hospitalId);

        if (CollectionUtil.isEmpty(caseList)) {
            log.warn( "===" + batch +  "=== genExitAuditDto 目标单据不存在！");
            return new ArrayList<>();
        }

        Map<String, List<BaseMedicalDetail>> feeMap = fees.stream().collect(Collectors.groupingBy(x -> x.getNo()));

        for (BaseMedicalDetail fee : fees) {
            fee.setBatchNo(batch);
            fee.setOperationType("0");
            fee.setIsCurrent("0");
            fee.setLastUpdatedBy(99L);
        }

        for (BaseMedicalCase medicalCase : caseList) {
            BaseMedicalParam baseMedicalParam = new BaseMedicalParam();
            baseMedicalParamList.add(baseMedicalParam);
            String NO = medicalCase.getNo();
            medicalCase.setBatchNo(batch);
            medicalCase.setLastUpdatedBy(99l);
            medicalCase.setIsIncrement("true");
            baseMedicalParam.setMedical_case(medicalCase);
            baseMedicalParam.setIs_increment(true);
            baseMedicalParam.setFee_list(feeMap.get(NO));
            baseMedicalParam.setOperation_list(new ArrayList<>());
            baseMedicalParam.setDiagnosis_list(new ArrayList<>());
            List<UnpaidRecordsVo> unpaidRecords = new ArrayList<>();
            List<UnVisitVo> unVisitVos = new ArrayList<>();
            baseMedicalParam.setUnpaid_list(unpaidRecords);
            baseMedicalParam.setUnvisit_list(unVisitVos);
        }

        return baseMedicalParamList;
    }

    //获取指定单据的审核结果

    private List<BaseAudit> genCrossAuditDto(String batch, Set<String> nolist, Set<String> details, String auditScenario, String hospitalId) {

        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();

        List<BaseAudit> baseAuditList = this.baseAuditMapper.selectCaseAudit(tableName, nolist, details, hospitalId);
        for (BaseAudit baseAudit : baseAuditList) {
            baseAudit.setBatchNo(batch);
            baseAudit.setLastUpdatedBy(99L);
        }
        return baseAuditList;

    }

    /**
     * 获取单据的统计次数
     * @param table
     * @param hospitalId
     * @param no
     * @return
     */
    int genAuditNums(String table,String hospitalId,String no){
        String redisKey = StrUtil.format(GloablData.GLOBAL_SYS_AUDITNUMS_KEY, hospitalId, table);
        Object num = redisTemplate.opsForHash().get(redisKey, no);
        if(ObjectUtil.isNull(num)){
            num = baseMedicalMapper.selectAuditNoNums(table,no,hospitalId);
        }
        int nums = ObjectUtil.isNull(num)? 0:(Integer) num;
        ++nums;
        redisTemplate.opsForHash().put(redisKey, no,nums);
        return nums;
    }

    /**
     * 记录返回修改金额
     * @param admissionNo
     * @param batch
     * @param auditScenario
     * @param hospitalId
     */
    @Async
    public void claimAuditBackHospital(String admissionNo, BigDecimal totalCosts, String batch, String personnelTypeId, String auditScenario, String hospitalId) {
        //医保返回修改三方调用
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();

        List<BaseAudit> baseAudits = baseAuditMapper.queryAuditHisByBatchNo(tableName, null, batch, hospitalId);
        //异步反馈医保端
        baseMedicalService.BmiCallBack(auditScenario, baseAudits, new ArrayList<>(), hospitalId,"2");

        this.baseAuditMapper.insertAuditReturnLog(admissionNo, totalCosts,personnelTypeId, auditScenario, hospitalId);
    }
    /**
     * 自费协议导出
     * @param response
     * @param no
     * @param auditScenario
     * @param hospitalId
     */
    public void exportSelfAgreement(HttpServletResponse response, String no, List<String> detailNos, String auditScenario, String batch, String hospitalId,String codePrefix) {
        boolean result = true;
        if(StringUtils.isNotBlank(codePrefix)){
            result = getFilterSceneResult(hospitalId,null,"1",null,"1","hp".equals(codePrefix) ? "hpTr" : "opTr",null,null);
        } else {
            result = getFilterSceneResult(hospitalId,null,"1",null,"1",auditScenario,null,null);
        }
        if(!result){
            throw new BaseException("当前场景未启用或未配置生成自费协议书，本次生成自费协议书操作失败，场景编码："+auditScenario);
        }

        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        String caseTable = tableName;
        String feeTable = tableName + "_detail";
        if(StrUtil.isNotEmpty(batch)){
            caseTable = caseTable+ "_his";
            feeTable = feeTable + "_his";
        }
        //查询自费协议导出的单据数据
        BaseMedicalCase baseMedicalCase = baseMedicalMapper.querySelfExportCase(caseTable, no, batch, hospitalId);
        DictUtils.translateDict(baseMedicalCase);
        List<BaseMedicalDetail> medicalDetails = baseMedicalMapper.querySelfExportCaseDetail(feeTable, no, detailNos,batch, hospitalId);
        String applyDept = baseMedicalMapper.querySelfExportApplyDept(feeTable, no, detailNos, batch, hospitalId);
        if(ObjectUtil.isNull(baseMedicalCase)){
            return;
        }
        String sysTitleConfig = sysConfigService.queryValueByKey(MQS_SELFAGREEMENT_TITLE, hospitalId);
        String sysTempConfig = sysConfigService.queryValueByKey(MQS_SELFAGREEMENT_TEMP, hospitalId);
        String fileName = "自费协议-" + baseMedicalCase.getPatientName() + "-" + dateFormat.format(new Date());
        // 替换文本数据构建
        HashMap<String, String> insertTextMap = new HashMap<>(16);
        insertTextMap.put("title", sysTitleConfig);
        insertTextMap.put("name", baseMedicalCase.getPatientName());
        insertTextMap.put("gender",baseMedicalCase.getPatientGender());

        if(StrUtil.isNotEmpty(baseMedicalCase.getPatientBirthday())){
            String patientBirthday = baseMedicalCase.getPatientBirthday();
            DateTime parse = DateUtil.parse(patientBirthday, "yyyy-MM-dd");
            insertTextMap.put("age",Integer.toString(MqsUtils.getAge(parse,baseMedicalCase.getAdmissionDate())));
        }
        insertTextMap.put("organization","");
        insertTextMap.put("hospital",baseMedicalCase.getHospitalId());
        insertTextMap.put("patientIdno",baseMedicalCase.getPatientIdno());
        insertTextMap.put("dept", applyDept);
        insertTextMap.put("anamnesis", baseMedicalCase.getMedicalRecordId());
        insertTextMap.put("diagnosis", baseMedicalCase.getSecondaryDiseaseZh());
        BigDecimal total = medicalDetails.stream().filter(x -> x.getCosts() != null).map(BaseMedicalDetail::getCosts).reduce(BigDecimal.ZERO, BigDecimal::add);
        insertTextMap.put("total", total.toPlainString());

        // 插入数据构建
        ArrayList<String[]> addList = new ArrayList<>();
        for (BaseMedicalDetail medicalDetail : medicalDetails) {
            addList.add(new String[]{medicalDetail.getItemName(),
                    medicalDetail.getPrice() == null ? null : medicalDetail.getPrice().stripTrailingZeros().toPlainString() ,
                    medicalDetail.getNumbers() == null ? null : medicalDetail.getNumbers().stripTrailingZeros().toPlainString(),
                    medicalDetail.getCosts() == null ? null : medicalDetail.getCosts().stripTrailingZeros().toPlainString()});
        }
        List<ExportWordService.TableTag> tableTagList = new ArrayList<>();
        tableTagList.add(new ExportWordService.TableTag(0, 4, 1, 16, addList));

        //记录日志信息
        List<ComprePopupDetailVO> comprePopupSaveVO = BeanUtil.copyToList(medicalDetails,ComprePopupDetailVO.class);
        sendLogs (comprePopupSaveVO,hospitalId,"opup","生成自费协议",baseMedicalCase.getPatientId(),baseMedicalCase.getPatientName(),"异常信息：{}，生成自费协议操作日记记录失败！！！");
        //导出数据内容
        exportWordService.exportSelfAgreement(insertTextMap, tableTagList, fileName, sysTempConfig, response, hospitalId);
    }
    /**
     * 查询流水号下最新的一次审批
     * @param admissionNo
     * @param hospitalId
     * @return
     */
    public BaseMedicalCase queryClinicalCase(String admissionNo, String hospitalId,String auditScenario) {
        BaseMedicalCase baseMedicalCase = null;
        List<BaseMedicalCase> medicalCase = new ArrayList<>();

        if(StrUtil.isNotEmpty(auditScenario)){
            //指定场景
            String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
            medicalCase = this.baseMedicalMapper.queryClinicalCase(tableName, admissionNo, hospitalId);
            for (BaseMedicalCase x : medicalCase) {
                x.setAuditScenario(auditScenario);
            }

        }else {
            //未指定场景查询最新记录
            for (AuditScenarioEnum value : AuditScenarioEnum.values()) {
                List<BaseMedicalCase> list = this.baseMedicalMapper.queryClinicalCase(value.getTableName(), admissionNo, hospitalId);
                if(CollUtil.isNotEmpty(list)){
                    for (BaseMedicalCase x : list) {
                        x.setAuditScenario(value.getAuditScenario());
                    }
                    medicalCase.addAll(list);
                }
            }
        }
        if(ObjectUtil.isNotNull(medicalCase)){
            Optional<BaseMedicalCase> max = medicalCase.stream().max(Comparator.comparing(BaseMedicalCase::getAuditTime));
            if(max.isPresent()){
                baseMedicalCase = max.get();
            }
        }
        return baseMedicalCase;
    }

    /**
     * 查询 - 查询违规风险项目
     * @param admissionNo
     * @param selfExpense
     * @param item
     * @param itemTypes
     * @param hospitalId
     * @return
     */
    public List<BaseMedicalDetail> queryClinicalDetail(String auditScenario, String admissionNo, String violationFlag,String selfExpense, String item, List<String> itemTypes, String hospitalId) {
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        //查询明细信息
        List<BaseMedicalDetail> medicalDetails = this.baseMedicalMapper.queryClinicalDetail(tableName, admissionNo, violationFlag, selfExpense, item, itemTypes, hospitalId);
        //查询明细
        List<String> nos = medicalDetails.stream().map(BaseMedicalDetail::getNo).collect(Collectors.toList());
        //查询违规项目
        List<BaseAudit> baseAudits = baseAuditMapper.selectAudit(tableName, admissionNo, nos, hospitalId);
        Map<String, List<BaseAudit>> baseAuditMap = baseAudits.stream().collect(Collectors.groupingBy(BaseAudit::getDetailNo));
        //查询规则级别配置
        List<SysRuleLevel> ruleLevels = sysRuleLevelService.list(new QueryWrapper<SysRuleLevel>().select(SysRuleLevel.FIELD_CODE,SysRuleLevel.FIELD_LEVEL_CONFIG).eq(SysRuleLevel.FIELD_HOSPITAL_ID, hospitalId));
        Map<String, String> ruleLevelMap = ruleLevels.stream().collect(Collectors.toMap(SysRuleLevel::getCode, SysRuleLevel::getLevelConfig));

        //数据组装
        //1.主单违规记录
        if(CollUtil.isNotEmpty(baseAuditMap.get("0"))){
            BaseMedicalDetail caseLine = new BaseMedicalDetail();
            caseLine.setDetailNo("0");
            medicalDetails.add(0,caseLine);
        }
        for (BaseMedicalDetail medicalDetail : medicalDetails) {
            List<BaseAudit> audits = baseAuditMap.get(medicalDetail.getDetailNo());
            if(CollUtil.isNotEmpty(audits)){
                BaseAudit min = audits.stream().min(Comparator.comparing(x -> ruleLevelMap.get(x.getRuleType()))).get();
                medicalDetail.setRiskLevel(ruleLevelMap.get(min.getRuleType()));
                medicalDetail.setRuleNames(audits.stream().map(BaseAudit::getRuleName).collect(Collectors.joining(";")));
                medicalDetail.setRuleReasons(audits.stream().map(BaseAudit::getRuleReason).collect(Collectors.joining(";")));
            }
        }
        try {
            Collections.sort(medicalDetails, new MedicalDetailComparator());
        }catch (Exception e){
            log.warn("sort is error");
        }
//        DictUtils.translateDict(medicalDetails);
        return medicalDetails;
    }

    /**
     * 查询丙类项目
     * @param auditScenario
     * @param admissionNo
     * @param item
     * @param itemTypes
     * @param hospitalId
     * @return
     */
    public List<BaseMedicalDetail> queryClinicalSelfItem(String auditScenario, String admissionNo, String item, List<String> itemTypes, String hospitalId) {
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();

        List<BaseMedicalDetail> medicalDetails = this.baseMedicalMapper.queryClinicalSelfItem(tableName, admissionNo, item, itemTypes, hospitalId);
//        DictUtils.translateDict(medicalDetails);
        return medicalDetails;
    }

    /**
     * 查询漏收费项目
     * @param auditScenario
     * @param admissionNo
     * @param item
     * @param itemTypes
     * @param hospitalId
     * @return
     */
    public List<BaseMedicalDetail> queryMissedItem(String auditScenario, String admissionNo, String item, List<String> itemTypes, String hospitalId) {
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        List<BaseMedicalDetail> medicalDetails = this.baseMedicalMapper.queryMissedItem(tableName, admissionNo, item, itemTypes, hospitalId);
        return medicalDetails;
    }

    /**
     * 更新自费状态
     * @param auditScenario
     * @param selfDetail        转自费明细
     * @param noSelfDetail      转非自费明细
     * @param hospitalId
     */
    @Transactional
    public void changeSelfAgreement(String auditScenario, List<BaseMedicalDetail> selfDetail,List<BaseMedicalDetail> noSelfDetail, String hospitalId) {
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        //更新明细状态
        if(CollUtil.isNotEmpty(selfDetail)){
            List<String> collect = selfDetail.stream().map(BaseMedicalDetail::getDetailNo).collect(Collectors.toList());
            this.baseMedicalMapper.updateDetailsToSelfExpense(tableName, collect, hospitalId);
        }
        if(CollUtil.isNotEmpty(noSelfDetail)){
            List<String> collect = noSelfDetail.stream().map(BaseMedicalDetail::getDetailNo).collect(Collectors.toList());
            this.baseMedicalMapper.updateDetailsToNoSelfExpense(tableName, collect, hospitalId);
        }
    }

    public List<BaseMedicalDiag> queryDiagnosisList(String admissionNo, String no, String hospitalId) {

        List<BaseMedicalDiag> medicalDiagList = this.hpDiagMapper.queryDiagList(admissionNo, no, hospitalId);
        DictUtils.translateDict(medicalDiagList);
        return medicalDiagList;
    }

    public class MedicalDetailComparator implements Comparator<BaseMedicalDetail> {
        public int compare(BaseMedicalDetail detail1, BaseMedicalDetail detail2) {
            // 按照 prop1 进行排序
            int compareResult = "0".equals(detail1.getDetailNo())? -1:detail1.getDetailNo().compareTo(detail2.getDetailNo());
            if (compareResult != 0) {
                return compareResult;
            }

            // 按照 prop2 进行排序
            compareResult = detail1.getRiskLevel().compareTo(detail2.getRiskLevel());
            if (compareResult != 0) {
                return compareResult;
            }

            // 按照 prop3 进行排序
            return detail1.getItemDate() == null? 1 : - detail1.getItemDate().compareTo(detail2.getItemDate());
        }
    }

    /*
    *  根据人工审核配置对数据进入以及数据权限进行过滤
    * */
    private boolean getMrFlag (List<MrPlan> mrPlans,BaseMedicalCase medicalCase,List<BaseAudit> baseAudits,List<String> sceneCodes,String auditScenario,Map<String,List<BaseMedicalStats>> baseMedicalStatsMap){
        if(CollectionUtil.isEmpty(mrPlans) || !"1".equals(mrPlans.get(0).getEnable())){
            return false;
        }
        return mrPlanFilterImpl.exec(mrPlans.get(0),medicalCase,baseAudits,sceneCodes,auditScenario, baseMedicalStatsMap);
    }

    /*
    * 获取待处理患者列表
    * @param codePrefix
    * @param hospitalId
    * @param patientName
    * @param presentDeptCode
    * @param docId                主诊医生
    * */
    public MqsResult getPendingPatient  (String codePrefix,String hospitalId,String patientName,String presentDeptCode,String docId) {
        try {
            //首先查询当前门诊/住院那些场景启用
            List<SysScene> sysScenes = sysSceneService.querySceneList(codePrefix,"1",hospitalId);
            if(CollectionUtil.isEmpty(sysScenes)){
                return MqsResult.ERROR("请确认是否有已启用的场景！！！");
            }
            List<String> sysSceneCodes = sysScenes.stream().map(sysScene -> sysScene.getSceneCode()).collect(Collectors.toList());
            Map<String,List<AuditPatientVO>> map = new HashMap<>();
            //查询人工质控待处理  （查询人工质控表mqs_mr_audit 并且该detail并不在忽略表以及mqs_mr_base_detail表的自费状态还是医保） 自费（0-医保，1-自费 2-转自费）
            List<AuditPatientVO> mrMedicalDetails = mrReviewMapper.queryMrAuditPatient(hospitalId,patientName,presentDeptCode,docId,sysSceneCodes);
            if(CollectionUtil.isNotEmpty(mrMedicalDetails)){
                //根据参保人id进行去重操作
                map.put("mrAuditPatient",mrMedicalDetails.stream().collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(comparing(AuditPatientVO::getPatientId))),
                                ArrayList::new
                        )
                ));
            }
            //查询机审待处理
            List<AuditPatientVO> mrBaseMedicalDetails = mrReviewMapper.queryMrBaseAuditPatient(hospitalId,patientName,presentDeptCode,docId,sysSceneCodes);
            if(CollectionUtil.isNotEmpty(mrBaseMedicalDetails)){
                List<AuditPatientVO> auditPatient = mrBaseMedicalDetails.stream().collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(comparing(AuditPatientVO::getPatientId))),
                                ArrayList::new
                        )
                );
                //人工待处理优先展示，所以这里要过滤掉有人工待处理的患者
                if(CollectionUtil.isNotEmpty(mrMedicalDetails)){
                    List<String> mrPatientIds = mrMedicalDetails.stream().map(detail -> detail.getPatientId()).collect(Collectors.toList());
                    List<AuditPatientVO> filterAuditPatient = auditPatient.stream().filter(detail -> !mrPatientIds.contains(detail.getPatientId())).collect(Collectors.toList());
                    map.put("auditPatient",filterAuditPatient);
                } else {
                    map.put("auditPatient",auditPatient);
                }
            }
            return MqsResult.OK(map);
        } catch (Exception e) {
            return MqsResult.Exception("获取质控患者失败！！！",e.getMessage());
        }
    }

    /**
     *  查询主单信息（如果有传场景编码则为正常审核流程进入，如果只传单据号则为质控用户进入）
     * */
    public MqsResult getBaseMedical (String no,String batch,String auditScenario,String hospitalId,String admissionNo){
        try {
            Map<String,Object> map = new HashMap<>();
            BaseMedicalCase baseMedicalCase = null;
            List<BaseMedicalDiag> medicalDiags = new ArrayList<>();
            String tableName = "";
            //如果只传了no  单据号 则是从质控患者列表进入
            if(StringUtils.isNotBlank(no)){
                //查询人工审核信息
                baseMedicalCase = mrReviewMapper.queryMrBaseByNo(no,hospitalId);
                if(Objects.nonNull(baseMedicalCase)){
                    admissionNo = baseMedicalCase.getAdmissionNo();
                    auditScenario = baseMedicalCase.getSourceId();
                    //获取诊断信息
                    medicalDiags.addAll(hpDiagMapper.queryDiagList(admissionNo,no,hospitalId));
                }
            } else {
                //从正常审核渠道进入，根据场景编码和批次号查询_his表的信息
                tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
                //查询主单信息
                List<BaseMedicalCase> baseMedicalCases = this.baseMedicalMapper.auditQueryMedicalCaseHis(tableName, admissionNo, batch, hospitalId);
                if(CollectionUtil.isNotEmpty(baseMedicalCases)){
                    baseMedicalCase = baseMedicalCases.get(0);
                    //获取诊断信息
                    medicalDiags.addAll(hpDiagMapper.queryDiagListHis(admissionNo,no,batch,hospitalId));
                }
            }
            //处理特殊字段
            convert(baseMedicalCase,hospitalId,medicalDiags);
            //字典
            DictUtils.translateDict(baseMedicalCase);
            map.put("auditScenario",auditScenario);
            map.put("baseMedicalCase",baseMedicalCase);
            return MqsResult.OK(map);
        } catch (Exception e) {
            return MqsResult.Exception("获取主单信息失败！！！",getStackTraceAsString(e));
        }
    }

    /**
     * 特殊字段处理
     * @param baseMedicalCase      主单对象
     * @param hospitalId           医院id
     * @param medicalDiags         诊断信息
     * */
    public void convert (BaseMedicalCase baseMedicalCase,String hospitalId,List<BaseMedicalDiag> medicalDiags) {
        if(Objects.isNull(baseMedicalCase)){
            return;
        }
        String no = baseMedicalCase.getNo();
        //获取诊断信息
        if(CollectionUtil.isNotEmpty(medicalDiags)){
            //获取全部出院诊断
            //主诊断在最前边，然后按照diagnosisOrder  诊断排序号进行倒序排列
            String result = medicalDiags.stream()
                    .filter(medicalDiag-> "1".equals(medicalDiag.getDiagInoutType()))
                    .sorted(Comparator.comparing(BaseMedicalDiag::getMainFlag).reversed()
                            .thenComparing(Comparator.comparing(BaseMedicalDiag::getDiagnosisOrder).reversed()))
                    .map(BaseMedicalDiag::getDiagnosisName)
                    .collect(Collectors.joining("-"));
            baseMedicalCase.setInDiagnosisName(result);
        }
        //计算年龄
        if(Objects.nonNull(baseMedicalCase.getPatientBirthday())){
            baseMedicalCase.setAge(DateUtil.age(DateUtil.parseDate(baseMedicalCase.getPatientBirthday()),Objects.isNull(baseMedicalCase.getAdmissionDate())? new Date() : baseMedicalCase.getAdmissionDate()));
        }
        //在院状态  (1-出院，0-在院) （根据出院时间判断，有出院时间则为出院，无则为在院）
        boolean dischargeState = Objects.nonNull(baseMedicalCase.getDischargeDate());
        //入院天数   出院=出院时间-入院时间+1，在院=当前时间-入院时间+1
        Date admissionDate = Objects.nonNull(baseMedicalCase.getAdmissionDate()) ? baseMedicalCase.getAdmissionDate() : new Date();
        long days = 0;
        if(dischargeState){
            //已出院
            days = DateUtil.betweenDay(baseMedicalCase.getDischargeDate(),admissionDate,true) + 1;
        } else {
            // 在院
            days = DateUtil.betweenDay(new Date(),admissionDate,true) + 1;
        }
        baseMedicalCase.setInhospitalDays(String.valueOf(days));
        //是否结算
        baseMedicalCase.setBillState(Objects.nonNull(baseMedicalCase.getBillDate())?"1" : "0");
        //人工审核状态
        List<MrCase> cases = mrCaseService.queryMrCase(no,hospitalId,null);
        if(CollectionUtil.isNotEmpty(cases)){
            baseMedicalCase.setMrStatus(cases.get(0).getMrStatus());
        } else {
            baseMedicalCase.setMrStatus("0");
        }
    }


    public List<ComprePopupDetailVO> queryDetailHis (String tableName,ComprePopupQueryVO comprePopupQueryVO,String hospitalId,String clinicalStatus,String batchNo,String admissionNo) {
        List<ComprePopupDetailVO> hisList = new ArrayList<>();
        String auditTableName = tableName + "_audit_his";
        String detailTableName = tableName + "_detail_his";
        List<ComprePopupDetailVO> hisDetails = baseMedicalMapper.queryDetailHis(tableName,comprePopupQueryVO,hospitalId);
        if(CollectionUtil.isNotEmpty(hisDetails)){
            hisList.addAll(convert (hisDetails,clinicalStatus,auditTableName,detailTableName,hospitalId,batchNo));
        }
        //查询his主单违规记录
        if("1".equals(comprePopupQueryVO.getViolationFlag()) && "0".equals(comprePopupQueryVO.getClinicalStatus())){
            List<ComprePopupDetailVO> hisMedical = baseMedicalMapper.queryMedicalAudit(tableName,auditTableName,admissionNo,hospitalId,comprePopupQueryVO,batchNo);
            if(CollectionUtil.isNotEmpty(hisMedical)){
                //转换字典项
                DictUtils.translateDict(hisMedical);
                hisList.addAll(hisMedical);
            }
        }
        return hisList;
    }


    public List<ComprePopupDetailVO> queryDetail (String tableName,ComprePopupQueryVO comprePopupQueryVO,String hospitalId,String clinicalStatus,String admissionNo) {
        List<ComprePopupDetailVO> list = new ArrayList<>();
        String auditTableName = tableName + "_audit";
        String detailTableName = tableName + "_detail";
        log.info("查询明细表参数:"+ JSONUtil.toJsonStr(comprePopupQueryVO));
        log.info("查询明细表参数:tableName,hospitalId"+tableName+"/\\"+hospitalId);
        List<ComprePopupDetailVO> details = baseMedicalMapper.queryDetail(tableName,comprePopupQueryVO,hospitalId);
        if(CollectionUtil.isNotEmpty(details)){
            list.addAll(convert (details,clinicalStatus,auditTableName,detailTableName,hospitalId,null));
        }
        //查询主单违规
        if("1".equals(comprePopupQueryVO.getViolationFlag())){
            if((!MrCase.BASE_TABLE.equals(tableName) && "0".equals(comprePopupQueryVO.getClinicalStatus())) || MrCase.BASE_TABLE.equals(tableName)){
                List<ComprePopupDetailVO> hisMedical = baseMedicalMapper.queryMedicalAudit(tableName,auditTableName,admissionNo,hospitalId,comprePopupQueryVO,null);
                //质控页面又要转单违规不标黄
                if(MrCase.BASE_TABLE.equals(tableName)){
                    for(ComprePopupDetailVO comprePopupDetailVO : hisMedical){
                        comprePopupDetailVO.setOperationType("0");
                        if("0".equals(comprePopupQueryVO.getClinicalStatus())){
                            comprePopupDetailVO.setIsIgnore("0");
                        } else {
                            comprePopupDetailVO.setIsIgnore("1");
                        }
                    }
                }
                //转换字典项
                DictUtils.translateDict(hisMedical);
                list.addAll(hisMedical);
            }
        }
        return list;
    }

    /**
     *  明细查询（分组）
     * @param  comprePopupQueryVO  查询对象
     */
    public List<ComprePopupDetailVO> queryMedicalDetailByItem (ComprePopupQueryVO comprePopupQueryVO) {
        //根据batchNo分辨是正常审核渠道还是诊疗查看
        String admissionNo = comprePopupQueryVO.getAdmissionNo();
        String hospitalId = comprePopupQueryVO.getHospitalId();
        String batchNo = comprePopupQueryVO.getBatchNo();
        String tableName = "";
        if(StringUtils.isNotBlank(comprePopupQueryVO.getAuditScenario())){
            tableName = AuditScenarioEnum.valueOf(comprePopupQueryVO.getAuditScenario()).getTableName();
        }else{
            tableName = MrCase.BASE_TABLE;
        }

        String isIncrement = comprePopupQueryVO.getIsIncrement();
        String clinicalStatus = comprePopupQueryVO.getClinicalStatus();
        List<ComprePopupDetailVO> details;
        if(StringUtils.isNotBlank(batchNo) && !StringUtils.equals(tableName,MrCase.BASE_TABLE)){
            //正常审核渠道进入
            //根据批次号batchNo查询对应的his表
            if("true".equals(isIncrement)){
                //增量查询his表以及正式表
                details = queryDetailHis(tableName,comprePopupQueryVO,hospitalId,clinicalStatus,batchNo,admissionNo);
                //查询正式表信息
                ComprePopupQueryVO queryVO = comprePopupQueryVO;
                queryVO.setBatchNo(null);
                queryVO.setAdmissionNo(admissionNo);
                List<ComprePopupDetailVO> otherDetails = queryDetail(tableName,queryVO,hospitalId,clinicalStatus,admissionNo);
                if(CollectionUtil.isNotEmpty(otherDetails)){
                    //进行去重操作，以his为准
                    List<String> detailNos = details.stream().map(detail -> detail.getDetailNo()).collect(Collectors.toList());
                    details.addAll(otherDetails.stream().filter(detail-> !detailNos.contains(detail.getDetailNo())).map(detail -> {detail.setOperationType("0"); return detail;}).collect(Collectors.toList()));
                }
                //}
            } else {
                //查询his表
                details =  queryDetailHis(tableName,comprePopupQueryVO,hospitalId,clinicalStatus,batchNo,admissionNo);
            }
        } else {
            //通过质控人进入
            //这里明细没有经过主诊医生以及当前科室进行过滤，原因是质控人列表已经经过主诊医生和当前科室过滤，所以根据质控人查到的明细一定是当前主诊医生和当前科室的数据
            //查询明细数据
            details = queryDetail(MrCase.BASE_TABLE,comprePopupQueryVO,hospitalId,clinicalStatus,admissionNo);
        }
        //明细为空则直接返回
        if(CollectionUtil.isEmpty(details)){
            return Collections.emptyList();
        }
        //分组
        details = groupDetails(details);

        //聚合并赋值
        for(ComprePopupDetailVO comprePopupDetailVO : details){
            List<ComprePopupDetailVO> child = comprePopupDetailVO.getChild();
            if(CollectionUtil.isNotEmpty(child) && child.size() > 1 ){
                MrDetailAggregation result = processDetailList(child,clinicalStatus);
                comprePopupDetailVO.setDetailNo("0");
                comprePopupDetailVO.setSelfExpense(result.getSelfExpense());
                comprePopupDetailVO.setSelfExpenseCh(result.getSelfExpenseCh());
                comprePopupDetailVO.setAuditSource(result.getAuditSource());
                comprePopupDetailVO.setAuditSourceName(result.getAuditSourceName());
                comprePopupDetailVO.setRuleType(result.getRuleType());
                comprePopupDetailVO.setRuleTypeName(result.getRuleTypeName());
                comprePopupDetailVO.setRuleReasons(result.getRuleReasons());
                comprePopupDetailVO.setNumbers(result.getNumbers());
                comprePopupDetailVO.setCosts(result.getCosts());
                comprePopupDetailVO.setItemName(result.getItemName());
                comprePopupDetailVO.setItemTypeName(result.getItemTypeName());
                comprePopupDetailVO.setCategoryName(result.getCategoryName());
                comprePopupDetailVO.setDetailNum(String.valueOf(child.size()));
                comprePopupDetailVO.setItemDate(null);
                comprePopupDetailVO.setReasonTypes(null);
                comprePopupDetailVO.setReasonDess(null);
                comprePopupDetailVO.setOutpatientMedication(result.getOutpatientMedication());
                comprePopupDetailVO.setRouteAdministration(result.getRouteAdministration());
            } else {
                comprePopupDetailVO.setDetailNum("1");
            }
        }
        //分组后的特殊处理
        return details;
    }


    public List<ComprePopupDetailVO> queryMedicalDetailByItem4 (ComprePopupQueryVO comprePopupQueryVO) {
        //根据batchNo分辨是正常审核渠道还是诊疗查看
        String admissionNo = comprePopupQueryVO.getAdmissionNo();
        String hospitalId = comprePopupQueryVO.getHospitalId();
        String batchNo = comprePopupQueryVO.getBatchNo();
        String tableName = AuditScenarioEnum.valueOf(comprePopupQueryVO.getAuditScenario()).getTableName();
        String isIncrement = comprePopupQueryVO.getIsIncrement();
        String clinicalStatus = comprePopupQueryVO.getClinicalStatus();
        List<ComprePopupDetailVO> details;
        if(StringUtils.isBlank(comprePopupQueryVO.getIsHis())){
            tableName = MrCase.BASE_TABLE;
        }
        if(StringUtils.isNotBlank(batchNo)){
            //正常审核渠道进入
            //根据批次号batchNo查询对应的his表
            if("true".equals(isIncrement)){
                //增量查询his表以及正式表
                details = queryDetailHis(tableName,comprePopupQueryVO,hospitalId,clinicalStatus,batchNo,admissionNo);
                //查询正式表信息
                ComprePopupQueryVO queryVO = comprePopupQueryVO;
                queryVO.setBatchNo(null);
                queryVO.setAdmissionNo(admissionNo);
                List<ComprePopupDetailVO> otherDetails = queryDetail(tableName,queryVO,hospitalId,clinicalStatus,admissionNo);
                if(CollectionUtil.isNotEmpty(otherDetails)){
                    //进行去重操作，以his为准
                    List<String> detailNos = details.stream().map(detail -> detail.getDetailNo()).collect(Collectors.toList());
                    details.addAll(otherDetails.stream().filter(detail-> !detailNos.contains(detail.getDetailNo())).map(detail -> {detail.setOperationType("0"); return detail;}).collect(Collectors.toList()));
                }
                //}
            } else {
                //查询his表
                details =  queryDetail(tableName,comprePopupQueryVO,hospitalId,clinicalStatus/*,batchNo*/,admissionNo);
            }
        } else {
            //通过质控人进入
            //这里明细没有经过主诊医生以及当前科室进行过滤，原因是质控人列表已经经过主诊医生和当前科室过滤，所以根据质控人查到的明细一定是当前主诊医生和当前科室的数据
            //查询明细数据
            details = queryDetail(tableName,comprePopupQueryVO,hospitalId,clinicalStatus,admissionNo);
        }
        //明细为空则直接返回
        if(CollectionUtil.isEmpty(details)){
            return Collections.emptyList();
        }
        //分组
        details = groupDetails(details);
        //聚合并赋值
        for(ComprePopupDetailVO comprePopupDetailVO : details){
            List<ComprePopupDetailVO> child = comprePopupDetailVO.getChild();
            if(CollectionUtil.isNotEmpty(child) && child.size() > 1 ){
                MrDetailAggregation result = processDetailList(child,clinicalStatus);
                comprePopupDetailVO.setDetailNo("0");
                comprePopupDetailVO.setSelfExpense(result.getSelfExpense());
                comprePopupDetailVO.setSelfExpenseCh(result.getSelfExpenseCh());
                comprePopupDetailVO.setAuditSource(result.getAuditSource());
                comprePopupDetailVO.setAuditSourceName(result.getAuditSourceName());
                comprePopupDetailVO.setRuleType(result.getRuleType());
                comprePopupDetailVO.setRuleTypeName(result.getRuleTypeName());
                comprePopupDetailVO.setRuleReasons(result.getRuleReasons());
                comprePopupDetailVO.setNumbers(result.getNumbers());
                comprePopupDetailVO.setCosts(result.getCosts());
                comprePopupDetailVO.setItemName(result.getItemName());
                comprePopupDetailVO.setItemTypeName(result.getItemTypeName());
                comprePopupDetailVO.setCategoryName(result.getCategoryName());
                comprePopupDetailVO.setDetailNum(String.valueOf(child.size()));
                comprePopupDetailVO.setItemDate(null);
                comprePopupDetailVO.setReasonTypes(null);
                comprePopupDetailVO.setReasonDess(null);
                comprePopupDetailVO.setOutpatientMedication(result.getOutpatientMedication());
                comprePopupDetailVO.setRouteAdministration(result.getRouteAdministration());
            } else {
                comprePopupDetailVO.setDetailNum("1");
            }
        }
        //分组后的特殊处理
        return details;
    }

    /**
     * 根据项目id进行分组
     * @param details              明细列表
     * @param auditTableName       违规表
     * @param detailTableName      明细表
     * @param hospitalId           医院id
     * @param batchNo              批次号
     *
     * */
    public List<ComprePopupDetailVO> convert (List<ComprePopupDetailVO> details,String clinicalStatus,String auditTableName,String detailTableName,String hospitalId,String batchNo) {
        Map<String,BaseMedicalDetail> detailMap = null;
        Map<String,MrMedicalDetail> detailClinicalMap = null;
        Map<String,BaseAuditIgnore> ignoreMap = null;
        //获取明细号
        List<String> detailNos = details.stream().map(detail->detail.getDetailNo()).collect(Collectors.toList());
        String no = details.get(0).getNo();
        //如果查询的是违规明细，否则不进行特殊字段的处理
        if(StringUtils.isNotBlank(clinicalStatus)){
            List<BaseMedicalDetail> detailLevels = baseMedicalMapper.queryMinRuleTypeByNo (auditTableName, null,hospitalId, detailNos,batchNo);
            List<MrMedicalDetail> detailClinicals = baseMedicalMapper.queryDetailSourceAndReasons(detailTableName,auditTableName,detailNos,hospitalId,batchNo);
            List<BaseAuditIgnore> ignores = baseMedicalMapper.queryIgnoreByDetailNos(detailNos,no);
            if(CollectionUtil.isNotEmpty(detailLevels)){
                //查询出全部违规记录（包括人工+机审）并违规级别取最小值，规则名称+违规原因进行拼接
                detailMap = detailLevels.stream()
                        .collect(Collectors.toMap(
                                BaseMedicalDetail::getDetailNo,
                                detailLevel -> detailLevel,
                                (existing, replacement) -> {
                                            String minRuleType = Stream.of(existing, replacement)
                                                    .map(BaseMedicalDetail::getRuleType)
                                                    .min(String::compareTo)
                                                    .orElse("");

                                            String ruleTypeNames = Stream.of(existing, replacement)
                                                    .filter(u -> u.getRuleType().equals(minRuleType))
                                                    .findFirst()
                                                    .map(BaseMedicalDetail::getRuleTypeName)
                                                    .orElse("");

                                            String ruleNames = Stream.of(existing, replacement)
                                                    .map(BaseMedicalDetail::getRuleNames)
                                                    .distinct()
                                                    .collect(Collectors.joining(","));

                                            String ruleReasons = Stream.of(existing, replacement)
                                                    .map(BaseMedicalDetail::getRuleReasons)
                                                    .distinct()
                                                    .collect(Collectors.joining(","));

                                            return  BaseMedicalDetail.builder().detailNo(existing.getDetailNo()).ruleType(minRuleType).ruleTypeName(ruleTypeNames).ruleNames(ruleNames).ruleReasons(ruleReasons).build();
                                        }
                        ));
            }

            if(CollectionUtil.isNotEmpty(detailClinicals)){
                detailClinicalMap = detailClinicals.stream().distinct().collect(Collectors.toMap(MrMedicalDetail::getDetailNo,detail->detail));
            }
            if(CollectionUtil.isNotEmpty(ignores)){
                ignoreMap = ignores.stream().collect(Collectors.toMap(BaseAuditIgnore::getDetailNo,detail->detail));
            }
            //遍历赋值特殊字段
            for(ComprePopupDetailVO detail : details){
                String detailNo = detail.getDetailNo();
                //违规级别 + 违规级别名称 + 违规名称 + 违规原因
                if(Objects.nonNull(detailMap)){
                    BaseMedicalDetail baseMedicalDetail = detailMap.get(detailNo);
                    if(Objects.nonNull(baseMedicalDetail)){
                        detail.setRuleType(baseMedicalDetail.getRuleType());
                        detail.setRuleTypeName(baseMedicalDetail.getRuleTypeName());
                        detail.setRuleNames(baseMedicalDetail.getRuleNames());
                        detail.setRuleReasons(baseMedicalDetail.getRuleReasons());
                    }
                }
                //审核渠道
                if(Objects.nonNull(detailClinicalMap)){
                    MrMedicalDetail detailClinical = detailClinicalMap.get(detailNo);
                    if(Objects.nonNull(detailClinical)){
                        detail.setAuditSource(detailClinical.getAuditSource());
                    }
                }
                //是否忽略
                if(Objects.nonNull(ignoreMap)){
                    BaseAuditIgnore ignore =  ignoreMap.get(detailNo);
                    detail.setIsIgnore(Objects.nonNull(ignore)?"1":"0");
                }
                //设置明细个数
                detail.setDetailNum("1");
            }
        } else {
            for(ComprePopupDetailVO detail : details){
                //设置明细个数
                detail.setDetailNum("1");
            }
        }
        //转换字典项
        DictUtils.translateDict(details);
        return details;
    }

    /**
     * 按照项目id进行分组
     * */
    public List<ComprePopupDetailVO> groupDetails (List<ComprePopupDetailVO> details) {
        return details.stream()
                .collect(Collectors.groupingBy(ComprePopupDetailVO::getItemId))
                .entrySet()
                .stream()
                .map(entry -> {
                    List<ComprePopupDetailVO> groupedUsers = entry.getValue();
                    if(groupedUsers.size() == 1){
                        return groupedUsers.get(0);
                    }
                    ComprePopupDetailVO parent = groupedUsers.get(0);
                    //避免序列化时循环依赖问题
                    CopyOptions copyOptions = CopyOptions.create().setIgnoreProperties("child");
                    List<ComprePopupDetailVO> child = BeanUtil.copyToList(groupedUsers,ComprePopupDetailVO.class,copyOptions);
                    //查找operationType = 1 的记录
                    boolean result = false;
                    boolean isIgnore = false;
                    for(ComprePopupDetailVO comprePopupDetail : child){
                        if("1".equals(comprePopupDetail.getOperationType())){
                            result = true;
                        }
                        if(Objects.isNull(comprePopupDetail.getIsIgnore()) || "0".equals(comprePopupDetail.getIsIgnore())){
                            isIgnore = true;
                        }
                    }
                    parent.setIsIgnore(isIgnore ? "0" : "1");
                    parent.setOperationType(result ? "1" : "0");
                    parent.setChild(child);
                    parent.setDetailNum(String.valueOf(child.size()));
                    return parent;
                })
                .collect(Collectors.toList());
    }

    /*
     * 聚合明细各项属性
     * @param fileterDetails 分组明细列表
     * */
    public MrDetailAggregation processDetailList(List<ComprePopupDetailVO> fileterDetails,String clinicalStatus) {
        //聚合结果
        return  fileterDetails.stream()
                .map(bean -> new MrDetailAggregation(
                        bean.getSelfExpense(),
                        bean.getSelfExpenseCh(),
                        bean.getAuditSource(),
                        bean.getAuditSourceName(),
                        bean.getRuleType(),
                        bean.getRuleTypeName(),
                        bean.getRuleReasons(),
                        bean.getNumbers(),
                        bean.getCosts(),
                        bean.getItemName(),
                        bean.getItemTypeName(),
                        bean.getCategoryName(),
                        bean.getOutpatientMedication(),
                        bean.getRouteAdministration(),
                        bean.getRuleNames()
                ))
                .reduce(new MrDetailAggregation( "","","","","","","",BigDecimal.ZERO,BigDecimal.ZERO,"","","","","",""), (acc, curr) -> {
                    //拼装自费状态
                    String selfExpense = String.join("/", mergeStringArrays(acc.getSelfExpense(),curr.getSelfExpense(),"/"));
                    String selfExpenseCh = String.join("/", mergeStringArrays(acc.getSelfExpenseCh(),curr.getSelfExpenseCh(),"/"));
                    String auditSource = "";
                    String auditSourceName = "";
                    String ruleType = "";
                    String ruleReasons = "";
                    String ruleTypeName = "";
                    String ruleNames = "";
                    if(StringUtils.isNotBlank(clinicalStatus)){
                        //审核渠道
                        auditSource = String.join("、", mergeStringArrays(acc.getAuditSource(),curr.getAuditSource(),"、"));
                        auditSourceName = String.join("、", mergeStringArrays(acc.getAuditSourceName(),curr.getAuditSourceName(),"、"));
                        //违规级别
                        if (!StringUtils.isBlank(acc.getRuleType()) || !StringUtils.isBlank(curr.getRuleType())) {
                            int accRuleType = StringUtils.isBlank(acc.getRuleType()) ? Integer.MAX_VALUE : Integer.valueOf(acc.getRuleType());
                            int currRuleType = StringUtils.isBlank(curr.getRuleType()) ? Integer.MAX_VALUE : Integer.valueOf(curr.getRuleType());
                            ruleType = String.valueOf(Math.min(accRuleType, currRuleType));
                        }
                        //违规原因
                        //ruleReasons = acc.getRuleReasons() + (acc.getRuleReasons().isEmpty() ? "" : ";") + curr.getRuleReasons();
                        ruleReasons = String.join(";", mergeStringArrays(acc.getRuleReasons(),curr.getRuleReasons(),";"));
                        //违规名称
                        ruleTypeName = String.join("、", mergeStringArrays(acc.getRuleTypeName(),curr.getRuleTypeName(),"、"));
                        //规则名称
                        ruleNames = String.join("、", mergeStringArrays(acc.getRuleNames(),curr.getRuleNames(),"、"));
                    }

                    //数量
                    BigDecimal numbers = acc.getNumbers().add(curr.getNumbers());
                    //金额
                    BigDecimal costs = acc.getCosts().add(curr.getCosts());
                    //项目名称
                    //String itemName = acc.getItemName() + (acc.getItemName().isEmpty() ? "" : ",") + curr.getItemName();
                    String itemName = String.join(",", mergeStringArrays(acc.getItemName(),curr.getItemName(),","));
                    //项目类型
                    String itemTypeName = String.join(",", mergeStringArrays(acc.getItemTypeName(),curr.getItemTypeName(),","));
                    //甲乙类
                    String categoryName = String.join(",",  mergeStringArrays(acc.getCategoryName(),curr.getCategoryName(),","));
                    //是否出院带药
                    String outpatientMedication = String.join("/", mergeStringArrays(acc.getOutpatientMedication(),curr.getOutpatientMedication(),"/"));
                    //用药途径
                    String  routeAdministration = String.join("/", mergeStringArrays(acc.getRouteAdministration(),curr.getRouteAdministration(),"/"));

                    return new MrDetailAggregation(
                            selfExpense,
                            selfExpenseCh,
                            auditSource,
                            auditSourceName,
                            ruleType,
                            ruleTypeName,
                            ruleReasons,
                            numbers,
                            costs,
                            itemName,
                            itemTypeName,
                            categoryName,
                            outpatientMedication,
                            routeAdministration,
                            ruleNames
                    );
                }, (left, right) -> left);
    }

    /*
    * 抽取的公共数据处理方法
    * */
    private Set<String> mergeStringArrays(String str1, String str2, String separator) {
        return Stream.concat(
                        Arrays.stream(Optional.ofNullable(str1).orElse("").split(separator)),
                        Arrays.stream(Optional.ofNullable(str2).orElse("").split(separator))
                )
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }

    /**
     * 批量忽略
     * @param auditScenario    场景编码
     * @param admissionNo      就诊流水号
     * @param hospitalId       医院id
     * @param batchNo          批次号
     * */
    @Transactional(rollbackFor = Exception.class)
    public MqsResult batchInsertIgnore (List<ComprePopupDetailVO> comprePopupSaveVO,String auditScenario,String admissionNo,String hospitalId,String batchNo,String patientId,String patientName,String codePrefix) {
        try {
            if(CollectionUtil.isNotEmpty(comprePopupSaveVO)){
                List<String> filterDetailNos = comprePopupSaveVO.stream().map(vo->vo.getDetailNo()).distinct().collect(Collectors.toList());
                String no = comprePopupSaveVO.get(0).getNo();
                //首先查询场景配置，查看当前场景是否开启了忽略配置，然后在进行去重+ 新增操作
                if(StringUtils.isNotBlank(codePrefix)){
                    auditScenario = "hp".equals(codePrefix) ? "hpTr" : "opTr";
                }
                boolean result = getFilterSceneResult(hospitalId,null,null,"1","1",auditScenario,null,null);
                if(!result){
                    return MqsResult.ERROR("当前场景未启用或未配置忽略违规，本次转忽略操作失败，场景编码："+auditScenario);
                }
                //查询以前有无忽略记录
                List<BaseAuditIgnore> oldIgnores = baseMedicalMapper.queryIgnoreByDetailNos(filterDetailNos,no);
                if(CollectionUtil.isNotEmpty(oldIgnores)){
                    return MqsResult.ERROR("存在重复忽略记录,本次忽略操作失败！！！");
                }
                List<BaseAuditIgnore> ignores = new ArrayList<>();
                filterDetailNos.forEach(detailNo -> {
                    ignores.add(BaseAuditIgnore.builder().admissionNo(admissionNo).no(no).detailNo(detailNo).enable("1").hospitalId(hospitalId).build());
                });
                baseMedicalMapper.batchInsertAuditIgnore(ignores);

                //异步更新人工质控临床处理状态
                mrCaseService.updateCaseClinicalStatus(Arrays.asList(no),hospitalId);
                //记录日志
                sendLogs (comprePopupSaveVO,hospitalId,"opup","忽略",patientId,patientName,"异常信息：{}，转忽略操作日记记录失败！！！");
            }
            return MqsResult.OK("新增忽略明细成功！！！");
        } catch (Exception e) {
            return MqsResult.Exception("新增忽略明细失败！！！",e.getMessage());
        }
    }

    /**
     * 批量撤销忽略
     * @param auditScenario    场景编码
     * @param hospitalId       医院id
     * */
    public MqsResult unBatchIgnore (List<ComprePopupDetailVO> comprePopupSaveVO,String auditScenario,String hospitalId,String patientId,String patientName,String codePrefix) {
        try {
            if(CollectionUtil.isNotEmpty(comprePopupSaveVO)){
                String no = comprePopupSaveVO.get(0).getNo();
                List<String> detailNos = comprePopupSaveVO.stream().map(vo->vo.getDetailNo()).collect(Collectors.toList());
                if(StringUtils.isNotBlank(codePrefix)){
                    auditScenario = "hp".equals(codePrefix) ? "hpTr" : "opTr";
                }
                boolean result = getFilterSceneResult(hospitalId,null,null,"1","1",auditScenario,null,null);
                if(!result){
                    return MqsResult.ERROR("当前场景未启用或未配置忽略违规，本次撤销忽略操作失败，场景编码："+auditScenario);
                }
                //首先去重
                List<String> filterDetailNos = detailNos.stream().distinct().collect(Collectors.toList());
                baseMedicalMapper.batchDeleteIgnoreByDetailNos(filterDetailNos,no);

                //异步更新人工质控临床处理状态
                mrCaseService.updateCaseClinicalStatus(Arrays.asList(no),hospitalId);
                //记录日志
                sendLogs (comprePopupSaveVO,hospitalId,"opup","撤销忽略",patientId,patientName,"异常信息：{}，转忽略操作日记记录失败！！！");
            }
            return MqsResult.OK("撤销忽略明细成功！！！");
        } catch (Exception e) {
            return MqsResult.Exception("撤销忽略明细失败！！！",e.getMessage());
        }
    }

    /**
     * 综合弹窗----确认保存（包含转自费操作）
     * @param batchNo          批次号
     * @param hospitalId       医院id
     * @param admissionNo      就诊流水号
     * @param isIncrement      是否增量
     * @param auditScenario    场景编码
     * @param details          明细号
     * */
    @Transactional(rollbackFor = Exception.class)
    public MqsResult selfExpenseAndSave (String batchNo,String hospitalId,String admissionNo,String isIncrement,String auditScenario,List<ComprePopupDetailVO> details,String patientId,String patientName,String billState, String mrStatus,String codePrefix) {
        String userId = String.valueOf(LoginContext.getUserId());
        if(CollectionUtil.isEmpty(details)){
            return MqsResult.ERROR("强制保存失败，失败原因：需要保存的明细不能为空！！！");
        }
        List<ComprePopupDetailVO> freeList = details.stream().filter(detail -> StringUtils.isNotBlank(detail.getSelfExpense()) && !detail.getSelfExpense().equals(detail.getOldSelfExpense())).collect(Collectors.toList());
        // 首先根据批次号区分是正常审核流程还是质控患者渠道进入
        // 质控患者渠道进入的只进行转自费操作，审核渠道进入的需要区分his表和正是表数据，正式表数据只进行转自费，his表数据只进行强制保存
        if(StringUtils.isNotBlank(batchNo)){
           //首先调用强制保存接口。检查是否需要必填反馈类型以及反馈说明
           if(CollectionUtil.isNotEmpty(details)){
               BaseAuditResonDto baseAuditResonDto  = new BaseAuditResonDto();
               List<SaveResonVo> data = new ArrayList<>();
               for(ComprePopupDetailVO his : details){
                   SaveResonVo saveResonVo = new SaveResonVo();
                   saveResonVo.setDetail_no(his.getDetailNo());
                   saveResonVo.setNo(his.getNo());
                   saveResonVo.setItem_id(his.getItemId());
                   saveResonVo.setSelf_expense(his.getSelfExpense());
                   saveResonVo.setReason_type(his.getReasonTypes());
                   saveResonVo.setReason_des(his.getReasonDess());
                   data.add(saveResonVo);
               }
               baseAuditResonDto.setBatch_no(batchNo);
               baseAuditResonDto.setHospital_id(hospitalId);
               baseAuditResonDto.setAudit_scenario(auditScenario);
               baseAuditResonDto.setAdmission_no(admissionNo);
               baseAuditResonDto.setIs_increment(isIncrement);
               baseAuditResonDto.setData(data);
               //调用强制保存接口
               MqsResult mqsResult = saveClaimAuditHospital(baseAuditResonDto);
               if(mqsResult.getCode() != 200){
                   return MqsResult.ERROR("强制保存失败，失败原因：" + mqsResult.getMsg());
               }
           }
        }
        //只进行转自费操作
        if(CollectionUtil.isNotEmpty(freeList)){
            MqsResult selfMqsResult = doSelfExpense (hospitalId,auditScenario,freeList,userId,patientId,patientName,billState,mrStatus,codePrefix);
            if(selfMqsResult.getCode() != 200){
                return selfMqsResult;
            }
        }
        return MqsResult.OK("保存成功！！！");
    }

    /**
     * 进行转自费
     * @param hospitalId        医院id
     * @param auditScenario     场景编码
     * @param freeList          转自费明细集合
     * @param userId            当前操作人
     * */
    public MqsResult doSelfExpense (String hospitalId,String auditScenario,List<ComprePopupDetailVO> freeList,String userId,String patientId,String patientName,String billState, String mrStatus,String codePrefix) {
        String filterAuditScenario = auditScenario;
        if(StringUtils.isNotBlank(codePrefix)){
            filterAuditScenario = "hp".equals(codePrefix) ? "hpTr" : "opTr";
        }
        boolean filterResult = getFilterSceneResult(hospitalId,"1",null,null,"1",filterAuditScenario,billState,mrStatus);
        if(!filterResult){
            return MqsResult.ERROR("当前场景未启用转自费或配置了转自费不可用条件，本次操作失败，场景编码："+auditScenario);
        }
        //拼装请求参数
        ComprePopupDetailVO vo = freeList.get(0);
        String no = vo.getNo();
        String admissionNo = vo.getAdmissionNo();
        List<String> selfList = freeList.stream().filter(self->"2".equals(self.getSelfExpense())).map(self->self.getDetailNo()).collect(Collectors.toList());
        List<String> unSelfList = freeList.stream().filter(self->"0".equals(self.getSelfExpense())).map(self->self.getDetailNo()).collect(Collectors.toList());
        Map<String,Object> params = new HashMap();
        params.put("no",no);
        params.put("admissionNo",admissionNo);
        params.put("patientId",patientId);
        params.put("selfDetailNos",selfList);
        params.put("unselfDetailNos",unSelfList);
        //请求his
        Object result = cdmpServiceInterface.sendToChangeSelfexpense(params);
        //处理返回结果
        JSONObject obj = JSONUtil.parseObj(result);
        String resultCode = obj.getJSONObject("data").get("code").toString();
        String tableName = AuditScenarioEnum.valueOf(auditScenario).getTableName();
        if("100100".equals(resultCode)){
            //这里需要修改两张表一张主表一张his表
            baseMedicalMapper.batchUpdateSelfExpense(tableName,userId,hospitalId,freeList);
            baseMedicalMapper.batchUpdateSelfExpenseHis(tableName,userId,hospitalId,freeList);
            //将自费状态同步到人工审核里边
            mrReviewMapper.updateSelfExpenseByDetailNo(freeList,hospitalId);
        } else {
            return MqsResult.ERROR("转自费保存失败，请联系信息科处理！！！");
        }

        //异步更新人工质控临床处理状态
        mrCaseService.updateCaseClinicalStatus(Arrays.asList(no),hospitalId);
        //记录日志
        Map<Boolean, List<ComprePopupDetailVO>> freeMap = freeList.stream().collect(Collectors.partitioningBy(free -> "2".equals(free.getSelfExpense())));
        if(Objects.nonNull(freeMap)){
            sendLogs (freeMap.get(true),hospitalId,"opup","转自费",patientId,patientName,"异常信息：{}，转忽略操作日记记录失败！！！");
            sendLogs (freeMap.get(false),hospitalId,"opup","转医保",patientId,patientName,"异常信息：{}，转忽略操作日记记录失败！！！");
        }
        return MqsResult.OK("转自费成功");
    }

    /**
     * 判断当前场景有无配置转自费、自费协议书、忽略等配置
     * @param hospitalId        医院id
     * @param isSelf            是否转自费
     * @param isSelfAgreement   是否启用自费协议
     * @param isIgnore          是否忽略
     * @param enable            是否启用
     * @param auditScenario     场景编码
     * */
    public boolean getFilterSceneResult (String hospitalId,String isSelf,String isSelfAgreement,String isIgnore,String enable,String auditScenario,String billState, String mrStatus) {
        List<SysSceneDTO> sceneList = sysSceneService.getIgnoreAndSelfExpenseConfig(hospitalId,isSelf,isSelfAgreement,isIgnore,enable,auditScenario);
        if(CollectionUtil.isEmpty(sceneList)){
            return false;
        }
        //如果isSelf不为空，并且
        SysSceneDTO sceneFunction = sceneList.get(0);
        String selfPaidCondition = sceneFunction.getSelfPaidCondition();
        if(StringUtils.isNotBlank(isSelf) && StringUtils.isNotBlank(selfPaidCondition)){
             //转自费不可用条件前端固定传 0 - 人工审核通过 ，1-结算完成
            if(selfPaidCondition.contains("0") && "2".equals(mrStatus)){
                return false;
            }
            if(selfPaidCondition.contains("1") && "1".equals(billState)){
                return false;
            }
        }
        return true;
    }


    /**
     * 异步记录日志
     * */
    public void sendLogs (List<ComprePopupDetailVO> comprePopupSaveVO,String hospitalId,String operatorSource,String operatorText,String patientId,String patientName,String errorMessage) {
        try{
            if (CollectionUtil.isEmpty(comprePopupSaveVO)) {
                return;
            }
            //综合弹窗无法获取到操作人
            CustomUser customUser = LoginContext.getUser();
            String operatorId =  Objects.nonNull(customUser)? String.valueOf(customUser.getUsername()) : "";
            String operatorName = Objects.nonNull(customUser)? customUser.getName() : "";
            ThreadPoolTaskExecutor threadPoolTaskExecutor = ApplicationContextUtil.getBean(ThreadPoolTaskExecutor.class);
            CompletableFuture.runAsync(() -> {
                List<SysLogs> logs = new ArrayList<>();
                for(ComprePopupDetailVO vo : comprePopupSaveVO){
                    logs.add(SysLogs.builder()
                            .operatorId(operatorId)
                            .operatorName(operatorName)
                            .operatorTime(new Date())
                            .operatorText(operatorText)
                            .admissionNo(vo.getAdmissionNo())
                            .patientId(patientId)
                            .patientName(patientName)
                            .detailNo(vo.getDetailNo())
                            .itemId(vo.getItemId())
                            .itemName(vo.getItemName())
                            .itemDate(vo.getItemDate())
                            .operatorSource(operatorSource)
                            .hospitalId(hospitalId).build());
                }
                sysLogsService.recordLogs(logs);
            },threadPoolTaskExecutor);
        } catch (Exception e) {
            log.info(errorMessage,e);
        }
    }

    //获取完整异常堆栈信息
    public static String getStackTraceAsString(Exception e) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }
    public void exportRelatedRecords(ComprePopupQueryVO comprePopupQueryVO, String detailNo, String admissionNo, String batch, String auditScenario,String ruleOrigin, String hospitalId,HttpServletResponse response){
        List<ComprePopupDetailVO>  list_parent =  this.queryMedicalDetailByItem(comprePopupQueryVO);
        List<Map> list = this.queryMedicalAuditRules(detailNo, admissionNo, batch, auditScenario,ruleOrigin,hospitalId);
        try{
            // 获取当前日期时间（包含毫秒）
            LocalDateTime now = LocalDateTime.now();
            System.out.println("当前日期时间: " + now);
            // 格式化输出
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
            String formattedDateTime = now.format(formatter);
            System.out.println("格式化后的日期时间: " + formattedDateTime);
            String title = admissionNo+ (StringUtils.isBlank(detailNo)?"全部":"明细")+"相关记录"+ formattedDateTime;
            log.info("导出相关记录,标题:"+title);
            /*开始处理需要导出的数据----------开始*/
            List<Map> list_common = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(list_parent)){
                list_parent.forEach(e->{
                    if(CollectionUtil.isNotEmpty(e.getChild())){
                        e.getChild().forEach(p->{
                            if(StringUtils.isNotBlank(detailNo)){
                                if(!p.getDetailNo().equals(detailNo)){
                                    return;
                                }
                            }
                            Map map = new HashMap();
                            String child_title = "项目："+p.getItemId() +"      "+ p.getItemName()+"；  项目时间："+  DateFormatUtils.format(p.getItemDate(), "yyyy-MM-dd HH:mm:ss")+ "；    违反规则："+ p.getRuleNames()+"；"+"违规原因："+p.getRuleReasons() +";";
                            map.put("title",child_title);
                            List<Map> childs = new ArrayList<>();
                            List<Map> inner_childs = list.stream().filter(inner->{
                               /* List<BaseMedicalDetail> details = (List<BaseMedicalDetail>)inner.get("details");
                                    return details.stream().anyMatch(d->d.getDetailNo().equals(p.getDetailNo()));*/
                                BaseAudit audit = (BaseAudit) inner.get("rule");
                                return audit.getDetailNo().equals(p.getDetailNo());
                            }).collect(Collectors.toList());
                            if(CollectionUtil.isNotEmpty(inner_childs)){
                                inner_childs.forEach(ic->{
                                    Map map_child = new HashMap();
                                    BaseAudit baseAudit =(BaseAudit) ic.get("rule");
                                    if(Objects.nonNull(baseAudit)){
                                        String inner_title = baseAudit.getRuleOrigin()+"-"+baseAudit.getRuleCode()+"-"+baseAudit.getRuleName()+"；";
                                        map_child.put("title",inner_title);
                                    }
                                    List<BaseMedicalDetail> tables = (List<BaseMedicalDetail>)ic.get("details");
                                    map_child.put("table",tables);
                                    childs.add(map_child);
                                });
                            }
                            map.put("child",childs);
                            list_common.add(map);
                        });
                    }else{
                        Map map = new HashMap();
                        String child_title = "项目："+ (StringUtils.isNotBlank(e.getItemId())?e.getItemId():"-") +"      "+ (StringUtils.isNotBlank( e.getItemName())?e.getItemName():"-")+"；  项目时间："+ (ObjectUtils.isEmpty(e.getItemDate())?"-":DateFormatUtils.format(e.getItemDate(), "yyyy-MM-dd HH:mm:ss"))+ "；    违反规则："+ e.getRuleNames()+"；"+"违规原因："+e.getRuleReasons() +";";
                        map.put("title",child_title);
                        if(StringUtils.isNotBlank(detailNo)){
                            if(!StringUtils.equals(e.getDetailNo(),detailNo)){
                                return;
                            }
                        }
                        List<Map> childs = new ArrayList<>();
                        List<Map> inner_childs = list.stream().filter(inner->{
                               /* List<BaseMedicalDetail> details = (List<BaseMedicalDetail>)inner.get("details");
                                    return details.stream().anyMatch(d->d.getDetailNo().equals(p.getDetailNo()));*/
                            BaseAudit audit = (BaseAudit) inner.get("rule");
                            return audit.getDetailNo().equals(e.getDetailNo());
                        }).collect(Collectors.toList());
                        if(CollectionUtil.isNotEmpty(inner_childs)){
                            inner_childs.forEach(ic->{
                                Map map_child = new HashMap();
                                BaseAudit baseAudit =(BaseAudit) ic.get("rule");
                                if(Objects.nonNull(baseAudit)){
                                    String inner_title = (StringUtils.isNotEmpty(baseAudit.getRuleOrigin())?baseAudit.getRuleOrigin()+"-":"")+baseAudit.getRuleCode()+"-"+baseAudit.getRuleName()+"；";
                                    map_child.put("title",inner_title);
                                }
                                List<BaseMedicalDetail> tables = (List<BaseMedicalDetail>)ic.get("details");
                                map_child.put("table",tables);
                                childs.add(map_child);
                            });
                        }
                        map.put("child",childs);
                        list_common.add(map);
                    }
                });
            }
            /*开始处理需要导出的数据----------结束*/
            DownloadWordDocument.generateAndDownloadWord(list_common,title,response);
        } catch (Exception e){
            log.info("导出相关记录异常", JSONUtil.toJsonStr(e));
        }
    }

    public void exportRelatedRecords4(ComprePopupQueryVO comprePopupQueryVO, String detailNo, String admissionNo, String batch, String auditScenario,String ruleOrigin, String hospitalId,HttpServletResponse response){
        List<ComprePopupDetailVO>  list_parent =  this.queryMedicalDetailByItem4(comprePopupQueryVO);
        log.info("list_parent:"+JSONUtil.toJsonStr(list_parent));
        List<Map> list = this.queryMedicalAuditRules4(detailNo, admissionNo, comprePopupQueryVO.getIsHis(), comprePopupQueryVO.getBatchNo(), auditScenario,ruleOrigin,hospitalId);
        log.info("list:"+JSONUtil.toJsonStr(list));
        try{
            // 获取当前日期时间（包含毫秒）
            LocalDateTime now = LocalDateTime.now();
            System.out.println("当前日期时间: " + now);
            // 格式化输出
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
            String formattedDateTime = now.format(formatter);
            System.out.println("格式化后的日期时间: " + formattedDateTime);
            String title = admissionNo+ (StringUtils.isBlank(detailNo)?"全部":"明细")+"相关记录"+ formattedDateTime;
            log.info("导出相关记录,标题:"+title);
            /*开始处理需要导出的数据----------开始*/
            List<Map> list_common = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(list_parent)){
                list_parent.forEach(e->{
                    if(CollectionUtil.isNotEmpty(e.getChild())){
                        e.getChild().forEach(p->{
                            if(StringUtils.isNotBlank(detailNo)){
                                if(!p.getDetailNo().equals(detailNo)){
                                    return;
                                }
                            }
                            Map map = new HashMap();
                            String child_title = "项目："+p.getItemId() +"      "+ p.getItemName()+"；  项目时间："+  DateFormatUtils.format(p.getItemDate(), "yyyy-MM-dd HH:mm:ss")+ "；    违反规则："+ p.getRuleNames()+"；"+"违规原因："+p.getRuleReasons() +";";
                            map.put("title",child_title);
                            List<Map> childs = new ArrayList<>();
                            List<Map> inner_childs = list.stream().filter(inner->{
                               /* List<BaseMedicalDetail> details = (List<BaseMedicalDetail>)inner.get("details");
                                    return details.stream().anyMatch(d->d.getDetailNo().equals(p.getDetailNo()));*/
                                BaseAudit audit = (BaseAudit) inner.get("rule");
                                return audit.getDetailNo().equals(p.getDetailNo());
                            }).collect(Collectors.toList());
                            if(CollectionUtil.isNotEmpty(inner_childs)){
                                inner_childs.forEach(ic->{
                                    Map map_child = new HashMap();
                                    BaseAudit baseAudit =(BaseAudit) ic.get("rule");
                                    if(Objects.nonNull(baseAudit)){
                                        String inner_title = (StringUtils.isNotEmpty(baseAudit.getRuleOrigin())?baseAudit.getRuleOrigin()+"-":"")+baseAudit.getRuleCode()+"-"+baseAudit.getRuleName()+"；";
                                        map_child.put("title",inner_title);
                                    }
                                    List<BaseMedicalDetail> tables = (List<BaseMedicalDetail>)ic.get("details");
                                    map_child.put("table",tables);
                                    childs.add(map_child);
                                });
                            }
                            map.put("child",childs);
                            list_common.add(map);
                        });
                    }else{
                        Map map = new HashMap();
                        String child_title = "项目："+ (StringUtils.isNotBlank(e.getItemId())?e.getItemId():"-") +"      "+ (StringUtils.isNotBlank( e.getItemName())?e.getItemName():"-")+"；  项目时间："+ (ObjectUtils.isEmpty(e.getItemDate())?"-":DateFormatUtils.format(e.getItemDate(), "yyyy-MM-dd HH:mm:ss"))+ "；    违反规则："+ e.getRuleNames()+"；"+"违规原因："+e.getRuleReasons() +";";
                        map.put("title",child_title);
                        if(StringUtils.isNotBlank(detailNo)){
                            if(!StringUtils.equals(e.getDetailNo(),detailNo)){
                                return;
                            }
                        }
                        List<Map> childs = new ArrayList<>();
                        List<Map> inner_childs = list.stream().filter(inner->{
                            BaseAudit audit = (BaseAudit) inner.get("rule");
                            return audit.getDetailNo().equals(e.getDetailNo());
                        }).collect(Collectors.toList());
                        if(CollectionUtil.isNotEmpty(inner_childs)){
                            inner_childs.forEach(ic->{
                                Map map_child = new HashMap();
                                BaseAudit baseAudit =(BaseAudit) ic.get("rule");
                                if(Objects.nonNull(baseAudit)){
                                    String inner_title = (StringUtils.isNotEmpty(baseAudit.getRuleOrigin())?baseAudit.getRuleOrigin()+"-":"")+baseAudit.getRuleCode()+"-"+baseAudit.getRuleName()+"；";
                                    map_child.put("title",inner_title);
                                }
                                List<BaseMedicalDetail> tables = (List<BaseMedicalDetail>)ic.get("details");
                                map_child.put("table",tables);
                                childs.add(map_child);
                            });
                        }
                        map.put("child",childs);
                        list_common.add(map);
                    }
                });
            }
            /*开始处理需要导出的数据----------结束*/
            DownloadWordDocument.generateAndDownloadWord(list_common,title,response);
        } catch (Exception e){
            log.info("导出相关记录异常", JSONUtil.toJsonStr(e));
        }
    }

}
