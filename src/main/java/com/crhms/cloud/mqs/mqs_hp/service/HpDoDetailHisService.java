package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpDoDetailHis;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpDoDetailHisMapper;
import com.crhms.cloud.mqs.mqs_hp.service.HpDoDetailHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-医嘱审核明细(HpDoDetailHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 10:04:33
 */
@Service("hpDoDetailHisService")
public class HpDoDetailHisService extends ServiceImpl< HpDoDetailHisMapper, HpDoDetailHis> {

}
