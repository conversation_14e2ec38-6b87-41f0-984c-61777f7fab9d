package com.crhms.cloud.mqs.basic.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.crhms.cloud.core.cache.RedisCache;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlan;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlanAudit;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrPlanAuditMapper;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrPlanMapper;
import com.crhms.cloud.mqs.sys.domain.*;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.mapper.*;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Log4j2
@Service
public class GlobalCache implements RedisCache {

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    SysRulesMapper sysRulesMapper;
    @Autowired
    SysSceneRuleMapper sysSceneRuleMapper;
    @Autowired
    BaseMedicalMapper baseMedicalMapper;
    @Autowired
    SysConfigMapper sysConfigMapper;

    @Autowired
    SysSceneMapper sysSceneMapper;

    @Autowired
    SysSceneFunctionMapper sysSceneFunctionMapper;

    @Autowired
    MrPlanMapper mrPlanMapper;

    @Autowired
    MrPlanAuditMapper mrPlanAuditMapper;

    @Override
    public void initCache() {
        // 初始化逻辑
        reflashRulesCache(null);
        reflashSceneRuleCache(null);
        //reflashAuditNumsCache(null);
        reflashSystemConfigCache(null);
        reflashSystemSceneConfigCache();
        reflashMrConfigCache();
        reflashSystemSceneFunctionConfigCache();
        reflashMrConfigAuditCache();
    }

    @Override
    public void reloadCache() {

    }

    @Override
    public void clearCache() {

    }

    /**
     * 刷新规则缓存
     *
     * @param hospitalId
     */
    public void reflashRulesCache(String hospitalId) {
        log.info("初始化审核规则 ==START==");
        List<SysRules> sysRules = sysRulesMapper.selectRulesCache(hospitalId);
        Map<String, List<SysRules>> collect = sysRules.stream().collect(Collectors.groupingBy(SysRules::getHospitalId));
        for (Map.Entry<String, List<SysRules>> listEntry : collect.entrySet()) {
            String key = listEntry.getKey();
            List<SysRules> value = listEntry.getValue();
            //规则编码-规则名称
            Map<String, Map> ruleCollect = value.stream().collect(Collectors.toMap(SysRules::getRuleCode, x -> {
                Map map = new HashMap<String,String>();
                map.put(SysRules.FIELD_RULE_CODE,x.getRuleCode());
                map.put(SysRules.FIELD_RULE_TYPE,x.getRuleType());
                map.put(SysRules.FIELD_RULE_NAME,x.getRuleName());
                map.put(SysRules.FIELD_RULE_LEVEL,x.getRuleLevel());
                map.put(SysRules.FIELD_RULE_LEVEL_NAME,x.getRuleLevelName());
                map.put(SysRuleLevel.FIELD_LEVEL_CONFIG,x.getLevelConfig());
                return map;
            }, (key1, key2) -> key2));
            redisTemplate.delete(StrUtil.format(GloablData.GLOBAL_SYS_RULES_KEY, key));
            redisTemplate.opsForHash().putAll(StrUtil.format(GloablData.GLOBAL_SYS_RULES_KEY, key), ruleCollect);
        }
        log.info("初始化审核规则 ==END==");
    }

    /**
     * 刷新场景规则缓存
     *
     * @param hospitalId
     */
    public void reflashSceneRuleCache(String hospitalId) {
        log.info("初始化审核场景规则==START==");
        List<SysSceneRule> sysSceneRules = sysRulesMapper.selectSceneRules(hospitalId);
        Map<String, List<SysSceneRule>> collect = sysSceneRules.stream().collect(Collectors.groupingBy(SysSceneRule::getHospitalId));
        for (Map.Entry<String, List<SysSceneRule>> listEntry : collect.entrySet()) {
            String key = listEntry.getKey();
            List<SysSceneRule> value = listEntry.getValue();
            //审核场景 - 审核规则list
            Map<String, List<String>> redisCollect = value.stream().collect(Collectors.groupingBy(SysSceneRule::getSceneCode, Collectors.mapping(SysSceneRule::getRuleCode, Collectors.toList())));
            redisTemplate.delete(StrUtil.format(GloablData.GLOBAL_SYS_SCENERULE_KEY, key));
            redisTemplate.opsForHash().putAll(StrUtil.format(GloablData.GLOBAL_SYS_SCENERULE_KEY, key), redisCollect);
        }
        log.info("初始化审核场景规则==END==");
    }

    /**
     * 加载近10天内的审核计数器
     *
     * @param hospitalId
     */
    public void reflashAuditNumsCache(String hospitalId) {
        log.info("初始化审核计数器==START==");

        for (AuditScenarioEnum scenarioEnum : AuditScenarioEnum.values()) {

            //查询单据审核次数统计
            List<Map<String,Object>> maps = baseMedicalMapper.selectAuditNums(scenarioEnum.getTableName(), hospitalId);

            Map<String, List<Map<String, Object>>> collect = maps.stream().collect(Collectors.groupingBy(x -> x.get("hospital_id").toString()));
            for (Map.Entry<String, List<Map<String, Object>>> listEntry : collect.entrySet()) {
                String key = listEntry.getKey();
                List<Map<String, Object>> value = listEntry.getValue();
                Map<String, Integer> redisCollect = value.stream().collect(Collectors.toMap(x -> x.get("no").toString(), x -> Integer.valueOf(x.get("nums").toString())));
                String redisKey = StrUtil.format(GloablData.GLOBAL_SYS_AUDITNUMS_KEY, key, scenarioEnum.getTableName());
                //redisTemplate.delete(redisKey);
                // 分批存储到 Redis
                batchPutToRedis(redisKey, redisCollect, 2000);
            }
        }

        log.info("初始化审核计数器==END==");
    }

    private void batchPutToRedis(String redisKey, Map<String, Integer> dataMap, int batchSize) {
        Map<String, Integer> batchMap = new HashMap<>();
        int counter = 0;
        for (Map.Entry<String, Integer> entry : dataMap.entrySet()) {
            batchMap.put(entry.getKey(), entry.getValue());
            counter++;
            if (counter == batchSize) {
                redisTemplate.opsForHash().putAll(redisKey, batchMap);
                batchMap.clear();
                counter = 0;
            }
        }
        // 处理最后一批不足 batchSize 的数据
        if (!batchMap.isEmpty()) {
            redisTemplate.opsForHash().putAll(redisKey, batchMap);
        }
    }

    /**
     * 刷新系统配置缓存
     * @param hospitalId
     */
    public void reflashSystemConfigCache(String hospitalId){
        log.info("初始化系统配置缓存 ==START==");
        QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<SysConfig>().eq(SysConfig.FIELD_ENABLE, "1");
        if (StrUtil.isNotEmpty(hospitalId)) {
            queryWrapper.eq(SysConfig.FIELD_HOSPITAL_ID, hospitalId);
        }
        List<SysConfig> sysConfigs = sysConfigMapper.selectList(queryWrapper);
        Map<String, List<SysConfig>> collect = sysConfigs.stream().collect(Collectors.groupingBy(SysConfig::getHospitalId));
        for (Map.Entry<String, List<SysConfig>> listEntry : collect.entrySet()) {
            String key = listEntry.getKey();
            List<SysConfig> value = listEntry.getValue();
            Map<String, String> redisMap = value.stream().collect(Collectors.toMap(SysConfig::getSysKey, SysConfig::getSysValue, (k1, k2) -> k1));
            String redisKey = StrUtil.format(GloablData.GLOBAL_SYS_CONFIG_KEY, key);
            redisTemplate.delete(redisKey);
            redisTemplate.opsForHash().putAll(redisKey, redisMap);
        }
        log.info("初始系统配置缓存 ==START==");
    }

    /**
     * 缓存场景配置
     * */
    public void reflashSystemSceneConfigCache(){
        //查询全部场景
        List<SysScene> sysSceneList = sysSceneMapper.selectList(new LambdaQueryWrapper<SysScene>());
        if(CollectionUtil.isEmpty(sysSceneList)){
            return;
        }
        //按照hospitalId进行分组 并缓存
        Map<String,List<SysScene>> sceneMap = sysSceneList.stream().collect(Collectors.groupingBy(SysScene::getHospitalId));
        sceneMap.forEach((k,v) -> {
            String redisKey = StrUtil.format(GloablData.GLOBAL_SYS_SCENE_KEY, k);
            redisTemplate.delete(redisKey);
            redisTemplate.opsForValue().set(redisKey, v);
        });
    }

    /**
     * 缓存场景配置
     * */
    public void reflashSystemSceneFunctionConfigCache(){
        //查询全部场景
        List<SysSceneFunction> sysSceneFunctionList = sysSceneFunctionMapper.selectList(new LambdaQueryWrapper<SysSceneFunction>());
        if(CollectionUtil.isEmpty(sysSceneFunctionList)){
            return;
        }
        //按照hospitalId进行分组 并缓存
        Map<String,List<SysSceneFunction>> sceneMap = sysSceneFunctionList.stream().collect(Collectors.groupingBy(SysSceneFunction::getHospitalId));
        sceneMap.forEach((k,v) -> {
            String redisKey = StrUtil.format(GloablData.GLOBAL_SYS_SCENE_FUNCTION_KEY, k);
            redisTemplate.delete(redisKey);
            redisTemplate.opsForValue().set(redisKey, v);
        });
    }

    /**
     * 缓存人工审核配置信息
     * */
    public void reflashMrConfigCache () {
        List<MrPlan> list = mrPlanMapper.selectList(new LambdaQueryWrapper<>());
        if(CollectionUtil.isEmpty(list)){
            return ;
        }
        //按照医院id进行分组
        Map<String,List<MrPlan>> planMap = list.stream().collect(Collectors.groupingBy(MrPlan::getHospitalId));
        planMap.forEach((k,v)->{
            String redisKey = StrUtil.format(GloablData.GLOBAL_MR_CONFIG_KEY,k);
            redisTemplate.delete(redisKey);
            redisTemplate.opsForValue().set(redisKey, v);
        });
    }

    /**
     * 缓存人工审核配置信息
     * */
    public void reflashMrConfigAuditCache () {
        List<MrPlanAudit> list = mrPlanAuditMapper.selectList(new LambdaQueryWrapper<>());
        if(CollectionUtil.isEmpty(list)){
            return ;
        }
        //按照医院id进行分组
        Map<String,List<MrPlanAudit>> planAuditMap = list.stream().collect(Collectors.groupingBy(MrPlanAudit::getHospitalId));
        planAuditMap.forEach((k,v)->{
            String redisKey = StrUtil.format(GloablData.GLOBAL_MR_CONFIG_AUDIT_KEY,k);
            redisTemplate.delete(redisKey);
            redisTemplate.opsForValue().set(redisKey, v);
        });
    }

}
