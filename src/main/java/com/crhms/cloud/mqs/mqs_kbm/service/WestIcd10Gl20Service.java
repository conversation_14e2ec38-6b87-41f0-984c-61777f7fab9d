package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.WestIcd10Gl20;
import com.crhms.cloud.mqs.mqs_kbm.mapper.WestIcd10Gl20Mapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 知识库管理-西医诊断手术-ICD-10国家临床版2.0
 *
 * @author: wangxingang
 * @date: 2023.02.06
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class WestIcd10Gl20Service extends ServiceImpl<WestIcd10Gl20Mapper, WestIcd10Gl20> {

    /**
     * ICD-10国家临床版2.0
     *
     * @param pageInfo 分页信息
     * @param code     诊断编码
     * @param name     诊断名称
     */
    public List<WestIcd10Gl20> queryGl20(Page<WestIcd10Gl20> pageInfo, String code, String name) {
        return page(pageInfo, new LambdaQueryWrapper<WestIcd10Gl20>()
                .like(StrUtil.isNotEmpty(code), WestIcd10Gl20::getCode, code)
                .like(StrUtil.isNotEmpty(name), WestIcd10Gl20::getName, name)).getRecords();
    }
}
