package com.crhms.cloud.mqs.mqs_kbm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

/**
 * 知识库管理-西医诊断手术-ICD-10医保版CHS1.0
 *
 * @author: wangxingang
 * @date: 2023.02.06
 */
@Data
@Builder
@TableName("mqs_kbm_west_icd10_yb10")
public class WestIcd10Yb10 {
    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 诊断代码
     */
    @TableField
    private String code;

    /**
     * 诊断名称
     */
    @TableField
    private String name;
}
