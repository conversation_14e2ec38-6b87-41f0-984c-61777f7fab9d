package com.crhms.cloud.mqs.mqs_kbm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 知识库管理-医保项目目录-国家医保医用耗材分类与代码
 *
 * @author: wangxingang
 * @date: 2023/02/03
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("mqs_kbm_item_consume")
public class ItemConsume {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 耗材代码
     */
    @TableField
    private String consumeCode;

    /**
     * 一级分类(学科、品类)
     */
    @TableField
    private String classFirst;

    /**
     * 二级分类(用途、品目)
     */
    @TableField
    private String classSecond;

    /**
     * 三级分类(部位、功能、品种)
     */
    @TableField
    private String classThird;

    /**
     * 通用名代码
     */
    @TableField
    private String commonNameCode;

    /**
     * 医保通用名
     */
    @TableField
    private String ybCommonName;

    /**
     * 材质
     */
    @TableField
    private String materialQuality;

    /**
     * 特征
     */
    @TableField
    private String feature;

    /**
     * 注册备案号
     */
    @TableField
    private String registrationNo;

    /**
     * 单件产品名称
     */
    @TableField
    private String singleProjectName;

    /**
     * 耗材企业
     */
    @TableField
    private String consumeEnterprise;

    /**
     * 规格型号数
     */
    @TableField
    private String specificationNo;

    /**
     * 注册备案人
     */
    @TableField
    private String registrant;
}
