package com.crhms.cloud.mqs.mqs_pm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物价管理-市级标准物价-更新日志Mapper
 *
 * @author: wangxingang
 * @date: 2023/02/01
 */

@Mapper
public interface PmStdLogMapper extends BaseMapper<PmStdLog> {

    List<PmStdLog> queryPage(@Param("categoryList") List<String> categoryList, @Param("updateBy") String updateBy, @Param("pageInfo") Page<PmStdLog> pageInfo);
}
