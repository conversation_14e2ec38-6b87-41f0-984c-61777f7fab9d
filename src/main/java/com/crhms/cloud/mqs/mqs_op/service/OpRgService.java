package com.crhms.cloud.mqs.mqs_op.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.service.BaseAuditService;
import com.crhms.cloud.mqs.basic.vo.BaseMedicalParam;
import com.crhms.cloud.mqs.basic.vo.HisBaseAuditDto;
import com.crhms.cloud.mqs.basic.vo.EngAuditResultVo;
import com.crhms.cloud.mqs.mqs_op.domain.OpRg;
import com.crhms.cloud.mqs.mqs_op.mapper.OpRgMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 门诊审核-门诊挂号审核(OpRg)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-19 09:39:26
 */
@Service("opRgService")
public class OpRgService extends ServiceImpl<OpRgMapper, OpRg> implements BaseAuditService {


    @Override
    public void beforeAudit(String batch, HisBaseAuditDto hisBaseAuditDto) {

    }

    @Override
    public void afterAudit(String batch, EngAuditResultVo result, HisBaseAuditDto hisBaseAuditDto, List<BaseMedicalParam> data) {

    }
}
