<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_pm.mapper.PmPriceCompMapper">

    <!--查询服务项目-->
    <select id="queryServiceItem" resultType="com.crhms.cloud.mqs.mqs_pm.domain.PmStdServiceItem">
        SELECT
            t.effect_time,
            t.project_code,
            t.project_name,
            unit,
            price
        FROM
            (
            SELECT
                effect_time,
                project_code,
                project_name,
                unit,
                price,
                ROW_NUMBER() over ( PARTITION BY project_code ORDER BY effect_time DESC ) AS rn
            FROM
                mqs_pm_std_service_item
            <where>
                <if test="projectCode != null and projectCode != '' ">
                    project_code like CONCAT('%', #{projectCode}, '%')
                </if>
                <if test="startPeriod != null and startPeriod != '' and endPeriod != null and endPeriod != '' ">
                    and effect_time between #{startPeriod} and #{endPeriod}
                </if>
            </where>
            ) t
        WHERE
        t.rn = 1
    </select>

    <!--查询药品-->
    <select id="queryDrug" resultType="com.crhms.cloud.mqs.mqs_pm.domain.PmStdDrug">
        SELECT
            t.effect_time,
            t.drug_code,
            t.drug_name,
            specification,
            price
        FROM
            (
                SELECT
                    effect_time,
                    drug_code,
                    drug_name,
                    specification,
                    price,
                    ROW_NUMBER() over ( PARTITION BY drug_code ORDER BY effect_time DESC ) AS rn
                FROM
                    mqs_pm_std_drug
                <where>
                    <if test="projectCode != null and projectCode != '' ">
                        drug_code like CONCAT('%', #{projectCode}, '%')
                    </if>
                    <if test="startPeriod != null and startPeriod != '' and endPeriod != null and endPeriod != '' ">
                        and effect_time between #{startPeriod} and #{endPeriod}
                    </if>
                </where>
            ) t
        WHERE
            t.rn = 1
    </select>

    <!--查询耗材-->
    <select id="queryConsume" resultType="com.crhms.cloud.mqs.mqs_pm.domain.PmStdConsume">
        SELECT
            t.effect_time,
            t.product_code,
            t.product_name,
            specification,
            price
        FROM
            (
                SELECT
                    effect_time,
                    product_code,
                    product_name,
                    specification,
                    price,
                    ROW_NUMBER() over ( PARTITION BY product_code ORDER BY effect_time DESC ) AS rn
                FROM
                    mqs_pm_std_consume
                <where>
                    <if test="projectCode != null and projectCode != '' ">
                        product_code like CONCAT('%', #{projectCode}, '%')
                    </if>
                    <if test="startPeriod != null and startPeriod != '' and endPeriod != null and endPeriod != '' ">
                        and effect_time between #{startPeriod} and #{endPeriod}
                    </if>
                </where>
            ) t
        WHERE
            t.rn = 1
    </select>
</mapper>