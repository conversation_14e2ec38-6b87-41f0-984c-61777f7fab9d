
package com.crhms.cloud.mqs.sys.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class BigDecimalUtils {

    public static BigDecimal dualAvg(BigDecimal amt, BigDecimal amt2){
        if(null==amt||null==amt2){
            return BigDecimal.ZERO;
        }
        if(amt.compareTo(BigDecimal.ZERO)==0){
            return new BigDecimal(0);
        }
        if(amt2.compareTo(BigDecimal.ZERO)==0){
            return new BigDecimal(0);
        }
        return amt.divide(amt2, 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal dualRate(BigDecimal amt,BigDecimal amt2){
        if(null==amt||null==amt2){
            return BigDecimal.ZERO;
        }
        if(amt.compareTo(BigDecimal.ZERO)==0){
            return new BigDecimal(0);
        }
        if(amt2.compareTo(BigDecimal.ZERO)==0){
            return new BigDecimal(100);
        }
        return amt.multiply(new BigDecimal(100)).divide(amt2, 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal dualAddRate(BigDecimal amt,BigDecimal amtAll){
        if(null==amt||null==amtAll){
            return BigDecimal.ZERO;
        }
        if(amt.compareTo(BigDecimal.ZERO)==0){
            return new BigDecimal(0);
        }
        if(amtAll.compareTo(BigDecimal.ZERO)==0){
            return new BigDecimal(100);
        }
        return amt.multiply(new BigDecimal(100)).divide(amtAll, 2, RoundingMode.HALF_UP).subtract(new BigDecimal(100));
    }

    public static BigDecimal dualAmtTransform(BigDecimal amt){
        if(null==amt){
            return BigDecimal.ZERO;
        }
        return amt.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal add(BigDecimal amt,BigDecimal amt1){

        if (amt == null && amt1 ==null){
            return BigDecimal.ZERO;
        }
        if (amt == null){
            return amt1;
        }
        if (amt1 ==null){
            return amt;
        }
        return amt.add(amt1);
    }

}