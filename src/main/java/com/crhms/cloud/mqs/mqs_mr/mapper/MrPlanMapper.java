package com.crhms.cloud.mqs.mqs_mr.mapper;

import com.crhms.cloud.mqs.mqs_mr.domain.MrPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 人工审核流程配置表(MrPlan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-06 14:58:23
 */

@Mapper
public interface MrPlanMapper extends BaseMapper<MrPlan> {


    List<MrPlan> listAuditScenario(@Param("hospitalId") String hospitalId);
}

