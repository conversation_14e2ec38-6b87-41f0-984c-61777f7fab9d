<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_hp.mapper.HpSettleMapper">

    <!--查询表的某个字段的去重值-->
    <select id="distinctColValue" resultType="java.lang.Object">
        select distinct `${tableColName}`
        from ${tableName}
        where `${tableColName}` is not null
    </select>

    <!--查询表的多个字段的去重值-->
    <select id="distinctColsValue" resultType="java.lang.Object">
        select distinct t.v_data
        from
        <foreach collection="tableColNames" open="(" close=")" separator=" union all " item="tableColName">
            select distinct `${tableColName}` as v_data from ${tableName}
        </foreach>
        t where t.v_data is not null
    </select>

    <!--查询最大结算日期-->
    <select id="queryMaxBillDate" resultType="java.util.Date">
        select max(bill_date) from mqs_hp_settle
    </select>

    <!--查询最小结算日期-->
    <select id="queryMinBillDate" resultType="java.util.Date">
        select min(bill_date) from mqs_hp_settle
    </select>
</mapper>

