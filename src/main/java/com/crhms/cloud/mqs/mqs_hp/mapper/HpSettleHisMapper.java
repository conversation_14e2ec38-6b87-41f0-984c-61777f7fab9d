package com.crhms.cloud.mqs.mqs_hp.mapper;

import com.crhms.cloud.mqs.mqs_hp.domain.HpSettleHis;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 住院审核-结算审核-历史表(HpSettleHis)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-17 10:00:58
 */

@Mapper
public interface HpSettleHisMapper extends BaseMapper<HpSettleHis> {

    /**
     * 通过ID查询单条数据
     *
     * @param  主键
     * @return 实例对象
     */
    HpSettleHis queryById( );

    /**
     * 查询指定行数据
     *
     * @param hpSettleHis 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<HpSettleHis> queryAllByLimit(HpSettleHis hpSettleHis, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param hpSettleHis 查询条件
     * @return 总行数
     */
    long count(HpSettleHis hpSettleHis);

    /**
     * 新增数据
     *
     * @param hpSettleHis 实例对象
     * @return 影响行数
     */
    int insert(HpSettleHis hpSettleHis);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<HpSettleHis> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<HpSettleHis> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<HpSettleHis> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<HpSettleHis> entities);

    /**
     * 修改数据
     *
     * @param hpSettleHis 实例对象
     * @return 影响行数
     */
    int update(HpSettleHis hpSettleHis);

    /**
     * 通过主键删除数据
     *
     * @param  主键
     * @return 影响行数
     */
    int deleteById( );

}

