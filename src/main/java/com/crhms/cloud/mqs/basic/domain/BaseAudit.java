package com.crhms.cloud.mqs.basic.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 审核结果表(BaseAudit)实体类
 *
 * <AUTHOR>
 * @since 2023-01-18 15:53:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class BaseAudit extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -39269360572574815L;

    public static final String FIELD_ID = "id";
    public static final String FIELD_DETAIL_NO = "detail_no";
    public static final String FIELD_NO = "no";
    public static final String FIELD_RULE_CODE = "rule_code";
    public static final String FIELD_RULE_NAME = "rule_name";
    public static final String FIELD_RULE_REASON = "rule_reason";
    public static final String FIELD_BATCH_NO = "batch_no";
    public static final String FIELD_LAST_UPDATED_BY = "last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE = "last_updated_date";
    public static final String FIELD_CREATED_BY = "created_by";
    public static final String FIELD_CREATED_DATE = "created_date";
    public static final String FIELD_HOSPITAL_ID = "hospital_id";

    /**
     * 表id
     */
    private Integer id;

    /**
     * 费用明细流水号
     */
    private String detailNo;
    /**
     * 医嘱明细流水号
     */
    private String orderDetailNo;
    /**
     * 单据号
     */
    private String no;
    /**
     * 就诊流水号
     */
    private String admissionNo;
    /**
     * 违规规则
     */
    private String ruleCode;
    /**
     * 违规规则名称
     */
    private String ruleName;
    /**
     * 结果类型
     */
    private String ruleType;
    /**
     * 结果类型名称
     */
    private String ruleTypeName;

    /**
     * 1.院内 2医保
     */
    @DictTranslate(dictCode = "LOCAL_RULE_ORIGIN", dictSplit = "|")
    private String ruleOrigin;

    /**
     * 医保信息平台结果唯一id
     */
    private String jrId;
    /**
     * 违规原因
     */
    private String ruleReason;
    /**
     * 项目编码
     */
    private String itemId;
    /**
     * 项目名称
     */
    private String itemName;
    /**
     * 项目日期
     */
    private Date itemDate;
    /**
     * 项目类型编码(A-Z大类)
     */
    @TableField
    private String itemTypeCode;
    /**
     * 项目类型名称
     */
    @TableField
    private String itemTypeName;
    /**
     * 数量
     */
    private BigDecimal numbers;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 总费用
     */
    private BigDecimal costs;

    /**
     * 批次号
     */
    private String batchNo;


    /**
     * 违规相关明细 （，分割）
     */
    private String related;
    /**
     * 完整提示
     */
    private String fullTip;
    /**
     * 分组编码
     */
    private String groupCode;
    /**
     * 违规比例
     */
    private String violationRatio;
    /**
     * 违规金额
     */
    private BigDecimal violationAmount;
    /**
     * 规则设置提示
     */
    private String tipsCode4Hospital;

    //非数据库字段 是否违反阻断规则
    private String isBlock;

    //违规计费标记，0未违规 1违规，默认1
    private String violationType;

    /**
     * 院区id
     */
    private String hospitalId;

    /**
     * 预留字段1
     */
    private String expandField1;
    /**
     * 预留字段2
     */
    private String expandField2;
    /**
     * 预留字段3
     */
    private String expandField3;
    /**
     * 预留字段4
     */
    private String expandField4;
    /**
     * 预留字段5
     */
    private String expandField5;
}

