package com.crhms.cloud.mqs.sys.controller;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.sys.domain.SysRules;
import com.crhms.cloud.mqs.sys.dto.RuleConfigQueryVO;
import com.crhms.cloud.mqs.sys.service.SysRulesService;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 运营管理-规则管理(SysRules)表控制层
 *
 * <AUTHOR>
 * @since 2022-12-16 16:52:45
 */
@RestController
@RequestMapping("api/mqs/sysRules")
public class SysRulesController {
    /**
     * 服务对象
     */
    @Resource
    private SysRulesService sysRulesService;

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @GetMapping("/queryByPage")
    public ResponseEntity<List<SysRules>> queryByPage(@RequestParam(value = "rule", required = false) String rule,
                                                      @RequestParam(value = "broadHeading", required = false) String broadHeading,
                                                      @RequestParam(value = "ruleLevel", required = false) String ruleLevel,
                                                      @RequestParam(value = "isHis", required = false) String isHis,
                                                      @RequestParam(value = "isMain", required = false) String isMain,
                                                      @RequestParam(value = "ruleType", required = false) String ruleType,
                                                      @RequestParam(value = "page", defaultValue = "1") int page,
                                                      @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {

        Page pageRequest = PageUtil.getPage(page, pageSize);
        return new ResponseEntity(this.sysRulesService.queryByPage(rule, broadHeading, ruleLevel, isHis, isMain, ruleType, LoginContext.getHospitalId(), pageRequest), PageUtil.getTotalHeader(pageRequest), HttpStatus.OK);
    }


    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @GetMapping("/seletRuleList")
    public ResponseEntity<List<SysRules>> seletRuleList(@RequestParam(value = "rule", required = false) String rule) {

        return ResponseEntity.ok(this.sysRulesService.seletRuleList(rule, LoginContext.getHospitalId()));
    }

    /**
     * 编辑/新增数据
     *
     * @param sysRules 实体
     * @return 编辑结果
     */
    @PostMapping("/edit")
    public ResponseEntity<SysRules> edit(SysRules sysRules) {
        return ResponseEntity.ok(this.sysRulesService.insertData(sysRules));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping("/delete")
    public ResponseEntity<Boolean> deleteById(Long id) {
        return ResponseEntity.ok(sysRulesService.deleteById(id));
    }

    /**
     * 规则导入
     *
     * @param file
     * @return
     */
    @PostMapping("/import")
    public ResponseEntity<List<Map<String, String>>> importExcel(@RequestBody MultipartFile file) {
        return this.sysRulesService.importExcel(file);
    }


    /**
     * 规则设置页面 分页查询接口
     *
     * @param queryVO 查询参数
     * @return response entity
     */
    @PostMapping("/page")
    public ResponseEntity<List<SysRules>> queryPageByVO(@RequestBody RuleConfigQueryVO queryVO) {
        Page<SysRules> pageInfo = PageUtil.getPage(queryVO.getPage(), queryVO.getPageSize());
        List<SysRules> result = sysRulesService.queryPageByVO(pageInfo, queryVO);
        return new ResponseEntity<>(result, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /** 
     * 同步引擎内置的规则列表到数据库
     * <AUTHOR>
     * @date 2023/3/20
     **/
    @GetMapping("/sync")
    public ResponseEntity syncEngineRule(){
        sysRulesService.syncEngineRules();

        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * 查询规则逻辑说明
     * <AUTHOR>
     * @date 2023/3/20
     **/
    @GetMapping("/getRuleLogicDesc")
    public ResponseEntity<String> getRuleLogicDesc(@RequestParam(value = "ruleCode") String ruleCode){
        return ResponseEntity.ok(sysRulesService.getRuleLogicDesc(ruleCode,LoginContext.getHospitalId()));
    }

    /**
     * 规则数据说明
     * <AUTHOR>
     * @date 2023/3/20
     **/
    @GetMapping("/getRuleDataDesc")
    public ResponseEntity<String> getRuleDataDesc(@RequestParam(value = "ruleCode") String ruleCode){
        return ResponseEntity.ok(sysRulesService.getRuleDataDesc(ruleCode,LoginContext.getHospitalId()));
    }


    /**
     * 新增/修改规则配置信息
     *
     * @param rule 规则信息
     * @return ok
     */
    @PostMapping("/submit")
    public ResponseEntity<Object> submit(@RequestBody(required = false) SysRules rule) {
        sysRulesService.submit(rule);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    /**
     * 规则导出Excel
     *
     * @param response 请求响应
     * @param queryVO  查询参数
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody RuleConfigQueryVO queryVO) {
        sysRulesService.export(response, queryVO);
    }

    /**
     * 根据id批量删除 - 逻辑删除
     * @param ids id集合
     * @return ok
     */
    @PostMapping("/delete")
    public ResponseEntity<?> deleteByIds(@RequestBody List<Long> ids) {
        sysRulesService.deleteByIds(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}

