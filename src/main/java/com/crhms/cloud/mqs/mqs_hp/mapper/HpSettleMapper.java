package com.crhms.cloud.mqs.mqs_hp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.mqs_hp.domain.HpSettle;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 住院审核-结算审核(HpSettle)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-17 09:48:17
 */

@Mapper
public interface HpSettleMapper extends BaseMapper<HpSettle> {

    /**
     * 查询表的某个字段的去重值
     * <AUTHOR>
     * @date 2023/3/28
     * @param tableName     表名
     * @param tableColName  字段名
     **/
    List<Object> distinctColValue(@Param(value = "tableName") String tableName,
                                  @Param(value = "tableColName") String tableColName);

    /**
     * 查询表的多个字段的去重值
     * <AUTHOR>
     * @date 2023/3/28
     * @param tableName     表名
     * @param tableColNames 字段名集合
     **/
    List<Object> distinctColsValue(@Param(value = "tableName") String tableName,
                                   @Param(value = "tableColNames") List<String> tableColNames);

    /**
     * 查询最大结算日期
     * <AUTHOR>
     * @date 2023/3/30
     **/
    Date queryMaxBillDate();

    /**
     * 查询最小结算日期
     * <AUTHOR>
     * @date 2023/3/30
     **/
    Date queryMinBillDate();



}

