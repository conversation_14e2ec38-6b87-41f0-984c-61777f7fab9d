package com.crhms.cloud.mqs.mqs_mr.manager;


import com.crhms.cloud.mqs.basic.domain.BaseAudit;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalStats;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlan;

import java.util.List;
import java.util.Map;

/*
*  审核流程节点算法实现
* */
public class DataReviewConditions extends MrPlanFilter{
    @Override
    public boolean filter(BaseMedicalCase medicalCase, MrPlan mrPlan, List<BaseAudit> baseAudits, String filterFlag, List<String> sceneCodes, String auditScenario, Map<String,List<BaseMedicalStats>> baseMedicalStatsMap) {

        //跳过审核流程节点
        if("DataReviewConditions".equals(filterFlag)) {
            return next(medicalCase,mrPlan,baseAudits,"",sceneCodes,auditScenario,  baseMedicalStatsMap);
        }
        // 判断当前主单的所属场景是否在上述场景内
        boolean result = sceneCodes.contains(auditScenario);
        //如果是或的关系
        if("4".equals(mrPlan.getDataConditions())){
            if(result){
                return next(medicalCase,mrPlan,baseAudits,"DataBussinessConditions",sceneCodes,auditScenario, baseMedicalStatsMap);
            } else {
                return next(medicalCase,mrPlan,baseAudits,filterFlag,sceneCodes,auditScenario, baseMedicalStatsMap);
            }
        } else {
            //返回结果
            return result && next(medicalCase,mrPlan,baseAudits,filterFlag,sceneCodes,auditScenario, baseMedicalStatsMap);
        }
    }
}
