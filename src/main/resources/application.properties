crhms.sso-client_id=mqs2
crhms.sso-client_secret=crhms-mqs2
spring.main.allow-bean-definition-overriding=true


##########################################################################################
###########################     青岛产品服务通用配置（禁止修改）     ###########################
##########################################################################################
## 默认数据库 Mysql
## privateKey:MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAjel/652ANwojh97q8OE9k33uxWTsQT8gmj0iyDm91exQeRpNMwDIMO+HYYJl1ldj+qnIlUPYyUQq81cpgF20BwIDAQABAkAJCT4w9YVte8LJawNQtTbjhlhGz2dPvEJKL+xgDXUuAtbIkw8ao/X6+ZsxBOXSTLXEOl0EQT6qOB7zZjaBeNSZAiEAyfT35bAeQshkln/4GXbDdVA79QCOz+oI28CCJeN89DMCIQCz4yuQkhhIvYiJx7UwgoNNomJ2W3RhTjiZhNe4HUGM3QIhALMVGKWCULMjg565B0V9PMFQTnpo7jqL6yMCR+ZVHT5lAiBtAU6n12+xvHBdlU/damjohWsG75pRUJYJqS8TGV9cXQIgbROxAsHMJidxzEghdXdzPNFk3IG5r++rsKtYaIhn1d8=
spring.datasource.dynamic.primary=${DATASOURCE_PRIMARY:mysql}
spring.datasource.dynamic.public-key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAI3pf+udgDcKI4fe6vDhPZN97sVk7EE/IJo9Isg5vdXsUHkaTTMAyDDvh2GCZdZXY/qpyJVD2MlEKvNXKYBdtAcCAwEAAQ==

#spring.datasource.dynamic.datasource.kingbase.url=${KING_DATASOURCE_URL:********************************************}
#spring.datasource.dynamic.datasource.kingbase.username=${KING_DATASOURCE_USERNAME:mqs2_dev}
#spring.datasource.dynamic.datasource.kingbase.password=${KING_DATASOURCE_PASSWORD:ENC(MNY0pHEwj4grnCZ/WHBY9vpjeQSokmi33577zEtJECDrDgl0XdNkfCBaT+BP0RAdfDpFEYhNt9HJI3uyxUZxqg==)}
#spring.datasource.dynamic.datasource.kingbase.driver-class-name=org.postgresql.Driver
#spring.datasource.dynamic.datasource.kingbase.druid.initialSize=50
#spring.datasource.dynamic.datasource.kingbase.druid.maxActive=500
#spring.datasource.dynamic.datasource.kingbase.druid.min-idle=20


spring.datasource.dynamic.datasource.mysql.url=${DATASOURCE_URL:****************************************************************************************************************************************}
spring.datasource.dynamic.datasource.mysql.username=${DATASOURCE_USERNAME:mqs2_dev}
spring.datasource.dynamic.datasource.mysql.password=${DATASOURCE_PASSWORD:ENC(MNY0pHEwj4grnCZ/WHBY9vpjeQSokmi33577zEtJECDrDgl0XdNkfCBaT+BP0RAdfDpFEYhNt9HJI3uyxUZxqg==)}
spring.datasource.dynamic.datasource.mysql.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.mysql.druid.initialSize=50
spring.datasource.dynamic.datasource.mysql.druid.maxActive=500
spring.datasource.dynamic.datasource.mysql.druid.min-idle=20

## 分析数据库 Clickhouse
spring.datasource.dynamic.datasource.clickhouse.url=${CK_DATASOURCE_URL:*********************************************}
spring.datasource.dynamic.datasource.clickhouse.username=${CK_DATASOURCE_USERNAME:default}
spring.datasource.dynamic.datasource.clickhouse.password=${CK_DATASOURCE_PASSWORD:ENC(MNY0pHEwj4grnCZ/WHBY9vpjeQSokmi33577zEtJECDrDgl0XdNkfCBaT+BP0RAdfDpFEYhNt9HJI3uyxUZxqg==)}
spring.datasource.dynamic.datasource.clickhouse.driver-class-name=ru.yandex.clickhouse.ClickHouseDriver
spring.datasource.dynamic.datasource.clickhouse.druid.initial-size=50
spring.datasource.dynamic.datasource.clickhouse.druid.max-active=500
spring.datasource.dynamic.datasource.clickhouse.druid.min-idle=20
