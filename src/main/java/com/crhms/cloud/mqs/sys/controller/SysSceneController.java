package com.crhms.cloud.mqs.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.sys.domain.*;
import com.crhms.cloud.mqs.sys.dto.SceneRuleQueryVO;
import com.crhms.cloud.mqs.sys.dto.SysSceneDTO;
import com.crhms.cloud.mqs.sys.service.SysReasonTypeService;
import com.crhms.cloud.mqs.sys.service.SysSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 运营管理-审核管理(SysScene)表控制层
 *
 * <AUTHOR>
 * @since 2022-12-21 13:37:48
 */
@RestController
@RequestMapping("api/mqs/sysScene")
public class SysSceneController {
    /**
     * 服务对象
     */
    @Autowired
    private SysSceneService sysSceneService;
    @Autowired
    private SysReasonTypeService sysReasonTypeService;

    /**
     * 查询场景
     *
     * @param
     * @return
     */
    @GetMapping("/querySceneList")
    public ResponseEntity<List<SysScene>> querySceneList(@RequestParam(value = "scene", required = false) String scene,
                                                         @RequestParam(value = "enable", required = false) String enable) {
        return ResponseEntity.ok(this.sysSceneService.querySceneList(scene, enable, LoginContext.getHospitalId()));

    }
    /**
     * 查询启用的业务(门诊 住院)
     *
     * @param
     * @return
     */
    @GetMapping("/queryEnableSceneType")
    public ResponseEntity< List<Map<String, String>>> queryEnableScene() {
        return ResponseEntity.ok(this.sysSceneService.queryEnableScene(LoginContext.getHospitalId()));

    }


    /**
     * 列表查询
     *
     * @return 查询结果
     */
    @GetMapping("/queryList")
    public ResponseEntity<List<SysScene>> queryList() {
        return ResponseEntity.ok(this.sysSceneService.queryList(LoginContext.getHospitalId()));
    }

    /**
     * 查询场景下规则集
     *
     * @return 查询结果
     */
    @PostMapping("/queryRulesList")
    public ResponseEntity<List<SysRules>> queryRulesList(@RequestBody SceneRuleQueryVO queryVO) {
        Page<Object> pageInfo = PageUtil.getPage(queryVO.getPage(), queryVO.getPageSize());
        return new ResponseEntity<>(this.sysSceneService.queryRulesList(pageInfo, queryVO.getSceneCode(), queryVO.getRule(), queryVO.getRuleLevelList(), LoginContext.getHospitalId()), PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }


    /**
     * 查询可添加到当前审核场景的规则
     *
     * @param sceneCode 场景编码
     * @param rule      规则名称或编码模糊查询
     * <AUTHOR>
     * @date 2023.02.20
     */
    @GetMapping("/queryAddableRules")
    public ResponseEntity<List<SysRules>> queryAddableRules(@RequestParam(value = "sceneCode") String sceneCode,
                                                            @RequestParam(value = "rule", required = false) String rule) {
        return ResponseEntity.ok(sysSceneService.queryAddableRules(sceneCode, rule));
    }


    /**
     * 添加规则到场景中
     *
     * @param sceneCode    场景编码
     * @param ruleCodeList 规则编码集合
     * <AUTHOR>
     * @date 2023.02.20
     */
    @PostMapping("/add")
    public ResponseEntity<Object> addRulesToScene(@RequestParam(value = "sceneCode") String sceneCode,
                                                  @RequestBody(required = false) List<String> ruleCodeList) {
        sysSceneService.addRulesToScene(sceneCode, ruleCodeList);
        return ResponseEntity.ok().build();
    }


    /**
     * 批量删除
     *
     * @param ids 规则id集合
     * <AUTHOR>
     * @date 2023.02.20
     */
    @PostMapping("/delete")
    public ResponseEntity<Object> delete(@RequestBody(required = false) List<Long> ids) {
        sysSceneService.delete(ids);
        return ResponseEntity.ok().build();
    }


    /**
     * 编辑、新增
     *
     * @return 查询结果
     */
    @PostMapping("/edit")
    public ResponseEntity editData(@RequestBody SysScene sysScene) {
        this.sysSceneService.editData(sysScene);
        return ResponseEntity.ok(HttpStatus.OK);
    }


    /**
     * 查询场景反馈原因配置
     *
     * @return
     */
    @GetMapping("/reasonType/queryList")
    public ResponseEntity<List<SysReasonType>> queryReasonTypeList(@RequestParam(value = "sceneCode") String sceneCode) {

        return ResponseEntity.ok(sysReasonTypeService.list(new QueryWrapper<SysReasonType>().eq(SysReasonType.FIELD_SCENE_CODE,sceneCode).eq(SysReasonType.FIELD_HOSPITAL_ID,LoginContext.getHospitalId())));
    }

    /**
     * 单条删除场景反馈原因配置
     *
     * @return 场景配置页面信息
     */
    @GetMapping("/reasonType/delete")
    public ResponseEntity deleteReasonType(@RequestParam(value = "id") Long id) {
        sysReasonTypeService.removeById(id);
        return ResponseEntity.ok("删除成功！");
    }
    /**
     * 保存场景反馈原因配置
     *
     * @return
     */
    @PostMapping("/reasonType/submit")
    public ResponseEntity submitReasonType(@RequestParam(value = "sceneCode") String sceneCode, @RequestBody List<SysReasonType> sysReasonTypes) {
        sysReasonTypeService.remove(new QueryWrapper<SysReasonType>().eq(SysReasonType.FIELD_SCENE_CODE,sceneCode).eq(SysReasonType.FIELD_HOSPITAL_ID,LoginContext.getHospitalId()));
        for (SysReasonType sysReasonType : sysReasonTypes) {
            sysReasonType.setId(null);
            sysReasonType.setSceneCode(sceneCode);
            sysReasonType.setHospitalId(LoginContext.getHospitalId());
            sysReasonType.setLastUpdatedBy(null);
            sysReasonType.setLastUpdatedDate(null);
            sysReasonType.setCreatedBy(null);
            sysReasonType.setCreatedDate(null);
        }
        sysReasonTypeService.saveOrUpdateBatch(sysReasonTypes);
        return ResponseEntity.ok("保存成功！");
    }


    /**
     * 查询审核场景 - 功能配置列表
     *
     * @param
     * @return
     */
    @GetMapping("function/queryList")
    public ResponseEntity<List<SysSceneFunction>> querySceneFunctionList() {
        return ResponseEntity.ok(this.sysSceneService.querySceneFunctionList(LoginContext.getHospitalId()));
    }
    /**
     * 查询审核场景 - 功能配置
     *
     * @param
     * @return
     */
    @GetMapping("function/queryOne")
    public ResponseEntity<SysSceneFunction> querySceneFunctionOne(@RequestParam(value = "sceneCode") String sceneCode,
                                                                  @RequestParam(value = "functionCode") String functionCode) {
        return ResponseEntity.ok(this.sysSceneService.querySceneFunctionOne(sceneCode, functionCode, LoginContext.getHospitalId()));
    }
    /**
     * 编辑审核场景 - 功能配置列表
     *
     * @param
     * @return
     */
    @PostMapping("function/edit")
    public ResponseEntity editSceneFunction(@RequestBody List<SysSceneFunction> functionList) {
        this.sysSceneService.editSceneFunction(functionList, LoginContext.getHospitalId());
        return ResponseEntity.ok("保存成功！");
    }

    /**
     * 全场景配置查询
     *
     * @param
     * @return
     */
    @GetMapping("queryAllScene")
    public ResponseEntity<List<SysMainScene>> queryAllScene() {
        return ResponseEntity.ok(this.sysSceneService.queryAllScene(LoginContext.getHospitalId()));
    }

    /**
     * 子场景弹窗功能配置查询
     *
     * @param
     * @return
     */
    @GetMapping("queryScene")
    public ResponseEntity<List<SysSceneDTO>> queryScene() {
        return ResponseEntity.ok(this.sysSceneService.queryScene(LoginContext.getHospitalId()));
    }

    /**
     * 全场景配置编辑
     *
     * @param
     * @return
     */
    @PostMapping("submitAllScene")
    public ResponseEntity submitAllScene(@RequestBody List<SysMainScene> list) {
        sysSceneService.submitAllScene(list,LoginContext.getHospitalId());
        return ResponseEntity.ok("保存成功");
    }

    /**
     * 子场景弹窗功能配置编辑
     *
     * @param
     * @return
     */
    @PostMapping("submitScene")
    public ResponseEntity submitScene(@RequestBody List<SysSceneDTO> list) {
        sysSceneService.submitScene(list,LoginContext.getHospitalId());
        return ResponseEntity.ok("保存成功");
    }

    /**
     * 全场景配置默认恢复
     *
     * @param
     * @return
     */
    @GetMapping("restDefulatAllScene")
    public ResponseEntity restDefulatAllScene() {
        sysSceneService.restDefulatAllScene();
        return ResponseEntity.ok("恢复成功");
    }

    /**
     * 子场景及配置默认恢复
     *
     * @param
     * @return
     */
    @GetMapping("restDefaultScene")
    public ResponseEntity restDefaultScene() {
        sysSceneService.restDefaultScene();
        return ResponseEntity.ok("恢复成功");
    }

    /**
     * 查询枚举类所有子场景
     *
     * @param
     * @return
     */
    @GetMapping("getAllScenes")
    public ResponseEntity<List<SysScene>> getAllScenes() {
        return ResponseEntity.ok(sysSceneService.getAllScenes());
    }

    /**
     * 询子场景列表以及根据展示维度以及展示形式以及场景编码以及是否开启过滤
     *
     * @param
     * @return
     */
    @GetMapping("getScenesList")
    public ResponseEntity<List<SysSceneDTO>> getScenesList(@RequestParam String codePrefix,
                                                          @RequestParam String enable) {
        return ResponseEntity.ok(sysSceneService.getScenesList (codePrefix ,enable));
    }

    /**
     * 查询所有主场景
     *
     * @param
     * @return
     */
    @GetMapping("queryAllMainScenes")
    public ResponseEntity<List<SysMainScene>> queryAllMainSscenes() {
        return ResponseEntity.ok(sysSceneService.queryAllMainSscenes ());
    }

    /**
     * 根据主场景code查询子场景
     *
     * @param
     * @return
     */
    @GetMapping("queryByMainSceneCode")
    public ResponseEntity<List<SysSceneDTO>> queryByMainSceneCode(@RequestParam String mainCode,@RequestParam(required = false) String enable) {
        return ResponseEntity.ok(sysSceneService.queryByMainSceneCode (mainCode,enable));
    }


    /**
     * 查询住院诊疗+门诊诊疗配置信息
     *
     * @param
     * @return
     */
    @GetMapping("queryTrList")
    public ResponseEntity<List<SysSceneFunction>> queryTrList() {
        return ResponseEntity.ok(sysSceneService.queryTrList ());
    }

    /**
     * 查询住院诊疗+门诊诊疗配置信息
     *
     * @param
     * @return
     */
    @PostMapping("submitTr")
    public ResponseEntity submitTr(@RequestBody List<SysSceneDTO> list) {
        sysSceneService.submitTr(list);
        return ResponseEntity.ok("编辑成功");
    }

}

