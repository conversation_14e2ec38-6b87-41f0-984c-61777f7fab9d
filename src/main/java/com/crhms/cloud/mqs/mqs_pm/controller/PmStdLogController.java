package com.crhms.cloud.mqs.mqs_pm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdLog;
import com.crhms.cloud.mqs.mqs_pm.service.PmStdLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 物价管理-市级标准物价-更新日志controller
 *
 * @author: wangxingang
 * @date: 2023/02/01
 */

@RestController
@RequestMapping("/api/mqs/pmStd/log")
public class PmStdLogController {

    @Autowired
    private PmStdLogService service;


    /**
     * 分页查询
     *
     * @param categoryList 更新大类
     * @param updateBy     更新人
     * @param page         页序号
     * @param pageSize     页大小
     */
    @PostMapping("/page")
    public ResponseEntity<List<PmStdLog>> queryPage(@RequestBody(required = false) List<String> categoryList,
                                                    @RequestParam(value = "updateBy", required = false) String updateBy,
                                                    @RequestParam(value = "page", defaultValue = "1") int page,
                                                    @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        Page<PmStdLog> pageInfo = PageUtil.getPage(page, pageSize);
        List<PmStdLog> result = service.queryPage(categoryList, updateBy, pageInfo);
        return new ResponseEntity<>(result, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }
}
