<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_mr.mapper.MrLogMapper">

    <resultMap type="com.crhms.cloud.mqs.mqs_mr.domain.MrLog" id="MrLogMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="no" column="no" jdbcType="VARCHAR"/>
        <result property="admissionNo" column="admission_no" jdbcType="VARCHAR"/>
        <result property="mrTime" column="mr_time" jdbcType="TIMESTAMP"/>
        <result property="mrStatus" column="mr_status" jdbcType="VARCHAR"/>
        <result property="mrOpinion" column="mr_opinion" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="TIMESTAMP"/>
        <result property="userName" column="user_name" jdbcType="TIMESTAMP"/>
        <result property="hospitalId" column="hospital_id" jdbcType="VARCHAR"/>
    </resultMap>
</mapper>

