package com.crhms.cloud.mqs.basic.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *  综合弹窗明细列表VO
 */
@Data
public class ComprePopupDetailVO {

    /**
     * 明细流水号
     */

    private String detailNo;
    /**
     * 就诊流水号
     */
    private String admissionNo;
    /**
     * 单据号
     */
    private String no;

    /**
     * 明细个数
     */
    private String detailNum;

    @DictTranslate(dictCode = "LOCLA_SELF_EXPENSE", dictText = "selfExpenseCh",dictSplit = "/")
    private String selfExpense;
    private String selfExpenseCh;

    /**
     * 审核渠道
     */
    @DictTranslate(dictCode = "LOCLA_AUDITSOURCE", dictText = "auditSourceName",dictSplit = ",")
    private String auditSource;
    private String auditSourceName;

    /**
     * 违规结果类型最高违规级别)
     */
    private String ruleType;

    /**
     * 违规名称
     */
    private String ruleTypeName;

    /**
     * 违规名称集
     */
    private String ruleNames;

    /**
     * 违规原因集
     */
    private String ruleReasons;

    /**
     * 项目编码
     */
    private String itemId;
    /**
     * 项目名称
     */
    private String itemName;
    /**
     * 项目类型名称
     */

    private String itemTypeName;

    /**
     * 项目日期
     */
    private Date itemDate;

    /**
     * 数量
     */
    private BigDecimal numbers;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 包装单位
     */
    private String usageUnit;
    /**
     * 总费用
     */
    private BigDecimal costs;
    /**
     * 计费数量
     */
    private BigDecimal costNumber;
    /**
     * 计费金额
     */
    private BigDecimal costCosts;

    /**
     * 规格
     */
    private String specification;

    /**
     * 反馈类型集
     */
    private String reasonTypes;

    /**
     * 反馈原因集
     */
    private String reasonDess;

    /**
     * 备注
     */
    private String memo;

    //是否忽略（1-是，0-否）
    private String isIgnore;

    //甲乙类
    private String categoryName;

    //是否增量（0-否,1-是）用于标识该条明细是否为增量明细（增量操作包含insert以及update）
    private String operationType;

    //旧自费状态
    private String oldSelfExpense;

    //是否出院带药
    @DictTranslate(dictCode = "LOCAL_YN_CODE")
    private String outpatientMedication;

    //用药途径
    private String routeAdministration;

    private List<ComprePopupDetailVO> child;

}
