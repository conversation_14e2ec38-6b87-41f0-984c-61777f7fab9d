package com.crhms.cloud.mqs.mqs_bmi.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.client.cdmp.dic.CdmpDicInterface;
import com.crhms.cloud.core.excel.dto.ExcelExport;
import com.crhms.cloud.core.excel.dto.SheetData;
import com.crhms.cloud.core.excel.util.BigExcelUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.mapper.BaseMedicalMapper;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFb;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFbTemp;
import com.crhms.cloud.mqs.mqs_bmi.mapper.BmiFbMapper;
import com.crhms.cloud.mqs.mqs_bmi.vo.BmiImportVo;
import com.crhms.cloud.mqs.mqs_bmi.vo.BmiQueryVo;
import com.crhms.cloud.mqs.mqs_bmi.vo.FbMedicalCaseDto;
import com.crhms.cloud.mqs.mqs_bmi.vo.FbMedicalDetailDto;
import com.crhms.cloud.mqs.mqs_hp.domain.HpSettle;
import com.crhms.cloud.mqs.mqs_hp.service.HpSettleService;
import com.crhms.cloud.mqs.mqs_op.domain.OpPt;
import com.crhms.cloud.mqs.mqs_op.service.OpPtService;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import com.crhms.cloud.mqs.sys.mapper.IppMapper;
import com.crhms.cloud.mqs.sys.utils.DictUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 医保管理-申诉审核表(BmiFb)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-10 10:40:08
 */
@Service("bmiFbService")
public class BmiFbService extends ServiceImpl<BmiFbMapper, BmiFb> {
    @Autowired
    private HpSettleService hpSettleService;
    @Autowired
    private OpPtService opPtService;
    @Autowired
    private BmiFbTempService bmiFbTempService;
    @Autowired
    private IppMapper ippMapper;
    @Autowired
    private BaseMedicalMapper baseMedicalMapper;


    static String[] title1 = new String[]{"序号", "交易流水号", "单据号", "结算时间", "反馈申诉", "科室编码", "科室名称", "项目编码", "医生编码", "医生名称", "项目名称", "意见书编码", "医保内金额", "扣除金额", "规则名称", "扣款原因", "还款原因", "费用日期", "数量", "单价", "就医方式", "个人编号", "参保人姓名", "费用机构编码", "费用机构名称", "住院号", "参保类型", "人员类别", "规格", "用量", "类别", "版本号"};
    static String[] title2 = new String[]{"序号", "结算时间", "参保类型", "单据号", "医保内金额", "交易流水号", "参保人编码", "机构名称", "费用机构编码", "费用机构名称", "单据扣除金额", "主单扣除金额", "明细扣除金额"};


    /**
     * 医保导入页面查询
     *
     * @param bmiQueryVo
     * @param hospitalId
     * @param myPage
     * @return
     */
    public List<BmiFb> queryByPage(BmiQueryVo bmiQueryVo, String hospitalId, Page myPage) {
        bmiQueryVo.setHospitalId(hospitalId);
        return this.baseMapper.queryByPage(bmiQueryVo, myPage);
    }

    /**
     * 院内反馈页面查询 - 用户权限
     *
     * @param bmiQueryVo
     * @param hospitalId
     * @param myPage
     * @return
     */
    public List<BmiFb> queryByPageWithAuth(BmiQueryVo bmiQueryVo, String hospitalId, Page myPage) {
        bmiQueryVo.setHospitalId(hospitalId);
        //查询用户拥有科室
        Long userId = LoginContext.getUserId();
        List<String> detps = ippMapper.selectUserAuthDetp(userId, hospitalId);
        bmiQueryVo.setAuthDeptCodes(detps);
        return this.baseMapper.queryByPageWithAuth(bmiQueryVo, myPage);
    }

    /**
     * 查询详情
     *
     * @param no
     * @param hospitalId
     * @return
     */
    public FbMedicalCaseDto queryFbMedicalCaseDto(String no, String hospitalId) {
        //查询医保导入内容
        FbMedicalCaseDto fbMedicalCase = this.baseMapper.queryHeadWithNo(no, hospitalId);
        if (fbMedicalCase == null) {
            return null;
        }
        //院内 门诊/住院表
        String table = fbMedicalCase.getVisitType().equals("1") ? AuditScenarioEnum.opPt.getTableName() : AuditScenarioEnum.hpSettle.getTableName();
        FbMedicalCaseDto fbMedicalCaseDto = baseMedicalMapper.queryBmiFbHead(table, no, hospitalId);
        if (fbMedicalCaseDto != null) {
            if (StringUtils.isBlank(fbMedicalCase.getPatientName())) {
                fbMedicalCase.setPatientName(fbMedicalCaseDto.getPatientName());
            }
            if (StringUtils.isBlank(fbMedicalCase.getPatientGender())) {
                fbMedicalCase.setPatientGender(fbMedicalCaseDto.getPatientGender());
            }
            if (StringUtils.isBlank(fbMedicalCase.getPatientBirthday())) {
                fbMedicalCase.setPatientBirthday(fbMedicalCaseDto.getPatientBirthday());
            }
            if (fbMedicalCase.getBmiConveredAmount() == null) {
                fbMedicalCase.setBmiConveredAmount(fbMedicalCaseDto.getBmiConveredAmount());
            }
            if (StringUtils.isBlank(fbMedicalCase.getBenefitType())) {
                fbMedicalCase.setBenefitType(fbMedicalCaseDto.getBenefitType());
            }
            if (StringUtils.isBlank(fbMedicalCase.getClaimType())) {
                fbMedicalCase.setClaimType(fbMedicalCaseDto.getClaimType());
            }
            if (StringUtils.isBlank(fbMedicalCase.getPersonnelType())) {
                fbMedicalCase.setPersonnelType(fbMedicalCaseDto.getPersonnelType());
            }
            fbMedicalCase.setTotalAmount(fbMedicalCaseDto.getTotalAmount());
            fbMedicalCase.setOutDiagnosisName(fbMedicalCaseDto.getOutDiagnosisName());
            fbMedicalCase.setDiagnosis(fbMedicalCaseDto.getDiagnosis());
        }
        //计算年龄
        try {
            if (StringUtils.isNotEmpty(fbMedicalCase.getPatientBirthday())) {
                String patientBirthday = fbMedicalCase.getPatientBirthday();
                DateTime parse = DateUtil.parse(patientBirthday, "yyyy-MM-dd");
                fbMedicalCase.setAge(MqsUtils.getAge(parse));
            }
        } catch (Exception e) {
            e.printStackTrace();
            fbMedicalCase.setAge(-9999);
        }
        DictUtils.translateDict(fbMedicalCase);
        return fbMedicalCase;
    }

    public List<FbMedicalDetailDto> queryFbMedicalDetailDto(String no, String detailNo, String item, String violationFlag, String visitType, String hospitalId,String relatedRecords) {
        String table = visitType.equals("1") ? AuditScenarioEnum.opPt.getTableName() : AuditScenarioEnum.hpSettle.getTableName();

        return baseMedicalMapper.queryBmiFbDetail(table, no, detailNo, item, violationFlag, hospitalId,relatedRecords);
    }

    /**
     * 查询详情明细
     *
     * @param no
     * @param hospitalId
     * @return
     */
    public List<BmiFb> queryFbCaseDetail(String no, String statuss, String hospitalId) {
        QueryWrapper<BmiFb> eq = new QueryWrapper<BmiFb>().eq(BmiFb.FIELD_HOSPITAL_ID, hospitalId).eq(BmiFb.FIELD_NO, no);
        if (Strings.isNotBlank(statuss)) {
            String[] split = statuss.split(",");
            eq.in(BmiFb.FIELD_STATUS, split);
        }
        return list(eq);
    }

    /**
     * 反馈意见
     *
     * @param fbStauts
     * @param fbReason
     * @param ids
     * @param hospitalId
     */
    public void fbBmiReason(String fbStauts, String fbReason, List<String> ids, String hospitalId) {
        //更新反馈意见 更新状态为已反馈
        if (CollectionUtils.isEmpty(ids)) {
            throw new BaseException("请选择反馈的数据！");
        }
//        if ("0".equals(fbStauts) && StringUtils.isBlank(fbReason)) {
//            throw new BaseException("请填写反馈原因！");
//        }
        this.update(new UpdateWrapper<BmiFb>().set(BmiFb.FIELD_STATUS, "2").set(BmiFb.FIELD_FB_STAUTS, fbStauts).set(BmiFb.FIELD_FB_REASON, fbReason).in(BmiFb.FIELD_ID, ids).eq(BmiFb.FIELD_HOSPITAL_ID, hospitalId));

    }

    public void batchChangeStatus(List<String> ids, String status, String hospitalId) {
        //批量更新数据状态
        if (CollectionUtils.isEmpty(ids)) {
            throw new BaseException("请选择数据！");
        }
        this.update(new UpdateWrapper<BmiFb>().set(BmiFb.FIELD_STATUS, status).in(BmiFb.FIELD_ID, ids).eq(BmiFb.FIELD_HOSPITAL_ID, hospitalId));

    }

    /**
     * todo 医保导入明细查询-查询单据详情
     */
    public Map queryNoDetails(String no, String hospitalId) {
        Map<String, Object> resultMap = new HashMap<>();
        List<BmiFb> list = this.list(new QueryWrapper<BmiFb>().eq(BmiFb.FIELD_NO, no).eq(BmiFb.FIELD_HOSPITAL_ID, hospitalId));

        if (CollectionUtils.isNotEmpty(list)) {
            BmiFb bmiFb = list.get(0);


            resultMap.put("patientName", bmiFb.getPatientName());

        }
        return resultMap;

    }


    /**
     * 批量分配单据到反馈科室
     *
     * @param deptCode
     * @param deptName
     * @param nos
     * @param hospitalId
     */
    public void allotToDept(String deptCode, String deptName, List<String> nos, String hospitalId) {
        if (CollectionUtils.isEmpty(nos)) {
            throw new BaseException("请选择要分配的单据！");
        }
        if (StringUtils.isBlank(deptCode) || StringUtils.isBlank(deptName)) {
            throw new BaseException("请选择科室！");
        }
        this.update(new UpdateWrapper<BmiFb>().set(BmiFb.FIELD_STATUS, "1").set(BmiFb.FIELD_FB_DEPT_CODE, deptCode).set(BmiFb.FIELD_FB_DEPT_NAME, deptName).in(BmiFb.FIELD_NO, nos).eq(BmiFb.FIELD_HOSPITAL_ID, hospitalId));
    }

    /**
     * 删除医保导入数据
     *
     * @param ids
     * @param hospitalId
     */
    public void deleteBmiFbImp(List<Long> ids, String hospitalId) {
        remove(new QueryWrapper<BmiFb>().eq(BmiFb.FIELD_HOSPITAL_ID, hospitalId).in(BmiFb.FIELD_ID, ids));
    }

    /**
     * 医保导入方法
     *
     * @param importVo
     * @return
     */
    public Map importFbExcel(BmiImportVo importVo, String hospitalId) {
        Map<String, String> resultMap = new HashMap<>();
        String appealDate = importVo.getAppealDate();
        Date appDate = new Date();
        try {
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            appDate = dateFormat.parse(appealDate);
        } catch (Exception e) {
            new BaseException("申诉截止时间格式错误！" + e.getMessage());
        }
        if (Objects.isNull(appealDate)) {
            throw new BaseException("申诉截止时间未填写！");
        }
        //生成批次号
        String batchNo = MqsUtils.getUUBatch();
        MultipartFile file = importVo.getFile();
        // 获取sheet页
        List<SheetData> sheets = BigExcelUtil.parseExcel(file).getSheets();

        // 校验Excel
        checkExcel(sheets);

        // 解析Excel数据
        List<BmiFbTemp> importDatas = parseExcel(sheets, importVo.getAppealDate());
        if (importDatas != null) {
            Set<String> sets = importDatas.stream().map(BmiFbTemp::getNo).collect(Collectors.toSet());
            List importNos = new ArrayList<>();
            importNos.addAll(sets);
            //分批次匹配单据处理
            List<OpPt> opPtList = new ArrayList<>();
            List<HpSettle> hpSettleList = new ArrayList<>();
            List<List<String>> split = ListUtil.split(importNos, 1000);
            for (List<String> importNoList : split) {
                hpSettleList.addAll(hpSettleService.list(new QueryWrapper<HpSettle>().select(HpSettle.FIELD_ADMISSION_NO, HpSettle.FIELD_NO).in(HpSettle.FIELD_NO, importNoList).eq(HpSettle.FIELD_HOSPITAL_ID, hospitalId)));
                opPtList.addAll(opPtService.list(new QueryWrapper<OpPt>().select(OpPt.FIELD_ADMISSION_NO, OpPt.FIELD_NO).in(OpPt.FIELD_NO, importNoList).eq(HpSettle.FIELD_HOSPITAL_ID, hospitalId)));
            }
            Map<String, OpPt> opPtMap = opPtList.stream().collect(Collectors.toMap(OpPt::getNo, x -> x));

            Map<String, HpSettle> hpSettleMap = hpSettleList.stream().collect(Collectors.toMap(HpSettle::getNo, x -> x));
            //匹配数据 验证重复数据
            List<String> nos = importDatas.stream().map(BmiFbTemp::getNo).collect(Collectors.toList());

            Set<String> bmiList = this.list(new QueryWrapper<BmiFb>().select(BmiFb.FIELD_NO).in(BmiFb.FIELD_NO, nos)).stream().map(BmiFb::getNo).collect(Collectors.toSet());

            for (BmiFbTemp importData : importDatas) {
                importData.setAppealDate(appDate);
                importData.setBatchNo(batchNo);
                importData.setHospitalId(hospitalId);
                OpPt opPt = opPtMap.get(importData.getNo());
                HpSettle hpSettle = hpSettleMap.get(importData.getNo());
                if (opPt != null) {
                    importData.setIsMatch("1");
                    importData.setVisitType("1");
                } else if (hpSettle != null) {
                    importData.setIsMatch("1");
                    importData.setVisitType("2");
                } else {
                    importData.setIsMatch("0");
                    importData.setVisitType("3");
                }
                importData.setIsRepeat(bmiList.contains(importData.getNo()) ? "1" : "0");
            }
            //分批保存至临时表
            bmiFbTempService.saveBatch(importDatas, 2000);
        }
        resultMap.put("batchNo", batchNo);
        return resultMap;
    }


    /**
     * 查询导入临时表的数据
     *
     * @param batchNo
     * @param hospitalId
     * @return
     */
    public Map queryImportTotal(String batchNo, String hospitalId) {
        List<BmiFbTemp> list = bmiFbTempService.list(new QueryWrapper<BmiFbTemp>().select(BmiFbTemp.FIELD_DETAIL_NO, BmiFbTemp.FIELD_NO, BmiFbTemp.FIELD_BATCH_NO, BmiFbTemp.FIELD_IS_MATCH, BmiFbTemp.FIELD_IS_REPEAT).eq(BmiFbTemp.FIELD_BATCH_NO, batchNo).eq(BmiFbTemp.FIELD_HOSPITAL_ID, hospitalId));
        //总单据数
        int size = list.stream().map(BmiFbTemp::getNo).collect(Collectors.toSet()).size();
        //未匹配单据数
        int unMatch = list.stream().filter(x -> "0".equals(x.getIsMatch())).map(BmiFbTemp::getNo).collect(Collectors.toSet()).size();
        //重复单据数
        int isRepeat = list.stream().filter(x -> "1".equals(x.getIsRepeat())).map(BmiFbTemp::getNo).collect(Collectors.toSet()).size();
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", size);
        resultMap.put("unMatch", unMatch);
        resultMap.put("match", size - unMatch);
        resultMap.put("repeat", isRepeat);
        return resultMap;
    }

    /**
     * 分页查询临时表数据
     *
     * @param batchNo
     * @param type
     * @param hospitalId
     * @param myPage
     * @return
     */
    public List<BmiFbTemp> queryImportByPage(String batchNo, String type, String hospitalId, Page myPage) {

        QueryWrapper<BmiFbTemp> queryWrapper = new QueryWrapper<BmiFbTemp>().eq(BmiFbTemp.FIELD_BATCH_NO, batchNo).eq(BmiFbTemp.FIELD_HOSPITAL_ID, hospitalId);
        if ("match".equals(type)) {
            queryWrapper.eq(BmiFbTemp.FIELD_IS_MATCH, "0");
        } else if ("repeat".equals(type)) {
            queryWrapper.eq(BmiFbTemp.FIELD_IS_REPEAT, "1");
        }
        Page page = bmiFbTempService.page(myPage, queryWrapper);
        return page.getRecords();
    }

    public List<BmiFbTemp> queryImportNoPage(String batchNo, String type, String hospitalId) {
        QueryWrapper<BmiFbTemp> queryWrapper = new QueryWrapper<BmiFbTemp>().eq(BmiFbTemp.FIELD_BATCH_NO, batchNo).eq(BmiFbTemp.FIELD_HOSPITAL_ID, hospitalId);
        if ("match".equals(type)) {
            queryWrapper.eq(BmiFbTemp.FIELD_IS_MATCH, "0");
        } else if ("repeat".equals(type)) {
            queryWrapper.eq(BmiFbTemp.FIELD_IS_REPEAT, "1");
        }

        return bmiFbTempService.list(queryWrapper);
    }

    /**
     * 导入数据到正式表
     *
     * @param batchNo
     * @param ignoreNos
     */
    @Transactional
    public void loadIn(String batchNo, List<String> ignoreNos, String hospitalId) {
        QueryWrapper<BmiFbTemp> queryWrapper = new QueryWrapper<BmiFbTemp>().eq(BmiFbTemp.FIELD_BATCH_NO, batchNo).eq(BmiFbTemp.FIELD_HOSPITAL_ID, hospitalId);
        if (CollectionUtil.isNotEmpty(ignoreNos)) {
            queryWrapper.notIn(BmiFbTemp.FIELD_NO, ignoreNos);
        }
        List<BmiFbTemp> list = bmiFbTempService.list(queryWrapper);
        List<BmiFb> bmiFbList = new ArrayList<>();
        Set<String> collect = list.stream().map(BmiFbTemp::getNo).collect(Collectors.toSet());
        //删除已有的单据号相同的数据
        if (CollectionUtil.isNotEmpty(collect)) {
            this.remove(new QueryWrapper<BmiFb>().eq(BmiFb.FIELD_HOSPITAL_ID, hospitalId).in(BmiFb.FIELD_NO, collect));
        }
        //保存数据
        for (BmiFbTemp bmiFbTemp : list) {
            BmiFb bmiFb = new BmiFb();
            BeanUtils.copyProperties(bmiFbTemp, bmiFb);
            bmiFb.setStatus("0");
            bmiFbList.add(bmiFb);
        }
        this.saveBatch(bmiFbList, 2000);
        //清空记录
        bmiFbTempService.remove(new QueryWrapper<BmiFbTemp>().eq(BmiFbTemp.FIELD_BATCH_NO, batchNo).eq(BmiFbTemp.FIELD_HOSPITAL_ID, hospitalId));
    }

    /**
     * 校验Excel表头及数据是否为空
     *
     * @param sheetDatas sheet页
     */
    public void checkExcel(List<SheetData> sheetDatas) {

        // 校验表头
        if (sheetDatas.size() != 2) {
            throw new BaseException("请正确维护导入格式!");
        }
        String checkDetail = BigExcelUtil.checkHead(sheetDatas.get(0), CollUtil.list(true, title1));
        if (StrUtil.isNotEmpty(checkDetail)) {
            throw new BaseException(checkDetail);
        }
        String checkHead = BigExcelUtil.checkHead(sheetDatas.get(1), CollUtil.list(true, title2));
        if (StrUtil.isNotEmpty(checkHead)) {
            throw new BaseException(checkHead);
        }
    }

    //导入文件解析
    List<BmiFbTemp> parseExcel(List<SheetData> sheetDatas, String appealDate) {
        // 获取数据
        List<BmiFbTemp> headDatas = new ArrayList<>();
        List<BmiFbTemp> detailDatas = new ArrayList<>();
        //单据扣除金额汇总
        SheetData headSheet = sheetDatas.get(1);
        //明细扣款
        SheetData detailSheet = sheetDatas.get(0);

        //解析
        if (headSheet.getLines().size() > 0) {
            for (int i = 1; i < headSheet.getLines().size(); i++) {
                List<Object> head = headSheet.getLines().get(i);
                headDatas.add(BmiFbTemp.builder().billDate(BigExcelUtil.getItemDate(head, 1))
                        .benefitType(BigExcelUtil.getItemStr(head, 2))
                        .no(BigExcelUtil.getItemStr(head, 3))
                        .bmiConveredAmount(MqsUtils.getBigDecimal(BigExcelUtil.getItem(head, 4)))
                        .admissionNo(BigExcelUtil.getItemStr(head, 5))
                        .patientId(BigExcelUtil.getItemStr(head, 6))
                        .org(BigExcelUtil.getItemStr(head, 7))
                        .feeOrgCode(BigExcelUtil.getItemStr(head, 8))
                        .feeOrgName(BigExcelUtil.getItemStr(head, 9))
                        .deductionAmount1(MqsUtils.getBigDecimal(BigExcelUtil.getItem(head, 10)))
                        .deductionAmount2(MqsUtils.getBigDecimal(BigExcelUtil.getItem(head, 11)))
                        .deductionAmount3(MqsUtils.getBigDecimal(BigExcelUtil.getItem(head, 12)))
                        .build());
            }
        }
        Map<String, BmiFbTemp> headMaps = headDatas.stream().collect(Collectors.toMap(BmiFbTemp::getNo, e -> e, (k1, k2) -> k1));

        if (detailSheet.getLines().size() > 0) {
            for (int i = 1; i < detailSheet.getLines().size(); i++) {
                List<Object> detail = detailSheet.getLines().get(i);
                String no = BigExcelUtil.getItemStr(detail, 2);
                BmiFbTemp bmiFbTemp = headMaps.get(no) == null ? new BmiFbTemp() : headMaps.get(no);

                detailDatas.add(BmiFbTemp.builder().detailNo(BigExcelUtil.getItemStr(detail, 1))
                        .no(BigExcelUtil.getItemStr(detail, 2))
                        .billDate(BigExcelUtil.getItemDate(detail, 3))
                        .fbComplain(BigExcelUtil.getItemStr(detail, 4))
                        .deptCode(BigExcelUtil.getItemStr(detail, 5))
                        .deptName(BigExcelUtil.getItemStr(detail, 6))
                        .itemId(BigExcelUtil.getItemStr(detail, 7))
                        .docId(BigExcelUtil.getItemStr(detail, 8))
                        .docName(BigExcelUtil.getItemStr(detail, 9))
                        .itemName(BigExcelUtil.getItemStr(detail, 10))
                        .proposalCode(BigExcelUtil.getItemStr(detail, 11))
                        .bmiConveredAmount(MqsUtils.getBigDecimal(BigExcelUtil.getItem(detail, 12)))
                        .deductionAmount(MqsUtils.getBigDecimal(BigExcelUtil.getItem(detail, 13)))
                        .rules(BigExcelUtil.getItemStr(detail, 14))
                        .deductionReason(BigExcelUtil.getItemStr(detail, 15))
                        .returnReason(BigExcelUtil.getItemStr(detail, 16))
                        .itemDate(BigExcelUtil.getItemDate(detail, 17))
                        .numbers(MqsUtils.getBigDecimal(BigExcelUtil.getItem(detail, 18)))
                        .price(MqsUtils.getBigDecimal(BigExcelUtil.getItem(detail, 19)))
                        .claimType(BigExcelUtil.getItemStr(detail, 20))
                        .patientId(BigExcelUtil.getItemStr(detail, 21))
                        .patientName(BigExcelUtil.getItemStr(detail, 22))
                        .feeOrgCode(BigExcelUtil.getItemStr(detail, 23))
                        .feeOrgName(BigExcelUtil.getItemStr(detail, 24))
                        .admissionNo(BigExcelUtil.getItemStr(detail, 25))
                        .benefitType(BigExcelUtil.getItemStr(detail, 26))
                        .personnelType(BigExcelUtil.getItemStr(detail, 27))
                        .specification(BigExcelUtil.getItemStr(detail, 28))
                        .usages(BigExcelUtil.getItemStr(detail, 29))
                        .ptype(BigExcelUtil.getItemStr(detail, 30))
                        .version(BigExcelUtil.getItemStr(detail, 31))
                        .deductionAmount1(bmiFbTemp.getDeductionAmount1())
                        .deductionAmount2(bmiFbTemp.getDeductionAmount2())
                        .deductionAmount3(bmiFbTemp.getDeductionAmount3())
                        .isMatch("0")                                                   //填充默认值
                        .isRepeat("0")                                                  //填充默认值
                        .build());
            }
        }
        return detailDatas;
    }


    /**
     * 导出模板
     *
     * @param response
     */
    public void exportExcel(HttpServletResponse response) {
        List<List<String>> heads1 = new ArrayList<>();
        // 明细扣款表头
        heads1.add(CollUtil.list(true, title1));
        List<List<String>> heads2 = new ArrayList<>();
        // 单据扣除金额汇总表头
        heads2.add(CollUtil.list(true, title2));
        // 导出
        List<ExcelExport> exports = new ArrayList<>();
        exports.add(ExcelExport.builder().sheetName("明细扣款").datas(new ArrayList<>()).heads(heads1).build());
        exports.add(ExcelExport.builder().sheetName("单据扣除金额汇总").datas(new ArrayList<>()).heads(heads2).build());
        BigExcelUtil.exportExcel(response, "医保导入模板", "XLSX", exports);
    }

    public List<Map<String, String>> selectDeptList(String dept, String hospitalId) {
        return this.baseMapper.selectDeptList(dept, hospitalId);
    }

    public List<Map<String, String>> selectDoctorList(String doctor, String hospitalId) {
        return this.baseMapper.selectDoctorList(doctor, hospitalId);
    }

    public List<String> selectClaimTypeList(String claimType, String hospitalId) {
        return this.baseMapper.selectClaimTypeList(claimType, hospitalId);
    }

    /**
     * 一键分配到入院科室
     *
     * @param billDateFrom
     * @param billDateTo
     * @param hospitalId
     * @return
     */
    public Map autoAllotToDept(String billDateFrom, String billDateTo, String tag, String hospitalId) {
        //查询期间内待分配数据
        List<BmiFb> unMatchList = new ArrayList<>();
        List<BmiFb> list = list(new QueryWrapper<BmiFb>().select(BmiFb.FIELD_NO, BmiFb.FIELD_DETAIL_NO, BmiFb.FIELD_DEPT_NAME, BmiFb.FIELD_RULES, BmiFb.FIELD_ITEM_ID, BmiFb.FIELD_ITEM_NAME, BmiFb.FIELD_DEDUCTION_AMOUNT, BmiFb.FIELD_ITEM_DATE, BmiFb.FIELD_PATIENT_NAME)
                .eq(BmiFb.FIELD_HOSPITAL_ID, hospitalId)
                .eq(BmiFb.FIELD_STATUS, "0")
                .between(BmiFb.FIELD_BILL_DATE, billDateFrom + " 00:00:00",billDateTo + " 23:59:59"));
        Set<String> collect = list.stream().map(BmiFb::getNo).collect(Collectors.toSet());
        List nos = new ArrayList<>();
        nos.addAll(collect);
        //分批次匹配单据处理
        List<OpPt> opPtList = new ArrayList<>();
        List<HpSettle> hpSettleList = new ArrayList<>();
        List<List<String>> split = ListUtil.split(nos, 1000);
        //查询可以匹配到的科室
        for (List<String> noList : split) {
            hpSettleList.addAll(hpSettleService.list(new QueryWrapper<HpSettle>().select(HpSettle.FIELD_NO, HpSettle.FIELD_DEPT_CODE, HpSettle.FIELD_DEPT_NAME, HpSettle.FIELD_DISCHARGE_DEPT_CODE, HpSettle.FIELD_DISCHARGE_DEPT_NAME).in(HpSettle.FIELD_NO, noList).eq(HpSettle.FIELD_HOSPITAL_ID, hospitalId)));
            opPtList.addAll(opPtService.list(new QueryWrapper<OpPt>().select(OpPt.FIELD_NO, OpPt.FIELD_DEPT_CODE, OpPt.FIELD_DEPT_NAME).in(OpPt.FIELD_NO, noList).eq(HpSettle.FIELD_HOSPITAL_ID, hospitalId)));
        }
        Map<String, OpPt> opPtMap = opPtList.stream().collect(Collectors.toMap(OpPt::getNo, x -> x));
        Map<String, HpSettle> hpSettleMap = hpSettleList.stream().collect(Collectors.toMap(HpSettle::getNo, x -> x));

        Map<String, List<BmiFb>> collect1 = list.stream().collect(Collectors.groupingBy(BmiFb::getNo));
        for (Map.Entry<String, List<BmiFb>> stringListEntry : collect1.entrySet()) {
            String key = stringListEntry.getKey();
            List<BmiFb> value = stringListEntry.getValue();
            OpPt opPt = opPtMap.get(key);
            HpSettle hpSettle = hpSettleMap.get(key);
            String fbDeptCode = "";
            String fbDeptName = "";
            if (opPt != null) {
                fbDeptCode = opPt.getDeptCode();
                fbDeptName = opPt.getDeptName();
            } else if (hpSettle != null) {
                if ("1".equals(tag)) {
                    fbDeptCode = hpSettle.getDeptCode();
                    fbDeptName = hpSettle.getDeptName();
                } else {
                    fbDeptCode = hpSettle.getDischargeDeptCode();
                    fbDeptName = hpSettle.getDischargeDeptName();
                }
            }
            if (Strings.isNotBlank(fbDeptCode) && Strings.isNotBlank(fbDeptName)) {
                //更新反馈科室状态变为待反馈
                this.update(new UpdateWrapper<BmiFb>().set(BmiFb.FIELD_STATUS, "1").set(BmiFb.FIELD_FB_DEPT_CODE, fbDeptCode).set(BmiFb.FIELD_FB_DEPT_NAME, fbDeptName).set(BmiFb.FIELD_IS_MATCH, "1").eq(BmiFb.FIELD_NO, key).eq(BmiFb.FIELD_STATUS, "0").eq(BmiFb.FIELD_HOSPITAL_ID, hospitalId));
            } else {
                unMatchList.addAll(value);
            }
        }
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", list.size());
        resultMap.put("unMatchTotal", unMatchList.size());
        return resultMap;
    }

    /**
     * 通用方法 - 更新处理意见和状态
     *
     * @param status
     * @param id
     * @param fbStauts
     * @param fbReason
     * @param hospitalId
     */
    public void checkDetail(String status, String id, String fbStauts, String fbReason, String hospitalId) {
        BmiFb one = this.getOne(new QueryWrapper<BmiFb>().select(BmiFb.FIELD_STATUS).eq(BmiFb.FIELD_ID, id).eq(BmiFb.FIELD_HOSPITAL_ID, hospitalId));
        if (!"2".equals(one.getStatus())) {
            throw new BaseException("只能操作已反馈数据！");
        }
        this.update(new UpdateWrapper<BmiFb>().set(BmiFb.FIELD_STATUS, status).set(BmiFb.FIELD_FB_STAUTS, fbStauts).set(BmiFb.FIELD_FB_REASON, fbReason).eq(BmiFb.FIELD_ID, id).eq(BmiFb.FIELD_HOSPITAL_ID, hospitalId));
    }


    /**
     * 导出"导入分发"
     *
     * @param response   响应
     * @param bmiQueryVo 查询条件
     */
    public void exportImp(HttpServletResponse response, BmiQueryVo bmiQueryVo) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        heads.add(CollUtil.list(true, "STATUS", "IS_MATCH", "FB_DEPT_NAME", "APPEAL_DATE", "DETAIL_NO",
                "ADMISSION_NO", "NO", "CLAIM_TYPE", "BILL_DATE", "PATIENT_ID", "PATIENT_NAME", "BENEFIT_TYPE", "PERSONNEL_TYPE", "DEPT_NAME",
                "DOC_NAME", "ITEM_DATE", "ITEM_ID", "ITEM_NAME", "NUMBERS", "BMI_CONVERED_AMOUNT", "DEDUCTION_AMOUNT", "DEDUCTION_REASON", "RULES"));
        heads.add(CollUtil.list(true, "处理状态", "是否匹配", "反馈科室", "申诉截止时间", "交易流水号",
                "住院号(门诊号)", "单据号", "就医方式", "结算日期", "个人编号", "参保人姓名", "参保类型", "人员类别", "科室名称",
                "医生姓名", "费用日期", "项目编码", "项目名称", "数量", "医保内金额", "扣除金额", "扣款原因", "规则名称"));

        List<BmiFb> list = queryByPage(bmiQueryVo, LoginContext.getHospitalId(), null);

        for (BmiFb item : list) {
            List<Object> line = CollUtil.list(true, mapToName(item.getStatus()), StrUtil.equals(item.getIsMatch(), "0") ? "否" : "是", item.getFbDeptName(), DateUtil.format(item.getAppealDate(), "yyyy-MM-dd HH:mm:ss"), item.getDetailNo(),
                    item.getAdmissionNo(), item.getNo(), item.getClaimType(), DateUtil.format(item.getBillDate(), "yyyy-MM-dd HH:mm:ss"), item.getPatientId(), item.getPatientName(), item.getBenefitType(), item.getPersonnelType(), item.getDeptName(),
                    item.getDocName(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"), item.getItemId(), item.getItemName(), item.getNumbers(), item.getBmiConveredAmount(), item.getDeductionAmount(), item.getDeductionReason(), item.getRules());
            datas.add(line);
        }

        StringBuilder fileName = new StringBuilder();
        fileName.append("医保管理-导入分发");
        MqsUtils.buildExportFileNameSuffix(fileName, bmiQueryVo.getBillDateFrom(), bmiQueryVo.getBillDateTo());

        List<ExcelExport> exports = new ArrayList<>(1);
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);

    }

    /**
     * 编码映射成名称
     * 处理状态 0.待分配 1.待反馈 2.已反馈 3.已退回 4.已确认 5. 已过期 6.已申诉
     *
     * @param status 编码
     */
    private String mapToName(String status) {

        if (StrUtil.equals(status, "0")) {
            status = "待分配";
        }
        if (StrUtil.equals(status, "1")) {
            status = "待反馈";
        }
        if (StrUtil.equals(status, "2")) {
            status = "已反馈";
        }
        if (StrUtil.equals(status, "3")) {
            status = "已退回";
        }
        if (StrUtil.equals(status, "4")) {
            status = "已确认";
        }
        if (StrUtil.equals(status, "5")) {
            status = "已过期";
        }
        if (StrUtil.equals(status, "6")) {
            status = "已申诉";
        }

        return status;
    }


    /**
     * 导出院内反馈
     *
     * @param response   响应
     * @param bmiQueryVo 查询条件
     */
    public void exportWithAuth(HttpServletResponse response, BmiQueryVo bmiQueryVo) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        heads.add(CollUtil.list(true, "STATUS", "FB_DEPT_NAME", "APPEAL_DATE", "FB_STAUTS", "FB_REASON", "DETAIL_NO",
                "NO", "ADMISSION_NO", "CLAIM_TYPE", "BILL_DATE", "PATIENT_ID", "PATIENT_NAME", "BENEFIT_TYPE", "PERSONNEL_TYPE", "DEPT_NAME",
                "DOC_NAME", "ITEM_DATE", "ITEM_ID", "ITEM_NAME", "NUMBERS", "BMI_CONVERED_AMOUNT", "DEDUCTION_AMOUNT", "DEDUCTION_REASON", "RULES"));
        heads.add(CollUtil.list(true, "处理状态", "反馈科室", "申诉截止时间", "扣费意见", "意见描述", "交易流水号",
                "单据号", "住院号(门诊号)", "就医方式", "结算日期", "个人编号", "参保人姓名", "参保类型", "人员类别", "科室名称",
                "医生姓名", "费用日期", "项目编码", "项目名称", "数量", "医保内金额", "扣除金额", "扣款原因", "规则名称"));

        List<BmiFb> list = queryByPageWithAuth(bmiQueryVo, LoginContext.getHospitalId(), null);

        for (BmiFb item : list) {
            if (null != item.getFbStauts()) {
                if (item.getFbStauts().equals("1")) {
                    item.setFbStauts("同意");
                } else {
                    item.setFbStauts("不同意");
                }
            }
            List<Object> line = CollUtil.list(true, mapToName(item.getStatus()), item.getFbDeptName(), DateUtil.format(item.getAppealDate(), "yyyy-MM-dd HH:mm:ss"), item.getFbStauts(), item.getFbReason(), item.getDetailNo(),
                    item.getNo(), item.getAdmissionNo(), item.getClaimType(), DateUtil.format(item.getBillDate(), "yyyy-MM-dd HH:mm:ss"), item.getPatientId(), item.getPatientName(), item.getBenefitType(), item.getPersonnelType(), item.getDeptName(),
                    item.getDocName(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"), item.getItemId(), item.getItemName(), item.getNumbers(), item.getBmiConveredAmount(), item.getDeductionAmount(), item.getDeductionReason(), item.getRules());
            datas.add(line);
        }

        StringBuilder fileName = new StringBuilder();
        fileName.append("医保管理-院内审核");
        MqsUtils.buildExportFileNameSuffix(fileName, bmiQueryVo.getBillDateFrom(), bmiQueryVo.getBillDateTo());

        List<ExcelExport> exports = new ArrayList<>(1);
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }


    /**
     * 导出审核申诉
     *
     * @param response   响应
     * @param bmiQueryVo 查询条件
     */
    public void export(HttpServletResponse response, BmiQueryVo bmiQueryVo) {
        List<List<String>> heads = new ArrayList<>();
        List<List<Object>> datas = new ArrayList<>();

        heads.add(CollUtil.list(true, "STATUS", "FB_DEPT_NAME", "APPEAL_DATE", "FB_STAUTS", "FB_REASON", "DETAIL_NO",
                "NO", "ADMISSION_NO", "CLAIM_TYPE", "BILL_DATE", "PATIENT_ID", "PATIENT_NAME", "BENEFIT_TYPE", "PERSONNEL_TYPE", "DEPT_NAME",
                "DOC_NAME", "ITEM_DATE", "ITEM_ID", "ITEM_NAME", "NUMBERS", "BMI_CONVERED_AMOUNT", "DEDUCTION_AMOUNT", "DEDUCTION_REASON", "RULES"));
        heads.add(CollUtil.list(true, "处理状态", "反馈科室", "申诉截止时间", "扣费意见", "意见描述", "交易流水号",
                "单据号", "住院号(门诊号)", "就医方式", "结算日期", "个人编号", "参保人姓名", "参保类型", "人员类别", "科室名称",
                "医生姓名", "费用日期", "项目编码", "项目名称", "数量", "医保内金额", "扣除金额", "扣款原因", "规则名称"));

        List<BmiFb> list = queryByPage(bmiQueryVo, LoginContext.getHospitalId(), null);

        for (BmiFb item : list) {
            if (null != item.getFbStauts()) {
                if (item.getFbStauts().equals("1")) {
                    item.setFbStauts("同意");
                } else {
                    item.setFbStauts("不同意");
                }
            }
            List<Object> line = CollUtil.list(true, mapToName(item.getStatus()), item.getFbDeptName(), DateUtil.format(item.getAppealDate(), "yyyy-MM-dd HH:mm:ss"), item.getFbStauts(), item.getFbReason(), item.getDetailNo(),
                    item.getNo(), item.getAdmissionNo(), item.getClaimType(), DateUtil.format(item.getBillDate(), "yyyy-MM-dd HH:mm:ss"), item.getPatientId(), item.getPatientName(), item.getBenefitType(), item.getPersonnelType(), item.getDeptName(),
                    item.getDocName(), DateUtil.format(item.getItemDate(), "yyyy-MM-dd HH:mm:ss"), item.getItemId(), item.getItemName(), item.getNumbers(), item.getBmiConveredAmount(), item.getDeductionAmount(), item.getDeductionReason(), item.getRules());
            datas.add(line);
        }

        StringBuilder fileName = new StringBuilder();
        fileName.append("医保管理-审核申诉");
        MqsUtils.buildExportFileNameSuffix(fileName, bmiQueryVo.getBillDateFrom(), bmiQueryVo.getBillDateTo());

        List<ExcelExport> exports = new ArrayList<>(1);
        exports.add(ExcelExport.builder().sheetName("sheet").heads(heads).datas(datas).build());
        BigExcelUtil.exportExcel(response, fileName.toString(), "XLSX", exports);
    }
}
