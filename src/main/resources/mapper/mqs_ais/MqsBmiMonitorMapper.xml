<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_ais.mapper.MqsBmiMonitorMapper">

    <select id="selectBmiAmount" resultType="java.math.BigDecimal">
        select SUM(bmi_overall_amount)
        from (
        <foreach collection="scenarioList" separator="union all" item="table">
            (
            select SUM(bmi_overall_amount) as bmi_overall_amount
            from ${table}
            where
            hospital_id = #{hospitalId}
            and audit_time &gt;= concat(#{now},' 00:00:00')
            and audit_time &lt;= concat(#{now},' 23:59:59')
            )
        </foreach>
        )c
    </select>
    <select id="selectBmiAuditTimes" resultType="java.math.BigDecimal">
        select SUM(times)
        from (
        <foreach collection="scenarioList" separator="union all" item="table">
            (
            select COUNT(1) as times
            from ${table}_his
            where
            hospital_id = #{hospitalId}
            and audit_time &gt;= concat(#{now},' 00:00:00')
            and audit_time &lt;= concat(#{now},' 23:59:59')
            )
        </foreach>
        )c
    </select>
    <select id="selectBmiAmountByItemType" resultType="java.util.Map">
        SELECT
        detail.item_type_code as item_type_code ,
        detail.item_type_name as item_type_name ,
        SUM(detail.bmi_overall_amount) as bmiAmount
        FROM mqs_fee_list detail
        <where>
            1 = 1
            <if test="startTime != null and startTime != '' ">
                and detail.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and detail.bill_date &lt;= #{endTime}
            </if>
            <if test="scenarioList != null and scenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="scenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
        </where>
        GROUP BY
        item_type_code,
        item_type_name


    </select>
    <select id="deptFundsBmiAmount" resultType="java.util.Map">

        SELECT
            apply_dept_code as deptCode,
            apply_dept_name as deptName,
            intDivOrZero(SUM(detail.bmi_overall_amount),10000) as usedAmount
        FROM mqs_fee_list detail
            where
                detail.hospital_id = #{hospitalId}
            and detail.bill_date &gt;= #{startTime}
            and detail.bill_date &lt;= #{endTime}
            and detail.audit_scenario in
            <foreach collection="scenarioList" open="(" close=")" separator="," item="auditScenario">
                #{auditScenario}
            </foreach>
        GROUP BY
        detail.apply_dept_code,
        detail.apply_dept_name
        ORDER BY
        usedAmount

    </select>
    <select id="selectDeptFuns" resultType="java.util.Map">
        SELECT
            dep.`code` as deptCode,
            dep.`name` as deptName,
            years_amount,
            years_warn,
            amount1 as '1',
            amount2 as '2',
            amount3 as '3',
            amount4 as '4',
            amount5 as '5',
            amount6 as '6',
            amount7 as '7',
            amount8 as '8',
            amount9 as '9',
            amount10 as '10',
            amount11 as '11',
            amount12 as '12',
            warn1 as 'warn1',
            warn2 as 'warn2',
            warn3 as 'warn3',
            warn4 as 'warn4',
            warn5 as 'warn5',
            warn6 as 'warn6',
            warn7 as 'warn7',
            warn8 as 'warn8',
            warn9 as 'warn9',
            warn10 as 'warn10',
            warn11 as 'warn11',
            warn12 as 'warn12'
        FROM
            mqs_sys_funds_dept dep
            LEFT JOIN mqs_sys_funds fun ON fun.id = dep.funds_id
            where dep.hospital_id = #{hospitalId}
            and fun.years = #{filterYear}
            <choose>
                <when test="auditScenario == 'hp'">
                   and fun.visit_type = '2'
                </when>
                <when test="auditScenario == 'op'">
                   and fun.visit_type = '1'
                </when>
            </choose>
    </select>
    <select id="getTopDeptBmiAmount" resultType="java.util.Map">
        SELECT
        detail.apply_dept_code as deptCode,
        detail.apply_dept_name as deptName,
        SUM(detail.bmi_overall_amount) as usedAmount
        FROM mqs_fee_list detail
        <where>
            detail.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and detail.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and detail.bill_date &lt;= #{endTime}
            </if>
            <if test="scenarioList != null and scenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="scenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
        </where>
        GROUP BY
        detail.apply_dept_code,
        detail.apply_dept_name
        ORDER BY
        usedAmount desc
        LIMIT 1
    </select>
    <select id="getTopItemBmiAmount" resultType="java.util.Map">
        SELECT
        detail.item_id,
        detail.item_name,
        SUM(detail.bmi_overall_amount) as usedAmount
        FROM mqs_fee_list detail
        <where>
            detail.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and detail.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and detail.bill_date &lt;= #{endTime}
            </if>
            <if test="scenarioList != null and scenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="scenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
        </where>
        GROUP BY
        detail.item_id,
        detail.item_name
        ORDER BY
        usedAmount desc
        LIMIT 1
    </select>
    <select id="getCdsBmiAmount" resultType="java.util.Map">

        SELECT
        COUNT(DISTINCT head.patient_id) as patients,
        intDivOrZero(SUM(head.bmi_overall_amount),patients) as avgUsedAmount
        FROM  mqs_medical_case head
        <where>
            head.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and head.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and head.bill_date &lt;= #{endTime}
            </if>
            <if test="scenarioList != null and scenarioList.size > 0">
                and head.audit_scenario in
                <foreach collection="scenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="cdsList != null and cdsList.size > 0">
                and head.cs_disease_code in
                <foreach collection="cdsList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>
    <select id="getCiBmiAmount" resultType="java.util.Map">
        SELECT
        COUNT(DISTINCT head.patient_id) as patients,
        intDivOrZero(SUM(head.bmi_overall_amount),patients) as avgUsedAmount
        FROM  mqs_medical_case head
        <where>
            head.hospital_id = #{hospitalId}
            <if test="startTime != null and startTime != '' ">
                and head.bill_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                and head.bill_date &lt;= #{endTime}
            </if>
            <if test="scenarioList != null and scenarioList.size > 0">
                and head.audit_scenario in
                <foreach collection="scenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>
            <if test="ciList != null and ciList.size > 0">
                and head.cs_disease_code in
                <foreach collection="ciList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getUsedBmiAmountByMonth" resultType="java.util.Map">

        SELECT
            formatDateTime(detail.bill_date,'%Y-%m') as whichMonth,
            SUM(detail.bmi_overall_amount) as totalAmount
        FROM mqs_fee_list detail
        where
            detail.hospital_id = #{hospitalId}
            and detail.bill_date &gt;= #{startTime}
            and detail.bill_date &lt;= #{endTime}
            <if test="scenarioList != null and scenarioList.size > 0">
                and detail.audit_scenario in
                <foreach collection="scenarioList" open="(" close=")" separator="," item="auditScenario">
                    #{auditScenario}
                </foreach>
            </if>

        group by whichMonth
    </select>

</mapper>