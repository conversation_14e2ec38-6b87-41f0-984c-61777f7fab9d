package com.crhms.cloud.mqs.mqs_kbm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 知识库管理-分组合理性-收费与入组合理性
 *
 * @author: wangxing<PERSON>
 * @date: 2023.02.06
 */
@Data
@TableName("mqs_kbm_group_charge")
public class GroupCharge {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 类型
     */
    @TableField
    private String type;

    /**
     * ADRG
     */
    @TableField
    private String adrg;

    /**
     * ADRG名称
     */
    @TableField
    private String adrgName;

    /**
     * 项目编码
     */
    @TableField
    private String projCode;

    /**
     * 项目名称
     */
    @TableField
    private String projName;
}
