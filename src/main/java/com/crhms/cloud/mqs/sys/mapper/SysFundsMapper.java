package com.crhms.cloud.mqs.sys.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.sys.domain.SysFunds;
import com.crhms.cloud.mqs.sys.domain.SysFundsAlone;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 运营管理-基金管理(SysFunds)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-12-19 09:40:46
 */

@Mapper
public interface SysFundsMapper extends BaseMapper<SysFunds> {

    /**
     * 查询慢特病数据
     * @param hospitalId
     * @return
     */
    List<Map> queryCsDisease(@Param("hospitalId") String hospitalId);

    /**
     *  统计医保统筹金额 慢特病 + 单病种维度
     * @param year
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    List<Map> queryUsedAmountByYear(@Param("scenario") String scenario, @Param("year") String year, @Param("hospitalId") String hospitalId, boolean isCdsCi, boolean isSd, boolean isSi, boolean isTd);

    /**
     *  统计部门医保统筹金额（排除单独管理基金）
     * @param year
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    List<Map> queryUsedAmountByDept(@Param("scenario") String scenario, @Param("year") String year,
                                    @Param("ignoreCsDiseaseCode") Set<String> ignoreCsDiseaseCode,
                                    @Param("ignoreSingleDiseaseCode") Set<String> ignoreSingleDiseaseCode,
                                    @Param("ignoreSiDiagnosisCode") Set<String> ignoreSiDiagnosisCode,
                                    @Param("ignoreTdDieasess") Set<String> ignoreTdDieasess,
                                    @Param("hospitalId") String hospitalId);

    /**
     *  统计院区医保统筹金额(排除单独管理基金）
     * @param year
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    List<Map> queryUsedAmountByZone(@Param("scenario") String scenario, @Param("year") String year,
                                    @Param("ignoreCsDiseaseCode") Set<String> ignoreCsDiseaseCode,
                                    @Param("ignoreSingleDiseaseCode") Set<String> ignoreSingleDiseaseCode,
                                    @Param("ignoreSiDiagnosisCode") Set<String> ignoreSiDiagnosisCode,
                                    @Param("ignoreTdDieasess") Set<String> ignoreTdDieasess,
                                    @Param("hospitalId") String hospitalId);


    /**
     *  统计年度使用金额（以单独基金维度分组）
     * @param year
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    List<Map> queryUsedAmountAll(@Param("scenarios") List<String> scenarios, @Param("year") String year, @Param("hospitalId") String hospitalId);

    /**
     *  统计年度使用金额（靶向药）
     * @param year
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    List<Map> queryUsedAmountAlltdDieasess(@Param("scenarios") List<String> scenarios, @Param("year") String year, @Param("hospitalId") String hospitalId,@Param("tdDieasess") List<String> tdDieasess);

    /**
     * 统计年度使用金额（以单独基金维度分组 按月）
     * @param scenarioList
     * @param filterYear
     * @param hospitalId
     * @return
     */
    @DS("clickhouse")
    List<Map> queryUsedAmountAllByMonth(@Param("scenarios") List<String> scenarioList, @Param("year") String filterYear, @Param("hospitalId") String hospitalId);

    /**
     * 查询大病数据
     * @param hospitalId
     * @return
     */
    List<Map> querySiDisease(@Param("hospitalId") String hospitalId);

    /**
     * 靶向药
     * @param hospitalId
     * @return
     */
    List<Map> queryTdDisease(@Param("hospitalId") String hospitalId);

    /**
     * 查询月度基金
     * @param deptIds
     * @param zoneIds
     * @param hospitalId
     * @return
     */
    List<Map<String,Object>> getAmountByMonth(@Param("deptIds") List<Long> deptIds, @Param("zoneIds") List<Long> zoneIds, @Param("hospitalId") String hospitalId);

    /**
     * 同步基金总金额
     * @param id
     * @param hospitalId
     */
    void syncTotalAmount(@Param("id") Long id, @Param("hospitalId") String hospitalId);

    /**
     *
     * @param fundIds
     * @param hospitalId
     * @return
     */
    List<SysFundsAlone> queryAloneAmount(@Param("fundIds") List<Long> fundIds, @Param("hospitalId") String hospitalId);

    /**
     * 查询年度基金(门诊/住院）
     * @param filterYear
     * @param hospitalId
     * @return
     */
    List<Map> queryFundsByYear(@Param("filterYear") String filterYear, @Param("hospitalId") String hospitalId);

    /**
     * 查询慢特病基金
     * @param year
     * @param hospitalId
     * @return
     */
    List<Map<String,Object>> selectCdsCiAmount(@Param("filterYear") String year, @Param("hospitalId") String hospitalId);
}

