package com.crhms.cloud.mqs.mqs_mr.vo;

import com.crhms.cloud.mqs.mqs_mr.domain.MrPlan;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlanAudit;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MrPlanVO implements Serializable {
    //门诊配置
    private List<MrPlan> opData;
    //住院配置
    private List<MrPlan> hpData;
    //审核配置
    private List<MrPlanAudit> audits;
}
