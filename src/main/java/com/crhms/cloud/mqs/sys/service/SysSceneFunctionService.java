package com.crhms.cloud.mqs.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.sys.domain.SysSceneFunction;
import com.crhms.cloud.mqs.sys.mapper.SysSceneFunctionMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统管理-审核场景-功能配置(SysSceneFunction)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 09:24:09
 */
@Service("sysSceneFunctionService")
public class SysSceneFunctionService extends ServiceImpl< SysSceneFunctionMapper, SysSceneFunction> {

    public void delAll(String hospitalId){
        remove(new LambdaQueryWrapper<SysSceneFunction>().eq(SysSceneFunction::getHospitalId,hospitalId));
    }

    /**
     * 查询转自费、转自费协议书、忽略配置信息
     * @param hospitalId        医院id
     * @param isSelf            是否自费
     * @param isSelfAgreement   是否打印自费协议
     * @param isIgnore          是否忽略
     * @param enable            是否启用
     * */
    public List<SysSceneFunction> getIgnoreAndSelfExpenseConfig (String hospitalId,String isSelf,String isSelfAgreement,String isIgnore,String enable,String auditScenario) {
        List<SysSceneFunction> functionList = getBaseMapper().selectList(new LambdaQueryWrapper<SysSceneFunction>()
                .eq(SysSceneFunction::getHospitalId,hospitalId)
                .eq(StringUtils.isNotBlank(isSelf),SysSceneFunction::getIsSelf,isSelf)
                .eq(StringUtils.isNotBlank(isIgnore),SysSceneFunction::getIsIgnore,isIgnore)
                .eq(StringUtils.isNotBlank(isSelfAgreement),SysSceneFunction::getIsSelfAgreement,isSelfAgreement)
                .eq(StringUtils.isNotBlank(auditScenario),SysSceneFunction::getSceneCode,auditScenario)
                .eq(StringUtils.isNotBlank(enable),SysSceneFunction::getEnable,enable));
        return functionList;
    }
}
