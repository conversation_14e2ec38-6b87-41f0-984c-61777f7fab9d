package com.crhms.cloud.mqs.mqs_kbm.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.List;

/**
 * 临床路径新增接口参数
 *
 * @author: wang<PERSON><PERSON>
 * @date: 2023.02.08
 */
@Data
public class CgPathSaveVo {
    /**
     * 适用科室列表
     */
    private List<String> deptNameList;

    /**
     * 临床路径文件名
     */
    @TableField
    private String fileName;

    /**
     * 临床路径文件oid
     */
    @TableField
    private String oid;

    /**
     * 发布时间:选择年份
     */
    @TableField
    private Integer releaseTime;
}
