package com.crhms.cloud.mqs.basic.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 审核接口基础类
 */
@Data
public class HisBaseAuditDto {
    /**
     * 批次号唯一
     */
    public String batch_no;
    /**
     * 院区id
     */
    public String hospital_id;

    /**
     * 审核场景
     */
    public String audit_scenario;

    /**
     *  就诊流水号（多单据审核可不填，要求唯一）
     */
    public String admission_no;

    /**
     * 是否预审 预审模式下，非违规单据不会自动保存
     */
    public String is_pretrial;

    /**
     * 业务数据
     */
    public List<HisMedicalParamsVo> data;

    /**
     * 审核规则
     */
    public List<Map<String,Object>> auditRules;



}
