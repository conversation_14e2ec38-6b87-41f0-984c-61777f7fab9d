package com.crhms.cloud.mqs.sys.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.Date;


/**
 * 病区表(IppZone)实体类
 *
 * <AUTHOR>
 * @since 2023-01-12 09:26:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("ipp_dept")
public class IppZone extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 356981640591045460L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_ORGANIZATION_CODE="organization_code";
    public static final String FIELD_CODE="code";
    public static final String FIELD_NAME="name";

        
    @TableId
    private Integer id;

    /**
     * 机构编码
     */    
    @TableField
    private String organizationCode;
    /**
     * 病区编码
     */    
    @TableField
    private String code;
    /**
     * 病区名称
     */    
    @TableField
    private String name;
        
    @TableField
    private Date createTime;

}

