package com.crhms.cloud.mqs.sys.scheduling;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiDeductionTemp;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFb;
import com.crhms.cloud.mqs.mqs_bmi.domain.BmiFbTemp;
import com.crhms.cloud.mqs.mqs_bmi.service.BmiDeductionTempService;
import com.crhms.cloud.mqs.mqs_bmi.service.BmiFbService;
import com.crhms.cloud.mqs.mqs_bmi.service.BmiFbTempService;
import com.crhms.cloud.mqs.sys.domain.SysConfig;
import com.crhms.cloud.mqs.sys.service.SysConfigService;
import com.crhms.cloud.mqs.sys.service.SysFundsService;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

/**
 * @Description   统计本年度基金年度基金使用情况
 * @ClassName BmiTask
 * <AUTHOR>
 * @date 2023.02.10 15:23
 */
@Log4j2
@Component
@EnableScheduling
public class StatisticsFundsTask implements SchedulingConfigurer {

    @Autowired
    protected SysConfigService sysConfigService;
    @Autowired
    protected SysFundsService sysFundsService;

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        scheduledTaskRegistrar.addTriggerTask(() -> process(),
                triggerContext -> {
                    SysConfig sysConfig = sysConfigService.queryByKey("FUNDS_AMOUNT_UPDATE_TIME", "1");
                    if (sysConfig == null || Strings.isEmpty(sysConfig.getSysValue())) {
                        log.warn("FUNDS_AMOUNT_UPDATE_TIME cron is null");
                    }
                    return new CronTrigger(sysConfig.getSysValue()).nextExecutionTime(triggerContext);
                });
    }

    private void process() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        log.info("执行 基金管理【"+ year +"】 定时任务 ===== start");
        sysFundsService.upDateUsedAmountByYear(Integer.toString(year),null);
    }
}
