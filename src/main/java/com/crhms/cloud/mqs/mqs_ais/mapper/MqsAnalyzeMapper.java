package com.crhms.cloud.mqs.mqs_ais.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.mqs.mqs_ais.vo.CsDiseaseDto;
import com.crhms.cloud.mqs.mqs_ais.vo.CsDiseaseQueryVo;
import com.crhms.cloud.mqs.mqs_ais.vo.ViolationQueryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 决策支持 数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-15 14:40:08
 */

@Mapper
@DS("clickhouse")
public interface MqsAnalyzeMapper {

    Map<String, Object> totalTimes(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    Map<String, Object> busDetailTotal(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> getAnalysisDetailsByDept(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> getAnalysisHisByDept(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> getAnalysisDetailsByDoc(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> getAnalysisHisByDoc(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> genptTrend(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> gendpTrendDept(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> gendpTrendDoc(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> feeTrend(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> feeTypeTrend(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> getAnalysisType(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("proDept") String proDept, @Param("proDoc") String proDoc, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> getAnalysisTypeHis(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("proDept") String proDept, @Param("proDoc") String proDoc, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    Map getFeeAnalysisTotal(@Param("queryVo") ViolationQueryVo queryVo, @Param("scenario") String scenario);

    List<Map> getFeeAnalysis(@Param("queryVo") ViolationQueryVo queryVo, @Param("scenario") String scenario, @Param("page") Page page);

    Map getVRuleTital(@Param("queryVo") ViolationQueryVo queryVo, @Param("scenario") String scenario);
    Map getVRuleTital2(@Param("queryVo") ViolationQueryVo queryVo, @Param("scenario") String scenario);

    List<Map> getVRuleListByPage(@Param("queryVo") ViolationQueryVo queryVo, @Param("scenario") String scenario, @Param("page") Page page);

    Map getVRuleListTotal(@Param("queryVo") ViolationQueryVo queryVo, @Param("scenario") String scenario);

    List<Map> getVRuleList(@Param("queryVo") ViolationQueryVo queryVo, @Param("scenario") String scenario);

    List<Map> getVItemTop10(@Param("queryVo") ViolationQueryVo queryVo, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> getVRuleTop10(@Param("queryVo") ViolationQueryVo queryVo, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);


    BigDecimal getcsTotalBmiYear(@Param("year") String year, @Param("hospitalId") String hospitalId);

    List<Map> getcsDiseaseTop5(@Param("year") String year, @Param("hospitalId") String hospitalId);

    List<Map> getcsDiseaseDetailTop5(@Param("year") String year, @Param("csDiseaseCodes") List<String> csDiseaseCodes, @Param("hospitalId") String hospitalId);

    List<CsDiseaseDto> getcsgetListByPage(@Param("queryVo") CsDiseaseQueryVo queryVo, @Param("page") Page page);

    List<Map> getRuleNumsByDept(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> getRuleNumsByDoc(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

    List<Map> getAnalysisTypeRuleNums(@Param("itemDateFrom") String itemDateFrom, @Param("itemDateTo") String itemDateTo, @Param("proDept") String proDept, @Param("proDoc") String proDoc, @Param("dept") String dept, @Param("scenario") String scenario, @Param("hospitalId") String hospitalId);

}
