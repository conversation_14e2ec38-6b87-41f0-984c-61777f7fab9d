<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.sys.mapper.SysRuleLevelThirdMapper">


    <select id="ruleLevelList" resultType="com.crhms.cloud.mqs.sys.domain.SysRuleLevelThird">
        SELECT
            t1.*,
            t2.`name`
        FROM
            mqs_sys_rule_level_third t1
                LEFT JOIN mqs_sys_rule_level t2 ON t1.sys_code = t2.`code` and t1.hospital_id = t2.hospital_id
        WHERE
            t1.hospital_id =  #{hospitalId}
        <if test="platform != null and platform != ''">
            and t1.platform = #{platform}
        </if>
    </select>
</mapper>

