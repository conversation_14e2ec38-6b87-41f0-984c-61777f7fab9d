package com.crhms.cloud.mqs.sys.utils;

import com.baomidou.dynamic.datasource.toolkit.CryptoUtils;

public class DBCryptoUtils extends CryptoUtils {

    // 第一种方式 使用默认key 加密解密
    public static void test1() throws Exception {
        System.out.println("-------------------------------------默认加密-------------------------------------");
        String password = "**";
        String encodePassword = CryptoUtils.encrypt(password);
        System.out.println("加密后密码:" + CryptoUtils.encrypt(password));
    }

    // 第二种方式 使用自定义key，强烈建议
    public static void test2() throws Exception {
        System.out.println("-------------------------------------自定义key-------------------------------------");
        String[] pair = CryptoUtils.genKeyPair(512);
        System.out.println("privateKey:  " + pair[0]);
        System.out.println("publicKey:  " + pair[1]);
        System.out.println("加密后密码mysql:  " + CryptoUtils.encrypt(pair[0], "**"));
        System.out.println("加密后密码CK:  " + CryptoUtils.encrypt(pair[0], "**"));

    }

    public static void main(String[] args) throws Exception {
        test1();
        test2();
    }
}
