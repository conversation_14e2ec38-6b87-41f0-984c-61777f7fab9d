package com.crhms.cloud.mqs.mqs_mr.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;


/**
 *  人工审核转自费转违规+审核 请求
 *
 * <AUTHOR>
 * @since 2022-12-19 09:40:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class MrChangeDTO {
    // no单据号 必输
    private String no;
    // 就诊流水号
    private String admissionNo;
    // 明细编码集合
    private List<String> detailNos;
    // 明细编码 必输
    private String detailNo;

    //是否违规
    private String violationFlag;
    //审核状态
    private String mrStatus;
    //人工审核原因
    private String mrOpinion;
    //标记（0-未标记，1-已标记）
    private String lables;

    //规则编码
    private String ruleCode;

    //规则级别
    private String ruleType;















    //审核场景
    private String auditScenario;
    // 院区id
    private String hospitalId;
    //是否自费
    private String selfExpense;

    //规则级别名称
    private String ruleTypeName;
    //批次号
    private String batchNo;
    //项目编码
    private String itemId;
    //项目名称
    private String itemName;

}

