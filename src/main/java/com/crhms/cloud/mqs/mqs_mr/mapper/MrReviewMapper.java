package com.crhms.cloud.mqs.mqs_mr.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.basic.vo.AuditPatientVO;
import com.crhms.cloud.mqs.basic.vo.BaseCaseQueryVO;
import com.crhms.cloud.mqs.basic.vo.ComprePopupDetailVO;
import com.crhms.cloud.mqs.mqs_mr.domain.MrLog;
import com.crhms.cloud.mqs.mqs_mr.domain.MrReview;
import com.crhms.cloud.mqs.mqs_mr.vo.MrDetailQueryVO;
import com.crhms.cloud.mqs.mqs_mr.vo.MrMedicalDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-01-18 15:40:47
 */

@Mapper
public interface MrReviewMapper {
    /**
     * 分页查询指定行数据
     *
     * @param table    指定表
     * @param queryVO  查询条件
     * @param pageable 分页对象
     * @return 对象列表
     */
    List<MrReview> queryListByPage(@Param("table") String table, @Param("queryVO") BaseCaseQueryVO queryVO, Page pageable);

    /**
     * 更新人工审核结果
     *
     * @param no
     * @param admissionNo
     * @param mrStatus
     * @param mrOpinion
     * @return
     */
    int updateReviewData(@Param("no") String no, @Param("admissionNo") String admissionNo, @Param("mrStatus") String mrStatus, @Param("lables") String lables, @Param("mrOpinion") String mrOpinion, @Param("hospitalId") String hospitalId, @Param("mrBy") String mrBy,@Param("mrByName") String mrByName);


    /**
     * 分页查询人工审核日志
     * @param no
     * @param admissionNo
     * @param userId
     * @param userName
     * @param hospitalId
     * @param pageable
     * @return
     */
    List<MrLog> queryLogByPage(@Param("no") String no, @Param("admissionNo") String admissionNo, @Param("userId") String userId, @Param("userName") String userName,  @Param("hospitalId") String hospitalId, Page pageable);

    /**
     * 查询明细的审核渠道以及临床处理状态
     * @param detailNos
     * @param hospitalId
     * @return
     */
    List<MrMedicalDetail> queryDetailSourceAndClinical(@Param("detailNos") List<String> detailNos,@Param("hospitalId") String hospitalId);

    /**
     * 查询主单信息
     * @param no
     * @param hospitalId
     * @return
     */
    BaseMedicalCase queryMrCase (@Param("no") String no, @Param("hospitalId") String hospitalId);

    /**
     * 根据单据号查询违规级别
     * @param nos
     * @param hospitalId
     */
    List<BaseMedicalDetail> queryMinRuleTypeByNo (@Param("nos") List<String> nos, @Param("hospitalId") String hospitalId);


    List<AuditPatientVO> queryMrAuditPatient (@Param("hospitalId") String hospitalId,@Param("patientName") String patientName,@Param("presentDeptCode") String presentDeptCode,@Param("docId") String docId,@Param("sysSceneCodes") List<String> sysSceneCodes);

    List<AuditPatientVO> queryMrBaseAuditPatient (@Param("hospitalId") String hospitalId,@Param("patientName") String patientName,@Param("presentDeptCode") String presentDeptCode,@Param("docId") String docId,@Param("sysSceneCodes") List<String> sysSceneCodes);

    /**
     * 查询单据明细
     * @param table
     * @param no
     * @param auditScenario
     * @param hospitalId
     * @return
     */
    BaseMedicalCase queryMrBaseMedicalCase(@Param("table") String table, @Param("no") String no, @Param("auditScenario") String auditScenario, @Param("hospitalId") String hospitalId);

    /**
     * 查询费用明细
     * @param baseDetailQueryVO
     * @return
     */
    List<MrMedicalDetail> queryMrDetailByPage(@Param("table") String table, @Param("queryVO") MrDetailQueryVO baseDetailQueryVO, @Param("pageable") Page pageable, @Param("hospitalId") String hospitalId);

    /**
     * 查询费用明细(根据项目id分组)
     * @param baseDetailQueryVO
     * @return
     */
    List<MrMedicalDetail> queryMrDetailByItemId(@Param("table") String table, @Param("queryVO") MrDetailQueryVO baseDetailQueryVO, @Param("pageable") Page pageable, @Param("hospitalId") String hospitalId);
    /**
     * 查询人工审核主单
     * @param queryVO
     * @param pageable
     * @return
     */
    List<BaseMedicalCase> queryMrCaseListByPage(@Param("queryVO") BaseCaseQueryVO queryVO, @Param("pageable") Page pageable,@Param("hospitalId") String hospitalId);

    /**
     * 根据明细号查询明细信息
     * @param detailNos
     * @param hospitalId
     * */
    List<MrMedicalDetail> queryMrDetailByDetailNos(@Param("table") String table,@Param("detailNos") List<String> detailNos,@Param("hospitalId") String hospitalId);

    /**
     * 更新人工审核自费状态结果
     *
     * @param comprePopupSaveVOs
     * @param hospitalId
     * @return
     */
    int updateSelfExpenseByDetailNo(@Param("items") List<ComprePopupDetailVO> comprePopupSaveVOs, @Param("hospitalId") String hospitalId);

    /**
     * 根据单据号+就诊流水号查询人工违规信息
     * @param table
     * @param nos
     * @param hospitalId
     * */
    List<MrMedicalDetail> queryMrBaseAudit(@Param("table") String table,@Param("nos") List<String> nos,@Param("hospitalId") String hospitalId);

    /**
     * 查询主单违规信息
     * @param no
     * @param admissionNo
     * @param hospitalId
     * @return
     */
    List<MrMedicalDetail> queryCaseAudit(@Param("no") String no, @Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    /**
     * 根据单据号查询人工质控主单信息
     * @param no
     * @param hospitalId
     * */
    BaseMedicalCase queryMrBaseByNo(@Param("no") String no,@Param("hospitalId") String hospitalId);

    /**
     * 根据单据号查询人工质控违规信息
     * @param no
     * @param hospitalId
     * */
    List<BaseMedicalCase> queryMrBaseAuditByNo(@Param("no") String no,@Param("hospitalId") String hospitalId);

    /**
     * 查询费用明细
     * @param no
     * @param hospitalId
     * @return
     */
    List<BaseMedicalDetail> queryMrCaseDetail(@Param("no") String no, @Param("hospitalId") String hospitalId);
}

