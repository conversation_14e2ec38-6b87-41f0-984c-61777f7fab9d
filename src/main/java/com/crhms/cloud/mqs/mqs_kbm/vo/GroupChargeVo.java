package com.crhms.cloud.mqs.mqs_kbm.vo;

import lombok.Data;

import java.util.List;

/**
 * 封装知识库管理-分组合理性-收费与入组合理性的查询条件
 *
 * @author: wangxingang
 * @date: 2023.02.07
 */
@Data
public class GroupChargeVo {

    /**
     * 类型
     */
    private List<String> typeList;

    /**
     * ADRG
     */
    private List<String> adrgList;

    /**
     * ADRG名称
     */
    private List<String> adrgNameList;

    /**
     * 项目编码
     */
    private String projCode;

    /**
     * 项目名称
     */
    private String projName;
}
