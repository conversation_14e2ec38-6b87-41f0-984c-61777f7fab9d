package com.crhms.cloud.mqs.ai.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalDetail;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrReviewMapper;
import com.crhms.cloud.mqs.mqs_mr.service.MrAuditService;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.utils.DictUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

@Slf4j
@Service
public class AiService {

    @Value("${crhms.mqs.ai.url}")
    private String aiUrl;

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    MrReviewMapper mrReviewMapper;

    private static String AI_REDIS_RESULT =  "CRHMS:MQS:AI:RESULT:{}";

    /**
     * ai审核指定单据
     * @param no
     * @return
     */
    public Map<String, Object> aiAudit(String no) {
        // 缓存获取 没有则调用ai审核结果
        String redisKey = StrUtil.format(AI_REDIS_RESULT, no);
        Map<String, Object> res =  (Map<String, Object>)redisTemplate.opsForValue().get(redisKey);
        if(Objects.isNull(res)){
            res = aiAuditReflush(no, buildAuditBody(no,"1"));
        }
        return res;
    }

    public Map<String, Object> aiAuditReflush(String no) {
        return aiAuditReflush(no, buildAuditBody(no,"1"));
    }


    /**
     * 查询审核报文数据
     * @param no
     * @return
     */
    private String buildAuditBody(String no,String hospitalId){

        BaseMedicalCase baseMedicalCase = mrReviewMapper.queryMrCase(no,hospitalId);

        if(Objects.isNull(baseMedicalCase)){
            throw new BaseException("无效的单据！");
        }
        List<BaseMedicalDetail> baseMedicalDetails =  mrReviewMapper.queryMrCaseDetail(no,hospitalId);

        //字典翻译
        DictUtils.translateDict(baseMedicalCase);
        DictUtils.translateDict(baseMedicalDetails);

        JSONObject medCase = new JSONObject();
        JSONArray details = new JSONArray();
        if(Objects.nonNull(baseMedicalCase.getPatientBirthday())){
            baseMedicalCase.setAge(DateUtil.age(DateUtil.parseDate(baseMedicalCase.getPatientBirthday()),Objects.isNull(baseMedicalCase.getAdmissionDate())? new Date() : baseMedicalCase.getAdmissionDate()));
        }
        medCase.set("psn_name",baseMedicalCase.getPatientName());
        medCase.set("med_type_name",baseMedicalCase.getClaimTypeId());
        medCase.set("age",baseMedicalCase.getAge());
        medCase.set("gend_name",baseMedicalCase.getPatientGender());
        medCase.set("begntime", baseMedicalCase.getAdmissionDate() ==null ? "" : DateFormatUtils.format(baseMedicalCase.getAdmissionDate(), "yyyy-MM-dd HH:mm:ss") );
        medCase.set("endtime",baseMedicalCase.getDischargeDate() ==null ? "" : DateFormatUtils.format(baseMedicalCase.getDischargeDate(), "yyyy-MM-dd HH:mm:ss") );
        medCase.set("dise_name",baseMedicalCase.getOutDiagnosisName());
        medCase.set("insutype_name",baseMedicalCase.getBenefitTypeId());
        for (BaseMedicalDetail baseMedicalDetail : baseMedicalDetails) {
            JSONObject detail = new JSONObject();
            detail.set("fee_ocur_time", baseMedicalDetail.getBillDate() ==null ? "" :DateFormatUtils.format( baseMedicalDetail.getBillDate(), "yyyy-MM-dd HH:mm:ss") );
            detail.set("cnt",baseMedicalDetail.getNumbers());
            detail.set("price",baseMedicalDetail.getPrice());
            detail.set("det_item_fee_sumamt",baseMedicalDetail.getCosts());
            detail.set("spec",baseMedicalDetail.getSpecification());
            detail.set("hilist_name",baseMedicalDetail.getItemName());
            detail.set("hilist_code",baseMedicalDetail.getItemId());

            details.add(detail);
        }
        medCase.set("fee_list",details);

        return medCase.toString();
    }

    private Map<String, Object> aiAuditReflush(String no, String body) {

        // 获取当前时间戳
        long timestamp = System.currentTimeMillis();

        // 构造请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("api-key", "GDFS4682");
        headers.put("api-timestamp", String.valueOf(timestamp));

        log.debug("AI审核传参入参body：" + body);

        try {
            String sign = hmacSha1("TS76JDH109U", "GDFS4682" + timestamp);
            headers.put("api-sign", sign);
        } catch (Exception e) {
            throw new RuntimeException("生成签名失败", e);
        }

        // 发起 POST 请求
        HttpResponse response = HttpRequest.post(aiUrl + "/cerebra/v1/fee/analyze")
                .addHeaders(headers)
                .body(body)
                .execute();

        // 解析返回结果
        if (response.isOk()) {
            String result = response.body();
            JSONObject jsonObject = JSONUtil.parseObj(result);
            //缓存记录
            String redisKey = StrUtil.format(AI_REDIS_RESULT, no);
            redisTemplate.opsForValue().set(redisKey, jsonObject);
            return jsonObject; // 返回 Map 形式的结果
        } else {
            throw new RuntimeException("AI 接口调用失败，状态码：" + response.getStatus());
        }

    }



    /**
     * HMAC-SHA1加密
     *
     * @param key
     * @param message
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public String hmacSha1(String key, String message) throws NoSuchAlgorithmException, InvalidKeyException {
        // 创建一个 HMAC-SHA1 的 Mac 实例
        Mac sha1_HMAC = Mac.getInstance("HmacSHA1");

        // 初始化密钥
        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
        sha1_HMAC.init(secret_key);

        // 更新消息并完成哈希计算
        byte[] hash = sha1_HMAC.doFinal(message.getBytes(StandardCharsets.UTF_8));

        // 将结果转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        try (Formatter formatter = new Formatter(hexString)) {
            for (byte b : hash) {
                formatter.format("%02x", b);
            }
        }
        return hexString.toString();
    }
}
