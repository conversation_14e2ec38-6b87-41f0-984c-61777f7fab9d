package com.crhms.cloud.mqs.mqs_kbm.vo;

import lombok.Data;

import java.util.List;

/**
 * 临床路径列表查询条件
 *
 * @author: wangxingang
 * @date: 2023.02.08
 */
@Data
public class CgPathQueryVo {
    /**
     * 适用科室
     */
    private List<String> deptNameList;

    /**
     * 临床路径
     */
    private String fileName;

    /**
     * 发布时间
     */
    private List<Integer> releaseTimeList;

    /**
     * 来源
     */
    private List<String> dataSourceList;

}
