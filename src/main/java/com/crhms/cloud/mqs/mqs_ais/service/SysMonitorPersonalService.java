package com.crhms.cloud.mqs.mqs_ais.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_ais.domain.SysMonitorPersonal;
import com.crhms.cloud.mqs.mqs_ais.mapper.SysMonitorPersonalMapper;
import com.crhms.cloud.mqs.mqs_ais.service.SysMonitorPersonalService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 运营管理-基金管理-个性化配置(SysMonitorPersonal)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-20 10:38:07
 */
@Service("sysMonitorPersonalService")
public class SysMonitorPersonalService extends ServiceImpl< SysMonitorPersonalMapper, SysMonitorPersonal> {

}
