package com.crhms.cloud.mqs.sys.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.sys.domain.SysRuleBroadHeading;
import com.crhms.cloud.mqs.sys.mapper.SysRuleBroadHeadingMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 规则与规则大类对应关系 service
 *
 * @author: wangxingang
 * @date: 2023.02.24
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysRuleBroadHeadingService extends ServiceImpl<SysRuleBroadHeadingMapper, SysRuleBroadHeading> {
}
