package com.crhms.cloud.mqs.mqs_kbm.vo;

import lombok.Data;

/**
 * 临床指南-"概述"页面信息
 *
 * @author: wangxingang
 * @date: 2023.02.08
 */
@Data
public class CgGuideSummary {

    /**
     * 疾病编码
     */
    private String diseaseCode;

    /**
     * 疾病名称
     */
    private String diseaseName;

    /**
     * 关联ICD10
     */
    private String icd10;

    /**
     * 疾病概念
     */
    private String diseaseEname;

    /**
     * 疾病别名1
     */
    private String otherName1;

    /**
     * 疾病别名2
     */
    private String otherName2;

    /**
     * 疾病别名3
     */
    private String otherName3;

    /**
     * 疾病别名4
     */
    private String otherName4;

    /**
     * 疾病别名5
     */
    private String otherName5;

    /**
     * 临床分型
     */
    private String clinicalClass;

    /**
     * 治疗通则
     */
    private String treatment;

    /**
     * 资料来源
     */
    private String dataSource;

}
