<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_pm.mapper.PmStdLogMapper">


    <select id="queryPage" resultType="com.crhms.cloud.mqs.mqs_pm.domain.PmStdLog">
        select
            t.id,
            category,
            log,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            ti.username as updated_by
        from mqs_pm_std_log t
        left join ipp_user ti on ti.id = t.created_by
        <where>
            <if test="categoryList != null and categoryList.size > 0">
                t.category in
                <foreach collection="categoryList" open="(" close=")" separator="," item="category">
                    #{category}
                </foreach>
            </if>
            <if test="updateBy != null and updateBy != '' ">
                and ti.username like CONCAT('%',#{updateBy},'%')
            </if>
        </where>
        ORDER BY last_updated_date DESC
    </select>
</mapper>