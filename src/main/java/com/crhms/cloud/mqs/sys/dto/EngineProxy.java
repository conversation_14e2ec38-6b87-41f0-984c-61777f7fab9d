package com.crhms.cloud.mqs.sys.dto;

import cn.hutool.json.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.Map;


/**
 * 引擎请求转发信息
 * <AUTHOR>
 * @date 2023/3/20
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class EngineProxy {

    /**
     * 请求类型 GET/POST
     */
    private String method;

    /**
     * 请求URL
     */
    private String url;

    /**
     * 请求参数
     */
    private Map<String, Object> params;

    /**
     * 请求头
     */
    private Map<String, String> headers;

    /**
     * 文件
     */
    private MultipartFile file;

    /**
     * 请求体
     */
    private Object body;

}

