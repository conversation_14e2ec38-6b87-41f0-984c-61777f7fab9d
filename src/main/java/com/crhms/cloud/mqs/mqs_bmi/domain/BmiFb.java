package com.crhms.cloud.mqs.mqs_bmi.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 医保管理-申诉审核表(BmiFb)实体类
 *
 * <AUTHOR>
 * @since 2023-02-10 10:40:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_bmi_fb")
public class BmiFb extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -61042679674543465L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_BATCH_NO="batch_no";
    public static final String FIELD_DETAIL_NO="detail_no";
    public static final String FIELD_NO="no";
    public static final String FIELD_ADMISSION_NO="admission_no";
    public static final String FIELD_BMI_NUMBER="bmi_number";
    public static final String FIELD_IS_MATCH="is_match";
    public static final String FIELD_IS_REPEAT="is_repeat";
    public static final String FIELD_FB_DEPT_CODE="fb_dept_code";
    public static final String FIELD_FB_DEPT_NAME="fb_dept_name";
    public static final String FIELD_APPEAL_DATE="appeal_date";
    public static final String FIELD_CLAIM_TYPE="claim_type";
    public static final String FIELD_BILL_DATE="bill_date";
    public static final String FIELD_PATIENT_ID="patient_id";
    public static final String FIELD_PATIENT_NAME="patient_name";
    public static final String FIELD_PATIENT_GENDER="patient_gender";
    public static final String FIELD_PATIENT_BIRTHDAY="patient_birthday";
    public static final String FIELD_BENEFIT_TYPE="benefit_type";
    public static final String FIELD_PERSONNEL_TYPE="personnel_type";
    public static final String FIELD_DEPT_CODE="dept_code";
    public static final String FIELD_DEPT_NAME="dept_name";
    public static final String FIELD_DOC_ID="doc_id";
    public static final String FIELD_DOC_NAME="doc_name";
    public static final String FIELD_ITEM_DATE="item_date";
    public static final String FIELD_ITEM_ID="item_id";
    public static final String FIELD_ITEM_NAME="item_name";
    public static final String FIELD_PROPOSAL_CODE="proposal_code";
    public static final String FIELD_NUMBERS="numbers";
    public static final String FIELD_PRICE="price";
    public static final String FIELD_SPECIFICATION="specification";
    public static final String FIELD_USAGE="usages";
    public static final String FIELD_PTYPE="ptype";
    public static final String FIELD_RULES="rules";
    public static final String FIELD_BMI_CONVERED_AMOUNT="bmi_convered_amount";
    public static final String FIELD_DEDUCTION_AMOUNT="deduction_amount";
    public static final String FIELD_DEDUCTION_REASON="deduction_reason";
    public static final String FIELD_DEDUCTION_AMOUNT1="deduction_amount1";
    public static final String FIELD_DEDUCTION_AMOUNT2="deduction_amount2";
    public static final String FIELD_DEDUCTION_AMOUNT3="deduction_amount3";
    public static final String FIELD_STATUS="status";
    public static final String FIELD_FB_STAUTS="fb_stauts";
    public static final String FIELD_FB_REASON="fb_reason";
    public static final String FIELD_VERSION="version";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * id
     */    
    @TableId
    private Long id;

    /**
     * 导入批次号
     */    
    @TableField
    private String batchNo;
    /**
     * 交易流水号
     */    
    @TableField
    private String detailNo;
    /**
     * 单据号
     */    
    @TableField
    private String no;
    /**
     * 就诊流水号
     */    
    @TableField
    private String admissionNo;
    /**
     * 医保反馈编码
     */    
    @TableField
    private String bmiNumber;
    /**
     * 是否匹配
     */    
    @TableField
    private String isMatch;
    /**
     * 是否重复
     */    
    @TableField
    private String isRepeat;
    /**
     * 反馈科室编码
     */    
    @TableField
    private String fbDeptCode;
    /**
     * 反馈科室名称
     */    
    @TableField
    private String fbDeptName;
    /**
     * 申诉截止时间
     */    
    @TableField
    private Date appealDate;
    /**
     * 医疗类别（原就医方式）
     */    
    @TableField
    private String claimType;
    /**
     * 就诊类型1.门诊 2 住院
     */
    @TableField
    private String visitType;
    /**
     * 单据结算日期
     */    
    @TableField
    private Date billDate;
    /**
     * 参保人编码
     */    
    @TableField
    private String patientId;
    /**
     * 参保人姓名
     */    
    @TableField
    private String patientName;
    /**
     * 性别
     */    
    @TableField
    private String patientGender;
    /**
     * 出生日期
     */    
    @TableField
    private String patientBirthday;
    /**
     * 参保类型
     */    
    @TableField
    private String benefitType;
    /**
     * 人员类别
     */    
    @TableField
    private String personnelType;
    /**
     * 反馈申诉
     */
    @TableField
    private String fbComplain;
    /**
     * 科室编码
     */    
    @TableField
    private String deptCode;
    /**
     * 科室名称
     */    
    @TableField
    private String deptName;
    /**
     * 医生编码
     */    
    @TableField
    private String docId;
    /**
     * 医生名称
     */    
    @TableField
    private String docName;
    /**
     * 项目日期(费用日期)
     */    
    @TableField
    private Date itemDate;
    /**
     * 项目编码
     */    
    @TableField
    private String itemId;
    /**
     * 项目名称
     */    
    @TableField
    private String itemName;
    /**
     * 意见书编码
     */    
    @TableField
    private String proposalCode;
    /**
     * 数量
     */    
    @TableField
    private BigDecimal numbers;
    /**
     * 单价
     */    
    @TableField
    private BigDecimal price;
    /**
     * 规格
     */    
    @TableField
    private String specification;
    /**
     * 每次用量
     */    
    @TableField
    private String usages;
    /**
     * 项目类型名称（0：药品，1：项目）
     */    
    @TableField
    private String ptype;
    /**
     * 规则名称
     */    
    @TableField
    private String rules;
    /**
     * 医保内金额
     */    
    @TableField
    private BigDecimal bmiConveredAmount;
    /**
     * 扣款金额
     */    
    @TableField
    private BigDecimal deductionAmount;
    /**
     * 扣款原因
     */    
    @TableField
    private String deductionReason;
    /**
     * 还款原因
     */
    @TableField
    private String returnReason;
    /**
     * 单据扣除金额
     */    
    @TableField
    private BigDecimal deductionAmount1;
    /**
     * 主单扣除金额
     */    
    @TableField
    private BigDecimal deductionAmount2;
    /**
     * 明细扣除金额
     */    
    @TableField
    private BigDecimal deductionAmount3;
    /**
     * 处理状态 0.待分配 1.待反馈 2.已反馈 3.已退回 4.已确认 5. 已过期 6.已申诉
     */    
    @TableField
    private String status;
    /**
     * 扣费意见类型 1.同意扣费 0.不同意扣费
     */    
    @TableField
    private String fbStauts;
    /**
     * 意见描述
     */    
    @TableField
    private String fbReason;
    /**
     * 版本号
     */    
    @TableField
    private String version;
    /**
     * 医院id
     */    
    @TableField
    private String hospitalId;
    /**
     * 机构名称
     */
    @TableField
    private String org;
    /**
     * 费用机构编码
     */
    @TableField
    private String feeOrgCode;
    /**
     * 费用机构名称
     */
    @TableField
    private String feeOrgName;

}

