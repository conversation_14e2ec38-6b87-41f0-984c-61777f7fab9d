package com.crhms.cloud.mqs.mqs_mr.domain;

import java.util.Date;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 人工审核流程配置表(MrPlan)实体类
 *
 * <AUTHOR>
 * @since 2023-02-06 14:58:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_mr_plan")
public class Mr<PERSON><PERSON> extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -24285598356407451L;
    
    public static final String FIELD_ID="id";
    public static final String FIELD_ENABLE="enable";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * id
     */    
    @TableId
    private Long id;

    /**
     * 是否启用1.启用 0.禁用
     */
    @TableField
    private String enable;

    /**
     * 场景分类（op-门诊/ hp-住院）
     */
    @TableField
    private String sceneClassification;

    /**
     * 数据条件类型（1-数据进入条件/2-数据审核条件）
     */
    @TableField
    private String dataConditionsType;

    /**
     * 数据条件值（1-审核流程节点进入/2-业务流程节点进入/3-1且2/4-1或2）
     */
    @TableField
    private String dataConditions;

    /**
     * 审核场景(审核流程节点)
     */
    @TableField
    private String auditScenario;

    /**
     * 业务流程节点 (1-已开预出院、2-今日出院、3-已出院、4-已结算)
     */
    @TableField
    private String businessProcessNode;

    /**
     * 系统规则级别((多级别,分开))
     */
    @TableField
    private String ruleLevel1;

    /**
    * 院区id
    */
    @TableField
    private String hospitalId;
}

