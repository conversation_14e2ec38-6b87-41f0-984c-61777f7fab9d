package com.crhms.cloud.mqs.mqs_mr.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.user.dto.CustomUser;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.vo.BaseCaseQueryVO;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlan;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlanAudit;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrPlanMapper;
import com.crhms.cloud.mqs.mqs_mr.vo.MrPlanVO;
import com.crhms.cloud.mqs.sys.dto.IppUser;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.mapper.IppMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 人工审核流程配置表(MrPlan)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-06 14:35:12
 */
@Slf4j
@Service("mrPlanService")
public class MrPlanService extends ServiceImpl<MrPlanMapper, MrPlan> {

    @Autowired
    private IppMapper ippMapper;

    @Autowired
    private MrPlanAuditService mrPlanAuditService;

    @Autowired
    private RedisTemplate redisTemplate;

    /*
    *  查询人工审核配置信息
    * */
    public MrPlanVO queryList() {
        String hospitalId = LoginContext.getHospitalId();
        List<MrPlan> allPlans = list(new LambdaQueryWrapper<MrPlan>().eq(MrPlan::getHospitalId, hospitalId));
        if(CollectionUtil.isEmpty(allPlans)){
            return null;
        }
        Map<Boolean, List<MrPlan>> partitionedMap = allPlans.stream()
                .collect(Collectors.partitioningBy(obj -> "op".equals(obj.getSceneClassification())));
        // 门诊数据
        List<MrPlan> opPlans = partitionedMap.get(true);
        // 住院数据
        List<MrPlan> hpPlans = partitionedMap.get(false);
        //查询审核人员+科室配置信息
        List<MrPlanAudit> audits = mrPlanAuditService.list(new LambdaQueryWrapper<MrPlanAudit>().eq(MrPlanAudit::getHospitalId, hospitalId));
        MrPlanVO mrPlanVO = new MrPlanVO();
        mrPlanVO.setOpData(opPlans);
        mrPlanVO.setHpData(hpPlans);
        mrPlanVO.setAudits(audits);
        return mrPlanVO;
    }

    /*
     *  新增/修改配置信息
     * @Param mrPlanVO 人工审核配置信息
     * */
    @Transactional(rollbackFor = Exception.class)
    public void submit (MrPlanVO mrPlanVO) {
        String hospitalId = LoginContext.getHospitalId();
        if(Objects.nonNull(mrPlanVO)){
            //删除旧数据
            remove(new LambdaQueryWrapper<MrPlan>().eq(MrPlan::getHospitalId, hospitalId));
            mrPlanAuditService.remove(new LambdaQueryWrapper<MrPlanAudit>().eq(MrPlanAudit::getHospitalId,hospitalId));
            //批量新增
            List<MrPlan> allPlans = Stream.concat(mrPlanVO.getOpData().stream(), mrPlanVO.getHpData().stream()).map(plan->{plan.setHospitalId(hospitalId);return plan;}).collect(Collectors.toList());
            List<MrPlanAudit> planAudits = mrPlanVO.getAudits();
            for(MrPlanAudit mrPlanAudit : planAudits){
                if(StringUtils.isBlank(mrPlanAudit.getMrPlanUser()) || StringUtils.isBlank(mrPlanAudit.getMrPlanDepartment())){
                    throw new RuntimeException("审核人以及科室不允许为空");
                }
                mrPlanAudit.setHospitalId(hospitalId);
            }
            mrPlanAuditService.saveBatch(planAudits);
            saveBatch(allPlans);

            //放入缓存
            try{
                setMrPlanConfigCache(hospitalId,allPlans);
                setMrPlanConfigAuditCache(hospitalId,planAudits);
            } catch (Exception e) {
                log.info("人工审核配置缓存添加失败！！！");
            }
        }
    }

    /*
    *  根据医院id,数据条件类型获取人工审核配置信息
    * */
    public Map<String,List<MrPlan>> queryByHospitalIdAndType (String hospitalId,String dataConditionsType) {
        List<MrPlan> list = null;
        //首先从redis获取缓存
        Object obj = getMrPlanConfigCache(hospitalId);
        if(Objects.nonNull(obj)){
            list = (List<MrPlan>) obj;
        } else {
            list = list(new LambdaQueryWrapper<MrPlan>().eq(MrPlan::getHospitalId,hospitalId));
            if(CollectionUtil.isNotEmpty(list)){
                setMrPlanConfigCache(hospitalId,list);
            } else {
                return Collections.emptyMap();
            }
        }
        //按照场景进行分组
        return list.stream().filter(mrPlan->dataConditionsType.equals(mrPlan.getDataConditionsType())).collect(Collectors.groupingBy(MrPlan::getSceneClassification));
    }

    /*
     *  根据医院id,数据条件类型获取人工审核配置信息
     * */
    public List<MrPlanAudit> queryMrPlanAuditByHospitalId (String hospitalId) {
        List<MrPlanAudit> list = null;
        //首先从redis获取缓存
        Object obj = getMrPlanConfigAuditCache(hospitalId);
        if(Objects.nonNull(obj)){
            list = (List<MrPlanAudit>) obj;
        } else {
            list = mrPlanAuditService.list(new LambdaQueryWrapper<MrPlanAudit>().eq(MrPlanAudit::getHospitalId,hospitalId));
            if(CollectionUtil.isNotEmpty(list)){
                setMrPlanConfigAuditCache(hospitalId,list);
            } else {
                return Collections.emptyList();
            }
        }
        //按照场景进行分组
        return list;
    }

    /*
     *  恢复默认
     * */
    public void restoreDefault () {
        String jsonStr = "{\n" +
                "  \"opData\":[\n" +
                "    {\n" +
                "      \"createdDate\":\"2024-10-18 14:59:32\",\n" +
                "      \"lastUpdatedDate\":\"2024-10-18 14:59:32\",\n" +
                "      \"id\":\"1847170633478692866\",\n" +
                "      \"enable\":\"1\",\n" +
                "      \"sceneClassification\":\"op\",\n" +
                "      \"dataConditionsType\":\"1\",\n" +
                "      \"dataConditions\":\"1\",\n" +
                "      \"auditScenario\":\"opPct\",\n" +
                "      \"ruleLevel1\":\"2,1,4,5,6,3\",\n" +
                "      \"hospitalId\":\"1\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"createdDate\":\"2024-10-18 14:59:32\",\n" +
                "      \"lastUpdatedDate\":\"2024-10-18 14:59:32\",\n" +
                "      \"id\":\"1847170633520635905\",\n" +
                "      \"enable\":\"1\",\n" +
                "      \"sceneClassification\":\"op\",\n" +
                "      \"dataConditionsType\":\"2\",\n" +
                "      \"dataConditions\":\"1\",\n" +
                "      \"auditScenario\":\"opPct\",\n" +
                "      \"hospitalId\":\"1\",\n" +
                "      \"ruleLevel1\":\"2,1,4,5,6,3\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"hpData\":[\n" +
                "    {\n" +
                "      \"createdDate\":\"2024-10-18 14:59:32\",\n" +
                "      \"lastUpdatedDate\":\"2024-10-18 14:59:32\",\n" +
                "      \"id\":\"1847170633529024513\",\n" +
                "      \"enable\":\"1\",\n" +
                "      \"sceneClassification\":\"hp\",\n" +
                "      \"dataConditionsType\":\"1\",\n" +
                "      \"dataConditions\":\"1\",\n" +
                "      \"auditScenario\":\"hpBk\",\n" +
                "      \"businessProcessNode\":\"4\",\n" +
                "      \"ruleLevel1\":\"1,6,5,4,3,2\",\n" +
                "      \"hospitalId\":\"1\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"createdDate\":\"2024-10-18 14:59:32\",\n" +
                "      \"lastUpdatedDate\":\"2024-10-18 14:59:32\",\n" +
                "      \"id\":\"1847170633529024514\",\n" +
                "      \"enable\":\"1\",\n" +
                "      \"sceneClassification\":\"hp\",\n" +
                "      \"dataConditionsType\":\"2\",\n" +
                "      \"dataConditions\":\"4\",\n" +
                "      \"auditScenario\":\"hpPred\",\n" +
                "      \"businessProcessNode\":\"1\",\n" +
                "      \"hospitalId\":\"1\",\n" +
                "      \"ruleLevel1\":\"1,6,5,4,3,2\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"audits\":[\n" +
                "    {\n" +
                "      \"createdDate\":\"2024-10-18 14:59:32\",\n" +
                "      \"lastUpdatedDate\":\"2024-10-18 14:59:32\",\n" +
                "      \"id\":\"1847170633180897282\",\n" +
                "      \"mrPlanUser\":\"crhms,admin,医保审核01\",\n" +
                "      \"mrPlanDepartment\":\"203030,0001\",\n" +
                "      \"hospitalId\":\"1\",\n" +
                "      \"mrPlanUserName\":\"内置账号,医院管理员,零一\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"mrPlanUser\":\"医保审核02\",\n" +
                "      \"mrPlanUserName\":\"灵儿\",\n" +
                "      \"mrPlanDepartment\":\"0001\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"mrPlanUser\":\"医保审核03\",\n" +
                "      \"mrPlanUserName\":\"零散\",\n" +
                "      \"mrPlanDepartment\":\"203030\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        MrPlanVO mrPlanVO = JSONUtil.toBean(jsonStr,MrPlanVO.class);
        submit(mrPlanVO);
    }

    /*
    * 查询拥有某个菜单权限的用户信息
    * */
    public List<IppUser> queryAuditPerson (String menuCode) {
        //查询当前菜单所属角色
        List<String> roleIds = ippMapper.queryRoleByMenuCode (menuCode);
        //很具角色查询用户信息
        List<IppUser> users = ippMapper.queryUserNameAndId(roleIds);
        if(CollectionUtil.isNotEmpty(users)){
            //去重
            return users.stream().distinct().collect(Collectors.toList());
        } else {
            return users;
        }
    }

    /*
    * 获取人工审核权限
    * */
    public boolean getAuth (String hospitalId,BaseCaseQueryVO queryVO){
        //查询人工审核配置按权限查看数据
        //List<MrPlanAudit> mrPlanAudits = mrPlanAuditService.list(new LambdaQueryWrapper<MrPlanAudit>().eq(MrPlanAudit::getHospitalId, hospitalId));
        List<MrPlanAudit> mrPlanAudits = queryMrPlanAuditByHospitalId(hospitalId);
        //如果配置了审核人
        if(CollectionUtil.isNotEmpty(mrPlanAudits)){
            //审核人过滤
            CustomUser user = LoginContext.getUser();
            String userName = user.getUsername();
            //筛选出未配置审核人或者配置了当前登陆人的记录
            List<MrPlanAudit> filterAudits = mrPlanAudits.stream().filter(planAudit-> planAudit.getMrPlanUser().contains(userName)).collect(Collectors.toList());
            //如果没有空配置项以及配置的审核人不包含当前用户
            if(CollectionUtil.isEmpty(filterAudits)){
                return false;
            }
            //科室权限过滤
            List<String> deptCodes = filterAudits.stream().filter(planAudit-> StringUtils.isNotBlank(planAudit.getMrPlanDepartment())).map(planAudit-> planAudit.getMrPlanDepartment()).collect(Collectors.toList());
            //拆分去重
            List<String> result = deptCodes.stream()
                    .flatMap(item -> Arrays.stream(item.split(",")))
                    .distinct()
                    .collect(Collectors.toList());
            //和前端传入的科室进行比较
            if(CollectionUtil.isNotEmpty(result)){
                List<String> presentDeptCodes = queryVO.getPresentDeptCodes();
                //如果前端没有传入科室筛选条件则以人工审核配置的科室为准
                if(CollectionUtil.isEmpty(presentDeptCodes)){
                    queryVO.setPresentDeptCodes(result);
                } else {
                    //权限设置的审核科室和前端传入的当前科室取交集
                    List<String> intersection = result.stream()
                            .filter(presentDeptCodes::contains)
                            .collect(Collectors.toList());
                    if(CollectionUtil.isEmpty(intersection)){
                        return false;
                    } else {
                        queryVO.setPresentDeptCodes(intersection);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取人工审核配置缓存信息
     * */
    public Object getMrPlanConfigCache (String hospitalId) {
        String redisKey = StrUtil.format(GloablData.GLOBAL_MR_CONFIG_KEY,hospitalId);
        return redisTemplate.opsForValue().get(redisKey);
    }

    /**
     * 保存人工审核配置缓存信息
     * */
    public void setMrPlanConfigCache (String hospitalId,List<MrPlan> list){
        String redisKey = StrUtil.format(GloablData.GLOBAL_MR_CONFIG_KEY,hospitalId);
        redisTemplate.delete(redisKey);
        redisTemplate.opsForValue().set(redisKey,list);
    }

    /**
     * 获取人工审核配置缓存信息
     * */
    public Object getMrPlanConfigAuditCache (String hospitalId) {
        String redisKey = StrUtil.format(GloablData.GLOBAL_MR_CONFIG_AUDIT_KEY,hospitalId);
        return redisTemplate.opsForValue().get(redisKey);
    }

    /**
     * 保存人工审核配置缓存信息
     * */
    public void setMrPlanConfigAuditCache (String hospitalId,List<MrPlanAudit> list){
        String redisKey = StrUtil.format(GloablData.GLOBAL_MR_CONFIG_AUDIT_KEY,hospitalId);
        redisTemplate.delete(redisKey);
        redisTemplate.opsForValue().set(redisKey,list);
    }
}
