package com.crhms.cloud.mqs.mqs_kbm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.mqs_kbm.domain.TcmDisease;
import com.crhms.cloud.mqs.mqs_kbm.domain.TcmSyndrome;
import com.crhms.cloud.mqs.mqs_kbm.service.TcmDiseaseService;
import com.crhms.cloud.mqs.mqs_kbm.service.TcmSyndromeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 知识库管理-中医诊断 controller, 包括中医疾病分类与代码和中医证候分类与代码的查询
 *
 * @author: wangxingang
 * @date: 2023.02.03
 */

@RestController
@RequestMapping("/api/mqs/kbm/tcm")
public class TcmController {


    @Autowired
    TcmDiseaseService diseaseService;

    @Autowired
    TcmSyndromeService syndromeService;


    /**
     * 查询中医疾病分类与代码
     *
     * @param diseaseCode 疾病名编码
     * @param diseaseName 疾病名
     */
    @GetMapping("/disease/page")
    public ResponseEntity<List<TcmDisease>> queryDisease(@RequestParam(value = "diseaseCode", required = false) String diseaseCode,
                                                         @RequestParam(value = "diseaseName", required = false) String diseaseName,
                                                         @RequestParam(value = "page", defaultValue = "1") int page,
                                                         @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<TcmDisease> pageInfo = PageUtil.getPage(page, pageSize);
        List<TcmDisease> diseaseList = diseaseService.queryList(pageInfo, diseaseCode, diseaseName);
        return new ResponseEntity<>(diseaseList, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

    /**
     * 查询中医证候分类与代码
     *
     * @param syndromeCode 证候名编码
     * @param syndromeName 证候名
     */
    @GetMapping("/syndrome/page")
    public ResponseEntity<List<TcmSyndrome>> querySyndrome(@RequestParam(value = "syndromeCode", required = false) String syndromeCode,
                                                           @RequestParam(value = "syndromeName", required = false) String syndromeName,
                                                           @RequestParam(value = "page", defaultValue = "1") int page,
                                                           @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<TcmSyndrome> pageInfo = PageUtil.getPage(page, pageSize);
        List<TcmSyndrome> syndromeList = syndromeService.queryList(pageInfo, syndromeCode, syndromeName);
        return new ResponseEntity<>(syndromeList, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }
}
