package com.crhms.cloud.mqs.mqs_ais.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.mqs_ais.domain.SysMonitorPersonal;
import com.crhms.cloud.mqs.mqs_ais.service.MqsAuditMonitorService;
import com.crhms.cloud.mqs.mqs_ais.service.MqsBmiMonitorService;
import com.crhms.cloud.mqs.mqs_ais.service.SysMonitorPersonalService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 运营监控 - 基金监控
 *
 * <AUTHOR>
 * @date 2023/3/17
 **/
@RestController
@RequestMapping("api/mqs/bmi/monitor")
public class MqsBmiMonitorController {

    @Autowired
    private MqsBmiMonitorService monitorService;
    @Autowired
    private SysMonitorPersonalService personalService;

    /**
     * 今日实时
     *
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * <AUTHOR>
     * @date 2023/03/17
     */
    @GetMapping("/realTime")
    public ResponseEntity<Map<String, Object>> realTime(@RequestParam(value = "auditScenario") String auditScenario) {
        Map<String, Object> result = monitorService.realTime(auditScenario, LoginContext.getHospitalId());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    /**
     * 年度使用情况
     *
     * @param filterYear    前端只传年份
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * <AUTHOR>
     * @date 2023/03/17
     */
    @GetMapping("/annualUsage")
    public ResponseEntity<Map<String, Object>> annualUsage(@RequestParam(value = "filterYear") String filterYear,
                                                           @RequestParam(value = "auditScenario") String auditScenario) {
        Map<String, Object> result = monitorService.annualUsage(filterYear, auditScenario, LoginContext.getHospitalId());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 支出金额 - 总额预付基金
     *
     * @param filterYear    年份
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     */
    @GetMapping("/usedBmiAmountByMonth")
    public ResponseEntity<List<Map<String, Object>>> usedBmiAmountByMonth(@RequestParam(value = "filterYear") String filterYear,
                                                                    @RequestParam(value = "auditScenario") String auditScenario) {
        return new ResponseEntity<>(monitorService.usedBmiAmountByMonth(filterYear, auditScenario, LoginContext.getHospitalId()), HttpStatus.OK);
    }

    /**
     * 支出金额 - 特殊基金
     *
     * @param filterYear    年份
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     */
    @GetMapping("/usedBmiAloneAmount")
    public ResponseEntity<Map<String, Object>> usedBmiAloneAmount(@RequestParam(value = "filterYear") String filterYear,
                                                                    @RequestParam(value = "auditScenario") String auditScenario) {
        return new ResponseEntity<>(monitorService.usedBmiAloneAmount(filterYear, auditScenario, LoginContext.getHospitalId()), HttpStatus.OK);
    }


    /**
     * 费用结构异常占比
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     */
    @GetMapping("/costStructure")
    public ResponseEntity<List<Map<String, Object>>> costStructure(@RequestParam(value = "filterYear") String filterYear,
                                                                   @RequestParam(value = "filterMonth", required = false) String filterMonth,
                                                                   @RequestParam(value = "filterDay", required = false) String filterDay,
                                                                   @RequestParam(value = "auditScenario") String auditScenario) {
        return new ResponseEntity<>(monitorService.costStructure(filterYear, filterMonth, filterDay, auditScenario, LoginContext.getHospitalId()), HttpStatus.OK);
    }

    /**
     * 各科室基金使用情况
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     */
    @GetMapping("/deptFundsBmiAmount")
    public ResponseEntity<List<Map<String, Object>>> deptFundsBmiAmount(@RequestParam(value = "filterYear") String filterYear,
                                                                        @RequestParam(value = "filterMonth", required = false) String filterMonth,
                                                                        @RequestParam(value = "filterDay", required = false) String filterDay,
                                                                        @RequestParam(value = "auditScenario") String auditScenario) {
        return new ResponseEntity<>(monitorService.deptFundsBmiAmount(filterYear, filterMonth, filterDay, auditScenario, LoginContext.getHospitalId()), HttpStatus.OK);
    }

    /**
     * 消息提醒
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     */
    @GetMapping("/getMessageWarn")
    public ResponseEntity<Map<String, Object>> getMessageWarn(@RequestParam(value = "filterYear") String filterYear,
                                                                        @RequestParam(value = "filterMonth", required = false) String filterMonth,
                                                                        @RequestParam(value = "filterDay", required = false) String filterDay,
                                                                        @RequestParam(value = "auditScenario") String auditScenario) {
        return new ResponseEntity<>(monitorService.getMessageWarn(filterYear, filterMonth, filterDay, auditScenario, LoginContext.getHospitalId()), HttpStatus.OK);
    }

    /**
     * 页面个性化配置 - 编辑
     *
     */
    @PostMapping("/personal/edit")
    public ResponseEntity editPersonalMonitor(@RequestBody SysMonitorPersonal sysMonitorPersonal) {
        sysMonitorPersonal.setUserId(Long.toString(LoginContext.getUserId()));
        sysMonitorPersonal.setHospitalId(LoginContext.getHospitalId());
        personalService.saveOrUpdate(sysMonitorPersonal);
        return ResponseEntity.ok("success");
    }
    /**
     * 页面个性化配置 - 编辑
     *
     */
    @GetMapping("/personal/get")
    public ResponseEntity<SysMonitorPersonal> editPersonalMonitor() {
        return ResponseEntity.ok(personalService.getOne(new QueryWrapper<SysMonitorPersonal>().eq(SysMonitorPersonal.FIELD_USER_ID,Long.toString(LoginContext.getUserId())).eq(SysMonitorPersonal.FIELD_HOSPITAL_ID,LoginContext.getHospitalId())));
    }

}

