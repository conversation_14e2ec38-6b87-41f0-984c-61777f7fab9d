# crhms-mqs

### 介绍
诊间审核2.0服务

### 模块
| 模块编码 | 模块名称           | 备注 | 
|---------------|--------------| ---- |
| std | 数据标准 |  |


### CI/CD
**注意：开发阶段不需要手动发版，当代码变更提交/合并到dev分支后，会自动触发流水线进行开发环境发布。**

测试环境、演示环境发布需要手动触发流水线：
- 创建tag
```
在GitLab中，点击【仓库】->【标签】菜单，点击【新建标签】按钮；
填写【标签名称(Tag name)】，选择【分支(Create from)】指定要发布的分支，点击【创建标签】按钮。
```
- 等待流水线执行完毕
```
点击【CI/CD】->【流水线】菜单，即能看到刚刚创建tag触发的自动部署流水线。
等待流水线所有节点执行完毕，表示代码就已经成功更新到k8s集群中。
```

- 创建标签时的Tag name，请遵循**tag命名规则**，否则将无法触发流水线：

| 环境 | 规则           | 示例 | 
|---------------|--------------| ---- |
| 开发环境 | dev-[日期][序号] | dev-2021091201 |
| 测试环境 | test-[日期][序号] | test-2021091201 |
| 演示环境 | demo-[日期][序号] | demo-2021091201 |

### 环境变量
服务提供以下环境变量，以方便标准镜像的对外发布。

| 变量 | 说明 | 示例 |
| ---- | ---- | ---- |
| NACOS_SERVER | nacos的地址 | 172.25.1.54:8848 |
| NACOS_NAMESPACE | nacos环境 | dev |

使用示例：
- docker run -e NACOS_SERVER=172.25.1.54:8848 ...  
- 其他集群编排工具的环境变量配置

### 数据库变更
严禁直接修改已发版的表结构，如需修改旧表结构，需评审后将脚本加入下个版本的发布计划中！

#### 1.创建表
规范:
- changeSet 的id格式：cdmp - 版本 - t - 表名 - 当前时间
- 列的类型可以填写: bigint(长整数)、int(整数)、varchar(字符)、text(长字符)、datetime(时间)、date(日期)，liquibase会自动处理与目标数据库类型的映射关系。
```xml
    <!-- sys_user表创建 -->
    <changeSet id="cdmp-v1.0.0-t-sys_user-202204131316" author="zhaoxudong">
        <createTable tableName="sys_user" remarks="用户信息表">
            <!--主键-->
            <column name="ID" type="bigint" remarks="ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="NAME" type="varchar(255)" remarks="姓名">
                <constraints nullable="false"/>
            </column>
            <column name="STATUS" type="int(5)" remarks="状态: 1启用, 2不启用"/>
            <column name="REMARK" type="text" remarks="用户说明"/>

            <column name="CREATED_BY" type="bigint" remarks="创建人" defaultValueComputed="-1"/>
            <column name="CREATED_DATE" type="datetime" remarks="创建时间" defaultValueComputed="${now}"/>
            <column name="LAST_UPDATED_BY" type="bigint" remarks="更新人" defaultValueComputed="-1"/>
            <column name="LAST_UPDATED_DATE" type="datetime" remarks="更新时间" defaultValueComputed="${now}"/>
            <column name="TENANT_ID" type="bigint" remarks="租户ID"/>
        </createTable>
    </changeSet>
```

#### 2.创建索引
规范:
- changeSet 的id格式：cdmp - 版本 - inx - 索引名 - 当前时间
```xml
    <!-- sys_user表索引: 用户名&租户ID -->
    <changeSet id="cdmp-v1.0.0-inx-sys_user_u1-202204131338" author="zhaoxudong" failOnError="false" >
        <createIndex tableName="sys_user" indexName="sys_user_u1" unique="true">
            <column name="NAME"/>
            <column name="TENANT_ID"/>
        </createIndex>
    </changeSet>
```

#### 3.添加字段
规范:
- changeSet 的id格式：cdmp - 版本 - alt - 表名 - 当前时间
```xml
    <!-- sys_user表修改: 添加PASSWORD字段 -->
    <changeSet id="cdmp-v1.0.0-alt-sys_user-202204131337" author="zhaoxudong" >
        <addColumn tableName="sys_user">
            <column name="PASSWORD" type="varchar(100)" remarks="密码"/>
        </addColumn>
    </changeSet>
```

#### 4.删除字段
规范:
- changeSet 的id格式：cdmp - 版本 - alt - 表名 - 当前时间
```xml
    <!-- sys_user表修改：删除REMARK字段 -->
    <changeSet id="cdmp-v1.0.0-alt-sys_user-202204131342" author="zhaoxudong" failOnError="false" >
        <dropColumn tableName="sys_user" columnName="REMARK" />
    </changeSet>
```
