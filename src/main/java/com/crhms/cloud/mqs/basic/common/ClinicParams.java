package com.crhms.cloud.mqs.basic.common;


import lombok.Data;

import java.util.List;

@Data
public class ClinicParams {
    private String hospitalId;
    private String hospital_id;
    private String auditScenario;
    private String IsIncrement;
    private String no;
    private String docId;
    private String presentDeptCode;
    private String selfExpense;
    private String itemName;
    private String itemTypeCode;
    private String categoryName;
    private String clinicalStatus;
    private String auditSource;
    private String outpatientMedication;
    private List<String> ruleTypes;
    private String admission_no;
    private String batch;
    private String detail_no;
    private String audit_scenario;
    private String ruleOrigin;
    private String isHis;
}
