package com.crhms.cloud.mqs.mqs_mr.manager;



import com.crhms.cloud.mqs.basic.domain.BaseAudit;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalStats;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlan;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/*
*  责任连执行类
* */
@Component
public class MrPlanFilterImpl {

    /*
    * 配置节点顺序并获取执行结果
    * @param sceneCodes  场景编码集合
    * @param auditScenario 主单所属场景
    * @param baseAudits 审核结果
    * @parame mrPlan  人工审核配置信息
    * */
    public boolean exec (MrPlan mrPlan, BaseMedicalCase medicalCase, List<BaseAudit> baseAudits, List<String> sceneCodes, String auditScenario, Map<String,List<BaseMedicalStats>> baseMedicalStatsMap) {
        MrPlanFilter node1 = new DataReviewConditions();
        MrPlanFilter node2 = new DataBussinessConditions();
        MrPlanFilter node3 = new RuleLevel();
        node1.setNextFilter(node2);
        node2.setNextFilter(node3);
        //节点跳过标识
        String filterFlag = "";
        //判断节点略过条件
        if("1".equals(mrPlan.getDataConditions())){
            filterFlag = "DataBussinessConditions";
        } else if ("2".equals(mrPlan.getDataConditions())){
            filterFlag = "DataReviewConditions";
        }
        //执行
        return node1.filter(medicalCase, mrPlan,baseAudits, filterFlag,sceneCodes,auditScenario, baseMedicalStatsMap);
    }
}
