package com.crhms.cloud.mqs.mqs_mr.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_mr.domain.MrLog;
import com.crhms.cloud.mqs.mqs_mr.mapper.MrLogMapper;
import com.crhms.cloud.mqs.mqs_mr.service.MrLogService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 人工审核日志(MrLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-06 17:51:53
 */
@Service("mrLogService")
public class MrLogService extends ServiceImpl< MrLogMapper, MrLog> {
    @Resource
    private MrLogMapper mrLogMapper;



}
