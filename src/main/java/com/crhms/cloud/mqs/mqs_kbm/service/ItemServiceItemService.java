package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.ItemServiceItem;
import com.crhms.cloud.mqs.mqs_kbm.mapper.ItemServiceItemMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 知识库管理-医保项目目录-国家医保服务项目信息 service
 *
 * @author: wangxingang
 * @date: 2023/02/03
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class ItemServiceItemService extends ServiceImpl<ItemServiceItemMapper, ItemServiceItem> {

    /**
     * 查询列表 - 国家医保服务项目信息
     *
     * @param pageInfo    分页信息
     * @param projectCode 项目代码
     * @param projectName 项目名称
     */
    public List<ItemServiceItem> queryList(Page<ItemServiceItem> pageInfo, String projectCode, String projectName) {
        return baseMapper.selectPage(pageInfo, new LambdaQueryWrapper<ItemServiceItem>()
                .like(StrUtil.isNotEmpty(projectCode), ItemServiceItem::getProjectCode, projectCode)
                .like(StrUtil.isNotEmpty(projectName), ItemServiceItem::getProjectName, projectName)
        ).getRecords();
    }
}
