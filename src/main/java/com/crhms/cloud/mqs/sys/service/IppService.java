package com.crhms.cloud.mqs.sys.service;


import com.crhms.cloud.mqs.sys.domain.IppDept;
import com.crhms.cloud.mqs.sys.domain.IppDoctor;
import com.crhms.cloud.mqs.sys.domain.IppZone;
import com.crhms.cloud.mqs.sys.mapper.IppMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class IppService {
    @Autowired
    IppMapper ippMapper;

    /**
     * 查询部门列表
     * @param dept
     * @param hospitalId
     * @return
     */
    public List<IppDept> selectDeptList(String dept, String hospitalId){
        return ippMapper.selectDeptList(dept,hospitalId);
    }

    /**
     * 查询医生列表
     * @param doctor
     * @param hospitalId
     * @return
     */
    public List<IppDoctor> selectDoctorList( String doctor, String hospitalId){
        return ippMapper.selectDoctorList(doctor,hospitalId);
    }
    /**
     * 查询病区列表
     * @param zone
     * @param hospitalId
     * @return
     */
    public List<IppZone> selectZoneList(String zone, String hospitalId){
        return ippMapper.selectZoneList(zone,hospitalId);
    }

    /**
     * 查询病区列表
     * @param user
     * @param hospitalId
     * @return
     */
    public List<Map> selectUserList(String user, String hospitalId){
        return ippMapper.selectUserList(user, null, hospitalId);
    }

    /**
     * 查询病区Map
     * @param zone
     * @param hospitalId
     * @return
     */
    public Map<String, IppZone> selectZoneMap(String zone, String hospitalId){
        return ippMapper.selectZoneMap(zone,hospitalId);
    }
}
