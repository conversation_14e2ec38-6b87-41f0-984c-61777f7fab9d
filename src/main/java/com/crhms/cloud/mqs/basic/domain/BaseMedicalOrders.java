package com.crhms.cloud.mqs.basic.domain;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.googlecode.jmapper.annotations.JMapConversion;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * 医嘱明细(BaseMedicalOrders)实体类
 *
 * <AUTHOR>
 * @since 2023-04-10 13:47:54
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
public class BaseMedicalOrders extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 278173647350430885L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_ORDER_DETAIL_NO="order_detail_no";
    public static final String FIELD_ADMISSION_NO="admission_no";
    public static final String FIELD_ORDER_NO="order_no";
    public static final String FIELD_ORDER_SUB_NO="order_sub_no";
    public static final String FIELD_ITEM_NO="item_no";
    public static final String FIELD_ITEM_ID="item_id";
    public static final String FIELD_ITEM_NAME="item_name";
    public static final String FIELD_ITEM_TYPE_CODE="item_type_code";
    public static final String FIELD_ITEM_TYPE_NAME="item_type_name";
    public static final String FIELD_ITEM_DATE="item_date";
    public static final String FIELD_BROAD_HEADING="broad_heading";
    public static final String FIELD_APPLY_DEPT_CODE="apply_dept_code";
    public static final String FIELD_APPLY_DEPT_NAME="apply_dept_name";
    public static final String FIELD_NUMBERS="numbers";
    public static final String FIELD_SPECIFICATION="specification";
    public static final String FIELD_USAGE_UNIT="usage_unit";
    public static final String FIELD_USAGE="usage";
    public static final String FIELD_FREQUENCY_INTERVAL="frequency_interval";
    public static final String FIELD_PRICE="price";
    public static final String FIELD_COSTS="costs";
    public static final String FIELD_BATCH_NO="batch_no";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 医嘱明细流水号
     */    
    @TableField
    private String orderDetailNo;
    /**
     * 单据号
     */
    @TableField
    private String no;
    /**
     * 就诊流水号
     */    
    @TableField
    private String admissionNo;
    /**
     * 医嘱序号
     */    
    @TableField
    private String orderNo;
    /**
     * 医嘱子序号
     */    
    @TableField
    private String orderSubNo;
    /**
     * 项目序号
     */    
    @TableField
    private String itemNo;
    /**
     * 项目编码
     */    
    @TableField
    private String itemId;
    /**
     * 项目名称
     */    
    @TableField
    private String itemName;
    /**
     * 项目类型编码
     */    
    @TableField
    private String itemTypeCode;
    /**
     * 项目类型名称
     */    
    @TableField
    private String itemTypeName;
    /**
     * 项目日期
     */    
    @TableField
    private Date itemDate;
    /**
     * 项目大类 1.医嘱项目 2.计价项目
     */    
    @TableField
    private String broadHeading;
    /**
     * 开医嘱科室编码
     */    
    @TableField
    private String applyDeptCode;
    /**
     * 开医嘱科室名称
     */    
    @TableField
    private String applyDeptName;
    /**
     * 数量
     */    
    @TableField
    private BigDecimal numbers;
    /**
     * 规格
     */    
    @TableField
    private String specification;
    /**
     * 包装/剂量/计价单位
     */    
    @TableField
    private String usageUnit;
    /**
     * 每次用量/药品一次使用剂量
     */    
    @TableField
    private String usage;
    /**
     * 使用频次(频次值集)
     */    
    @TableField
    private String frequencyInterval;
    /**
     * 单价
     */    
    @TableField
    private BigDecimal price;
    /**
     * 总费用
     */    
    @TableField
    private BigDecimal costs;
    /**
     * 下达医嘱时间
     */
    @TableField
    private Date enterDateTime;
    /**
     * 开医嘱医生编码
     */
    @TableField
    private String applyDoctorCode;
    /**
     * 开医嘱医生名称
     */
    @TableField
    private String applyDoctorName;
    /**
     * 长期医嘱标记  1.长期 0.临时
     */
    @TableField
    private String repeatIndicator;

    /**
     * 执行时间详细描述
     */
    @TableField
    private String freqDetail;

    /**
     * 频率次数
     */
    @TableField
    private String  freqCounter;

    /**
     * 频率间隔
     */
    @TableField
    private String freqInterval;
    /**
     * 频率间隔单位
     */
    @TableField
    private String freqIntervalUnit;
    /**
     * 开始日期
     */
    @TableField
    private Date startDateTime;
    /**
     * 停止日期
     */
    @TableField
    private Date stopDateTime;

    /**
     * 批次号
     */    
    @TableField
    private String batchNo;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;

    /**
     * jmapper类型转换
     *
     */
    @JMapConversion(from = {"item_date","enter_date_time","start_date_time","stop_date_time"},
            to = {"itemDate", "enterDateTime","startDateTime","stopDateTime"})
    public Date convertDateFields(String dateString) throws ParseException {
        if (StrUtil.isBlank(dateString)) {
            return null;
        }
        for (String format : GloablData.DATE_FORMATS) {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            try {
                return sdf.parse(dateString);
            } catch (ParseException e) {
                // 忽略该异常，尝试下一个格式
            }
        }
        throw new RuntimeException("无法解析日期: " + dateString);
    }

}

