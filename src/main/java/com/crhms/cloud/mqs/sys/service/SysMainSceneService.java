package com.crhms.cloud.mqs.sys.service;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.mqs.sys.domain.SysMainScene;
import com.crhms.cloud.mqs.sys.mapper.SysMainSceneMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 运营管理-主审核场景(SysMainSceneService)服务类
 *
 * <AUTHOR>
 * @since 2024-09-12 13:37:48
 */
@Service
@Slf4j
public class SysMainSceneService extends ServiceImpl<SysMainSceneMapper, SysMainScene> {

    /*
     * 批量新增主场景
     * @param mainScenes  主场景集合
     * */
      public void submitAll (List<SysMainScene> mainScenes) {
          boolean result = mainScenes.stream()
                                   .map(SysMainScene::getMainSceneCode)
                                   .anyMatch(code -> !Collections.addAll(new HashSet<>(), code));
          if(result){
              throw new BaseException("当前主场景已存在，请重试！！！");
          }
          saveOrUpdateBatch(mainScenes);
      }

     /*
     * 根据hospitalId 删除全部主场景信息
     * @param hospitalId  医院id
     * */
      public void removeAll (String hospitalId) {
          getBaseMapper().delete(new LambdaQueryWrapper<SysMainScene>().eq(SysMainScene::getHospitalId,hospitalId));
      }

    /*
     * 删除不在当前ids内的所有记录
     * @param hospitalId  医院id
     * */
      public void removeOtherByIds (List<SysMainScene> list , String hospitalId) {
          if(CollectionUtil.isEmpty(list)){
              getBaseMapper().delete(new LambdaQueryWrapper<SysMainScene>().eq(SysMainScene::getHospitalId,hospitalId));
          } else {
              List<Long> ids = list.stream().map(SysMainScene::getId).collect(Collectors.toList());
              getBaseMapper().delete(new LambdaQueryWrapper<SysMainScene>().eq(SysMainScene::getHospitalId,hospitalId).notIn(SysMainScene::getId,ids));
          }
      }
}
