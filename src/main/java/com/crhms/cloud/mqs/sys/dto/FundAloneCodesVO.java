package com.crhms.cloud.mqs.sys.dto;

import com.crhms.cloud.mqs.sys.domain.SysScene;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 特殊基金数据vo
 *
 * @author: zhaoyac
 * @date: 2023.03.29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FundAloneCodesVO {

    //门诊
    //慢特病集
    private List<String> ignoreOpCsDiseaseCode;
    //是否单病种
    private boolean ignoreOpSingleDisease;
    //大病集
    private List<String> ignoreOpSiDiagnosisCode;
    //靶向药集
    private List<String> ignoreOpTdDieasess;

    //住院
    //慢特病集
    private List<String> ignoreHpCsDiseaseCode;
    //是否单病种
    private boolean ignoreHpSingleDisease;
    //大病集
    private List<String> ignoreHpSiDiagnosisCode;
    //靶向药集
    private List<String> ignoreHpTdDieasess;

}
