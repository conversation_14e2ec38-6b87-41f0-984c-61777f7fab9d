package com.crhms.cloud.mqs.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.sys.domain.SysScene;
import com.crhms.cloud.mqs.sys.domain.SysSceneFunction;
import com.crhms.cloud.mqs.sys.dto.SysSceneDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 系统管理-审核场景-功能配置(SysSceneFunction)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-06 09:24:09
 */

@Mapper
public interface SysSceneFunctionMapper extends BaseMapper<SysSceneFunction> {

    /**
     * 根据编码批量修改
     * @param sceneFunctions   子场景配置集合
     * @return
     */
    @Update({
            "<script>",
            "<foreach collection='list' item='sceneFunction' open='' separator=';' close=''>",
            "UPDATE mqs_sys_scene_function",
            "SET function_code = #{sceneFunction.functionCode}, ",
            "is_self = #{sceneFunction.isSelf}, ",
            "self_paid_condition = #{sceneFunction.selfPaidCondition}, ",
            "is_self_agreement = #{sceneFunction.isSelfAgreement}, ",
            "is_ignore = #{sceneFunction.isIgnore}",
            "WHERE scene_code = #{sceneFunction.sceneCode} AND hospital_id = #{sceneFunction.hospitalId}",
            "</foreach>",
            "</script>"
    })
    void batchUpdateConfigByCode (@Param("list") List<SysSceneDTO> sceneFunctions);
}

