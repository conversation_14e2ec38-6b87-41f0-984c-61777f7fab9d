package com.crhms.cloud.mqs.mqs_bmi.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 *  院内反馈明细实体类
 *
 * <AUTHOR>
 * @since 2023-02-08 17:07:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
public class FbMedicalDetailDto implements Serializable {


    /**
     * 明细流水号
     */    
    private String detailNo;
    /**
     * 单据号
     */    
    private String no;
    /**
     * 项目编码
     */    
    private String itemId;
    /**
     * 项目名称
     */    
    private String itemName;
    /**
     * 项目类型名称 0.药品 1.项目
     */    
    private String ptype;
    /**
     * 项目类型编码(A-Z大类)
     */
    private String itemTypeCode;
    /**
     * 项目类型名称
     */
    private String itemTypeName;
    /**
     * 项目日期
     */    
    private Date itemDate;
    /**
     * 数量
     */    
    private BigDecimal numbers;
    /**
     * 单价
     */    
    private BigDecimal price;
    /**
     * 总费用
     */    
    private BigDecimal costs;
    /**
     * 医保内金额
     */    
    private String bmiConveredAmount;
    /**
     * 医生
     */
    private String doctorName;
    /**
     * 医生
     */
    private String violationFlag;
    /**
     * 科室
     */
    private String deptName;
    /**
     * 违规名称集
     */
    private String ruleNames;


    private String relatedRecords;

    private String batchNo;


}

