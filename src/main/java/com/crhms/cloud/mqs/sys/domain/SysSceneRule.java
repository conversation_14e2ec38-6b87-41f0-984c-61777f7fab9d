package com.crhms.cloud.mqs.sys.domain;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 运营管理-审核场景-规则(SysSceneRule)实体类
 *
 * <AUTHOR>
 * @since 2022-12-19 09:40:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_scene_rule")
public class SysSceneRule extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 651838138633004110L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_SCENE_CODE="scene_code";
    public static final String FIELD_RULE_CODE="rule_code";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 审核场景
     */    
    @TableField
    private String sceneCode;
    /**
     * 规则编码
     */    
    @TableField
    private String ruleCode;

    /**
     * 院区id
     */
    @TableField
    private String hospitalId;

}

