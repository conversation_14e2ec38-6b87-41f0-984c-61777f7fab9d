package com.crhms.cloud.mqs.basic.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.Date;

/**
 * 住院/门诊审核-通用(BaseMedicalStats)实体类
 *
 * <AUTHOR>
 * @since 2024-10-22 17:07:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_base_medical_stats")
public class BaseMedicalStats extends BaseDomain implements Serializable {

    /**
     *  单据号
     */
    @TableId
    private String no;

    /**
     * 预出院状态（1-已开预出院，0-未开预出院）
     */
    @TableField
    @DictTranslate(dictCode = "LOCLA_PRE_DISCHARGE")
    private String preDischarge;

    /**
     * 患者id
     */
    @TableField
    private String patientId;

    /**
     * 人员类别
     */
    @TableField
    @DictTranslate(dictCode = "DIC_PSN_TYPE")
    private String personnelType;
    /**
     * 参保类型编码
     */
    @TableField
    @DictTranslate(dictCode = "DIC_INSUR_TYPE")
    private String benefitTypeId;

    /**
     * 预出院时间
     */
    @TableField
    private Date preDischargeDate;

    /**
     * 开出院医嘱日期
     */
    @TableField
    private Date dischargeOrderDate;

    /**
     * 已出院（1-是，0-否）
     */
    @TableField
    private String discharged;

    /**
     * 已结算（1-是，0-否）
     */
    @TableField
    private String settled;

    /**
     * 院区id
     */
    @TableField
    private String hospitalId;


}
