package com.crhms.cloud.mqs.sys.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统日志 实体类
 *
 * <AUTHOR>
 * @since 2024-11-01 13:37:48
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_logs")
public class SysLogs extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -83110375245574167L;

    @TableId
    private Long id;

    //操作人
    @TableField
    private String operatorName;

    //操作人id
    @TableField
    private String operatorId;

    //操作时间
    @TableField
    private Date operatorTime;

    //操作内容(批量）转违规项、（批量）撤销转违规项、标记、取消标记、审核完成、撤销审核完成、生成自费协议、转自费、转医保、忽略、取消忽略)
    @TableField
    private String operatorText;

    //就诊流水号
    @TableField
    private String admissionNo;

    //患者id
    @TableField
    private String patientId;

    //患者名称
    @TableField
    private String patientName;

    //明细号
    @TableField
    private String detailNo;

    //项目编码
    @TableField
    private String itemId;

    //项目名称
    @TableField
    private String itemName;

    //项目时间
    @TableField
    private Date itemDate;

    //操作来源（sys-系统接口，mr-人工审核，opup-弹窗）
    @TableField
    private String operatorSource;

    //医院id
    @TableField
    private String hospitalId;

}
