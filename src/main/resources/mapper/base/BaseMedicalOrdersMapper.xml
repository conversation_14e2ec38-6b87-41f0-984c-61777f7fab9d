<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.basic.mapper.BaseMedicalOrdersMapper">


    <sql id="BaseOrdersSql">
        order_detail_no, no, admission_no, order_no, order_sub_no, enter_date_time, repeat_indicator, item_no, item_id, item_name, item_type_code, item_type_name, item_date, broad_heading, apply_dept_code, apply_dept_name, apply_doctor_code, apply_doctor_name, numbers, specification, usage_unit, `usage`, frequency_interval, freq_detail, freq_counter, freq_interval, freq_interval_unit, price, costs, start_date_time, stop_date_time, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id
    </sql>

    <delete id="delOrders">
        DELETE
        from
        ${table}_orders
        where
        hospital_id = #{hospitalId}
        and admission_no = #{admissionNo}
        <if test="no != null and no != ''" >
            and `no` = #{no}
        </if>
        <if test="orderDetailNo != null and orderDetailNo != ''" >
            and order_detail_no = #{orderDetailNo}
        </if>

    </delete>

    <delete id="deleteOrdersByAdNo">
        DELETE FROM ${table}_orders
        WHERE
        hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and admission_no = #{admissionNo}
        </if>
        <if test="nos != null and nos.size() >0 ">
            and `no` in
            <foreach collection="nos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="orderDetailNos != null and orderDetailNos.size() >0 ">
            and order_detail_no in
            <foreach collection="orderDetailNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </delete>
    <select id="queryOrdersHisByBatchNo" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalOrders">
        SELECT
        <include refid="BaseOrdersSql"/>
        FROM ${table}_orders_his
        WHERE  hospital_id = #{hospitalId}
        AND batch_no = #{batchNo}
        <if test="admissionNo != null and admissionNo != ''">
            AND admission_no = #{admissionNo}
        </if>
    </select>


    <insert id="batchInsertOrders">
        insert into ${table}_orders (order_detail_no, no, admission_no, order_no, order_sub_no, enter_date_time, repeat_indicator, item_no, item_id, item_name, item_type_code, item_type_name, item_date, broad_heading, apply_dept_code, apply_dept_name, apply_doctor_code, apply_doctor_name, numbers, specification, usage_unit, `usage`, frequency_interval, freq_detail, freq_counter, freq_interval, freq_interval_unit, price, costs, start_date_time, stop_date_time, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderDetailNo}, #{entity.no}, #{entity.admissionNo}, #{entity.orderNo}, #{entity.orderSubNo}, #{entity.enterDateTime}, #{entity.repeatIndicator}, #{entity.itemNo}, #{entity.itemId}, #{entity.itemName}, #{entity.itemTypeCode}, #{entity.itemTypeName}, #{entity.itemDate}, #{entity.broadHeading}, #{entity.applyDeptCode}, #{entity.applyDeptName}, #{entity.applyDoctorCode}, #{entity.applyDoctorName}, #{entity.numbers}, #{entity.specification}, #{entity.usageUnit}, #{entity.usage}, #{entity.frequencyInterval}, #{entity.freqDetail}, #{entity.freqCounter}
            , #{entity.freqInterval}, #{entity.freqIntervalUnit}, #{entity.price}, #{entity.costs}, #{entity.startDateTime}, #{entity.stopDateTime}, #{entity.batchNo}, #{entity.lastUpdatedBy}, now(), #{entity.createdBy}, now(), #{entity.hospitalId})
            </foreach>
    </insert>

    <insert id="batchInsertOrdersHis">
        insert into ${table}_orders_his (order_detail_no, no, admission_no, order_no, order_sub_no, enter_date_time, repeat_indicator, item_no, item_id, item_name, item_type_code, item_type_name, item_date, broad_heading, apply_dept_code, apply_dept_name, apply_doctor_code, apply_doctor_name, numbers, specification, usage_unit, `usage`, frequency_interval, freq_detail, freq_counter, freq_interval, freq_interval_unit, price, costs, start_date_time, stop_date_time, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderDetailNo}, #{entity.no}, #{entity.admissionNo}, #{entity.orderNo}, #{entity.orderSubNo}, #{entity.enterDateTime}, #{entity.repeatIndicator}, #{entity.itemNo}, #{entity.itemId}, #{entity.itemName}, #{entity.itemTypeCode}, #{entity.itemTypeName}, #{entity.itemDate}, #{entity.broadHeading}, #{entity.applyDeptCode}, #{entity.applyDeptName}, #{entity.applyDoctorCode}, #{entity.applyDoctorName}, #{entity.numbers}, #{entity.specification}, #{entity.usageUnit}, #{entity.usage}, #{entity.frequencyInterval}, #{entity.freqDetail}, #{entity.freqCounter}
            , #{entity.freqInterval}, #{entity.freqIntervalUnit}, #{entity.price}, #{entity.costs}, #{entity.startDateTime}, #{entity.stopDateTime}, #{entity.batchNo}, #{entity.lastUpdatedBy}, now(), #{entity.createdBy}, now(), #{entity.hospitalId})
        </foreach>
    </insert>

</mapper>

