package com.crhms.cloud.mqs.mqs_mr.manager;


import com.crhms.cloud.mqs.basic.domain.BaseAudit;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalStats;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlan;

import java.util.List;
import java.util.Map;

public abstract class MrPlanFilter {

    protected MrPlanFilter nextFilter;

    public void setNextFilter(MrPlanFilter nextFilter) {
        this.nextFilter = nextFilter;
    }

    // 过滤方法
    public abstract boolean filter(BaseMedicalCase medicalCase, MrPlan mrPlan, List<BaseAudit> baseAudits, String filterFlag, List<String> sceneCodes, String auditScenario, Map<String,List<BaseMedicalStats>> baseMedicalStatsMap);

    // 调用下一个过滤器
    protected boolean next( BaseMedicalCase medicalCase,Mr<PERSON><PERSON> mrPlan,List<BaseAudit> baseAudits, String filterFlag,List<String> sceneCodes,String auditScenario,Map<String,List<BaseMedicalStats>> baseMedicalStatsMap) {
        if (nextFilter != null) {
            return nextFilter.filter(medicalCase,mrPlan,baseAudits, filterFlag,sceneCodes,auditScenario, baseMedicalStatsMap);
        }
        return true;
    }
}
