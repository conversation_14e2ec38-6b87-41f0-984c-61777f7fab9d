package com.crhms.cloud.mqs.sys.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.sys.domain.SysFundsAlone;
import com.crhms.cloud.mqs.sys.mapper.SysFundsAloneMapper;
import com.crhms.cloud.mqs.sys.mapper.SysFundsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 运营管理-基金管理-单独管理(SysFundsAlone)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-21 18:49:56
 */
@Service("sysFundsAloneService")
public class SysFundsAloneService extends ServiceImpl< SysFundsAloneMapper, SysFundsAlone> {
    @Resource
    private SysFundsAloneMapper sysFundsAloneMapper;
    @Autowired
    private SysFundsMapper sysFundsMapper;

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    public boolean deleteById(Long id) {
        SysFundsAlone sysFundsAlone = sysFundsAloneMapper.selectById(id);
        this.sysFundsAloneMapper.deleteById(id);
        if(sysFundsAlone != null){
            sysFundsMapper.syncTotalAmount(sysFundsAlone.getFundsId(),sysFundsAlone.getHospitalId());
        }
        return true;
    }
}
