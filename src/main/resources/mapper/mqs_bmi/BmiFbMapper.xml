<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.mqs_bmi.mapper.BmiFbMapper">


    <sql id="baseSql">
        id, batch_no, detail_no, no, admission_no, bmi_number, is_match, is_repeat, fb_dept_code, fb_dept_name, appeal_date, claim_type, bill_date, patient_id, patient_name, patient_gender, patient_birthday, benefit_type, personnel_type, dept_code, dept_name, doc_id, doc_name, item_date, item_id, item_name, proposal_code, numbers, price, specification, ptype, rules, bmi_convered_amount, deduction_amount, deduction_reason, deduction_amount1, deduction_amount2, deduction_amount3, status, fb_stauts, fb_reason, version, last_updated_by, last_updated_date, created_by, created_date, hospital_id, visit_type, fb_complain, org, fee_org_code, fee_org_name, usages, return_reason
    </sql>

    <!--查询指定行数据-->
    <select id="queryByPage" resultType="com.crhms.cloud.mqs.mqs_bmi.domain.BmiFb">
        select
        <include refid="baseSql"/>
        from mqs_bmi_fb
        where hospital_id = #{queryVo.hospitalId}
        <if test="queryVo.detailNo != null and queryVo.detailNo != ''">
            and detail_no = #{queryVo.detailNo}
        </if>
        <if test="queryVo.no != null and queryVo.no != ''">
            and no = #{queryVo.no}
        </if>
        <if test="queryVo.admissionNo != null and queryVo.admissionNo != ''">
            and admission_no = #{queryVo.admissionNo}
        </if>
        <if test="queryVo.isMatch != null and queryVo.isMatch != ''">
            and is_match = #{queryVo.isMatch}
        </if>
        <if test="queryVo.fbStauts != null and queryVo.fbStauts != ''">
            and fb_stauts = #{queryVo.fbStauts}
        </if>

        <if test="queryVo.fbDeptCodes != null and queryVo.fbDeptCodes.size() > 0">
            and fb_dept_code in
            <foreach collection="queryVo.fbDeptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.appealDate != null and queryVo.appealDate != ''">
            and appeal_date = #{queryVo.appealDate}
        </if>
        <if test="queryVo.claimTypes != null and queryVo.claimTypes.size() >0">
            and claim_type in
            <foreach collection="queryVo.claimTypes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.billDateFrom != null and queryVo.billDateFrom != ''">
            and bill_date &gt;= concat(#{queryVo.billDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.billDateTo != null and queryVo.billDateTo != ''">
            and bill_date &lt;= concat(#{queryVo.billDateTo},' 23:59:59')
        </if>

        <if test="queryVo.patient != null and queryVo.patient != ''">
            and ( patient_id like CONCAT('%',#{queryVo.patient,jdbcType = VARCHAR},'%')
            or patient_name like CONCAT('%',#{queryVo.patient,jdbcType = VARCHAR},'%'))
        </if>

        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() >0">
            and dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() >0">
            and doc_Id in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.statuss != null and queryVo.statuss.size() > 0">
            and status in
            <foreach collection="queryVo.statuss" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.item != null and queryVo.item != ''">
            and ( item_id like CONCAT('%',#{queryVo.item,jdbcType = VARCHAR},'%')
            or item_name like CONCAT('%',#{queryVo.item,jdbcType = VARCHAR},'%'))
        </if>

    </select>
    <select id="queryByPageWithAuth" resultType="com.crhms.cloud.mqs.mqs_bmi.domain.BmiFb">
        select
        <include refid="baseSql"/>
        from mqs_bmi_fb
        where hospital_id = #{queryVo.hospitalId}
        <if test="queryVo.detailNo != null and queryVo.detailNo != ''">
            and detail_no = #{queryVo.detailNo}
        </if>
        <if test="queryVo.no != null and queryVo.no != ''">
            and no = #{queryVo.no}
        </if>
        <if test="queryVo.admissionNo != null and queryVo.admissionNo != ''">
            and admission_no = #{queryVo.admissionNo}
        </if>
        <if test="queryVo.fbDeptCodes != null and queryVo.fbDeptCodes.size() > 0">
            and fb_dept_code in
            <foreach collection="queryVo.fbDeptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.appealDate != null and queryVo.appealDate != ''">
            and appeal_date = #{queryVo.appealDate}
        </if>
        <if test="queryVo.claimTypes != null and queryVo.claimTypes.size() >0">
            and claim_type in
            <foreach collection="queryVo.claimTypes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.billDateFrom != null and queryVo.billDateFrom != ''">
            and bill_date &gt;= concat(#{queryVo.billDateFrom},' 00:00:00')
        </if>
        <if test="queryVo.billDateTo != null and queryVo.billDateTo != ''">
            and bill_date &lt;= concat(#{queryVo.billDateTo},' 23:59:59')
        </if>

        <if test="queryVo.patient != null and queryVo.patient != ''">
            and ( patient_id like CONCAT('%',#{queryVo.patient,jdbcType = VARCHAR},'%')
            or patient_name like CONCAT('%',#{queryVo.patient,jdbcType = VARCHAR},'%'))
        </if>
        <if test="queryVo.deptCodes != null and queryVo.deptCodes.size() >0">
            and dept_code in
            <foreach collection="queryVo.deptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.docIds != null and queryVo.docIds.size() >0">
            and doc_Id in
            <foreach collection="queryVo.docIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.statuss != null and queryVo.statuss.size() > 0">
            and status in
            <foreach collection="queryVo.statuss" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryVo.item != null and queryVo.item != ''">
            and ( item_id like CONCAT('%',#{queryVo.item,jdbcType = VARCHAR},'%')
            or item_name like CONCAT('%',#{queryVo.item,jdbcType = VARCHAR},'%'))
        </if>
        <if test="queryVo.fbStauts != null and queryVo.fbStauts != ''">
            and fb_stauts = #{queryVo.fbStauts}
        </if>
        <if test="queryVo.authDeptCodes != null and queryVo.authDeptCodes.size() > 0">
            and fb_dept_code in
            <foreach collection="queryVo.authDeptCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="queryHeadWithNo" resultType="com.crhms.cloud.mqs.mqs_bmi.vo.FbMedicalCaseDto">
        select
        head.no,
        head.admission_no,
        head.patient_id,
        head.patient_name,
        head.patient_gender,
        head.patient_birthday,
        head.claim_type,
        head.visit_type,
        head.personnel_type,
        head.benefit_type,
        head.bmi_convered_amount,
        head.deduction_amount1 as deduction_amount
        from mqs_bmi_fb head
        where head.hospital_id = #{hospitalId}
        and head.no = #{no}
        LIMIT 1;
    </select>
    <select id="selectDeptList" resultType="java.util.Map">
        SELECT
        dept_code as code,
        dept_name as name
        from mqs_bmi_fb
        WHERE hospital_id = #{hospitalId}
        <if test="dept != null and dept != ''">
            and ( dept_code like CONCAT('%',#{dept,jdbcType = VARCHAR},'%')
            or dept_name like CONCAT('%',#{dept,jdbcType = VARCHAR},'%'))
        </if>
        group by dept_code,dept_name
    </select>
    <select id="selectDoctorList" resultType="java.util.Map">
        SELECT
        doc_id as code,
        doc_name as name
        from mqs_bmi_fb
        WHERE hospital_id = #{hospitalId}
        <if test="doctor != null and doctor != ''">
            and ( doc_id like CONCAT('%',#{doctor,jdbcType = VARCHAR},'%')
            or doc_name like CONCAT('%',#{doctor,jdbcType = VARCHAR},'%'))
        </if>
        group by doc_id,doc_name
    </select>
    <select id="selectClaimTypeList" resultType="java.lang.String">
        SELECT
        claim_type
        from mqs_bmi_fb
        WHERE hospital_id = #{hospitalId}
        <if test="claimType != null and claimType != ''">
            and claim_type like CONCAT('%',#{claimType,jdbcType = VARCHAR},'%')
        </if>
        group by claim_type
    </select>

</mapper>

