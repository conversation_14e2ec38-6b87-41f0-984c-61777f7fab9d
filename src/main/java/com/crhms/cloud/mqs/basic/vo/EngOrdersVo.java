package com.crhms.cloud.mqs.basic.vo;

import com.googlecode.jmapper.annotations.JMap;
import com.googlecode.jmapper.annotations.JMapConversion;
import lombok.Data;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;


//医嘱明细(BaseMedicalOrders)实体类

@Data
public class EngOrdersVo  {
    //医嘱明细流水号
    @JMap("orderDetailNo")
    private String order_detail_no;
    //单据号
    @JMap("no")
    private String no;
    //就诊流水号
    @JMap("admissionNo")
    private String admission_no;
    //医嘱序号
    @JMap("orderNo")
    private String order_no;
    //医嘱子序号
    @JMap("orderSubNo")
    private String order_sub_no;
    //项目序号
    @JMap("itemNo")
    private String item_no;
    //项目编码
    @JMap("itemId")
    private String item_id;
    //项目名称
    @JMap("itemName")
    private String item_name;
    //项目类型编码
    @JMap("itemTypeCode")
    private String item_type_code;
    //项目类型名称
    @JMap("itemTypeName")
    private String item_type_name;
    //项目日期
    @JMap("itemDate")
    private String item_date;
    //项目大类 1.医嘱项目 2.计价项目
    @JMap("broadHeading")
    private String broad_heading;
    //开医嘱科室编码
    @JMap("applyDeptCode")
    private String apply_dept_code;
    //开医嘱科室名称
    @JMap("applyDeptName")
    private String apply_dept_name;
    //数量
    @JMap("numbers")
    private Double numbers;
    //规格
    @JMap("specification")
    private String specification;
    //包装/剂量/计价单位
    @JMap("usageUnit")
    private String usage_unit;
    //每次用量
    @JMap("usage")
    private String usage;
    //使用频次
    @JMap("frequencyInterval")
    private String frequency_interval;
    //单价
    @JMap("price")
    private Double price;
    //总费用
    @JMap("costs")
    private Double costs;
    //批次号
    @JMap("batchNo")
    private String batch_no;
    //院区id
    @JMap("hospitalId")
    private String hospital_id;
    //下达医嘱时间
    @JMap("enterDateTime")
    private String enter_date_time;
    //开医嘱医生编码
    @JMap("applyDoctorCode")
    private String apply_doctor_code;
    //开医嘱医生名称
    @JMap("applyDoctorName")
    private String apply_doctor_name;
    //长期医嘱标记  1.长期 0.临时
    @JMap("repeatIndicator")
    private String repeat_indicator;
    //执行时间详细描述
    @JMap("freqDetail")
    private String freq_detail;
    //频率次数
    @JMap("freqCounter")
    private String freq_counter;
    //频率间隔
    @JMap("freqInterval")
    private String freq_interval;
    //频率间隔单位
    @JMap("freqIntervalUnit")
    private String freq_interval_unit;
    //开始日期
    @JMap("startDateTime")
    private String start_date_time;
    //停止日期
    @JMap("stopDateTime")
    private String stop_date_time;

    /**
     * jmapper类型转换器
     */
    @JMapConversion(from = {"itemDate","enterDateTime","startDateTime","stopDateTime"},
            to = {"item_date","enter_date_time","start_date_time","stop_date_time"})
    public String convertDateToString(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        return date == null ? null : format.format(date);
    }
    @JMapConversion(from = {"costs", "price", "numbers"},
            to = { "costs","price","numbers"})
    public Double convertBigToDou(BigDecimal num) {
        return num == null ? null : num.doubleValue();
    }
}

