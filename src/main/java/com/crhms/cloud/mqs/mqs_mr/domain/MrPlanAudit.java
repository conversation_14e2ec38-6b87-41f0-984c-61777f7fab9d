package com.crhms.cloud.mqs.mqs_mr.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
/**
 * 人工审核流程配置表(MrPlanAudit)实体类
 *
 * <AUTHOR>
 * @since 2023-02-06 14:58:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_mr_plan_audit")
public class MrPlanAudit extends BaseDomain implements Serializable {

    private static final long serialVersionUID = -24285598356407441L;

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 审核人员(多个人员,分开)
     */
    @TableField
    private String mrPlanUser;

    /**
     * 审核人员名字(多个人员,分开)
     */
    @TableField
    private String mrPlanUserName;

    /**
     * 审核科室(多个人员,分开)
     */
    @TableField
    private String mrPlanDepartment;

    /**
     * 院区id
     */
    @TableField
    private String hospitalId;

}
