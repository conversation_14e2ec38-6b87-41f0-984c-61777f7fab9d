package com.crhms.cloud.mqs.basic.vo;

import com.googlecode.jmapper.annotations.JMap;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class HisOrdersVo {
    //医嘱明细流水号
    @JMap("orderDetailNo")
    private String order_detail_no;
    //医嘱序号
    @JMap("orderNo")
    private String order_no;
    //医嘱子序号
    @JMap("orderSubNo")
    private String order_sub_no;
    //项目序号
    @JMap("itemNo")
    private String item_no;
    //项目编码
    @JMap("itemId")
    private String item_id;
    //项目名称
    @JMap("itemName")
    private String item_name;
    //项目类型编码
    @JMap("itemTypeCode")
    private String item_type_code;
    //项目类型名称
    @JMap("itemTypeName")
    private String item_type_name;
    //项目日期
    @JMap("itemDate")
    private String item_date;
    //项目大类 1.医嘱项目 2.计价项目
    @JMap("broadHeading")
    private String broad_heading;
    //开医嘱科室编码
    @JMap("applyDeptCode")
    private String apply_dept_code;
    //开医嘱科室名称
    @JMap("applyDeptName")
    private String apply_dept_name;
    //开医嘱医生编码
    @JMap("applyDoctorCode")
    private String apply_doctor_code;
    //开医嘱医生名称
    @JMap("applyDoctorName")
    private String apply_doctor_name;
    //数量
    @JMap("numbers")
    private BigDecimal numbers;
    //规格
    @JMap("specification")
    private String specification;
    //包装/剂量/计价单位
    @JMap("usageUnit")
    private String usage_unit;
    //每次用量
    @JMap("usage")
    private String usage;
    //使用频次
    @JMap("frequencyInterval")
    private String frequency_interval;
    //单价
    @JMap("price")
    private BigDecimal price;
    //总费用
    @JMap("costs")
    private BigDecimal costs;
    //下达医嘱时间
    @JMap("enterDateTime")
    private String enter_date_time;
    //长期医嘱标记  1.长期 0.临时
    @JMap("repeatIndicator")
    private String repeat_indicator;
    //执行时间详细描述
    @JMap("freqDetail")
    private String freq_detail;
    //频率次数
    @JMap("freqCounter")
    private String freq_counter;
    //频率间隔
    @JMap("freqInterval")
    private String freq_interval;
    //频率间隔单位
    @JMap("freqIntervalUnit")
    private String freq_interval_unit;
    //开始日期
    @JMap("startDateTime")
    private String start_date_time;
    //停止日期
    @JMap("stopDateTime")
    private String stop_date_time;
    
}
