package com.crhms.cloud.mqs.mqs_kbm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.mqs_kbm.domain.ItemConsume;
import com.crhms.cloud.mqs.mqs_kbm.domain.ItemDrug;
import com.crhms.cloud.mqs.mqs_kbm.domain.ItemDrugTcms;
import com.crhms.cloud.mqs.mqs_kbm.domain.ItemServiceItem;
import com.crhms.cloud.mqs.mqs_kbm.service.ItemConsumeService;
import com.crhms.cloud.mqs.mqs_kbm.service.ItemDrugService;
import com.crhms.cloud.mqs.mqs_kbm.service.ItemDrugTcmsService;
import com.crhms.cloud.mqs.mqs_kbm.service.ItemServiceItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 知识库管理-医保项目目录 controller, 包括药品、药品（中药饮片）、耗材、服务项目查询
 *
 * @author: wangxingang
 * @date: 2023/02/03
 */

@RestController
@RequestMapping("/api/mqs/kbm/item")
public class ItemController {

    @Autowired
    ItemServiceItemService serviceItemService;

    @Autowired
    ItemDrugService drugService;

    @Autowired
    ItemDrugTcmsService drugTcmsService;

    @Autowired
    ItemConsumeService consumeService;


    /**
     * 查询列表 - 国家医保服务项目信息
     *
     * @param projectCode 项目代码
     * @param projectName 项目名称
     */
    @GetMapping("/serviceItem/page")
    public ResponseEntity<List<ItemServiceItem>> queryServiceItem(@RequestParam(value = "projectCode", required = false) String projectCode,
                                                                  @RequestParam(value = "projectName", required = false) String projectName,
                                                                  @RequestParam(value = "page", defaultValue = "1") int page,
                                                                  @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<ItemServiceItem> pageInfo = PageUtil.getPage(page, pageSize);
        List<ItemServiceItem> serviceItemList = serviceItemService.queryList(pageInfo, projectCode, projectName);
        return new ResponseEntity<>(serviceItemList, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }


    /**
     * 查询列表 - 国家医保药品分类与代码
     *
     * @param drugCode         药品代码
     * @param registeredName   注册名称
     * @param drugEnterprise   药品企业
     * @param approvalNo       批准文号
     * @param marketStatusList 市场状态
     */
    @PostMapping("/drug/page")
    public ResponseEntity<List<ItemDrug>> queryDrug(@RequestParam(value = "drugCode", required = false) String drugCode,
                                                    @RequestParam(value = "registeredName", required = false) String registeredName,
                                                    @RequestParam(value = "drugEnterprise", required = false) String drugEnterprise,
                                                    @RequestParam(value = "approvalNo", required = false) String approvalNo,
                                                    @RequestBody(required = false) List<String> marketStatusList,
                                                    @RequestParam(value = "page", defaultValue = "1") int page,
                                                    @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<ItemDrug> pageInfo = PageUtil.getPage(page, pageSize);
        List<ItemDrug> drugList = drugService.queryList(pageInfo, drugCode, registeredName, drugEnterprise, approvalNo, marketStatusList);
        return new ResponseEntity<>(drugList, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }


    /**
     * 查询市场状态下拉列表
     */
    @GetMapping("/drug/marketStatus")
    public ResponseEntity<List<String>> queryMarketStatusList() {
        return new ResponseEntity<>(drugService.queryMarketStatusList(), HttpStatus.OK);
    }


    /**
     * 查询列表 - 国家医保药品分类与代码(中药饮片)
     *
     * @param tcmsCode             中药饮片代码
     * @param tcmsName             中药饮片名称
     * @param medMaterialName      药材名称
     * @param efficacyClassifyList 功效分类
     * @param funcIndicate         功能与主治
     */
    @PostMapping("/drugTcms/page")
    public ResponseEntity<List<ItemDrugTcms>> queryDrugTcms(@RequestParam(value = "tcmsCode", required = false) String tcmsCode,
                                                            @RequestParam(value = "tcmsName", required = false) String tcmsName,
                                                            @RequestParam(value = "medMaterialName", required = false) String medMaterialName,
                                                            @RequestBody(required = false) List<String> efficacyClassifyList,
                                                            @RequestParam(value = "funcIndicate", required = false) String funcIndicate,
                                                            @RequestParam(value = "page", defaultValue = "1") int page,
                                                            @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<ItemDrugTcms> pageInfo = PageUtil.getPage(page, pageSize);
        List<ItemDrugTcms> drugTcmsList = drugTcmsService.queryList(pageInfo, tcmsCode, tcmsName, medMaterialName, efficacyClassifyList, funcIndicate);
        return new ResponseEntity<>(drugTcmsList, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }


    /**
     * 国家医保药品分类与代码(中药饮片) - 查询功效分类下拉列表
     */
    @GetMapping("/drugTcms/efficacyClassify")
    public ResponseEntity<List<String>> queryEfficacyClassifyList() {
        return new ResponseEntity<>(drugTcmsService.queryEfficacyClassifyList(), HttpStatus.OK);
    }


    /**
     * 查询列表 - 国家医保医用耗材分类与代码
     *
     * @param consumeCode     耗材代码
     * @param classFirst      一级分类(学科、品类)
     * @param classSecond     二级分类(用途、品目)
     * @param classThird      三级分类(部位、功能、品种)
     * @param ybCommonName    医保通用名
     * @param materialQuality 材质
     * @param feature         特征
     */
    @GetMapping("/consume/page")
    public ResponseEntity<List<ItemConsume>> queryConsume(@RequestParam(value = "consumeCode", required = false) String consumeCode,
                                                          @RequestParam(value = "classFirst", required = false) String classFirst,
                                                          @RequestParam(value = "classSecond", required = false) String classSecond,
                                                          @RequestParam(value = "classThird", required = false) String classThird,
                                                          @RequestParam(value = "ybCommonName", required = false) String ybCommonName,
                                                          @RequestParam(value = "materialQuality", required = false) String materialQuality,
                                                          @RequestParam(value = "feature", required = false) String feature,
                                                          @RequestParam(value = "page", defaultValue = "1") int page,
                                                          @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        Page<ItemConsume> pageInfo = PageUtil.getPage(page, pageSize);
        List<ItemConsume> consumeList = consumeService.queryList(pageInfo, consumeCode, classFirst, classSecond, classThird, ybCommonName, materialQuality, feature);
        return new ResponseEntity<>(consumeList, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }

}
