package com.crhms.cloud.mqs.basic.vo;

import lombok.Data;

import java.util.List;

/**
 * 审核接口基础类
 */
@Data
public class BaseAuditResonDto {
    /**
     * 批次号唯一
     */
    private String batch_no;
    /**
     * 院区id
     */
    private String hospital_id;

    /**
     * 审核场景
     */
    private String audit_scenario;

    /**
     *  就诊流水号（要求唯一，审核最大维度）
     */
    private String admission_no;

    private String is_increment;

    /**
     * 业务数据
     */

    private List<SaveResonVo> data;

}
