package com.crhms.cloud.mqs.sys.dto;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 门诊审核-处方审核-审核结果表(OpPctAudit)实体类
 *
 * <AUTHOR>
 * @since 2022-12-19 09:41:23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class SysAudit extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -99065584526519544L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_DETAIL_NO="detail_no";
    public static final String FIELD_NO="no";
    public static final String FIELD_RULE_CODE="rule_code";
    public static final String FIELD_RULE_NAME="rule_name";
    public static final String FIELD_RULE_REASON="rule_reason";
    public static final String FIELD_BATCH_NO="batch_no";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 明细流水号
     */    
    @TableField
    private String detailNo;
    /**
     * 单据号
     */    
    @TableField
    private String no;
    /**
     * 违规规则
     */    
    @TableField
    private String ruleCode;
    /**
     * 违规规则名称
     */    
    @TableField
    private String ruleName;
    /**
     * 违规原因
     */    
    @TableField
    private String ruleReason;
    /**
     * 批次号
     */    
    @TableField
    private String batchNo;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;


}

