package com.crhms.cloud.mqs.mqs_kbm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

/**
 * 知识库管理-西医诊断手术-ICD-9CM3医保版CHS2.0
 *
 * @author: wangxingang
 * @date: 2023.02.06
 */
@Data
@Builder
@TableName("mqs_kbm_west_icd9cm3_yb20")
public class WestIcd9cm3Yb20 {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 手术操作代码
     */
    @TableField
    private String code;

    /**
     * 手术操作名称
     */
    @TableField
    private String name;
}
