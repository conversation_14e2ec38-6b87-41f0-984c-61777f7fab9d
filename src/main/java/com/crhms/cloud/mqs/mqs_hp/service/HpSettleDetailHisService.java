package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpSettleDetailHis;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpSettleDetailHisMapper;
import com.crhms.cloud.mqs.mqs_hp.service.HpSettleDetailHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-结算审核明细-历史表(HpSettleDetailHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 10:04:33
 */
@Service("hpSettleDetailHisService")
public class HpSettleDetailHisService extends ServiceImpl< HpSettleDetailHisMapper, HpSettleDetailHis> {

}
