<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.sys.mapper.SysConfigMapper">


    <!--查询指定行数据-->
    <select id="queryList" resultType="com.crhms.cloud.mqs.sys.domain.SysConfig">
        select
          id, sys_key, sys_value, enable, last_updated_by, last_updated_date, created_by, created_date, hospital_id, remake
        from mqs_sys_config
        <where>
            <if test="sysKey != null and sysKey != ''">
                and (sys_key like CONCAT('%',#{sysKey,jdbcType = VARCHAR},'%')
                    or remake like CONCAT('%',#{sysKey,jdbcType = VARCHAR},'%')
                )
            </if>

            <if test="enable != null and enable != ''">
                and enable = #{enable}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and hospital_id = #{hospitalId}
            </if>
        </where>
    </select>
    <select id="queryRuleLevelList" resultType="com.crhms.cloud.mqs.sys.domain.SysRuleLevel">
        select *
        from mqs_sys_rule_level

        <where>
            <if test="platform != null and platform != ''">
                and platform = #{platform}
            </if>
            <if test="levelConfig != null and levelConfig != ''">
                and level_config &gt;= #{levelConfig}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and hospital_id = #{hospitalId}
            </if>
        </where>

    </select>


</mapper>

