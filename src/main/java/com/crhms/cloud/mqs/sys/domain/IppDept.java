package com.crhms.cloud.mqs.sys.domain;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 科室表(IppDept)实体类
 *
 * <AUTHOR>
 * @since 2023-01-12 09:26:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("ipp_dept")
public class IppDept extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 356981640591045460L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_ORGANIZATION_CODE="organization_code";
    public static final String FIELD_CODE="code";
    public static final String FIELD_NAME="name";
    public static final String FIELD_CREATE_TIME="create_time";
    public static final String FIELD_MEDICAL_FLAG="medical_flag";
    public static final String FIELD_COST_DEPT_CODE="cost_dept_code";
    public static final String FIELD_COST_DEPT_NAME="cost_dept_name";
    public static final String FIELD_DEPT_TYPE="dept_type";
    public static final String FIELD_DEPT_SPECIALTY="dept_specialty";
    public static final String FIELD_MEDICAL_TYPE="medical_type";
    public static final String FIELD_PARENT_DEPT_CODE="parent_dept_code";
    public static final String FIELD_LAST_LEVEL="last_level";
    public static final String FIELD_CALC_DIRECT="calc_direct";
    public static final String FIELD_ALLOCATION="allocation";
    public static final String FIELD_ENABED="enabed";
    public static final String FIELD_HOSPITAL_ID="hospital_id";
    public static final String FIELD_DATA_SOURCE="data_source";
    public static final String FIELD_DELETED="deleted";

        
    @TableId
    private Integer id;

    /**
     * 机构编码
     */    
    @TableField
    private String organizationCode;
    /**
     * 科室编码
     */    
    @TableField
    private String code;
    /**
     * 科室名称
     */    
    @TableField
    private String name;
        
    @TableField
    private Date createTime;
    /**
     * 医保办标识
     */    
    @TableField
    private String medicalFlag;
    /**
     * 成本科室编码
     */    
    @TableField
    private String costDeptCode;
    /**
     * 成本科室名称
     */    
    @TableField
    private String costDeptName;
    /**
     * 科室分类（按单元服务性质分类）-dic_dt_dept_type
     */    
    @TableField
    private String deptType;
    /**
     * 科室所在专业-dic_dt_specialty
     */    
    @TableField
    private String deptSpecialty;
    /**
     * 科室类型
     */    
    @TableField
    private String medicalType;
    /**
     * 上级成本科室
     */    
    @TableField
    private String parentDeptCode;
    /**
     * 是否末级成本科室
     */    
    @TableField
    private Integer lastLevel;
    /**
     * 是否核算直接成本
     */    
    @TableField
    private Integer calcDirect;
    /**
     * 是否参与科室分摊
     */    
    @TableField
    private Integer allocation;
    /**
     * 是否启用
     */    
    @TableField
    private Integer enabed;
    /**
     * 医院ID
     */    
    @TableField
    private String hospitalId;
    /**
     * 数据来源   0内置；1 公司内部接口；2外部接口 ；3文件 ；4界面
     */    
    @TableField
    private Integer dataSource;
    /**
     * 删除标识
     */    
    @TableField
    private Integer deleted;


}

