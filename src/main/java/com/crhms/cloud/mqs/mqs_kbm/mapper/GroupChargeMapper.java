package com.crhms.cloud.mqs.mqs_kbm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.mqs_kbm.domain.GroupCharge;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 知识库管理-分组合理性-收费与入组合理性
 *
 * @author: wangxingang
 * @date: 2023.02.06
 */

@Mapper
public interface GroupChargeMapper extends BaseMapper<GroupCharge> {
    /**
     * 查询类型下拉列表
     */
    List<String> selectTypeList();

    /**
     * 查询ADRG下拉列表
     */
    List<String> selectAdrgList();

    /**
     * 查询ADRG名称下拉列表
     */
    List<String> selectAdrgNameList();
}
