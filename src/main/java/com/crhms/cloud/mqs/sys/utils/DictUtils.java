package com.crhms.cloud.mqs.sys.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.crhms.cloud.client.cdmp.dic.CdmpDicInterface;
import com.crhms.cloud.mqs.sys.enums.GloablData;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 字典翻译工具类
 */
@Log4j2
public class DictUtils {

    //特殊字典 自定义的系统内数据映射
    public static final String DICT_DEPT_UNITS = "DICT_DEPT_UNITS";
    //字典全局变量
    public static Map<String,Map<String,String>> dictMaps = new HashMap<>();

    static {
        //加载本地字典
        for (GloablData.LOCAL_DICT e : GloablData.LOCAL_DICT.values()) {
            dictMaps.put(e.code,e.dicts);
        }
    }
    public static void translateDict(Object object){

        if(object == null){
            return;
        }
        Object[] objects;
        if(object instanceof Collection){
            objects = ((Collection) object).toArray();
            if(objects.length == 0){
                return;
            }
        }else {
            objects = new Object[1];
            objects[0] = object;
        }
        try {
            CdmpDicInterface cdmpDicInterface = SpringUtil.getBean(CdmpDicInterface.class);
            for (Object o : objects) {
                for (Field field : o.getClass().getDeclaredFields()) {
                    // 是否引用DictTranslate注解
                    boolean bool = field.isAnnotationPresent(DictTranslate.class);
                    if (bool) {
                        String dictCode = field.getAnnotation(DictTranslate.class).dictCode();
                        String dictText = field.getAnnotation(DictTranslate.class).dictText();
                        String dictSplit = field.getAnnotation(DictTranslate.class).dictSplit();
                        Map<String, String> dictMap = dictMaps.get(dictCode);
                        //加载数据平台字典
                        if(dictMaps.get(dictCode) == null){
                            try {
                                log.info("获取平台字典翻译：" + dictCode);
                                List<Map<String, Object>> dictList = cdmpDicInterface.queryDicDataList(dictCode);
                                dictMap = dictList.stream().collect(Collectors.toMap(e -> (String) e.get("DATA_CODE"), e -> (String) e.get("DATA_NAME"), (k1, k2) -> k1));
                                dictMaps.put(dictCode,dictMap);
                            }catch (Exception e){
                                log.warn("平台字典翻译异常：" + e.getMessage());
                            }
                        }
                        field.setAccessible(true);
                        if(dictMap != null && !Objects.isNull(field.get(o))){
                            String string = field.get(o).toString();
                            StringBuffer translate = new StringBuffer();
                            if(StrUtil.isNotEmpty(dictSplit)){
                                //分割符号翻译
                                List<String> splits = StrUtil.split(string,dictSplit);
                                HashSet<String> translateSet = new HashSet<>();
                                for (String str : splits) {
                                    translateSet.add(StrUtil.isNotEmpty(dictMap.get(str)) ? dictMap.get(str) : str);
                                }
                                translate.append(String.join(dictSplit, translateSet));
                            }else {
                                translate.append(StrUtil.isNotEmpty(dictMap.get(string)) ? dictMap.get(string) : string);
                            }
                            if(StringUtils.isNotEmpty(translate.toString())){
                                if(StringUtils.isEmpty(dictText)){
                                    field.set(o,translate.toString());
                                }else {
                                    for (Field f : o.getClass().getDeclaredFields()) {
                                        if(f.getName().equals(dictText)){
                                            f.setAccessible(true);
                                            f.set(o,translate.toString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            log.warn("字典翻译异常！" + e.getMessage());
        }
    }
}
