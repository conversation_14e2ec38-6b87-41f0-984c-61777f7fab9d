<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.basic.mapper.BaseAuditMapper">


    <sql id="Basesql">
      detail_no, no, order_detail_no, rule_code, rule_name, rule_reason, batch_no
      , last_updated_by, last_updated_date, created_by, created_date
      , hospital_id, admission_no, rule_type, rule_type_name, related
      , full_tip, group_code, violation_ratio,tips_code4_hospital,violation_amount,violation_type
      , item_id,item_name,item_date, item_type_code,item_type_name, numbers,price,costs
      ,expand_field1,expand_field2,expand_field3,expand_field4,expand_field5,rule_origin,jr_id
    </sql>

    <delete id="delAudit">
        DELETE
        from
        ${table}_audit
        where
        hospital_id = #{hospitalId}
        and admission_no = #{admissionNo}
        <if test="no != null and no != ''" >
            and `no` = #{no}
        </if>
        <if test="detailNos != null and detailNos.size() >0" >
            and detail_no in
            <foreach collection="detailNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

    </delete>
    <delete id="delAuditReason">
        DELETE
        from
        ${table}_reason
        where
        hospital_id = #{hospitalId}
        and admission_no = #{admissionNo}
        <if test="no != null and no != ''" >
            and `no` = #{no}
        </if>
        <if test="detailNos != null and detailNos.size() >0" >
            and detail_no in
            <foreach collection="detailNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

    </delete>
    <delete id="deleteAuditByAdNo">
        DELETE FROM ${table}_audit
        WHERE
        hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and admission_no = #{admissionNo}
        </if>
        <if test="nos != null and nos.size() >0 ">
            and `no` in
            <foreach collection="nos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="detailNos != null and detailNos.size() >0 ">
            and detail_no in
            <foreach collection="detailNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="deleteReasonByAdNo">
        DELETE FROM ${table}_reason
        WHERE
        hospital_id = #{hospitalId}
        <if test="admissionNo != null and admissionNo != ''">
            and admission_no = #{admissionNo}
        </if>
        <if test="nos != null and nos.size() >0 ">
            and `no` in
            <foreach collection="nos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="detailNos != null and detailNos.size() >0 ">
            and detail_no in
            <foreach collection="detailNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </delete>

    <select id="queryAuditHisByBatchNo" resultType="com.crhms.cloud.mqs.basic.domain.BaseAudit">
        SELECT
        <include refid="Basesql"/>
        FROM ${table}_audit_his audit
        WHERE  hospital_id = #{hospitalId}
        AND batch_no = #{batchNo}
        <if test="admissionNo != null and admissionNo != ''" >
            AND admission_no = #{admissionNo}
        </if>
    </select>
    <select id="querySavedCase" resultType="com.crhms.cloud.mqs.basic.domain.BaseMedicalCase">
        select
        `no`,
        admission_no,
        audit_time
        from ${table}
        where hospital_id = #{hospitalId}
        and batch_no = #{batch}
        and admission_no = #{admissionNo}
    </select>
    <select id="getClaimRes" resultType="java.util.Map">
        SELECT
            detail.`no`,
            detail.admission_no,
            detail.detail_no,
            detail.item_id,
            detail.item_name,
            detail.item_date,
            audit.rule_code,
            audit.rule_name,
            audit.rule_type,
            audit.rule_type_name,
            audit.rule_reason AS reason,
            audit.full_tip,
            audit.group_code,
            audit.related,
            audit.tips_code4_hospital,
            audit.violation_ratio
        FROM
            ${table}_audit audit
            LEFT JOIN ${table}_detail detail ON detail.detail_no = audit.detail_no
            where
            audit.admission_no = #{admissionNo}
            and audit.hospital_id = #{hospitalId}
            and audit.`no` = #{no}

    </select>
    <select id="selectCaseAudit" resultType="com.crhms.cloud.mqs.basic.domain.BaseAudit">
        select
        <include refid="Basesql"/>
        from
        ${table}_audit audit
        where
        audit.hospital_id = #{hospitalId}
        <if test="detailNos != null and detailNos.size() >0 ">
            and detail_no in
            <foreach collection="detailNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        and audit.`no` in
        <foreach collection="nos" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectCaseAuditHis" resultType="com.crhms.cloud.mqs.basic.domain.BaseAudit">
        select
        <include refid="Basesql"/>
        from
        ${table}_audit_his audit
        where
        audit.hospital_id = #{hospitalId}
        and audit.detail_no = '0'
        and audit.`no` = #{no}
    </select>

    <insert id="batchInsertAuditReason">
        insert into ${table}_reason(detail_no, no, admission_no, self_expense, item_id, rule_code, reason_type, reason_des, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.detailNo}, #{entity.no}, #{entity.admissionNo}, #{entity.selfExpense}, #{entity.itemId}, #{entity.ruleCode}, #{entity.reasonType}, #{entity.reasonDes}, #{entity.batchNo}, #{entity.lastUpdatedBy}, now(), #{entity.createdBy}, now(), #{entity.hospitalId})
        </foreach>
    </insert>
    <insert id="batchInsertAuditHis">
        insert into ${table}_audit_his(detail_no, no, order_detail_no, rule_code, rule_name, rule_reason, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id, admission_no, rule_type, rule_type_name, related, full_tip, group_code, violation_ratio,tips_code4_hospital,violation_amount,rule_origin,jr_id,violation_type,item_id,item_name,item_date, item_type_code,item_type_name,numbers,price,costs,expand_field1,expand_field2,expand_field3,expand_field4,expand_field5)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.detailNo}, #{entity.no},#{entity.orderDetailNo}, #{entity.ruleCode}, #{entity.ruleName}, #{entity.ruleReason}, #{entity.batchNo}, #{entity.lastUpdatedBy}, now(), #{entity.createdBy}, now(), #{entity.hospitalId}, #{entity.admissionNo}, #{entity.ruleType}, #{entity.ruleTypeName}, #{entity.related}, #{entity.fullTip}, #{entity.groupCode}, #{entity.violationRatio}, #{entity.tipsCode4Hospital}, #{entity.violationAmount}, #{entity.ruleOrigin}, #{entity.jrId}, #{entity.violationType}
            , #{entity.itemId}, #{entity.itemName}, #{entity.itemDate},#{entity.itemTypeCode}, #{entity.itemTypeName}, #{entity.numbers}, #{entity.price}, #{entity.costs}, #{entity.expandField1}, #{entity.expandField2}, #{entity.expandField3}, #{entity.expandField4}, #{entity.expandField5})
        </foreach>
    </insert>
    <insert id="batchInsertAudit">
        insert into ${table}_audit (detail_no, no, order_detail_no, rule_code, rule_name, rule_reason, batch_no, last_updated_by, last_updated_date, created_by, created_date, hospital_id, admission_no, rule_type, rule_type_name, related, full_tip, group_code, violation_ratio,tips_code4_hospital,violation_amount,rule_origin,jr_id,violation_type,item_id,item_name,item_date, item_type_code,item_type_name,numbers,price,costs,expand_field1,expand_field2,expand_field3,expand_field4,expand_field5)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.detailNo}, #{entity.no},#{entity.orderDetailNo}, #{entity.ruleCode}, #{entity.ruleName}, #{entity.ruleReason}, #{entity.batchNo}, #{entity.lastUpdatedBy}, now(), #{entity.createdBy}, now(), #{entity.hospitalId}, #{entity.admissionNo}, #{entity.ruleType}, #{entity.ruleTypeName}, #{entity.related}, #{entity.fullTip}, #{entity.groupCode}, #{entity.violationRatio}, #{entity.tipsCode4Hospital}, #{entity.violationAmount}, #{entity.ruleOrigin}, #{entity.jrId}, #{entity.violationType}
            , #{entity.itemId}, #{entity.itemName}, #{entity.itemDate},#{entity.itemTypeCode}, #{entity.itemTypeName}, #{entity.numbers}, #{entity.price}, #{entity.costs}, #{entity.expandField1}, #{entity.expandField2}, #{entity.expandField3}, #{entity.expandField4}, #{entity.expandField5})
        </foreach>

    </insert>
    <insert id="insertAuditReturnLog">
        insert into mqs_sys_audit_return_log(amount, admission_no, audit_scenario, personnel_type,return_date,hospital_id)
        values (#{totalCosts}, #{admissionNo}, #{auditScenario}, #{personnelTypeId},now(), #{hospitalId})
    </insert>


    <select id="selectAudit" resultType="com.crhms.cloud.mqs.basic.domain.BaseAudit">
        SELECT
        detail_no,
        `no`,
        rule_code,
        rule_name,
        rule_type,
        rule_type_name,
        rule_reason
        FROM
        ${table}_audit audit
        WHERE
        hospital_id = #{hospitalId}
        and admission_no = #{admissionNo}
        <if test="nos != null and nos.size() > 0">
        and `no` in
            <foreach collection="nos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>

</mapper>

