package com.crhms.cloud.mqs.mqs_pm.vo;

import lombok.Builder;
import lombok.Data;

/**
 * 物价管理-院内物价对比 前端列表需要展示的数据
 *
 * @author: wangxing<PERSON>
 * @date: 2023/02/02
 */
@Data
@Builder
public class PmPriceCompReturnVo {

    /**
     * 标准物价
     */
    private PriceCompStd stdPrice;


    /**
     * 院内物价
     */
    private PriceCompHp hpPrice;

    /**
     * 对比结果
     */
    private String compResult;

    /**
     * 差异大类
     */
    private String diffCategory;

    /**
     * 差异内容
     */
    private String diffContent;
}
