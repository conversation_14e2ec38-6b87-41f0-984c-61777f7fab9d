package com.crhms.cloud.mqs.basic.vo;

import com.googlecode.jmapper.annotations.JMap;
import com.googlecode.jmapper.annotations.JMapConversion;
import lombok.Data;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class EngDiagnosisVo implements Serializable {

    /**
     * 诊断号 诊断全局唯一号
     */
    @JMap("diagnosisNo")
    private String diagnosis_no;

    /**
     * 单据号
     */
    @JMap("no")
    private String admission_no;

    /**
     * 诊断编码
     */
    @JMap("diagnosisCode")
    private String diagnosis_code;

    /**
     * 诊断名称
     */
    @JMap("diagnosisName")
    private String diagnosis_name;

    /**
     * 诊断科室编码
     */
    @JMap("diagDeptCode")
    private String diagnosis_dept_code;

    /**
     * 诊断科室名称
     */
    @JMap("diagDeptName")
    private String diagnosis_dept_name;

    /**
     * 主诊断标志;0:否 1:是
     */
    @JMap("mainFlag")
    private String main_flag;

    /**
     * 出入院诊断类型 1.入院 2.出院
     */
    @JMap("diagInoutType")
    private String diagnosis_inout_type;
    /**
     * 诊断类别
     */
    @JMap("diagType")
    private String diagnosis_type;

    /**
     * 诊断排序号
     */
    @JMap("diagnosisOrder")
    private String diagnosis_order;

    /**
     * 诊断时间
     */
    @JMap("diagnosisTime")
    private String diagnosis_time;

    /**
     * 入院病情
     */
    @JMap("diagnosisSituation")
    private String diagnosis_situation;


    /**
     * jmapper类型转换器
     */
    @JMapConversion(from = {"diagnosisTime"},
            to = {"diagnosis_time"})
    public String convertDateToString(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        return date == null ? null : format.format(date);
    }
}
