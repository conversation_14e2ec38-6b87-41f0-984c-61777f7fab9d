package com.crhms.cloud.mqs.mqs_ais.service;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.crhms.cloud.core.exception.BaseException;
import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.basic.utils.MqsUtils;
import com.crhms.cloud.mqs.mqs_ais.mapper.MqsAuditMonitorMapper;
import com.crhms.cloud.mqs.sys.enums.AuditScenarioEnum;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运营监控-审核监控 service层
 *
 * @author: 王新刚
 * @date: 2023/03/14
 */
@Service
//@Transactional(rollbackFor = Exception.class)
@Log4j2
public class MqsAuditMonitorService {

    @Autowired
    private MqsAuditMonitorMapper monitorMapper;

    // 统计单位 "万"
    private static BigDecimal tenThousand = new BigDecimal(10000l);

    /**
     * 今日实时
     *
     * @param auditScenario     场景: all:全部，op:门诊，hp：住院
     * @param psnTypes          人员类别编码
     * <AUTHOR>
     * @date 2023/03/14
     */
    public Map<String, Object> realTime(String auditScenario, String psnTypes,String hospitalId) {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String now = formatter.format(currentTime);
        Map<String, Object> result = new HashMap<>();
        //统计场景范围
        List<String> scenarioList = new ArrayList<>();
        switch (auditScenario){
            case "all":
                scenarioList.addAll(Arrays.stream(AuditScenarioEnum.values()).map(AuditScenarioEnum::getTableName).collect(Collectors.toList()));
                break;
            case "op":
                scenarioList.addAll(Arrays.stream(AuditScenarioEnum.values()).filter(x -> x.getAuditScenario().contains("op")).map(AuditScenarioEnum::getTableName).collect(Collectors.toList()));
                break;
            case "hp":
                scenarioList.addAll(Arrays.stream(AuditScenarioEnum.values()).filter(x -> x.getAuditScenario().contains("hp")).map(AuditScenarioEnum::getTableName).collect(Collectors.toList()));
                break;
        }
        //统计审核人次
        BigDecimal totalAuditTimes = monitorMapper.selectTotalAuditTimes(scenarioList, now, psnTypes, hospitalId);
        //统计当前在院人数
        BigDecimal inNums = monitorMapper.selectTotalInNums(scenarioList, psnTypes, hospitalId);
        //统计当前出院人数
        BigDecimal outNums = monitorMapper.selectTotalOutNums(scenarioList, now, psnTypes, hospitalId);
        result.put("totalAuditTimes",totalAuditTimes);
        result.put("inNums",inNums);
        result.put("outNums",outNums);

        return result;
    }

    /**
     * 年度使用情况
     *
     * @param filterYear    年份
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     * <AUTHOR>
     * @date 2023/03/15
     */
    public Map<String, Object> annualUsage(String filterYear, String auditScenario, String psnTypes, String hospitalId) {
        // 根据传入的时间参数解析出sql查询需要用到的时间参数
        Map<String, String> timeMap = MqsUtils.buildDate(filterYear);
        String startTime = timeMap.getOrDefault("startTime", null);
        String endTime = timeMap.getOrDefault("endTime", null);
        // 查询审核明细数和审核费用
        Map<String, Object> nAuditDetailAndAuditFee = monitorMapper.nAuditDetailAndAuditFee(startTime, endTime, auditScenario, psnTypes, hospitalId);
        // 查询强制保存的明细单量和强制保存的项目费用
        Map<String, Object> nForcedSaveAndProjectCost = monitorMapper.nForcedSaveAndProjectCost(startTime, endTime, auditScenario, psnTypes,hospitalId);
        //统计场景范围
        List<String> scenarioList = new ArrayList<>();
        switch (auditScenario){
            case "all":
                scenarioList.addAll(Arrays.stream(AuditScenarioEnum.values()).map(AuditScenarioEnum::getAuditScenario).collect(Collectors.toList()));
                break;
            case "op":
                scenarioList.addAll(Arrays.stream(AuditScenarioEnum.values()).filter(x -> x.getAuditScenario().contains("op")).map(AuditScenarioEnum::getAuditScenario).collect(Collectors.toList()));
                break;
            case "hp":
                scenarioList.addAll(Arrays.stream(AuditScenarioEnum.values()).filter(x -> x.getAuditScenario().contains("hp")).map(AuditScenarioEnum::getAuditScenario).collect(Collectors.toList()));
                break;
        }

        // 返回修改人次数 和 返回修改费用
        Map auditReturnMap = monitorMapper.selectAuditReturn(scenarioList, startTime, endTime, psnTypes, hospitalId);

        return new HashMap<String, Object>() {{
            // 审核明细数
            put("nAuditDetail", nAuditDetailAndAuditFee.get("nAuditDetail"));
            // 审核费用
            put("nAuditFee", MqsUtils.getBigDecimal(nAuditDetailAndAuditFee.get("nAuditFee")).divide(tenThousand,4, RoundingMode.HALF_UP));
            // 强制保存的明细单量
            put("nForcedSave", nForcedSaveAndProjectCost.get("nForcedSave"));
            // 强制保存的项目费用
            put("costForcedSave", MqsUtils.getBigDecimal(nForcedSaveAndProjectCost.get("costForcedSave")).divide(tenThousand,4, RoundingMode.HALF_UP));
            // 返回修改次数
            put("totalReturnNums", auditReturnMap.get("totalReturnNums"));
            // 返回修改费用
            put("totalReturnAmount", MqsUtils.getBigDecimal(auditReturnMap.get("totalReturnAmount")).divide(tenThousand,4, RoundingMode.HALF_UP));
        }};
    }

    /**
     * 违规规则排名
     *
     * @param filterYear    年份
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     * @param rankFlag      number:人次  amount:金额
     * <AUTHOR>
     * @date 2023/03/15
     */
    public List<Map<String, Object>> rankRule(String filterYear, String filterMonth, String filterDay, String auditScenario, String psnTypes, String rankFlag) {
        // 根据传入的时间参数解析出sql查询需要用到的时间参数
        Map<String, String> timeMap = MqsUtils.buildDate(filterYear, filterMonth, filterDay);
        String startTime = timeMap.getOrDefault("startTime", null);
        String endTime = timeMap.getOrDefault("endTime", null);

        // 根据传入的场景构建sql查询需要用到的场景集合
        List<String> auditScenarioList = buildAuditScenario(auditScenario);

        // 查询每个违规规则的规则名称、违规人次、违规金额
        List<Map<String, Object>> result = monitorMapper.rankRule(startTime, endTime, auditScenarioList, psnTypes, rankFlag);

        // 查询所有违规规则的违规金额
        BigDecimal totalAmount = monitorMapper.ruleTotalAmount(startTime, endTime, auditScenarioList, psnTypes, LoginContext.getHospitalId());

        // 拼接违规金额占比
        buildRankResult(result, totalAmount);

        return result;
    }


    /**
     * 违规项目排名
     *
     * @param filterYear    年份
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     * @param rankFlag      number:人次  amount:金额
     * <AUTHOR>
     * @date 2023/03/15
     */
    public List<Map<String, Object>> rankProject(String filterYear, String filterMonth, String filterDay, String auditScenario, String psnTypes, String rankFlag) {
        // 根据传入的时间参数解析出sql查询需要用到的时间参数
        Map<String, String> timeMap = MqsUtils.buildDate(filterYear, filterMonth, filterDay);
        String startTime = timeMap.getOrDefault("startTime", null);
        String endTime = timeMap.getOrDefault("endTime", null);

        // 根据传入的场景构建sql查询需要用到的场景集合
        List<String> auditScenarioList = buildAuditScenario(auditScenario);

        // 查询每个违规项目的项目名称、违规人次、违规金额
        List<Map<String, Object>> result = monitorMapper.rankProject(startTime, endTime, auditScenarioList, psnTypes, rankFlag, LoginContext.getHospitalId());

        // 查询所有违规项目的违规金额
        BigDecimal totalAmount = monitorMapper.projectTotalAmount(startTime, endTime, auditScenarioList, psnTypes, LoginContext.getHospitalId());

        // 拼接违规金额占比
        buildRankResult(result, totalAmount);

        return result;
    }

    /**
     * 根据传入的场景构建sql查询需要用到的场景集合
     *
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @return 对应的场景集合
     */
    private List<String> buildAuditScenario(String auditScenario) {
        List<String> auditScenarioList = new ArrayList<>();
        if ("op".equals(auditScenario) || "all".equals(auditScenario)) {
            auditScenarioList.add(AuditScenarioEnum.opPt.getAuditScenario());
            auditScenarioList.add(AuditScenarioEnum.opPct.getAuditScenario());
            auditScenarioList.add(AuditScenarioEnum.opRg.getAuditScenario());
        }
        if ("hp".equals(auditScenario) || "all".equals(auditScenario)) {
            auditScenarioList.add(AuditScenarioEnum.hpRg.getAuditScenario());
            auditScenarioList.add(AuditScenarioEnum.hpDo.getAuditScenario());
            auditScenarioList.add(AuditScenarioEnum.hpBk.getAuditScenario());
            auditScenarioList.add(AuditScenarioEnum.hpTf.getAuditScenario());
            auditScenarioList.add(AuditScenarioEnum.hpPred.getAuditScenario());
            auditScenarioList.add(AuditScenarioEnum.hpOut.getAuditScenario());
            auditScenarioList.add(AuditScenarioEnum.hpSettle.getAuditScenario());
        }
        return auditScenarioList;
    }

    /**
     * 拼接违规金额占比
     *
     * @param result      拼接前结果
     * @param totalAmount 违规总金额
     */
    private void buildRankResult(List<Map<String, Object>> result, BigDecimal totalAmount) {
        for (Map<String, Object> map : result) {
            if (BigDecimal.ZERO.compareTo(totalAmount) == 0) {
                // 结果等于0
                map.put("violationAmountRate", "0%");
                continue;
            }

            BigDecimal violationAmount = MqsUtils.getBigDecimal(map.get("violationAmount"));
            BigDecimal rate = NumberUtil.div(violationAmount, totalAmount, 2);
            map.put("violationAmountRate", NumberUtil.mul(rate, 100));
        }
    }

    /**
     * 科室违规明细
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     */
    public Map<String, List<Map<String, Object>>> deptViolationDetailsAndFees(String filterYear, String filterMonth, String filterDay, String auditScenario, String psnTypes) {
        // 根据传入的时间参数解析出sql查询需要用到的时间参数
        Map<String, String> timeMap = MqsUtils.buildDate(filterYear, filterMonth, filterDay);
        String startTime = timeMap.getOrDefault("startTime", null);
        String endTime = timeMap.getOrDefault("endTime", null);

        // 根据传入的场景构建sql查询需要用到的场景集合
        List<String> auditScenarioList = buildAuditScenario(auditScenario);

        // 查询科室违规明细
        List<Map<String, Object>> deptViolationDetails = monitorMapper.deptViolationDetails(startTime, endTime, auditScenarioList, psnTypes, LoginContext.getHospitalId());

        // 查询科室违规费用
        List<Map<String, Object>> deptViolationFees = monitorMapper.deptViolationFees(startTime, endTime, auditScenarioList, psnTypes, LoginContext.getHospitalId());

        // 计算违规单量占比
        calcViolationRate(deptViolationDetails);

        // 计算违规费用占比
        calcViolationRate(deptViolationFees);

        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        result.put("deptViolationDetails", deptViolationDetails);
        result.put("feeViolationDetails", deptViolationFees);
        return result;
    }

    private void calcViolationRate(List<Map<String, Object>> mapList) {
        for (Map<String, Object> map : mapList) {
            // 总明细单量
            if(ObjectUtil.isNull(map)){
                continue;
            }
            log.info("===detail:"+ map.toString());
            BigDecimal details = MqsUtils.getBigDecimal(map.get("details"));
            if (BigDecimal.ZERO.compareTo(details) == 0) {
                map.put("rate", BigDecimal.ZERO);
                continue;
            }
            // 违规明细单量
            BigDecimal violationDetails = MqsUtils.getBigDecimal(map.get("violationDetails"));

            BigDecimal rate = NumberUtil.div(violationDetails, details, 2);
            map.put("rate", NumberUtil.mul(rate, 100));
        }
    }


    /**
     * 违规明细趋势 和 违规费用趋势
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     */
    public Map<String, List<Map<String, Object>>> violationTendency(String filterYear, String filterMonth, String filterDay, String auditScenario, String psnTypes) {
        // 得到12个月份集合
        Map<String, Object> map = buildMonths(filterYear, filterMonth, filterDay);
        String startTime = map.get("startTime").toString();
        String endTime = map.get("endTime").toString();
        List<String> months = (List<String>) map.get("months");

        // 根据传入的场景构建sql查询需要用到的场景集合
        List<String> auditScenarioList = buildAuditScenario(auditScenario);


        // 查询违规明细趋势
        List<Map<String, Object>> detailsTendency = monitorMapper.detailsTendency(startTime, endTime, auditScenarioList, psnTypes, LoginContext.getHospitalId());
        // 处理违规明细趋势查询结果
        List<Map<String, Object>> detailsTendencyResult = buildEveryMonthResult(months, detailsTendency);

        // 查询违规费用趋势
        List<Map<String, Object>> feesTendency = monitorMapper.feesTendency(months, auditScenarioList, psnTypes, LoginContext.getHospitalId());
        // 处理违规明细趋势查询结果
        List<Map<String, Object>> feesTendencyResult = buildEveryMonthResult(months, feesTendency);

        // 返回结果
        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        result.put("detailsTendency", detailsTendencyResult);
        result.put("feesTendency", feesTendencyResult);
        return result;
    }


    private List<Map<String, Object>> buildEveryMonthResult(List<String> months, List<Map<String, Object>> mapList) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (String month : months) {
            // 查询结果中有这个月份, 就加入result;
            int num = 0;
            for (Map<String, Object> map : mapList) {
                if (month.equals(map.get("whichMonth"))) {
                    result.add(map);
                    break;
                }
                num++;
            }
            // 查询结果中没有这个月份, 就将查询结果置零
            if (num == mapList.size()) {
                result.add(new HashMap<String, Object>(){{
                    put("whichMonth", month);
                    put("details", BigDecimal.ZERO);
                    put("violationDetails", BigDecimal.ZERO);
                }});
            }

        }

        // 计算违规占比
        calcViolationRate(result);

        return result;
    }

    /**
     * 返回12个月份
     * @param filterYear    年
     * @param filterMonth   月
     * @param filterDay     日
     * @return 12个月份集合
     */
    private Map<String, Object>  buildMonths(String filterYear, String filterMonth, String filterDay) {
        // 根据传入的时间参数解析出sql查询需要用到的时间参数
        DateTime endTime;
        DateTime startTime;

        if (StrUtil.isNotEmpty(filterYear)) {
            // 传了年份
            if (StrUtil.isNotEmpty(filterMonth)) {
                // 还传了月份
                endTime = DateUtil.parseDate(filterYear + "-" + filterMonth + "-" + 1);
                startTime = DateUtil.offset(endTime, DateField.MONTH, -11);
            } else {
                // 只传了年份
                endTime = DateUtil.parseDate(filterYear + "-" + 12 + "-" + 1);
                startTime = DateUtil.parseDate(filterYear + "-" + 1 + "-" + 1);
            }
        } else {
            // 没传年份
            throw new BaseException("必须至少传年月");
        }

        List<DateTime> monthList = DateUtil.rangeToList(startTime, endTime, DateField.MONTH);
        List<String> months = monthList.stream().map(month -> DateUtil.format(month, "yyyy-MM")).collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("startTime", startTime);
        result.put("endTime", endTime);
        result.put("months", months);
        return result;
    }


    /**
     * 费用结构异常占比
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     */
    public List<Map<String, Object>> costStructure(String filterYear, String filterMonth, String filterDay, String auditScenario, String psnTypes) {
        // 根据传入的时间参数解析出sql查询需要用到的时间参数
        Map<String, String> timeMap = MqsUtils.buildDate(filterYear, filterMonth, filterDay);
        String startTime = timeMap.getOrDefault("startTime", null);
        String endTime = timeMap.getOrDefault("endTime", null);

        // 根据传入的场景构建sql查询需要用到的场景集合
        List<String> auditScenarioList = buildAuditScenario(auditScenario);

        // 查询费用结构异常占比
        List<Map<String, Object>> result = monitorMapper.costStructure(startTime, endTime, auditScenarioList, psnTypes, LoginContext.getHospitalId());

        // get违规明细总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (Map<String, Object> map : result) {
            BigDecimal violationAmount = MqsUtils.getBigDecimal(map.get("violationAmount"));
            totalAmount = totalAmount.add(violationAmount);
        }
        buildCostStructure(result, totalAmount);
        return result;
    }

    private void buildCostStructure(List<Map<String, Object>> result, BigDecimal totalAmount) {
        buildRankResult(result, totalAmount);
    }

    /**
     * 场景审核人次
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     */
    public List<Map<String, Object>> nAuditScenario(String filterYear, String filterMonth, String filterDay, String auditScenario, String psnTypes) {
        // 根据传入的时间参数解析出sql查询需要用到的时间参数
        Map<String, String> timeMap = MqsUtils.buildDate(filterYear, filterMonth, filterDay);
        String startTime = timeMap.getOrDefault("startTime", null);
        String endTime = timeMap.getOrDefault("endTime", null);

        // 根据传入的场景构建sql查询需要用到的场景集合
        List<String> auditScenarioList = buildAuditScenario(auditScenario);

        // 查询场景审核人次
        List<Map<String, Object>> resultMapList = monitorMapper.nAuditScenario(startTime, endTime, auditScenarioList, psnTypes);
        // 审核场景编码匹配审核场景名称
        for (Map<String, Object> map : resultMapList) {
            map.put("auditScenarioName", AuditScenarioEnum.valueOf(String.valueOf(map.get("audit_scenario"))).getDesc());
        }
        return resultMapList;
    }

    /**
     * 违规主单统计
     *
     * @param filterYear    年份
     * @param filterMonth   月份
     * @param filterDay     日期
     * @param auditScenario 场景: all:全部，op:门诊，hp：住院
     * @param psnTypes      人员类别编码
     * @param rankFlag      排序字段: 主单数量或者金额
     */
    public List<Map<String, Object>> violationHead(String filterYear, String filterMonth, String filterDay, String auditScenario, String psnTypes, String rankFlag) {
        // 根据传入的时间参数解析出sql查询需要用到的时间参数
        Map<String, String> timeMap = MqsUtils.buildDate(filterYear, filterMonth, filterDay);
        String startTime = timeMap.getOrDefault("startTime", null);
        String endTime = timeMap.getOrDefault("endTime", null);

        // 根据传入的场景构建sql查询需要用到的场景集合
        List<String> auditScenarioList = buildAuditScenario(auditScenario);

        // 查询每个违规主单的规则名称、规则分类、违规主单数量、违规金额
        return monitorMapper.violationHead(startTime, endTime, auditScenarioList, psnTypes, rankFlag, LoginContext.getHospitalId());
    }
}
