package com.crhms.cloud.mqs.mqs_mr.manager;


import cn.hutool.core.date.DateUtil;
import com.crhms.cloud.mqs.basic.domain.BaseAudit;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalCase;
import com.crhms.cloud.mqs.basic.domain.BaseMedicalStats;
import com.crhms.cloud.mqs.mqs_mr.domain.MrPlan;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/*
*  业务审核节点实现
* */
public class DataBussinessConditions extends MrPlanFilter{


    @Override
    public boolean filter(BaseMedicalCase medicalCase, MrPlan mrPlan, List<BaseAudit> baseAudits, String filterFlag, List<String> sceneCodes, String auditScenario, Map<String,List<BaseMedicalStats>> baseMedicalStatsMap) {
        //跳过审核流程节点
        if("DataBussinessConditions".equals(filterFlag)) {
            return next(medicalCase,mrPlan,baseAudits,"",sceneCodes,auditScenario, baseMedicalStatsMap);
        }
        // 获取配置的业务场景
        String bussinessProcessNode = mrPlan.getBusinessProcessNode();

        //根据单据号获取对应的业务流程结果
        List<BaseMedicalStats> baseMedicalStats = baseMedicalStatsMap.get(medicalCase.getNo());
        BaseMedicalStats medicalStats = baseMedicalStats.get(0);

        boolean result = true;
        switch (bussinessProcessNode){
            case "1" :  {
                result = "1".equals(medicalStats.getPreDischarge());//已开预出院     出院时间在当前时间之后  (这个需要预先保存预出院状态然后根据no查询状态进行判断)
                break;
            }
            case "2" :   {
                if(Objects.nonNull(medicalCase.getDischargeDate())){        //今日出院       出院时间不为空则判断出院时间是否是今天，出院时间为空则判断预出院时间
                    result = DateUtil.isSameDay(medicalCase.getDischargeDate(),new Date());
                } else if (Objects.isNull(medicalCase.getDischargeDate()) && Objects.nonNull(medicalStats.getPreDischargeDate())){
                    result = DateUtil.isSameDay(medicalStats.getPreDischargeDate(),new Date());
                } else {
                    result = false;
                }
                break;
            }
            case "3" :  {
                result = "1".equals(medicalStats.getDischarged());    //已出院        出院时间在当前时间之前       根据主单的discharge_date  出院时间进行判断
                break;
            }
            case "4" :    {
                result = "1".equals(medicalStats.getSettled());   //已结算        单据结算日期在当前时间之前    根据主单的bill_date       单据结算日期进行判断
                break;
            }
        }
        //返回结果
        return result && next(medicalCase,mrPlan,baseAudits,filterFlag,sceneCodes,auditScenario, baseMedicalStatsMap);
    }
}
