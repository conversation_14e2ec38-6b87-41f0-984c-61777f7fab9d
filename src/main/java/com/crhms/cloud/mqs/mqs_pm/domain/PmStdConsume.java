package com.crhms.cloud.mqs.mqs_pm.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 物价管理-市级标准物价-耗材实体类
 *
 * @author: wangxingang
 * @date: 2023/01/31
 */

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("mqs_pm_std_consume")
public class PmStdConsume extends BaseDomain {
    @TableId
    private Long id;

    /**
     * 生效时间
     */
    @TableField
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private Date effectTime;

    /**
     * 产品编码
     */
    @TableField
    private String productCode;

    /**
     * 产品名称
     */
    @TableField
    private String productName;

    /**
     * 规格
     */
    @TableField
    private String specification;

    /**
     * 型号
     */
    @TableField
    private String model;

    /**
     * 注册证号
     */
    @TableField
    private String registerNumber;

    /**
     * 生产企业
     */
    @TableField
    private String prodEnterprise;

    /**
     * 包装规格
     */
    @TableField
    private String packSpecification;

    /**
     * 价格
     */
    @TableField
    private BigDecimal price;


}
