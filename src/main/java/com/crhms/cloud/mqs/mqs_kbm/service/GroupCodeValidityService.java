package com.crhms.cloud.mqs.mqs_kbm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_kbm.domain.GroupCodeValidity;
import com.crhms.cloud.mqs.mqs_kbm.mapper.GroupCodeValidityMapper;
import com.crhms.cloud.mqs.mqs_kbm.vo.GroupCodeValidityVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 知识库管理-分组合理性-编码有效性
 *
 * @author: wangxingang
 * @date: 2023.02.06
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class GroupCodeValidityService extends ServiceImpl<GroupCodeValidityMapper, GroupCodeValidity> {
    /**
     * 查询列表
     *
     * @param pageInfo 分页信息
     * @param filter   查询条件
     * @return 查询列表
     */
    public List<GroupCodeValidity> queryCodeValidity(Page<GroupCodeValidity> pageInfo, GroupCodeValidityVo filter) {
        return page(pageInfo, new LambdaQueryWrapper<GroupCodeValidity>()
                .in(CollUtil.isNotEmpty(filter.getVersionNoList()), GroupCodeValidity::getVersionNo, filter.getVersionNoList())
                .in(CollUtil.isNotEmpty(filter.getTypeList()), GroupCodeValidity::getType, filter.getTypeList())
                .like(StrUtil.isNotEmpty(filter.getDiagCode()), GroupCodeValidity::getDiagCode, filter.getDiagCode())
                .like(StrUtil.isNotEmpty(filter.getDiagName()), GroupCodeValidity::getDiagName, filter.getDiagName())
                .in(CollUtil.isNotEmpty(filter.getValidFlagList()), GroupCodeValidity::getValidFlag, filter.getValidFlagList())
        ).getRecords();
    }

    /**
     * 查询版本号下拉列表
     */
    public List<String> queryVersionNoList() {
        return baseMapper.selectVersionNoList();
    }

    /**
     * 查询类型下拉列表
     */
    public List<String> queryTypeList() {
        return baseMapper.selectTypeList();
    }

    /**
     * 查询是否有效下拉列表
     */
    public List<String> queryValidFlagList() {
        return baseMapper.selectValidFlagList();
    }
}
