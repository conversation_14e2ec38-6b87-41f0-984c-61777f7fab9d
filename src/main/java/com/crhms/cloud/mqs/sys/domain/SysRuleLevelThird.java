package com.crhms.cloud.mqs.sys.domain;

import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 三方规则级别映射(SysRuleLevelThird)实体类
 *
 * <AUTHOR>
 * @since 2023-03-21 17:53:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_rule_level_third")
public class SysRuleLevelThird extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 895305315787957653L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_PLATFORM="platform";
    public static final String FIELD_CODE="code";
    public static final String FIELD_DEFAULT_NAME="default_name";
    public static final String FIELD_SYS_CODE="sys_code";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 平台 1自定义2内置3医保平台
     */    
    @TableField
    private String platform;
    /**
     * 规则级别编码
     */    
    @TableField
    private String code;
    /**
     * 默认规则级别名称
     */    
    @TableField
    private String defaultName;
    /**
     * 映射到系统值
     */
    @TableField(exist = false)
    private String name;
    /**
     * 系统规则级别编码
     */    
    @TableField
    private String sysCode;
    /**
     * 更新人
     */    
    @TableField
    private Long lastUpdatedBy;
    /**
     * 更新时间
     */    
    @TableField
    private Date lastUpdatedDate;
    /**
     * 创建人
     */    
    @TableField
    private Long createdBy;
    /**
     * 创建时间
     */    
    @TableField
    private Date createdDate;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;


}

