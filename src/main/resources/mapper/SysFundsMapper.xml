<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crhms.cloud.mqs.sys.mapper.SysFundsMapper">
    <update id="syncTotalAmount">

        UPDATE mqs_sys_funds a
        SET total_amount = (
	IF
		(
			warn_type = '1',(
			SELECT
				IFNULL( SUM( b.years_amount ), 0 )
			FROM
				mqs_sys_funds_dept b
			WHERE
				b.funds_id = a.id
				),(
			SELECT
				IFNULL( SUM( c.years_amount ), 0 )
			FROM
				mqs_sys_funds_zone c
			WHERE
				c.funds_id = a.id
			)
		)) +
IF
	(
		is_alone = '1',(
		SELECT
			IFNULL( SUM( b.amount ), 0 )
		FROM
			mqs_sys_funds_alone b
		WHERE
			b.funds_id = a.id
		),
		0
	),
	total_warn_amount = (
	IF
		(
			warn_type = '1',(
			SELECT
				IFNULL( SUM( b.years_warn ), 0 )
			FROM
				mqs_sys_funds_dept b
			WHERE
				b.funds_id = a.id
				),(
			SELECT
				IFNULL( SUM( c.years_warn ), 0 )
			FROM
				mqs_sys_funds_zone c
			WHERE
				c.funds_id = a.id
			)
		)) +
IF
	(
		is_alone = '1',(
		SELECT
			IFNULL( SUM( b.warn ), 0 )
		FROM
			mqs_sys_funds_alone b
		WHERE
			b.funds_id = a.id
		),
		0
	)
	WHERE
    a.hospital_id = #{hospitalId}
	and a.id = #{id}
    </update>

    <select id="queryUsedAmountByYear" resultType="java.util.Map">
        SELECT
        <if test="isCdsCi">
            head.cs_disease_code,
        </if>
        <if test="isSd">
            head.single_disease_code,
        </if>
        <if test="isSi">
            head.out_diagnosis_code,
        </if>
        <if test="isTd">
            detail.item_id,
        </if>
        COALESCE(SUM(detail.bmi_overall_amount),0) as total
        from mqs_medical_case head
        left join mqs_fee_list detail on detail.`no` = head.`no` and detail.audit_scenario = head.audit_scenario
        where
        head.hospital_id = #{hospitalId}
        and head.audit_scenario = #{scenario}
        and head.bill_date &gt;= concat(#{year},'-01-01 00:00:00')
        and head.bill_date &lt;= concat(#{year},'-12-31 23:59:59')
        <if test="isCdsCi || isSd || isSi || isTd">
            <trim prefix="GROUP BY" suffixOverrides=",">

            <if test="isCdsCi">
                head.cs_disease_code,
            </if>
            <if test="isSd">
                head.single_disease_code,
            </if>
            <if test="isSi">
                head.out_diagnosis_code,
            </if>
            <if test="isTd">
                detail.item_id,
            </if>
            </trim>
        </if>


    </select>
    <select id="queryCsDisease" resultType="java.util.Map">
        SELECT
            cs_disease_code,
            cs_disease_name,
            type
        FROM
            mqs_kbm_cs_disease
        WHERE
            hospital_id = #{hospitalId}
    </select>
    <select id="queryUsedAmountByDept" resultType="java.util.Map">
        SELECT
        toMonth(head.bill_date) as `month`,
        head.discharge_dept_code,
        COALESCE(SUM(detail.bmi_overall_amount),0) as total
        from mqs_medical_case head
        left join mqs_fee_list detail on detail.`no` = head.`no` and detail.audit_scenario = head.audit_scenario
        where
        head.hospital_id = #{hospitalId}
        and head.audit_scenario = #{scenario}
        and head.bill_date &gt;= concat(#{year},'-01-01 00:00:00')
        and head.bill_date &lt;= concat(#{year},'-12-31 23:59:59')
        <if test="ignoreCsDiseaseCode != null and ignoreCsDiseaseCode.size >0 ">
            and (head.cs_disease_code is null or head.cs_disease_code not in
            <foreach collection="ignoreCsDiseaseCode" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>)
        </if>
        <if test="ignoreSiDiagnosisCode != null and ignoreSiDiagnosisCode.size >0 ">
            and (head.out_diagnosis_code is null or head.out_diagnosis_code not in
            <foreach collection="ignoreSiDiagnosisCode" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>)
        </if>
        <if test="ignoreSingleDiseaseCode != null and ignoreSingleDiseaseCode.size >0 ">
            and (head.single_disease_code is null or head.single_disease_code not in
            <foreach collection="ignoreSingleDiseaseCode" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>)
        </if>
        <if test="ignoreTdDieasess != null and ignoreTdDieasess.size >0 ">
            and detail.item_id not in
            <foreach collection="ignoreTdDieasess" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY
        discharge_dept_code,
        `month`
    </select>
    <select id="queryUsedAmountByZone" resultType="java.util.Map">
        SELECT
        toMonth(head.bill_date) as `month`,
        head.out_zone_code,
        COALESCE(SUM(detail.bmi_overall_amount),0) as total
        from mqs_medical_case head
        left join mqs_fee_list detail on detail.`no` = head.`no` and detail.audit_scenario = head.audit_scenario
        where
        head.hospital_id = #{hospitalId}
        and head.audit_scenario = #{scenario}
        and head.bill_date &gt;= concat(#{year},'-01-01 00:00:00')
        and head.bill_date &lt;= concat(#{year},'-12-31 23:59:59')
        <if test="ignoreCsDiseaseCode != null and ignoreCsDiseaseCode.size >0 ">
            and (head.cs_disease_code is null or head.cs_disease_code not in
            <foreach collection="ignoreCsDiseaseCode" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>)
        </if>
        <if test="ignoreSiDiagnosisCode != null and ignoreSiDiagnosisCode.size >0 ">
            and (head.out_diagnosis_code is null or head.out_diagnosis_code not in
            <foreach collection="ignoreSiDiagnosisCode" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>)
        </if>
        <if test="ignoreSingleDiseaseCode != null and ignoreSingleDiseaseCode.size >0 ">
            and (head.single_disease_code is null or head.single_disease_code not in
            <foreach collection="ignoreSingleDiseaseCode" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>)
        </if>
        <if test="ignoreTdDieasess != null and ignoreTdDieasess.size >0 ">
            and detail.item_id not in
            <foreach collection="ignoreTdDieasess" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY
        out_zone_code,
        `month`
    </select>


    <select id="queryUsedAmountAll" resultType="java.util.Map">

        SELECT
        head.audit_scenario,
        head.cs_disease_code,
        head.out_diagnosis_code,
        head.single_disease_code,
        COALESCE(SUM(head.bmi_overall_amount),0) as total
        from mqs_medical_case head
        where
        head.hospital_id = #{hospitalId}
        and head.audit_scenario in
        <foreach collection="scenarios" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and head.bill_date &gt;= concat(#{year},'-01-01 00:00:00')
        and head.bill_date &lt;= concat(#{year},'-12-31 23:59:59')
        GROUP BY
        head.audit_scenario,
        head.cs_disease_code,
        head.out_diagnosis_code,
        head.single_disease_code

    </select>


    <select id="queryUsedAmountAlltdDieasess" resultType="java.util.Map">

        SELECT
        head.audit_scenario,
        'tdDieasess' as tdDieasess,
        COALESCE(SUM(head.bmi_overall_amount),0) as total
        from mqs_fee_list head
        where
        head.hospital_id = #{hospitalId}
        and head.audit_scenario in
        <foreach collection="scenarios" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and head.item_id in
        <foreach collection="tdDieasess" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and head.bill_date &gt;= concat(#{year},'-01-01 00:00:00')
        and head.bill_date &lt;= concat(#{year},'-12-31 23:59:59')
        GROUP BY
        head.audit_scenario

    </select>

    <select id="queryUsedAmountAllByMonth" resultType="java.util.Map">
        SELECT
        toMonth(head.bill_date) as `month`,
        head.audit_scenario,
        head.cs_disease_code,
        head.out_diagnosis_code,
        head.single_disease_code,
        detail.item_id,
        COALESCE(SUM(detail.bmi_overall_amount),0) as total
        from mqs_medical_case head
        left join mqs_fee_list detail on detail.`no` = head.`no` and detail.audit_scenario = head.audit_scenario
        where
        head.hospital_id = #{hospitalId}
        and head.audit_scenario in
        <foreach collection="scenarios" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and head.bill_date &gt;= concat(#{year},'-01-01 00:00:00')
        and head.bill_date &lt;= concat(#{year},'-12-31 23:59:59')
        GROUP BY
        head.audit_scenario,
        head.cs_disease_code,
        head.out_diagnosis_code,
        head.single_disease_code,
        detail.item_id,
        `month`
    </select>

    <select id="querySiDisease" resultType="java.util.Map">
        SELECT
            code,
            name
        FROM
            mqs_kbm_si_disease
        WHERE
            hospital_id = #{hospitalId}

    </select>
    <select id="queryTdDisease" resultType="java.util.Map">
        SELECT
            item_id,
            item_name
        FROM
            mqs_kbm_td_disease
        WHERE
            hospital_id = #{hospitalId}
    </select>
    <select id="getAmountByMonth" resultType="java.util.Map">
        SELECT
        years,
        SUM(`1`) as '01',
        SUM(`2`) as '02',
        SUM(`3`) as '03',
        SUM(`4`) as '04',
        SUM(`5`) as '05',
        SUM(`6`) as '06',
        SUM(`7`) as '07',
        SUM(`8`) as '08',
        SUM(`9`) as '09',
        SUM(`10`) as '10',
        SUM(`11`) as '11',
        SUM(`12`) as '12'
        FROM
        (
        SELECT
        years,
        SUM(amount1) as '1',
        SUM(amount1) as '2',
        SUM(amount1) as '3',
        SUM(amount1) as '4',
        SUM(amount1) as '5',
        SUM(amount1) as '6',
        SUM(amount1) as '7',
        SUM(amount1) as '8',
        SUM(amount1) as '9',
        SUM(amount1) as '10',
        SUM(amount1) as '11',
        SUM(amount1) as '12'
        FROM mqs_sys_funds_dept a
        LEFT JOIN mqs_sys_funds b on a.funds_id = b.id
        WHERE a.hospital_id = #{hospitalId}
        <choose>
            <when test="deptIds != null and deptIds.size > 0">
                and funds_id in
                <foreach collection="deptIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
        GROUP BY years

        UNION ALL
        SELECT
        years,
        SUM(amount1) as '1',
        SUM(amount1) as '2',
        SUM(amount1) as '3',
        SUM(amount1) as '4',
        SUM(amount1) as '5',
        SUM(amount1) as '6',
        SUM(amount1) as '7',
        SUM(amount1) as '8',
        SUM(amount1) as '9',
        SUM(amount1) as '10',
        SUM(amount1) as '11',
        SUM(amount1) as '12'

        FROM mqs_sys_funds_zone a
        LEFT JOIN mqs_sys_funds b on a.funds_id = b.id
        WHERE a.hospital_id = #{hospitalId}
        <choose>
            <when test="zoneIds != null and zoneIds.size > 0">
                and funds_id in
                <foreach collection="zoneIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
        GROUP BY years
        ) x

        GROUP BY x.years

    </select>
    <select id="queryAloneAmount" resultType="com.crhms.cloud.mqs.sys.domain.SysFundsAlone">

        select
        alone.alone_code,
        alone.amount,
        alone.warn,
        fund.visit_type
        from
        mqs_sys_funds_alone alone
        left join mqs_sys_funds fund on fund.id = alone.funds_id
        where
        alone.hospital_id = #{hospitalId}
        and fund.is_alone = '1'
        and funds_id in
        <foreach collection="fundIds" open="(" close=")" separator="," item="item">
         #{item}
        </foreach>

    </select>
    <select id="queryFundsByYear" resultType="java.util.Map">
        SELECT
            SUM( years_amount ) years_amount,
            SUM( years_warn ) years_warn,
            visit_type
        FROM
            (
            SELECT
                fund.visit_type,
                dept.years_amount,
                dept.years_warn
            FROM
                mqs_sys_funds_dept dept
                LEFT JOIN mqs_sys_funds fund ON fund.id = dept.funds_id
            WHERE
                fund.warn_type = '1'
                AND fund.years = #{filterYear}
                AND dept.hospital_id = #{hospitalId}

            UNION ALL

            SELECT
                fund.visit_type,
                zo.years_amount,
                zo.years_warn
            FROM
                mqs_sys_funds_zone zo
                LEFT JOIN mqs_sys_funds fund ON fund.id = zo.funds_id
            WHERE
                fund.warn_type = '2'
                AND fund.years =  #{filterYear}
                AND zo.hospital_id = #{hospitalId}
            ) a
        GROUP BY
            a.visit_type
    </select>
    <select id="selectCdsCiAmount" resultType="java.util.Map">
        SELECT
            SUM( amount ) as amount,
            alone.alone_code as aloneCode
        FROM
            mqs_sys_funds_alone alone
            LEFT JOIN mqs_sys_funds funds ON alone.funds_id = funds.id
        WHERE
            funds.is_alone = #{hospitalId}
            AND alone.alone_code IN ( 'CdsCi', 'Cds', 'Ci' )
            AND funds.years = #{filterYear}
            GROUP BY alone.alone_code
    </select>

</mapper>

