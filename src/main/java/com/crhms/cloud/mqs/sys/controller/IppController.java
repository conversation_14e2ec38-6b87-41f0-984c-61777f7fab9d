package com.crhms.cloud.mqs.sys.controller;


import com.crhms.cloud.core.utils.LoginContext;
import com.crhms.cloud.mqs.sys.domain.IppDept;
import com.crhms.cloud.mqs.sys.domain.IppDoctor;
import com.crhms.cloud.mqs.sys.domain.IppZone;
import com.crhms.cloud.mqs.sys.service.IppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 集成平台主数据控制层
 * <AUTHOR>
 * @since 2023-01-12 09:43:03
 */
@RestController
@RequestMapping("api/mqs/sys")
public class IppController {

    @Autowired
    IppService ippService;

    /**
     * 获取科室列表
     * @param dept
     * @return
     */
    @GetMapping("/selectDeptList")
    public ResponseEntity<List<IppDept>> selectDeptList(@RequestParam(value = "dept",required = false) String dept){
        return ResponseEntity.ok(ippService.selectDeptList(dept, LoginContext.getHospitalId()));
    }

    /**
     * 获取医生列表
     * @param doctor
     * @return
     */
    @GetMapping("/selectDoctorList")
    public ResponseEntity<List<IppDoctor>> selectDoctorList(@RequestParam(value = "doctor" ,required = false) String doctor){
        return ResponseEntity.ok(ippService.selectDoctorList(doctor, LoginContext.getHospitalId()));
    }

    /**
     * 获取病区列表
     * @param zone
     * @return
     */
    @GetMapping("/selectZoneList")
    public ResponseEntity<List<IppZone>> selectZoneList(@RequestParam(value = "zone" ,required = false) String zone){
        return ResponseEntity.ok(ippService.selectZoneList(zone, LoginContext.getHospitalId()));
    }
    /**
     * 获取用户表
     */
    @GetMapping("/selectUserList")
    public ResponseEntity<List<Map>> selectUserList(@RequestParam(value = "user" ,required = false) String user){
        return ResponseEntity.ok(ippService.selectUserList(user, LoginContext.getHospitalId()));
    }

}
