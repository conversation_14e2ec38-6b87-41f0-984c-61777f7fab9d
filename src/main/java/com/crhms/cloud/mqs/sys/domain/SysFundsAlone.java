package com.crhms.cloud.mqs.sys.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 运营管理-基金管理-单独管理(SysFundsAlone)实体类
 *
 * <AUTHOR>
 * @since 2023-02-21 18:49:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_funds_alone")
public class SysFundsAlone extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 963113724287870446L;
    
        public static final String FIELD_ID="id";
    public static final String FIELD_YEARS="years";
    public static final String FIELD_FUNDS_ID="funds_id";
    public static final String FIELD_ALONE_CODE="alone_code";
    public static final String FIELD_ALONE_NAME="alone_name";
    public static final String FIELD_AMOUNT="amount";
    public static final String FIELD_WARN="warn";
    public static final String FIELD_USED_AMOUNT="used_amount";
    public static final String FIELD_USED_RATIO="used_ratio";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 年度
     */    
    @TableField
    private String years;
    /**
     * 头id
     */    
    @TableField
    private Long fundsId;
    /**
     * 管理维度
     */    
    @TableField
    private String aloneCode;
    /**
     * 管理维度名称
     */    
    @TableField
    private String aloneName;
    /**
     * 就诊类型1.门诊 2 住院
     */
    @TableField(exist = false)
    private String visitType;
    /**
     * 年度基金
     */    
    @TableField
    private BigDecimal amount;
    /**
     * 预警值
     */    
    @TableField
    private BigDecimal warn;
    /**
     * 年度预警占比
     */
    @TableField(exist = false)
    private BigDecimal yearsWarnRatio;
    /**
     * 已使用金额
     */
    @TableField
    private BigDecimal usedAmount;
    /**
     * 占比
     */    
    @TableField
    private BigDecimal usedRatio;

//    /**
//     * 占用权重(权重越大，基金使用优先级越高)
//     */
//    @TableField
//    private Integer weight;
    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;

    public BigDecimal getUsedAmount() {
        return ObjectUtil.isNull(usedAmount) ? BigDecimal.ZERO : usedAmount;
    }

    public BigDecimal getUsedRatio() {
        return ObjectUtil.isNull(usedRatio) ? BigDecimal.ZERO : usedRatio;
    }
}

