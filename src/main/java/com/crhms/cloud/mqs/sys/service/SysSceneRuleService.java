package com.crhms.cloud.mqs.sys.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.sys.domain.SysSceneRule;
import com.crhms.cloud.mqs.sys.mapper.SysSceneRuleMapper;
import com.crhms.cloud.mqs.sys.service.SysSceneRuleService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;
import java.util.List;

/**
 * 运营管理-审核场景-规则(SysSceneRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-23 17:14:43
 */
@Service("sysSceneRuleService")
public class SysSceneRuleService extends ServiceImpl< SysSceneRuleMapper, SysSceneRule> {
}
