package com.crhms.cloud.mqs.sys.domain;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


/**
 * 运营管理-审核场景(SysScene)实体类
 *
 * <AUTHOR>
 * @since 2022-12-21 13:37:48
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants()
@TableName("mqs_sys_scene")
public class SysScene extends BaseDomain implements Serializable {
    private static final long serialVersionUID = -83110375245574117L;
    
    public static final String FIELD_ID="id";
    public static final String FIELD_SCENE_CODE="scene_code";
    public static final String FIELD_SCENE_NAME="scene_name";
    public static final String FIELD_ENABLE= "enable";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * 表id
     */    
    @TableId
    private Long id;

    /**
     * 审核场景
     */    
    @TableField
    private String sceneCode;
    /**
     * 审核场景名称
     */    
    @TableField
    private String sceneName;

    /**
     * 是否启用
     */
    @TableField
    private String enable;

    /**
     * 院区id
     */    
    @TableField
    private String hospitalId;

    // 非数据库字段

    /**
     * 规则集
     */
    @TableField(exist = false)
    private String ruleNames;

    /**
     * 规则关联集
     */
    @TableField(exist = false)
    List<SysSceneRule> ruleLists;

    /**
     * 主场景编码
     */
    @TableField()
    private String mainSceneCode;

    /**
     * 排序
     */
    @TableField()
    private String orderNum;
}

