package com.crhms.cloud.mqs.mqs_mr.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.crhms.cloud.core.base.BaseDomain;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.Date;


/**
 * 人工审核实体类
 *
 * <AUTHOR>
 * @since 2023-01-17 09:48:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class MrReview implements Serializable {
    private static final long serialVersionUID = 279651493348686413L;

    public static final String FIELD_ID="id";
    public static final String FIELD_NO="no";
    public static final String FIELD_ADMISSION_NO="admission_no";
    public static final String FIELD_INPATIENT_AREA="inpatient_area";
    public static final String FIELD_BENEFIT_TYPE="benefit_type";
    public static final String FIELD_PERSONNEL_TYPE="personnel_type";
    public static final String FIELD_HS_STATUS="hs_status";
    public static final String FIELD_MEDICAL_TYPE="medical_type";
    public static final String FIELD_ITEM_DATE="item_date";
    public static final String FIELD_PATIENT="patient";
    public static final String FIELD_PATIENT_NAME="patient_name";
    public static final String FIELD_GENDER="gender";
    public static final String FIELD_BIRTH_DATE="birth_date";
    public static final String FIELD_IN_DISEASE_ID="in_disease_id";
    public static final String FIELD_IN_DISEASE_ZH="in_disease_zh";
    public static final String FIELD_OUT_DISEASE_ID="out_disease_id";
    public static final String FIELD_OUT_DISEASE_ZH="out_disease_zh";
    public static final String FIELD_PRIMARY_DISEASE_ID="primary_disease_id";
    public static final String FIELD_PRIMARY_DISEASE_ZH="primary_disease_zh";
    public static final String FIELD_SECONDARY_DISEASE_ID="secondary_disease_id";
    public static final String FIELD_SECONDARY_DISEASE_ZH="secondary_disease_zh";
    public static final String FIELD_VIOLATION_FLAG="violation_flag";
    public static final String FIELD_IN_DATE="in_date";
    public static final String FIELD_OUT_DATE="out_date";
    public static final String FIELD_SETTLE_DATE="settle_date";
    public static final String FIELD_COSTS="costs";
    public static final String FIELD_BMI_CONVERED_AMOUNT="bmi_convered_amount";
    public static final String FIELD_BMI_AMOUNT="bmi_overall_amount";
    public static final String FIELD_BATCH_NO="batch_no";
    public static final String FIELD_AUDIT_TIME="audit_time";
    public static final String FIELD_LAST_UPDATED_BY="last_updated_by";
    public static final String FIELD_LAST_UPDATED_DATE="last_updated_date";
    public static final String FIELD_CREATED_BY="created_by";
    public static final String FIELD_CREATED_DATE="created_date";
    public static final String FIELD_HOSPITAL_ID="hospital_id";

    /**
     * id
     */
    private Long id;

    /**
     * 单据号
     */     
    private String no;
    /**
     * 住院号
     */     
    private String admissionNo;
    /**
     * 病区
     */     
    private String inpatientArea;
    /**
     * 参保类型
     */ 
    @DictTranslate(dictCode = "DIC_INSUR_TYPE")
    private String benefitType;
    /**
     * 人员类别
     */ 
    @DictTranslate(dictCode = "DIC_PSN_TYPE")
    private String personnelType;
    /**
     * 医疗类别
     */ 
    @DictTranslate(dictCode = "DIC_MEDICAL_TYPE")
    private String medicalType;
    /**
     * 住院状态
     */     
    private String hsStatus;
    /**
     * 就诊日期
     */     
    private Date itemDate;
    /**
     * 参保人编码
     */     
    private String patient;
    /**
     * 参保人姓名
     */     
    private String patientName;
    /**
     * 性别
     */     
    @DictTranslate(dictCode = "DIC_GENDER")
    private String gender;
    /**
     * 出生日期
     */     
    private String birthDate;
    /**
     * 入院诊断编码
     */     
    private String inDiseaseId;
    /**
     * 入院诊断名称
     */     
    private String inDiseaseZh;
    /**
     * 出院诊断编码
     */     
    private String outDiseaseId;
    /**
     * 出院诊断名称
     */     
    private String outDiseaseZh;
    /**
     * 主诊断编码
     */     
    private String primaryDiseaseId;
    /**
     * 主诊断名称
     */     
    private String primaryDiseaseZh;
    /**
     * 次诊断编码
     */     
    private String secondaryDiseaseId;
    /**
     * 次诊断名称
     */     
    private String secondaryDiseaseZh;
    /**
     * 是否违规
     */     
    @DictTranslate(dictCode = "LOCAL_YN_CODE")
    private String violationFlag;
    /**
     * 入院日期
     */     
    private Date inDate;
    /**
     * 出院日期
     */     
    private Date outDate;
    /**
     * 结算日期
     */     
    private Date settleDate;
    /**
     * 总费用
     */     
    private BigDecimal costs;
    /**
     * 医保内金额
     */     
    private BigDecimal bmiConveredAmount;
    /**
     * 医保统筹金额
     */     
    private BigDecimal bmiOverallAmount;
    /**
     * 批次号
     */     
    private String batchNo;
    /**
     * 审核时间
     */     
    private Date auditTime;
    /**
     * 更新人
     */     
    private Long lastUpdatedBy;
    /**
     * 更新时间
     */     
    private Date lastUpdatedDate;
    /**
     * 创建人
     */     
    private Long createdBy;
    /**
     * 创建时间
     */     
    private Date createdDate;
    /**
     * 院区id
     */     
    private String hospitalId;

    /**
     * 人工审核状态
     */
    private String mrStatus;
    /**
     * 人工审核意见
     */
    private String mrOpinion;
    /**
     * 人工审核时间
     */
    private Date mrTime;
    /**
     * 审核次数
     */
    @TableField
    private Integer auditTimes;
    /**
     * 是否计费(1.是，0否)
     */
    @TableField
    private String chargingFlag;
    /**
     * 开单科室
     */
    @TableField
    private String deptCode;
    /**
     * 科室名称
     */
    @TableField
    private String deptName;
    /**
     * 开单医生
     */
    @TableField
    private String docId;
    /**
     * 开单医生名称
     */
    @TableField
    private String docName;
    /**
     * 开单医生级别
     */
    @TableField
    private String docLevel;
    /**
     * 自付比例：默认0，不能为空
     */
    @TableField
    private BigDecimal payRatio;
    /**
     * 自付金额
     */
    @TableField
    private BigDecimal payAmount;
    /**
     * 挂号医生id
     */
    @TableField
    private String registerDocId;
    /**
     * 挂号医生名称
     */
    @TableField
    private String registerDocName;
    /**
     * 挂号ID
     */
    @TableField
    private String registerId;
    /**
     * 挂号时间
     */
    @TableField
    private Date registerTime;
    /**
     * 挂号费
     */
    @TableField
    private BigDecimal registrationFee;
    /**
     * 当日累计挂号次数
     */
    @TableField
    private Integer times;
    /**
     * 入院诊断编码
     */
    @TableField
    private String hpDiseaseId;
    /**
     * 入院诊断名称
     */
    @TableField
    private String hpDiseaseZh;
    /**
     * 转出科室编码
     */
    @TableField
    private String deptCodeFrom;
    /**
     * 转出科室名称
     */
    @TableField
    private String deptNameFrom;
    /**
     * 转出医生编码
     */
    @TableField
    private String docCodeFrom;
    /**
     * 转出医生名称
     */
    @TableField
    private String docNameFrom;
    /**
     * 转入科室编码
     */
    @TableField
    private String deptCodeTo;
    /**
     * 转入科室名称
     */
    @TableField
    private String deptNameTo;
    /**
     * 转入医生编码
     */
    @TableField
    private String docCodeTo;
    /**
     * 转入医生名称
     */
    @TableField
    private String docNameTo;
    /**
     * 转科日期
     */
    @TableField
    private Date tfDate;
    /**
     * 收费日期
     */
    @TableField
    private Date feeDate;

}

