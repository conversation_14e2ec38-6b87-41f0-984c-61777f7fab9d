package com.crhms.cloud.mqs.basic.vo;

import com.googlecode.jmapper.annotations.JMap;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class HisFeeVo {
    //明细流水号
    @JMap("detailNo")
    private String detail_no;
    //冲销目标明细no，存在值时表示此行为冲销行，只做冲销逻辑，不记录
    @JMap("refundNo")
    private String refund_no;

    //结算日期
    @JMap("billDate")
    private String bill_date;

    //医保项目编码
    @JMap("itemId")
    private String item_id;
    //医保项目名称
    @JMap("itemName")
    private String item_name;

    //院内项目编码
    @JMap("hisItemId")
    private String his_item_id;
    //院内项目名称
    @JMap("hisItemName")
    private String his_item_name;

    //项目类型
    @JMap("itemTypeCode")
    private String item_type_code;
    //项目类型名称
    @JMap("itemTypeName")
    private String item_type_name;
    //项目日期
    @JMap("itemDate")
    private String item_date;
    //数量
    @JMap("numbers")
    private BigDecimal numbers;
    //单价
    @JMap("price")
    private BigDecimal price;
    //总费用
    @JMap("costs")
    private BigDecimal costs;
    //计费数量
    @JMap("costNumber")
    private BigDecimal cost_number;
    //计费金额
    @JMap("costCosts")
    private BigDecimal cost_costs;
    //医保内金额
    @JMap("bmiConveredAmount")
    private BigDecimal bmi_convered_amount;
    //医保统筹金额
    @JMap("bmiOverallAmount")
    private BigDecimal bmi_overall_amount;
    //规格
    @JMap("specification")
    private String specification;
    //包装单位
    @JMap("usageUnit")
    private String usage_unit;
    //用量 西药最多2位小数点，填一次服用的片数，如一次2片的2。
    @JMap("usage")
    private String usage;
    //用药天数	String(256)	整数，默认值 0	是
    @JMap("usageDays")
    private String usage_days;
    //产品类型（0：药品，1：项目）
    @JMap("ptype")
    private String ptype;

    //开单医生编码
    @JMap("applyDoctorCode")
    private String apply_doctor_code;
    //开单医生名
    @JMap("applyDoctorName")
    private String apply_doctor_name;
    //开单医生级别
    @JMap("applyDoctorLevel")
    private String apply_doctor_level;
    //开单科室
    @JMap("applyDeptCode")
    private String apply_dept_code;
    //开单科室名称
    @JMap("applyDeptName")
    private String apply_dept_name;
    //受单医生
    @JMap("execDoctorCode")
    private String exec_doctor_code;
    //受单医生
    @JMap("execDoctorName")
    private String exec_doctor_name;
    //受单科室
    @JMap("execDeptCode")
    private String exec_dept_code;
    //受单科室
    @JMap("execDeptName")
    private String exec_dept_name;
    //住院医师
    @JMap("rtDoctorCode")
    private String rt_doctor_code;
    //住院医师
    @JMap("rtDoctorName")
    private String rt_doctor_name;
    //责任护士
    @JMap("rpNurseCode")
    private String rp_nurse_code;
    //责任护士
    @JMap("rpNurseName")
    private String rp_nurse_name;

    //计费标记 -1未设置 0未计费 1已计费 住院默认是已计费的 门诊治疗费默认是已计费 门诊其他费根据结算时标记已计费为准
    @JMap("chargingFlag")
    private String charging_flag;

    //是否自费
    @JMap("selfExpense")
    private String self_expense;

    //频次
    @JMap("frequencyInterval")
    private String frequency_interval;
    //贴数
    @JMap("postsNumber")
    private String posts_number;
    //备案审批号
    @JMap("approvalNumber")
    private String approval_number;
    //医师行政职务
    @JMap("zPhysicianap")
    private String z_physicianap;
    //出国带药标志
    @JMap("abroadDrugFlag")
    private String abroad_drug_flag;
    //外院费用标志
    @JMap("outHospitalFlag")
    private String out_hospital_flag;
    //自付比例：默认0，不能为空
    @JMap("payRatio")
    private String pay_ratio;

    //是否当前费用,违规弹窗/待强制保存 只处理当前费用，1是 0否，默认为1
    @JMap("isCurrent")
    private String is_current;

    //甲乙类
    @JMap("categoryName")
    private String category_name;

    //是否增量（0-否,1-是）
    @JMap("operationType")
    private String operation_type;

    //是否被冲销标记（0-否,1-是）
    @JMap("reverseFlag")
    private String reverse_flag;
    //是否长期医嘱
    @JMap("longDrordFlag")
    private String long_drord_flag;
    //目录类别
    @JMap("hilistType")
    private String hilist_type;
    //收费类别
    @JMap("chrgType")
    private String chrg_type;
    //医嘱行为
    @JMap("drordBhvr")
    private String drord_bhvr;
    //医保目录等级
    @JMap("hilistLv")
    private String hilist_lv;
    //医保目录价格
    @JMap("hilistPric")
    private BigDecimal hilist_pric;
    //自费金额
    @JMap("ownpayAmt")
    private BigDecimal ownpay_amt;
    //自付金额
    @JMap("selfpayAmt")
    private BigDecimal selfpay_amt;

    //是否出院带药（0-否,1-是）
    @JMap("outpatientMedication")
    private String outpatient_medication;

    //用药途径
    @JMap("routeAdministration")
    private String route_administration;
}
