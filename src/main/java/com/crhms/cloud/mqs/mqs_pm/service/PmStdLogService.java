package com.crhms.cloud.mqs.mqs_pm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdLog;
import com.crhms.cloud.mqs.mqs_pm.mapper.PmStdLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 物价管理-市级标准物价-更新日志service
 *
 * @author: wangxingang
 * @date: 2023/02/01
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class PmStdLogService extends ServiceImpl<PmStdLogMapper, PmStdLog> {

    /**
     * 分页查询
     *
     * @param categoryList 更新大类
     * @param updateBy     更新人
     * @param pageInfo     分页信息
     */
    public List<PmStdLog> queryPage(List<String> categoryList, String updateBy, Page<PmStdLog> pageInfo) {
        return baseMapper.queryPage(categoryList, updateBy, pageInfo);
    }


    /**
     * 插入日志
     *
     * @param log 日志信息
     */
    public void insertLog(PmStdLog log) {
        baseMapper.insert(log);
    }

}
