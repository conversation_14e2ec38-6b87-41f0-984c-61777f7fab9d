package com.crhms.cloud.mqs.mqs_pm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crhms.cloud.core.utils.PageUtil;
import com.crhms.cloud.mqs.mqs_pm.config.PmStdLogRecord;
import com.crhms.cloud.mqs.mqs_pm.domain.PmStdConsume;
import com.crhms.cloud.mqs.mqs_pm.service.PmStdConsumeService;
import com.crhms.cloud.mqs.mqs_pm.vo.ConsumeImportVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 物价管理-市级标准物价-耗材
 *
 * @author: wangxingang
 * @date: 2023/01/31
 */

@RestController
@RequestMapping("/api/mqs/pmStd/consume")
public class PmStdConsumeController {

    @Autowired
    private PmStdConsumeService service;

    /**
     * 新增或修改
     *
     * @param consume 耗材信息
     * <AUTHOR>
     * @date 2023.01.31
     */
    @PmStdLogRecord(category = "耗材", log = "新增数据")
    @PostMapping("/submit")
    public ResponseEntity<Object> submitConsume(@RequestBody PmStdConsume consume) {
        service.submitConsume(consume);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * 分页查询
     *
     * @param productCode 产品编码
     * @param productName 产品名称
     * @param startPeriod 时间周期-开始时间
     * @param endPeriod   时间周期-结束时间
     * @param page        页序号
     * @param pageSize    页大小
     * <AUTHOR>
     * @date 2023.01.31
     */
    @PostMapping("/page")
    public ResponseEntity<List<PmStdConsume>> queryPage(@RequestParam(value = "productCode", required = false) String productCode,
                                                        @RequestParam(value = "productName", required = false) String productName,
                                                        @RequestParam(value = "startPeriod", required = false) @DateTimeFormat(pattern = "yyyy.MM.dd") String startPeriod,
                                                        @RequestParam(value = "endPeriod", required = false) @DateTimeFormat(pattern = "yyyy.MM.dd") String endPeriod,
                                                        @RequestParam(value = "page", defaultValue = "1") int page,
                                                        @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        Page<PmStdConsume> pageInfo = PageUtil.getPage(page, pageSize);
        List<PmStdConsume> result = service.queryPage(productCode, productName, startPeriod, endPeriod, pageInfo);
        return new ResponseEntity<>(result, PageUtil.getTotalHeader(pageInfo), HttpStatus.OK);
    }


    /**
     * 批量删除
     *
     * @param ids id集合
     * <AUTHOR>
     * @date 2023.01.31
     */
    @PmStdLogRecord(category = "耗材", log = "删除数据")
    @PostMapping("/delete")
    public ResponseEntity<Object> deleteByIds(@RequestBody List<Long> ids) {
        service.deleteByIds(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    /**
     * 导出Excel
     *
     * @param response    响应
     * @param productCode 产品编码
     * @param productName 产品名称
     * @param startPeriod 时间周期-开始时间
     * @param endPeriod   时间周期-结束时间
     * @param isModel     是否导出模板?true是false否(默认是否)
     * <AUTHOR>
     * @date 2023.01.30
     */
    @PmStdLogRecord(category = "耗材", log = "导出数据")
    @PostMapping("/export")
    public void exportExcel(HttpServletResponse response,
                            @RequestParam(value = "productCode", required = false) String productCode,
                            @RequestParam(value = "productName", required = false) String productName,
                            @RequestParam(value = "startPeriod", required = false) @DateTimeFormat(pattern = "yyyy.MM.dd") String startPeriod,
                            @RequestParam(value = "endPeriod", required = false) @DateTimeFormat(pattern = "yyyy.MM.dd") String endPeriod,
                            @RequestParam(value = "isModel", defaultValue = "false") boolean isModel) {
        service.exportExcel(response, productCode, productName, startPeriod, endPeriod, isModel);
    }


    /**
     * 导入Excel
     */
    @PmStdLogRecord(category = "耗材", log = "导入数据")
    @PostMapping("/import")
    public ResponseEntity<List<PmStdConsume>> importExcel(ConsumeImportVo importVo) {
        return new ResponseEntity<>(service.importExcel(importVo), HttpStatus.OK);
    }

}
