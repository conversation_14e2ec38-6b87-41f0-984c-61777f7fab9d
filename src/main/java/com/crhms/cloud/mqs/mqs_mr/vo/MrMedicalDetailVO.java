package com.crhms.cloud.mqs.mqs_mr.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.crhms.cloud.mqs.sys.mqsInterface.DictTranslate;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class MrMedicalDetailVO {

    //单据号
    private String no;

    /**
     * 参保人姓名
     */
    private String patientName;

    /**
     * 性别
     */
    @DictTranslate(dictCode = "DIC_GENDER")
    private String patientGender;

    /**
     * 出生日期
     */
    private String patientBirthday;

    /**
     * 参保类型编码（险种类型）
     */
    @DictTranslate(dictCode = "DIC_INSUR_TYPE")
    private String benefitTypeId;

    /**
     * 人员类别编码
     */
    @DictTranslate(dictCode = "DIC_PSN_TYPE")
    private String personnelType;

    /**
     * 在院状态 (1-出院，0-在院)
     */
    @DictTranslate(dictCode = "LOCAL_IS_DISCHARGE")
    private String dischargeState;

    /**
     * 违规来源（0-医保，1-院内，2-人工） 审核渠道
     */
    @DictTranslate(dictCode = "LOCLA_AUDITSOURCE", dictSplit = ",")
    private String auditSource;

    /**
     * 标记
     */
    private String lables;

    /**
     * 医疗类别(原就医方式编码)
     */
    @DictTranslate(dictCode = "DIC_MEDICAL_TYPE")
    private String claimTypeId;

    /**
     * 就诊流水号
     */
    private String admissionNo;

    /**
     * 参保人编码
     */
    private String patientId;

    /**
     * 院区
     */
    private String hospAreaName;

    /**
     * 当前科室
     */
    private String presentDeptName;

    /**
     * 开单医生名称
     */
    private String docName;

    /**
     * 单据总金额
     */
    private BigDecimal totalAmount;

    /**
     * 入院天数
     */
    private String inhospitalDays;

    /**
     * 出院诊断
     */
    private String diagnosisName;

    /**
     * 年龄
     */
    private int age;

    /**
     * 证件号
     */
    private String patientIdno;

    //审核状态 (0 未审核 1.审核通过)
    private String mrStatus;

    //明细列表
    private List<MrMedicalDetail> details;

}
