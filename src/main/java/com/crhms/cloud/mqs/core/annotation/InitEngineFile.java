package com.crhms.cloud.mqs.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义注解：应用于初始化引擎文件时字段处理
 * <AUTHOR>
 * @date 2023/4/3
 **/
@Target(ElementType.FIELD)  // 该注解应用于字段上
@Retention(RetentionPolicy.RUNTIME)  // 在执行期间发生作用
public @interface InitEngineFile {

    /* 该字段是否需要被索引化，部分数字类型的字段不需要被索引化 */
    boolean needIndex() default false;

    /* 表字段名 */
    String tableColName() default "";

    /* 列存区文件名 */
    String colFileName() default "";

    /* 时间字段格式化 */
    String dateFormat() default "yyyy-MM-dd HH:mm:ss";

}
