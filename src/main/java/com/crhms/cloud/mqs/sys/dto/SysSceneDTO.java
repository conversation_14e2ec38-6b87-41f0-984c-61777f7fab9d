package com.crhms.cloud.mqs.sys.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysSceneDTO {

    private Long id;

    //子场景名称
    private String sceneName;

    //子场景编码
    private String sceneCode;

    private String mainSceneCode;

    private Integer orderNum;

    private String enable;

    //审核弹窗名称
    private String functionCode;

    private String miReviewApi;

    private String miReviewApiSelect;

    private String manualReview;

    private String showDimensions;

    private String showFormat;

    //转自费
    private String isSelf;

    //转自费不可用条件
    private String selfPaidCondition;

    //生成资费协议书
    private String isSelfAgreement;

    private String hospitalId;

    //子场景id
    private Long sceneId;

    //子场景配置id
    private Long sceneFunctionId;

    //医保审核接口（反馈服务）
    private String miReviewApiBack;

    //是否忽略（1-是，0-否）
    private String isIgnore;

}
