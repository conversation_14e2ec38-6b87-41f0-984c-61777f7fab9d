package com.crhms.cloud.mqs.mqs_hp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crhms.cloud.mqs.mqs_hp.domain.HpPredHis;
import com.crhms.cloud.mqs.mqs_hp.mapper.HpPredHisMapper;
import com.crhms.cloud.mqs.mqs_hp.service.HpPredHisService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 住院审核-预出院审核-历史表(HpPredHis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-17 10:22:14
 */
@Service("hpPredHisService")
public class HpPredHisService extends ServiceImpl< HpPredHisMapper, HpPredHis> {

}
