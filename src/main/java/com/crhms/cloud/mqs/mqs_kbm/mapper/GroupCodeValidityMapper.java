package com.crhms.cloud.mqs.mqs_kbm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crhms.cloud.mqs.mqs_kbm.domain.GroupCodeValidity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 知识库管理-分组合理性-编码有效性
 *
 * @author: wangxingang
 * @date: 2023.02.06
 */

@Mapper
public interface GroupCodeValidityMapper extends BaseMapper<GroupCodeValidity> {
    /**
     * 查询版本号下拉列表
     */
    List<String> selectVersionNoList();

    /**
     * 查询类型下拉列表
     */
    List<String> selectTypeList();

    /**
     * 查询是否有效下拉列表
     */
    List<String> selectValidFlagList();
}
