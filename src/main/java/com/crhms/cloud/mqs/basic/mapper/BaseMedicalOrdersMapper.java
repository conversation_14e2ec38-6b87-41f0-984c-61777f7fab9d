package com.crhms.cloud.mqs.basic.mapper;

import com.crhms.cloud.mqs.basic.domain.BaseMedicalOrders;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 医嘱明细(BaseMedicalOrders)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-10 13:47:54
 */

@Mapper
public interface BaseMedicalOrdersMapper extends BaseMapper<BaseMedicalOrders> {
    /**
     * 删除医嘱明细
     * @param table
     * @param no
     * @param hospitalId
     * @return
     */
    int delOrders(@Param("table") String table, @Param("orderDetailNo") String orderDetailNo,@Param("no") String no,@Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    /**
     * 删除医嘱明细
     * @param table
     * @param admissionNo
     * @param hospitalId
     */
    void deleteOrdersByAdNo(@Param("table") String table, @Param("orderDetailNos") List<String> orderDetailNos,@Param("nos") List<String> nos,@Param("admissionNo") String admissionNo, @Param("hospitalId") String hospitalId);

    /**
     * 批量保存至医嘱明细表
     * @param table
     * @param baseMedicalOrders
     */
    void batchInsertOrders(@Param("table") String table, @Param("entities") List<BaseMedicalOrders> baseMedicalOrders);

    /**
     * 批量保存医嘱明细历史表
     * @param table
     * @param baseMedicalOrders
     */
    void batchInsertOrdersHis(@Param("table") String table, @Param("entities") List<BaseMedicalOrders> baseMedicalOrders);

    /**
     * 查询单次就诊下医嘱明细
     * @param tableName
     * @param admissionNo
     * @param batchNo
     * @param hospitalId
     * @return
     */
    List<BaseMedicalOrders> queryOrdersHisByBatchNo(@Param("table") String tableName, @Param("admissionNo") String admissionNo, @Param("batchNo") String batchNo, @Param("hospitalId") String hospitalId);

}

